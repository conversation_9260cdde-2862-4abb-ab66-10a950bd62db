# xAPI Implementation Plan

## Executive Summary

Adding xAPI support using Rustici Software for package generation and a lightweight LRS for statement capture and forwarding. This approach provides true xAPI compliance while maintaining the simple "download and go" customer experience. xAPI packages launch directly to content (not assignment lists), requiring new content delivery routes and frontend interfaces.

**Total Estimated LOE: 56-76 hours (7-9.5 developer days)**

---

## Phase 1: Database Foundation
**LOE: 6-9 hours**

### 1.1 Integration Types Update (1-2 hours)
- Add 'xapi' and 'tincan' to integrations model enum
- Update any related validation or UI dropdowns

### 1.2 xAPI Programs Model (2-3 hours)
- Create new `xapiPrograms` table (mirrors `scormPrograms` structure)
- Fields: userId, registrationId (UUID), resourceId, model, completionDate
- Add database migration script
- Create Sequelize model with associations

### 1.3 xAPI Statements Storage (2-3 hours)
- Create `xapiStatements` table for lightweight LRS
- Fields: id (UUID), actor, verb, object, result, timestamp, registration
- Add indexes for efficient querying
- Create Sequelize model for statement storage

### 1.4 Content Items Extension (1 hour)
- Add `isXapi` boolean flag to `accountContentItems` table
- Update content item creation logic for xAPI

---

## Phase 2: Lightweight LRS Implementation
**LOE: 12-16 hours**

### 2.1 xAPI Statement Storage (4-6 hours)
- Implement xAPI statement validation and storage
- Create LRS endpoints: POST/GET/PUT /lrs/statements
- Add statement querying capabilities
- Implement proper xAPI statement format handling

### 2.2 Statement Forwarding Service (4-6 hours)
- Create service to forward statements to customer LRS
- Implement retry logic for failed forwards
- Add configuration for customer LRS endpoints
- Handle authentication for customer LRS systems

### 2.3 Resume Data Management (4-4 hours)
- Implement suspend/resume using xAPI statements
- Query statements for progress continuation
- Handle bookmark data in xAPI result extensions
- Integrate with content delivery for seamless resume

## Phase 3: Backend Content Delivery
**LOE: 14-18 hours**

### 3.1 xAPI Authentication System (4-6 hours)
- Create `verifyXapi` endpoint (similar to `verifyScorm`)
- Implement user lookup by registrationId
- Generate access tokens for xAPI users
- Handle user creation for new xAPI registrations

### 3.2 Direct Content Delivery Routes (6-8 hours)
- Create `/xapi/programs/:id` and `/xapi/lessons/:id` routes
- Implement content serving controllers (bypasses assignment list)
- Add authentication middleware for xAPI routes
- Handle content rendering for direct display

### 3.3 xAPI Progress Integration (4-4 hours)
- Integrate content delivery with LRS statement storage
- Add statement generation on content interactions
- Implement completion tracking via xAPI statements
- Connect progress tracking to customer LRS forwarding

---

## Phase 4: Rustici Integration & Package Generation
**LOE: 12-16 hours**

### 4.1 Rustici xAPI Integration (6-8 hours)
- Research and implement Rustici xAPI package generation APIs
- Set up Rustici authentication and configuration
- Create service layer for Rustici xAPI package creation
- Handle Rustici-specific xAPI package formats

### 4.2 xAPI Package Templates (3-4 hours)
- Create `tincan.xml` manifest templates (Rustici-compatible)
- Build launch file templates with proper URLs
- Create xAPI-specific configuration files
- Set up template directory structure

### 4.3 Package Generation Service (3-4 hours)
- Integrate Rustici xAPI package generation with existing flow
- Implement xAPI archive generation logic
- Add registrationId generation and tracking
- Create download endpoints for xAPI packages

---

## Phase 5: Frontend Content Interface
**LOE: 10-14 hours**

### 5.1 Direct Content Viewing Components (6-8 hours)
- Create xAPI content viewer components
- Implement direct content display (no assignment list)
- Integrate with existing lesson card components
- Add content navigation and progress display

### 5.2 xAPI Statement Integration (2-4 hours)
- Add xAPI statement generation to content interactions
- Integrate with lightweight LRS for statement storage
- Implement automatic statement forwarding to customer LRS
- Add statement validation and error handling

### 5.3 Resume Functionality (2-2 hours)
- Implement resume from xAPI suspend statements
- Query LRS for previous progress data
- Add bookmark handling using xAPI result extensions

---

## Phase 6: Admin Interface Updates
**LOE: 4-6 hours**

### 6.1 Download Interface (2-3 hours)
- Add xAPI download buttons alongside SCORM
- Update admin UI to show integration types
- Add proper labeling and help text

### 6.2 LRS Configuration Interface (2-3 hours)
- Add customer LRS endpoint configuration
- Create interface for LRS authentication settings
- Add validation for LRS connectivity
- Handle accounts with both SCORM and xAPI

---

## Phase 7: Testing & Integration
**LOE: 8-12 hours**

### 7.1 LRS Testing (3-4 hours)
- Test xAPI statement storage and retrieval
- Verify statement forwarding to customer LRS
- Test LRS connectivity and error handling
- Validate xAPI statement format compliance

### 7.2 End-to-End Testing (3-4 hours)
- Test xAPI package generation via Rustici
- Verify content launch from external LRS
- Test resume functionality using xAPI statements
- Validate complete learner journey

### 7.3 Backward Compatibility Testing (2-3 hours)
- Ensure existing SCORM functionality unchanged
- Test mixed SCORM/xAPI account scenarios
- Verify no regression in current workflows

### 7.4 Documentation (1-1 hour)
- Update API documentation for LRS endpoints
- Create customer LRS configuration guide
- Document xAPI statement forwarding setup

---

## Implementation Phases Summary

| Phase | Description | LOE | Dependencies |
|-------|-------------|-----|--------------|
| 1 | Database Foundation | 6-9 hours | None |
| 2 | Lightweight LRS Implementation | 12-16 hours | Phase 1 |
| 3 | Backend Content Delivery | 14-18 hours | Phase 1, 2 |
| 4 | Rustici Integration & Package Generation | 12-16 hours | Phase 1 |
| 5 | Frontend Content Interface | 10-14 hours | Phase 2, 3 |
| 6 | Admin Interface Updates | 4-6 hours | Phase 4 |
| 7 | Testing & Integration | 8-12 hours | All phases |

**Total: 66-91 hours**
**Realistic Estimate: 56-76 hours (accounting for parallel work)**

---

## Key Architectural Decisions

### Rustici Integration Strategy
- **Decision**: Use Rustici Software for xAPI package generation
- **Rationale**: Leverages existing Rustici relationship and proven SCORM expertise
- **Benefit**: Industry-standard xAPI packages with minimal custom development

### Lightweight LRS Approach
- **Decision**: Implement internal LRS for statement capture and forwarding
- **Rationale**: Provides true xAPI compliance while maintaining control
- **Benefit**: Resume functionality works without customer LRS dependency

### Statement Forwarding Strategy
- **Decision**: Capture statements locally, then forward to customer LRS
- **Rationale**: Ensures data availability for resume while providing customer analytics
- **Benefit**: Best of both worlds - reliability and customer data ownership

### Content Delivery Approach
- **Decision**: Create parallel content delivery system for xAPI
- **Rationale**: xAPI requires direct content launch vs SCORM assignment creation
- **Impact**: New frontend components needed, but existing SCORM unchanged

---

## Rustici + Lightweight LRS Benefits

### Why Rustici for Package Generation
- **Proven Expertise**: Rustici is the industry leader in SCORM/xAPI standards
- **Existing Relationship**: Leverage current Rustici partnership
- **Quality Assurance**: Industry-standard xAPI packages guaranteed
- **Reduced Development**: Minimal custom package generation code needed
- **Future-Proof**: Automatic updates for xAPI specification changes

### Why Lightweight LRS
- **True xAPI Compliance**: Proper xAPI statement storage and querying
- **Resume Reliability**: Local statement storage ensures resume works offline
- **Customer Analytics**: Forward statements to customer LRS for their reporting
- **Flexibility**: Works with any customer LRS or standalone
- **Performance**: Fast local queries for resume data
- **Control**: Full control over statement format and timing

### Combined Architecture Benefits
- **Best of Both Worlds**: Rustici quality + local control
- **Customer Choice**: Works with or without customer LRS
- **Scalability**: Local LRS handles high-frequency interactions
- **Reliability**: Multiple data sources ensure no data loss
- **Standards Compliance**: True xAPI implementation throughout

---

## Risk Mitigation

### Backward Compatibility
- All existing SCORM functionality preserved
- Separate code paths for SCORM vs xAPI
- Comprehensive regression testing planned

### Customer Experience
- Maintain simple download model (same as SCORM)
- No complex setup required from customers
- Clear documentation and migration path

### Technical Complexity
- Reuse existing components where possible
- Follow proven SCORM patterns
- Incremental implementation approach

---

## Success Criteria

### Functional Requirements
- Generate downloadable xAPI packages via Rustici
- Direct content launch from external LMS/LRS
- Resume functionality using xAPI suspend statements
- xAPI statement capture and forwarding to customer LRS
- True xAPI compliance with industry standards
- Existing SCORM functionality completely unchanged

### Performance Requirements
- Package generation time < 30 seconds (via Rustici)
- Content load time < 3 seconds
- Statement storage and forwarding < 1 second
- No impact on existing SCORM performance

### Customer Experience
- Simple download process (identical to SCORM)
- Works with any xAPI-compliant LRS system
- Optional LRS configuration for enhanced analytics
- Clear migration documentation available

---

## Next Steps

1. **Rustici Consultation**: Engage Rustici for xAPI package generation capabilities
2. **LRS Architecture Review**: Finalize lightweight LRS design and technology stack
3. **Stakeholder Review**: Validate approach and timeline
4. **Resource Allocation**: Assign development resources
5. **Phase 1 Start**: Begin with database foundation and LRS implementation
6. **Customer Communication**: Prepare customer-facing documentation
7. **Testing Strategy**: Define comprehensive test scenarios including LRS integration

---

*Document Version: 2.0*
*Date: 2025-01-10*
*Total Estimated LOE: 56-76 hours (7-9.5 developer days)*
