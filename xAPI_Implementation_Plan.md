# xAPI Implementation Plan

## Executive Summary

Adding xAPI support to create downloadable packages alongside existing SCORM functionality. Key difference: xAPI packages launch directly to content (not assignment lists), requiring new content delivery routes and frontend interfaces.

**Total Estimated LOE: 40-56 hours (5-7 developer days)**

---

## Phase 1: Database Foundation
**LOE: 4-6 hours**

### 1.1 Integration Types Update (1-2 hours)
- Add 'xapi' and 'tincan' to integrations model enum
- Update any related validation or UI dropdowns

### 1.2 xAPI Programs Model (2-3 hours)
- Create new `xapiPrograms` table (mirrors `scormPrograms` structure)
- Fields: userId, registrationId (UUID), resourceId, model, completionDate
- Add database migration script
- Create Sequelize model with associations

### 1.3 Content Items Extension (1 hour)
- Add `isXapi` boolean flag to `accountContentItems` table
- Update content item creation logic for xAPI

---

## Phase 2: Backend Content Delivery
**LOE: 16-22 hours**

### 2.1 xAPI Authentication System (4-6 hours)
- Create `verifyXapi` endpoint (similar to `verifyScorm`)
- Implement user lookup by registrationId
- Generate access tokens for xAPI users
- Handle user creation for new xAPI registrations

### 2.2 Direct Content Delivery Routes (8-10 hours)
- Create `/xapi/programs/:id` and `/xapi/lessons/:id` routes
- Implement content serving controllers (bypasses assignment list)
- Add authentication middleware for xAPI routes
- Handle content rendering for direct display

### 2.3 xAPI Progress Tracking (4-6 hours)
- Create completion endpoints (mirror SCORM setComplete)
- Integrate with existing events system
- Add xAPI-specific event types
- Ensure progress tracking works with existing analytics

---

## Phase 3: Package Generation
**LOE: 10-14 hours**

### 3.1 xAPI Package Templates (4-6 hours)
- Create `tincan.xml` manifest templates
- Build launch file templates with proper URLs
- Create xAPI-specific configuration files
- Set up template directory structure

### 3.2 Package Generation Service (4-6 hours)
- Extend existing SCORM package service for xAPI
- Implement xAPI archive generation logic
- Add registrationId generation and tracking
- Create zip packaging for xAPI content

### 3.3 Download Endpoints (2-2 hours)
- Add xAPI download routes to programs and lessons
- Implement download controllers
- Add proper file headers and cleanup

---

## Phase 4: Frontend Content Interface
**LOE: 12-16 hours**

### 4.1 Direct Content Viewing Components (8-10 hours)
- Create xAPI content viewer components
- Implement direct content display (no assignment list)
- Integrate with existing lesson card components
- Add content navigation and progress display

### 4.2 xAPI Statement Integration (2-4 hours)
- Add xAPI statement generation to content interactions
- Integrate with existing event tracking
- Implement optional customer LRS statement sending
- Add statement validation and error handling

### 4.3 Resume Functionality (2-2 hours)
- Implement basic resume from registrationId
- Use existing progress tracking for continuation
- Add bookmark handling if needed

---

## Phase 5: Admin Interface Updates
**LOE: 4-6 hours**

### 5.1 Download Interface (2-3 hours)
- Add xAPI download buttons alongside SCORM
- Update admin UI to show integration types
- Add proper labeling and help text

### 5.2 Multi-Integration Support (2-3 hours)
- Handle accounts with both SCORM and xAPI
- Update integration management interface
- Add validation for integration configurations

---

## Phase 6: Testing & Integration
**LOE: 6-8 hours**

### 6.1 End-to-End Testing (3-4 hours)
- Test xAPI package generation and download
- Verify content launch from external LRS
- Test resume functionality and progress tracking
- Validate xAPI statement generation

### 6.2 Backward Compatibility Testing (2-3 hours)
- Ensure existing SCORM functionality unchanged
- Test mixed SCORM/xAPI account scenarios
- Verify no regression in current workflows

### 6.3 Documentation (1-1 hour)
- Update API documentation
- Create customer migration guide
- Document new xAPI endpoints and flows

---

## Implementation Phases Summary

| Phase | Description | LOE | Dependencies |
|-------|-------------|-----|--------------|
| 1 | Database Foundation | 4-6 hours | None |
| 2 | Backend Content Delivery | 16-22 hours | Phase 1 |
| 3 | Package Generation | 10-14 hours | Phase 1 |
| 4 | Frontend Content Interface | 12-16 hours | Phase 2 |
| 5 | Admin Interface Updates | 4-6 hours | Phase 3 |
| 6 | Testing & Integration | 6-8 hours | All phases |

**Total: 52-72 hours**
**Realistic Estimate: 40-56 hours (accounting for parallel work)**

---

## Key Architectural Decisions

### Resume Data Strategy
- **Decision**: Use existing progress tracking system (no suspend data storage initially)
- **Rationale**: Mirrors proven SCORM approach, simpler implementation
- **Future**: Can add enhanced suspend data later if needed

### Content Delivery Approach
- **Decision**: Create parallel content delivery system for xAPI
- **Rationale**: xAPI requires direct content launch vs SCORM assignment creation
- **Impact**: New frontend components needed, but existing SCORM unchanged

### LRS Integration
- **Decision**: Optional customer LRS integration (content works without it)
- **Rationale**: Maintains simple "download and go" customer experience
- **Benefit**: No complex LRS configuration required from customers

---

## Risk Mitigation

### Backward Compatibility
- All existing SCORM functionality preserved
- Separate code paths for SCORM vs xAPI
- Comprehensive regression testing planned

### Customer Experience
- Maintain simple download model (same as SCORM)
- No complex setup required from customers
- Clear documentation and migration path

### Technical Complexity
- Reuse existing components where possible
- Follow proven SCORM patterns
- Incremental implementation approach

---

## Success Criteria

### Functional Requirements
- Generate downloadable xAPI packages
- Direct content launch from external LMS/LRS
- Resume functionality without customer LRS dependency
- xAPI statement generation for customer analytics
- Existing SCORM functionality completely unchanged

### Performance Requirements
- Package generation time < 30 seconds
- Content load time < 3 seconds
- No impact on existing SCORM performance

### Customer Experience
- Simple download process (identical to SCORM)
- Works with any LRS system
- Clear migration documentation available

---

## Next Steps

1. **Stakeholder Review**: Validate approach and timeline
2. **Resource Allocation**: Assign development resources
3. **Phase 1 Start**: Begin with database foundation
4. **Customer Communication**: Prepare customer-facing documentation
5. **Testing Strategy**: Define comprehensive test scenarios

---

*Document Version: 1.0*  
*Date: 2025-01-10*  
*Total Estimated LOE: 40-56 hours (5-7 developer days)*
