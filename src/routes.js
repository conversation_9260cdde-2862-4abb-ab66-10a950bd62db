const { requireAuth } = require('./services/utils/authUtils');
const AuthRoutes = require('./routes/auth.routes');
const AuthManagementRoutes = require('./routes/auth-management.routes');
const UsersRoutes = require('./routes/user.routes');
const QuestionAnswerRoutes = require('./routes/question-answer.routes');
const ResourceRoutes = require('./routes/resource.routes');
const EventRoutes = require('./routes/event.routes');
const AccountRoutes = require('./routes/account.routes');
const MotivationRoutes = require('./routes/motivation.routes');
const TopicRoutes = require('./routes/topic.routes');
const TagRoutes = require('./routes/tag.routes');
const LessonRoutes = require('./routes/lesson.routes');
const LessonCardRoutes = require('./routes/lesson-card.routes');
const MediaAssetRoutes = require('./routes/media-assets.routes');
const AnswerCardRoutes = require('./routes/answer-card.routes');
const UserLessonRoutes = require('./routes/user-lessons.routes');
const GroupRoutes = require('./routes/groups.routes');
const UserResourceRoutes = require('./routes/user-resources.routes');
const ExpertRoutes = require('./routes/experts.routes');
const ProgramRoutes = require('./routes/programs.routes');
const DashboardRoutes = require('./routes/dashboard.routes');
const NotificationRoutes = require('./routes/notifications.routes');
const SendLinkRoutes = require('./routes/send-links.routes');
const CampaignRoutes = require('./routes/campaign.routes');
const ProgramLessonRoutes = require('./routes/lesson-program.routes');
const SimilarQuestionRoutes = require('./routes/simlilar-question.routes');
const LastContentViewedRoutes = require('./routes/last-content-viewed.routes');
const AccountFieldRoutes = require('./routes/account-fields.routes');
const CSVImportRoutes = require('./routes/csv-import.routes');
const ProtectedTraitsDefaultsRoutes = require('./routes/protected-traits-defaults.routes');
const ResourceBundleRoutes = require('./routes/resource-bundles.routes');
const AccountProgramRoutes = require('./routes/account-programs.routes');
const AccountLessonRoutes = require('./routes/account-lessons.routes');
const AccountLessonCardRoutes = require('./routes/account-lesson-cards.routes');
const ContentPackageRoutes = require('./routes/content-package.routes');
const SlackRoutes = require('./routes/slack.routes');
const RoleRoutes = require('./routes/roles.routes');
const ScormProgramRoutes = require('./routes/scorm-programs.routes');
const XapiContentRoutes = require('./routes/xapi-content.routes');
const VideoRoutes = require('./routes/videos.routes');
const catalogItemsRoutes = require('./routes/catalog-items.routes');
const assessmentItemsRoutes = require('./routes/assessment-items.routes');
const ZendeskRoutes = require('./routes/zendesk.routes');
const SweeperRoutes = require('./routes/sweeper.routes');
const AccountContentItemsRoutes = require('./routes/account-content-items.routes');
const ListingRoutes = require('./routes/listing.routes');
const CategoryRoutes = require('./routes/categories.routes');
const ConceptRoutes = require('./routes/concepts.routes');
const AuditLogRoutes = require('./routes/audit-log.routes');
const ResourceAssetRoutes = require('./routes/resource-assets.routes');
const ContentLibraryRoutes = require('./routes/content-library.routes');
const AnalyticsRoutes = require('./routes/analytics.routes');

module.exports.init = (app) => {
  // Api entity routes
  app.use('/authentication', AuthRoutes);
  app.use('/auth-management', AuthManagementRoutes);
  app.use('/accounts', AccountRoutes);
  app.use('/users', UsersRoutes);
  app.use('/question-answers', QuestionAnswerRoutes);
  app.use('/resources', ResourceRoutes);
  app.use('/events', EventRoutes);
  app.use('/motivations', MotivationRoutes);
  app.use('/topics', TopicRoutes);
  app.use('/tags', TagRoutes);
  app.use('/lessons', LessonRoutes);
  app.use('/lesson-cards', LessonCardRoutes);
  app.use('/media-assets', MediaAssetRoutes);
  app.use('/resource-assets', ResourceAssetRoutes);
  app.use('/answer-cards', AnswerCardRoutes);
  app.use('/user-lessons', UserLessonRoutes);
  app.use('/groups', GroupRoutes);
  app.use('/user-resources', UserResourceRoutes);
  app.use('/experts', ExpertRoutes);
  app.use('/programs', ProgramRoutes);
  app.use('/dashboard', DashboardRoutes);
  app.use('/notifications', NotificationRoutes);
  app.use('/send-links', SendLinkRoutes);
  app.use('/campaigns', CampaignRoutes);
  app.use('/program-lessons', ProgramLessonRoutes);
  app.use('/similar-questions', SimilarQuestionRoutes);
  app.use('/last-content-viewed', LastContentViewedRoutes);
  app.use('/account-fields', AccountFieldRoutes);
  app.use('/csv-import', CSVImportRoutes);
  app.use('/protected-traits-defaults', ProtectedTraitsDefaultsRoutes);
  app.use('/resource-bundles', ResourceBundleRoutes);
  app.use('/account-programs', AccountProgramRoutes);
  app.use('/account-lessons', AccountLessonRoutes);
  app.use('/account-lesson-cards', AccountLessonCardRoutes);
  app.use('/content-packages', ContentPackageRoutes);
  app.use('/slack', SlackRoutes);
  app.use('/roles', RoleRoutes);
  app.use('/scorm-programs', ScormProgramRoutes);
  app.use('/xapi', XapiContentRoutes);
  app.use('/videos', VideoRoutes);
  app.use('/catalog-items', catalogItemsRoutes);
  app.use('/assessment-items', assessmentItemsRoutes);
  app.use('/zendesk', ZendeskRoutes);
  app.use('/sweeper', SweeperRoutes);
  app.use('/account-content-items', AccountContentItemsRoutes);
  app.use('/listing', ListingRoutes);
  app.use('/categories', CategoryRoutes);
  app.use('/concepts', ConceptRoutes);
  app.use('/audit-logs', AuditLogRoutes);
  app.use('/content-library', ContentLibraryRoutes);
  app.use('/analytics', AnalyticsRoutes);

  // Some testing routes for authentication
  app.use('/secured', requireAuth);
  app.get('/ping', (req, res) => {
    res.status(200).send('Server is alive...');
  });
  app.get('/secured/ping', (req, res) => {
    res.status(200).send('All good. You only get this message if you\'re authenticated');
  });
};
