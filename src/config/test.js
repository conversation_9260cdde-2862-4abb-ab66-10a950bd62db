const defaultEnvConfig = require('./default');

module.exports = {
  app: {
    title: `${defaultEnvConfig.app.title} - Test Environment`,
  },
  db: {
    uri: 'mysql://root@localhost:3306/hootsworth_test',
    analyticsUri: 'mysql://root@localhost:3306/hootsworth_test',
    logging: null,
  },
  appSecret: '04e8f6c7-9eb7-402b-bb70-9d9c8d037d9a',
  port: process.env.PORT || 3032,
  logger: {
    level: 'silent',
  },
  elasticsearch: {
    index: 'test_resource',
  },
  scheduledTasks: {
    runCronJobs: false,
    sweepCampaigns: {
      sweepDelay: 0,
    },
  },
  csvImport: {
    chunkSize: 5, // use small enough chunk size to test chunking
  },
  adjustHireDate: false,
  s3: {
    bucket: 'hootsworth-test',
    keyPrefix: 'workplace-hub-local',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  },
  slack: {
    clientId: process.env.SLACK_CLIENT_ID,
    clientSecret: process.env.SLACK_CLIENT_SECRET,
    signingSecret: process.env.SLACK_SIGNING_SECRET,
  },
  tableau: {
    secret: process.env.TABLEAU_SECRET,
    secretId: process.env.TABLEAU_SECRET_ID,
    clientId: process.env.TABLEAU_CLIENT_ID,
    userId: process.env.TABLEAU_USER_ID,
  },
  authKeys: {
    privateKey: process.env.AUTH_PRIVATE_KEY,
    publicKey: process.env.AUTH_PUBLIC_KEY,
  },
  centerstage: {
    dbUrl: 'mysql://root@localhost:3306/hootsworth_test',
  },
};
