module.exports = {
  port: process.env.PORT || 3030,
  db: {
    uri: process.env.HW_DB_URL,
    reportingUri: process.env.HW_DB_URL_REPORTING,
    analyticsUri: process.env.ANALYTICS_DB_URL,
  },
  protocol: 'https',
  host: 'uat.emtrain.com',
  apiHost: 'uat-api.emtrain.com',
  frontEndHost: 'https://{subdomain}.uat.emtrain.com',
  frontEndManageHost: 'http://{subdomain}.uat-admin.emtrain.com',
  public: 'public',
  authManagement: {
    verify: 'https://{subdomain}.uat.emtrain.com/verify/{token}',
    reset: 'https://{subdomain}.uat.emtrain.com/reset/{token}',
    notifyReset: 'https://{subdomain}.uat.emtrain.com/notifyReset/{token}',
    verifyAndReset: 'https://{subdomain}.uat.emtrain.com/verifyAndReset/{token}',
    unsubscribe: 'https://{subdomain}.uat.emtrain.com/unsubscribe/{token}',
  },
  s3: {
    bucket: 'hootsworth-uat',
    keyPrefix: 'workplace-hub-local',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  },
  slack: {
    clientId: null,
    clientSecret: null,
    signingSecret: null,
    // clientId: process.env.SLACK_CLIENT_ID,
    // clientSecret: process.env.SLACK_CLIENT_SECRET,
    // signingSecret: process.env.SLACK_SIGNING_SECRET,
  },
  tableau: {
    secret: process.env.TABLEAU_SECRET,
    secretId: process.env.TABLEAU_SECRET_ID,
    clientId: process.env.TABLEAU_CLIENT_ID,
    userId: process.env.TABLEAU_USER_ID,
  },
  authKeys: {
    privateKey: process.env.AUTH_PRIVATE_KEY,
    publicKey: process.env.AUTH_PUBLIC_KEY,
  },
  elasticsearch: {
    useAWS: true,
  },
  sendgrid: {
    host: 'smtp.ethereal.email',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: '9X1Qmrh1KFXGvrQ8vg',
    },
  },
  // questionRecommendations: {
  //   enabled: process.env.ENABLE_RECOMMENDATIONS
  //     ? process.env.ENABLE_RECOMMENDATIONS === 'yes'
  //     : false,
  //   scriptPath: '/home/<USER>/hootsworth/recommendations/src/similar_questions.py',
  // },
  scheduledTasks: {
    runCronJobs: process.env.RUN_SCHEDULED_JOBS === 'yes' || false,
    sweepCampaigns: {
      enabled: true,
      schedule: '*/15 * * * *',
    },
    campaignsCompletion: {
      enabled: true,
    },
    synchUsers: {
      enabled: false,
    },
    synchGroups: {
      enabled: true,
      schedule: '*/15 * * * *',
    },
    synchScheduledImports: {
      enabled: false,
    },
    synchAccounts: {
      enabled: false,
    },
    synchVideos: {
      enabled: false,
    },
    expireUnverifiedUsers: {
      enabled: false,
    },
    sendExpertEmail: {
      enabled: false,
    },
    sendNotifications: {
      enabled: true,
      schedule: '*/15 * * * *',
    },
    genTextToSpeech: {
      enabled: false,
    },
    genTranslations: {
      enabled: false,
    },
    genGlobalResults: {
      enabled: true,
      schedule: '15 * * * *',
    },
    synchLicenseData: {
      enabled: true,
    },
    synchSalesforceData: {
      enabled: true,
    },
    synchMclData: {
      enabled: false,
    },
    synchWorkdayData: {
      enabled: true,
    },
    syncHireDateGroupsData: {
      enabled: true,
    },
  },
  centerstage: {
    url: 'https://centerstagelibrary.com',
  },
  // sftp: {
  //   keyPath: '/root/.ssh/id_rsa',
  // },
  clamOptions: {
    clamEnabled: process.env.SCAN_MALWARE
      ? process.env.SCAN_MALWARE === 'yes'
      : true,
    clamdscan: {
      host: process.env.CLAMDSCAN_HOST || '127.0.0.1',
      port: process.env.CLAMDSCAN_HOST || 3310,
    },
  },
  // pgp: {
  //   keyPath: process.env.PGP_KEY_FILE, // No default if env variable not present for security reasons
  //   passPhrase: process.env.PGP_PASS_PHRASE, // No default if env variable not present for security reasons
  // },
  translate: {
    languages: process.env.MACHINE_TRANSLATION_LANGUAGES || '',
  },
  disableNotifications: true,
};
