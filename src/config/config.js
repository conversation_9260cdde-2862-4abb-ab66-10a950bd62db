const _ = require('lodash');
const chalk = require('chalk');
const glob = require('glob');
const fs = require('fs');
const path = require('path');

const encryption = require('../encryption');

// Logger hasn't been initialized, so we need console.log,
// and we use require to pull in specific (run-time) config files to combine.
/* eslint-disable no-console */
/* eslint-disable import/no-dynamic-require */
/* eslint-disable global-require */

/**
 * Validate NODE_ENV existence
 */
const validateEnvironmentVariable = () => {
  const configFile = process.env.NODE_ENV === 'prod' ? 'production' : process.env.NODE_ENV;
  const environmentFiles = glob.sync(`./src/config/${configFile}.js`);
  if (!environmentFiles.length) {
    if (process.env.NODE_ENV) {
      console.error(chalk.red(`+ Error: No configuration file found for "${process.env.NODE_ENV}"
      environment using development instead`));
    } else {
      console.warn(chalk.green('+ Warning: NODE_ENV is not defined! Using default development environment'));
    }
    process.env.NODE_ENV = 'development';
  }
  // Reset console color
  console.log(chalk.white(''));
};

/**
 * Validate App Secret parameter is not set to default in production
 */
const validateSecret = (config) => {
  if (config.appSecret === 'default secret') {
    console.log(chalk.red(`+ WARNING: It is strongly recommended
    that you change appSecret config while running in production!`));
    console.log(chalk.red('  Please add `appSecret: process.env.APP_SECRET || \'super amazing secret\'` to '));
    console.log(chalk.red('  `config/production.js` or `config/local.js`'));
    console.log();
    return false;
  }
  return true;
};
/**
 * Initialize global configuration
 */

const noMergeArrayfunc = (origValue, srcValue) => {
  if (Array.isArray(origValue) && Array.isArray(srcValue)) {
    return srcValue;
  }
  return undefined; // This uses the regular _.merge algorithm.
};

const initGlobalConfig = () => {
  // Validate NODE_ENV existence
  validateEnvironmentVariable();

  // Get the default config
  const defaultConfig = require(path.join(process.cwd(), 'src/config/default'));

  // Get the current config
  const environmentConfig = require(path.join(
    process.cwd(), 'src/config/',
    process.env.NODE_ENV === 'prod' ? 'production' : process.env.NODE_ENV,
  )) || {};

  // Merge config files
  let config = _.mergeWith(defaultConfig, environmentConfig, noMergeArrayfunc);

  // Merge in local.js if it exists
  const localjs = fs.existsSync(path.join(process.cwd(), 'src/config/local.js'))
    && require(path.join(process.cwd(), 'src/config/local.js'));
  if (localjs) {
    // prevent overwriting the database we're working with locally during automated tests.
    if (process.env.NODE_ENV === 'test' && localjs.db && localjs.db.uri) {
      delete localjs.db.uri;
    }
    config = _.mergeWith(config, localjs, noMergeArrayfunc);
  }

  // Validate session secret
  validateSecret(config);

  config = encryption.init(config);

  return config;
};

/**
 * Set configuration object
 */
module.exports = initGlobalConfig();
