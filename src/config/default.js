const path = require('path');
const os = require('os');

const HOME_DIR = os.homedir();

module.exports = {
  app: {
    title: 'Emtrain',
    description: 'cool app',
    keywords: 'some keywords go here',
  },
  protocol: 'http',
  host: 'localhost',
  apiHost: 'localhost',
  port: process.env.PORT || 3030,
  frontEndHost: 'http://{subdomain}.localhost:8080',
  frontEndManageHost: 'http://{subdomain}.localhost:8080',
  public: 'public',
  paginate: {
    default: process.env.PAGINATE_DEFAULT || 10,
    max: process.env.PAGINATE_MAX || 500,
  },
  encryption: {
    key: 'bd5f59e9991fd83b4e8ef0d195011bc3',
    algorithm: 'aes-256-cbc',
    ivLength: 16,
  },
  appSecret: process.env.APP_SECRET || 'default secret',
  fromEmail: '<EMAIL>',
  authManagement: {
    verify: 'http://{subdomain}.localhost:8080/verify/{token}',
    reset: 'http://{subdomain}.localhost:8080/reset/{token}',
    notifyReset: 'http://{subdomain}.localhost:8080/notifyReset/{token}',
    verifyAndReset: 'http://{subdomain}.localhost:8080/verifyAndReset/{token}',
    unsubscribe: 'http://{subdomain}.localhost:8080/unsubscribe/{token}',
  },
  s3: {
    bucket: 'hootsworth-develop',
    keyPrefix: 'workplace-hub-local',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  },
  actTranslation: {
    bucket: 'act-translator',
    accessRole: 'arn:aws:iam::098787736674:role/service-role/AmazonTranslateServiceRoleS3FullAccess-act-s3',
  },
  aragorn: {
    aragornServer: 'https://dev-partner-gateway.api.aragorn.ai',
    aragornOrgId: process.env.ARAGORN_ORG_ID,
    aragornApiKey: process.env.ARAGORN_API_KEY,
  },
  workdayCCL: {
    authEndpoint: 'https://xapi.us.myworkdaymedia.com/v1/auth',
    trackingEndpoint: 'https://xapi.us.myworkdaymedia.com/v1/tracking',
    contentEndpoint: 'https://ccl-partner.myworkdaylearning.com/v1/content',
    providerId: process.env.WORKDAY_CCL_PROVIDER_ID,
  },
  slack: {
    clientId: process.env.SLACK_CLIENT_ID,
    clientSecret: process.env.SLACK_CLIENT_SECRET,
    signingSecret: process.env.SLACK_SIGNING_SECRET,
    botToken: process.env.SLACK_BOT_TOKEN,
  },
  tableau: {
    secret: process.env.TABLEAU_SECRET,
    secretId: process.env.TABLEAU_SECRET_ID,
    clientId: process.env.TABLEAU_CLIENT_ID,
    userId: process.env.TABLEAU_USER_ID,
  },
  authKeys: {
    privateKey: process.env.AUTH_PRIVATE_KEY,
    publicKey: process.env.AUTH_PUBLIC_KEY,
  },
  zendesk: {
    apiBaseURL: process.env.ZENDESK_API_BASE_URL,
    apiKey: process.env.ZENDESK_API_KEY,
    login: '<EMAIL>',
  },
  csvImport: {
    chunkSize: process.env.CSV_IMPORT_CHUNKSIZE * 1 || 50, // multiply by 1 is to convert string to number
  },
  elasticsearch: {
    useAWS: false,
    hosts: process.env.ES_HOST?.split(' ') || ['localhost:9200'],
    index: 'resource',
    log: 'info',
    searchScheme: 'pri_fields_synonyms',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    region: 'us-east-1',
  },
  sendgrid: {
    host: 'smtp.ethereal.email',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: 'jBBWuPbz5sXMACY1mK',
    },
  },
  logger: {
    level: process.env.LOG_LEVEL || 'info',
  },
  tokenTimeout: 60 * 60, // 1 hours in seconds
  samlTokenTimeout: 60 * 60 * 24, // 24 hours in seconds
  scormTokenTimeout: 60 * 60 * 48, // 48 hours in seconds
  tokenTimeoutUnconfirmed: 60 * 60 * 8, // 8 hours in seconds
  resetPasswordTimeout: 1000 * 60 * 60 * 24, // 24 hours in milliseconds?
  resetPasswordTimeoutCampaignNotification: 1000 * 60 * 60 * 24 * 7, // 7 days
  samlVerifyTimeout: 1000 * 60, // 1 minute in milliseconds?
  authVerifyTimeout: 1000 * 60, // 1 minute in milliseconds?
  confirmEmailTimeout: 604800000, // 7 days
  longTokenLen: 15,
  viewcontinueTimer: 30, // # of seconds that viewcontinue events are emitted from client, must be in sync with client

  questionRecommendations: {
    enabled: process.env.ENABLE_RECOMMENDATIONS === 'yes' || false,
    searchMode: 'sql_text_search', // possible values 'sql_text_search' or 'python_script'
    scriptPath: '~/programming/python/emlangmodel/similar_questions.py',
  },
  disableNotifications: process.env.DISABLE_NOTIFICATIONS === 'true' || false,
  adjustHireDate: true,
  centerstage: {
    url: 'http://localhost:3000',
    dbUrl: process.env.CENTERSTAGE_DB_URL || 'mysql://root@localhost:3306/hootsworth_dev',
  },
  videoImageURLTemplate: 'https://s3.amazonaws.com/emtrain-video-content/{videoId}/original/{ssFilename}',
  sftp: {
    enable: process.env.SFTP_ENABLE === 'yes' || false,
    port: '2222',
    keyPath: process.env.SFTP_KEY_PATH || path.join(HOME_DIR, '.ssh/id_rsa'),
  },
  pgp: {
    keyPath: process.env.PGP_KEY_FILE || path.join(__dirname, 'keys/9D6EDB9E3162B33929B9668C4AF5BFFA37C475AC.asc'),
    passPhrase: process.env.PGP_PASS_PHRASE || 'Emtrain123',
  },
  // '* * * * * *' every second - the first one is seconds and is optional
  // '0 */2 * * *' every two hours at zero minute
  scheduledTasks: {
    runCronJobs: true,
    sweeperProcess: process.env.SWEEPER_PROCESS === 'yes' || false,
    csvProcess: process.env.CSV_PROCESS === 'yes' || false,
    translationsProcess: process.env.TRANSLATIONS_PROCESS === 'yes' || false,
    defaultProcess: process.env.DEFAULT_PROCESS === 'yes' || false,
    actTranslator: false,
    sweepCampaigns: {
      enabled: true,
      schedule: '*/5 * * * *',
      sweepDelay: 8,
    },
    campaignsCompletion: {
      enabled: true,
      schedule: '0 2 * * *',
    },
    synchUsers: {
      enabled: true,
      schedule: '*/2 * * * *',
    },
    synchGroups: {
      enabled: true,
      schedule: '*/5 * * * *',
    },
    synchScheduledImports: {
      enabled: true,
      schedule: '0 1 * * *',
    },
    synchAccounts: {
      enabled: true,
      schedule: '0 1 * * *',
    },
    synchVideos: {
      enabled: true,
      schedule: '0 */1 * * *', // every hour
    },
    expireUnverifiedUsers: {
      enabled: true,
      schedule: '*/5 * * * *',
    },
    sendExpertEmail: {
      enabled: true,
      schedule: '*/5 * * * *',
    },
    sendNotifications: {
      enabled: true,
      schedule: '*/5 * * * *',
      notificationsPerSweep: 3000,
    },
    genTextToSpeech: {
      enabled: false,
      schedule: '0 */2 * * *',
    },
    genTranslations: {
      enabled: false,
      schedule: '10 * * * *',
    },
    genGlobalResults: {
      enabled: true,
      schedule: '15 * * * *',
    },
    synchLicenseData: {
      enabled: false,
      schedule: '0 0 * * *',
    },
    synchSalesforceData: {
      enabled: true,
      schedule: '0 4 * * *',
    },
    synchMclData: {
      enabled: true,
      schedule: '0 0 * * *',
    },
    synchWorkdayData: {
      enabled: true,
      schedule: '10 0 * * *',
    },
    syncHireDateGroupsData: {
      enabled: true,
      schedule: '0 0 * * *',
    },
  },
  clamOptions: {
    clamEnabled: false,
    clamdscan: {
      host: '127.0.0.1', // IP of host to connect to TCP interface
      port: 3310, // Port of host to use when connecting via TCP interface
    },
  },
  textToSpeech: {
    useSsml: true,
  },
  translate: {
    languages: '',
  },
  swaggerConfig: {
    enabled: process.env.NODE_ENV === 'ci',
  },
  salesforce: {
    domain: process.env.SF_DOMAIN,
    apiKey: process.env.SF_APIKEY,
    secret: process.env.SF_SECRET,
    userId: process.env.SF_USERID,
    password: process.env.SF_PASSWORD,
    entryURL: process.env.SF_ENTRYURL,
  },
  launchDarkly: {
    key: process.env.LAUNCH_DARKLY_KEY || '',
  },
  analyticsMethodologyFile: {
    folder: 'analytics',
    name: 'Analytics Methodology.pdf',
  },
};
