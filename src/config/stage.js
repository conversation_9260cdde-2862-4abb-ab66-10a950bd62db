module.exports = {
  db: {
    uri: process.env.HW_DB_URL,
    analyticsUri: process.env.ANALYTICS_DB_URL,
    logging: null,
  },
  protocol: 'http',
  host: 'stage-app.emtrain.com',
  apiHost: 'stage-api.emtrain.com',
  port: process.env.PORT || 3030,
  frontEndHost: 'http://{subdomain}.stage-app.emtrain.com',
  frontEndManageHost: 'http://{subdomain}.stage-admin.emtrain.com',
  public: 'public',
  authManagement: {
    verify: 'http://{subdomain}.stage-app.emtrain.com/verify/{token}',
    reset: 'http://{subdomain}.stage-app.emtrain.com/reset/{token}',
    notifyReset: 'http://{subdomain}.stage-app.emtrain.com/notifyReset/{token}',
    verifyAndReset: 'http://{subdomain}.stage-app.emtrain.com/verifyAndReset/{token}',
    unsubscribe: 'http://{subdomain}.stage-app.emtrain.com/unsubscribe/{token}',
  },
  s3: {
    bucket: 'hootsworth-stage-env',
    keyPrefix: 'workplace-hub-local',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  },
  aragorn: {
    aragornServer: 'https://dev-partner-gateway.api.aragorn.ai',
    aragornOrgId: process.env.ARAGORN_ORG_ID,
    aragornApiKey: process.env.ARAGORN_API_KEY,
  },
  slack: {
    clientId: process.env.SLACK_CLIENT_ID,
    clientSecret: process.env.SLACK_CLIENT_SECRET,
    signingSecret: process.env.SLACK_SIGNING_SECRET,
    botToken: process.env.SLACK_BOT_TOKEN,
  },
  authKeys: {
    privateKey: process.env.AUTH_PRIVATE_KEY,
    publicKey: process.env.AUTH_PUBLIC_KEY,
  },
  sendgrid: {
    host: 'smtp.sendgrid.net',
    port: 587,
    secure: false,
    auth: {
      user: 'apikey',
      pass: '*********************************************************************',
    },
  },
  sweepDelay: 0,
  adjustHireDate: false,
  centerstage: {
    url: 'https://centerstagelibrary.com',
    dbUrl: process.env.CENTERSTAGE_DB_URL,
  },
  clamOptions: {
    clamEnabled: false,
  },
  translate: {
    languages: process.env.MACHINE_TRANSLATION_LANGUAGES || '',
  },
  actTranslation: {
    bucket: 'stage-act-translator',
    accessRole: 'arn:aws:iam::************:role/service-role/AmazonTranslateServiceRoleS3FullAccess-act-s3',
  },
  scheduledTasks: {
    runCronJobs: process.env.RUN_SCHEDULED_JOBS === 'yes' || false,
    actTranslator: process.env.ENABLE_ACT_TRANSLATOR === 'yes' || false,
    synchVideos: {
      enabled: false,
    },
    synchAccounts: {
      enabled: true,
      schedule: '*/1 * * * *',
    },
    genTranslations: {
      enabled: process.env.GEN_TRANSLATIONS_JOB_ENABLED
        ? process.env.GEN_TRANSLATIONS_JOB_ENABLED === 'yes'
        : false,
      schedule: process.env.GEN_TRANSLATIONS_JOB_TIMING || '10 * * * *',
    },
    synchMclData: {
      enabled: process.env.SYNCH_MCL_DATA_ENABLED === 'yes' || false,
      schedule: process.env.SYNCH_MCL_DATA_TIMING || '0 0 * * *',
    },
  },
};
