module.exports = {
  db: {
    uri: process.env.HW_DB_URL,
    analyticsUri: process.env.ANALYTICS_DB_URL,
    logging: null,
  },
  protocol: 'http',
  host: 'ci-app.emtrain.com',
  apiHost: 'ci-api.emtrain.com',
  port: process.env.PORT || 3030,
  frontEndHost: 'http://{subdomain}.ci-app.emtrain.com',
  frontEndManageHost: 'http://{subdomain}.ci-admin.emtrain.com',
  public: 'public',
  authManagement: {
    verify: 'http://{subdomain}.ci-app.emtrain.com/verify/{token}',
    reset: 'http://{subdomain}.ci-app.emtrain.com/reset/{token}',
    notifyReset: 'http://{subdomain}.ci-app.emtrain.com/notifyReset/{token}',
    verifyAndReset: 'http://{subdomain}.ci-app.emtrain.com/verifyAndReset/{token}',
    unsubscribe: 'http://{subdomain}.ci-app.emtrain.com/unsubscribe/{token}',
  },
  s3: {
    bucket: 'hootsworth-ci',
    keyPrefix: 'workplace-hub-local',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  },
  actTranslation: {
    bucket: 'act-translator',
    accessRole: 'arn:aws:iam::098787736674:role/service-role/AmazonTranslateServiceRoleS3FullAccess-act-s3',
  },
  aragorn: {
    aragornServer: 'https://dev-partner-gateway.api.aragorn.ai',
    aragornOrgId: process.env.ARAGORN_ORG_ID,
    aragornApiKey: process.env.ARAGORN_API_KEY,
  },
  workdayCCL: {
    authEndpoint: 'https://xapi.us.myworkdaymedia.com/v1/auth',
    trackingEndpoint: 'https://xapi.us.myworkdaymedia.com/v1/tracking',
    contentEndpoint: 'https://ccl-partner.myworkdaylearning.com/v1/content',
    providerId: process.env.WORKDAY_CCL_PROVIDER_ID,
  },
  slack: {
    clientId: process.env.SLACK_CLIENT_ID,
    clientSecret: process.env.SLACK_CLIENT_SECRET,
    signingSecret: process.env.SLACK_SIGNING_SECRET,
    botToken: process.env.SLACK_BOT_TOKEN,
  },
  tableau: {
    secret: process.env.TABLEAU_SECRET,
    secretId: process.env.TABLEAU_SECRET_ID,
    clientId: process.env.TABLEAU_CLIENT_ID,
    userId: process.env.TABLEAU_USER_ID,
  },
  authKeys: {
    privateKey: process.env.AUTH_PRIVATE_KEY,
    publicKey: process.env.AUTH_PUBLIC_KEY,
  },
  sendgrid: {
    host: 'smtp.sendgrid.net',
    port: 587,
    secure: false,
    auth: {
      user: 'apikey',
      pass: '*********************************************************************',
    },
  },
  sweepDelay: 0,
  adjustHireDate: false,
  centerstage: {
    url: 'https://centerstagelibrary.com',
    dbUrl: process.env.CENTERSTAGE_DB_URL,
  },
  clamOptions: {
    clamEnabled: false,
  },
  translate: {
    languages: 'fr, es, it, zh-cn, ja, ko, de, ar, pt-br, ru, hi',
  },
  scheduledTasks: {
    runCronJobs: process.env.RUN_SCHEDULED_JOBS === 'yes' || false,
    actTranslator: process.env.ENABLE_ACT_TRANSLATOR === 'yes' || false,
    synchVideos: {
      enabled: false,
    },
    synchAccounts: {
      enabled: true,
      schedule: '*/1 * * * *',
    },
    synchMclData: {
      enabled: true,
      schedule: process.env.SYNCH_MCL_DATA_TIMING || '0 * * * *',
    },
    synchWorkdayData: {
      enabled: true,
      schedule: process.env.SYNCH_WORKDAY_DATA_TIMING || '0 2 * * *',
    },
    syncHireDateGroupsData: {
      enabled: true,
      schedule: process.env.SYNCH_HIRE_DATE_GROUP_DATA_TIMING || '0 0 * * *',
    },
  },
};
