module.exports = {
  port: process.env.PORT || 3030,
  db: {
    uri: process.env.HW_DB_URL,
    reportingUri: process.env.HW_DB_URL_REPORTING,
    analyticsUri: process.env.ANALYTICS_DB_URL,
  },
  protocol: 'https',
  host: 'app.emtrain.com',
  apiHost: 'ai-api.emtrain.com',
  frontEndHost: 'https://{subdomain}.app.emtrain.com',
  frontEndManageHost: 'http://{subdomain}.admin.emtrain.com',
  public: 'public',
  authManagement: {
    verify: 'https://{subdomain}.app.emtrain.com/verify/{token}',
    reset: 'https://{subdomain}.app.emtrain.com/reset/{token}',
    notifyReset: 'https://{subdomain}.app.emtrain.com/notifyReset/{token}',
    verifyAndReset: 'https://{subdomain}.app.emtrain.com/verifyAndReset/{token}',
    unsubscribe: 'https://{subdomain}.app.emtrain.com/unsubscribe/{token}',
  },
  s3: {
    bucket: 'hootsworth-ai',
    keyPrefix: 'workplace-hub-local',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  },
  actTranslation: {
    bucket: 'ai-act-translator',
    accessRole: 'arn:aws:iam::502534974546:role/service-role/AmazonTranslateServiceRole-S3FullAccess-act-s3',
  },
  aragorn: {
    aragornServer: 'https://prod-partner-gateway.api.aragorn.ai',
    aragornOrgId: process.env.ARAGORN_ORG_ID,
    aragornApiKey: process.env.ARAGORN_API_KEY,
  },
  workdayCCL: {
    authEndpoint: 'https://xapi.us.myworkdaymedia.com/v1/auth',
    trackingEndpoint: 'https://xapi.us.myworkdaymedia.com/v1/tracking',
    contentEndpoint: 'https://ccl-partner.myworkdaylearning.com/v1/content',
    providerId: process.env.WORKDAY_CCL_PROVIDER_ID,
  },
  slack: {
    clientId: process.env.SLACK_CLIENT_ID,
    clientSecret: process.env.SLACK_CLIENT_SECRET,
    signingSecret: process.env.SLACK_SIGNING_SECRET,
    botToken: process.env.SLACK_BOT_TOKEN,
  },
  tableau: {
    secret: process.env.TABLEAU_SECRET,
    secretId: process.env.TABLEAU_SECRET_ID,
    clientId: process.env.TABLEAU_CLIENT_ID,
    userId: process.env.TABLEAU_USER_ID,
  },
  authKeys: {
    privateKey: process.env.AUTH_PRIVATE_KEY,
    publicKey: process.env.AUTH_PUBLIC_KEY,
  },
  elasticsearch: {
    useAWS: true,
  },
  sendgrid: {
    host: 'smtp.sendgrid.net',
    port: 587,
    secure: false,
    auth: {
      user: 'apikey',
      pass: process.env.SEND_GRID_KEY,
    },
  },
  questionRecommendations: {
    enabled: process.env.ENABLE_RECOMMENDATIONS
      ? process.env.ENABLE_RECOMMENDATIONS === 'yes'
      : false,
    scriptPath: '/home/<USER>/hootsworth/recommendations/src/similar_questions.py',
  },
  scheduledTasks: {
    runCronJobs: process.env.RUN_SCHEDULED_JOBS
      ? process.env.RUN_SCHEDULED_JOBS === 'yes'
      : true,
    actTranslator: process.env.ENABLE_ACT_TRANSLATOR === 'yes' || false,
    sweepCampaigns: {
      enabled: process.env.SWEEP_CAMPAIGNS_ENABLED
        ? process.env.SWEEP_CAMPAIGNS_ENABLED === 'yes'
        : true,
      schedule: process.env.SCHEDULED_JOBS_TIMING || '*/5 * * * *',
      sweepDelay: process.env.SWEEP_CAMPAIGNS_SWEEP_DELAY || 8,
    },
    campaignsCompletion: {
      enabled: process.env.COMPLETION_JOB_ENABLED
        ? process.env.COMPLETION_JOB_ENABLED === 'yes'
        : true,
      schedule: process.env.COMPLETION_JOB_TIMING || '0 2 * * *',
    },
    synchUsers: {
      enabled: process.env.SYNCH_USER_JOB_ENABLED
        ? process.env.SYNCH_USER_JOB_ENABLED === 'yes'
        : true,
      schedule: process.env.SYNCH_USER_JOB_TIMING || '* */5 * * *',
    },
    synchGroups: {
      enabled: process.env.SYNCH_GROUP_JOB_ENABLED
        ? process.env.SYNCH_GROUP_JOB_ENABLED === 'yes'
        : true,
      schedule: process.env.SYNCH_GROUP_JOB_TIMING || '*/5 * * * *',
    },
    synchScheduledImports: {
      enabled: process.env.SYNCH_SCHEDULED_IMPORTS_JOB_ENABLED
        ? process.env.SYNCH_SCHEDULED_IMPORTS_JOB_ENABLED === 'yes'
        : true,
      schedule: process.env.SYNCH_SCHEDULED_IMPORTS_JOB_TIMING || '0 1 * * *',
    },
    synchAccounts: {
      enabled: process.env.SYNCH_ACCOUNTS_JOB_ENABLED
        ? process.env.SYNCH_ACCOUNTS_JOB_ENABLED === 'yes'
        : true,
      schedule: process.env.SYNCH_ACCOUNTS_JOB_TIMING || '0 1 * * *',
    },
    expireUnverifiedUsers: {
      enabled: process.env.EXPIRE_UNVERIFIED_USERS_JOB_ENABLED
        ? process.env.EXPIRE_UNVERIFIED_USERS_JOB_ENABLED === 'yes'
        : true,
      schedule: process.env.EXPIRE_UNVERIFIED_USERS_JOB_TIMING || '*/5 * * * *',
    },
    sendExpertEmail: {
      enabled: process.env.SEND_EXPERT_EMAIL_JOB_ENABLED
        ? process.env.SEND_EXPERT_EMAIL_JOB_ENABLED === 'yes'
        : true,
      schedule: process.env.SEND_EXPERT_EMAIL_JOB_TIMING || '*/5 * * * *',
    },
    sendNotifications: {
      enabled: process.env.SEND_NOTIFICATIONS_JOB_ENABLED
        ? process.env.SEND_NOTIFICATIONS_JOB_ENABLED === 'yes'
        : true,
      schedule: process.env.SEND_NOTIFICATIONS_JOB_TIMING || '*/5 * * * *',
      notificationsPerSweep: process.env.NOTIFICATIONS_PER_SWEEP * 1 || 5000,
    },
    genTextToSpeech: {
      enabled: process.env.GEN_TEXT_TO_SPEECH_JOB_ENABLED
        ? process.env.GEN_TEXT_TO_SPEECH_JOB_ENABLED === 'yes'
        : false,
      schedule: process.env.GEN_TEXT_TO_SPEECH_JOB_TIMING || '0 */2 * * *',
    },
    genTranslations: {
      enabled: process.env.GEN_TRANSLATIONS_JOB_ENABLED
        ? process.env.GEN_TRANSLATIONS_JOB_ENABLED === 'yes'
        : false,
      schedule: process.env.GEN_TRANSLATIONS_JOB_TIMING || '10 * * * *',
    },
    genGlobalResults: {
      enabled: process.env.GEN_GLOBAL_RESULTS_JOB_ENABLED
        ? process.env.GEN_GLOBAL_RESULTS_JOB_ENABLED === 'yes'
        : true,
      schedule: process.env.GEN_GLOBAL_RESULTS_JOB_TIMING || '10 3 * * *',
    },
    synchVideos: {
      enabled: process.env.SYNCH_VIDEOS_JOB_ENABLED
        ? process.env.SYNCH_VIDEOS_JOB_ENABLED === 'yes'
        : true,
      schedule: process.env.SYNCH_VIDEOS_JOB_TIMING || '0 */1 * * *',
    },
    synchLicenseData: {
      enabled: process.env.SYNCH_LICENSE_DATA_ENABLED === 'yes' || false,
      schedule: process.env.SYNCH_LICENSE_DATA_TIMING || '0 0 * * *',
    },
    synchSalesforceData: {
      enabled: process.env.SYNCH_SALESFORCE_DATA_ENABLED === 'yes' || false,
      schedule: process.env.SYNCH_SALESFORCE_DATA_TIMING || '0 4 * * *',
    },
    synchMclData: {
      enabled: process.env.SYNCH_MCL_DATA_ENABLED === 'yes' || false,
      schedule: process.env.SYNCH_MCL_DATA_TIMING || '0 0 * * *',
    },
    synchWorkdayData: {
      enabled: process.env.SYNCH_WORKDAY_DATA_ENABLED === 'yes' || false,
      schedule: process.env.SYNCH_WORKDAY_DATA_TIMING || '0 2 * * *',
    },
    syncHireDateGroupsData: {
      enabled: process.env.SYNCH_HIRE_DATE_GROUP_DATA_ENABLED === 'yes' || false,
      schedule: process.env.SYNCH_HIRE_DATE_GROUP_DATA_TIMING || '0 0 * * *',
    },    
  },
  centerstage: {
    url: 'https://centerstagelibrary.com',
    dbUrl: process.env.CENTERSTAGE_DB_URL,
  },
  clamOptions: {
    clamEnabled: process.env.SCAN_MALWARE
      ? process.env.SCAN_MALWARE === 'yes'
      : true,
    clamdscan: {
      host: process.env.CLAMDSCAN_HOST || '127.0.0.1',
      port: process.env.CLAMDSCAN_HOST || 3310,
    },
  },
  pgp: {
    keyPath: process.env.PGP_KEY_FILE, // No default if env variable not present for security reasons
    passPhrase: process.env.PGP_PASS_PHRASE, // No default if env variable not present for security reasons
  },
  translate: {
    languages: process.env.MACHINE_TRANSLATION_LANGUAGES || '',
  },
};
