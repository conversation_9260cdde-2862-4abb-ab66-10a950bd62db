const defaultEnvConfig = require('./default');

module.exports = {
  app: {
    title: `${defaultEnvConfig.app.title} - Development Environment`,
  },
  db: {
    uri: 'mysql://root@localhost:3306/hootsworth_dev',
    // reportingUri: 'mysql://root@localhost:3306/hootsworth_dev_reporting',
    analyticsUri: 'mysql://root@localhost:3306/hootsworth_dev_analytics',
  },
  appSecret: '169d2da3-c4c1-46b8-a0d5-0e00a9febc9c',
  sweepDelay: 0,
  adjustHireDate: false,
  launchDarkly: {
    key: process.env.LAUNCH_DARKLY_KEY,
  },
  analyticsMethodologyFile: {
    folder: 'analytics',
    name: 'Analytics Methodology.pdf',
  },
};
