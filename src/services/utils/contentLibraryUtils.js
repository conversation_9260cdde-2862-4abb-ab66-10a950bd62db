const { Op } = require('sequelize');
const db = require('../../db');
const logger = require('../../logger');
const { getContentItemsDeployedData } = require('./campaignUtils');
const { getResourceBundlesIds } = require('./resourceUtils');

const ContentLibrary = db.contentLibrary;
const AccountPrograms = db.accountPrograms;
const AccountLessons = db.accountLessons;
const Programs = db.programs;
const Lessons = db.lessons;
const CatalogItems = db.catalogItems;
const States = db.states;
const Resources = db.resources;
const ResourceBundles = db.resourceBundles;
const Listings = db.listings;
const Files = db.files;
const LessonCard = db.lessonCards;
const AccountLessonCard = db.accountLessonCards;

const getAllActiveAccountIds = async () => {
  return db.accounts.findAll({
    attributes: ['id'],
    where: {
      status: 'active',
    },
  });
};

const listingQuery = (accountBundleIds, accountId) => {
  const bundleIds = accountBundleIds.length ? accountBundleIds.join(',') : null;
  const whereCondition = "li.lifecycle = 'published' ";
  const query = `
      SELECT 
          li.id,
          li.type,
          li.title,
          li.description,
          li.rank AS listingRank,
          li.updatedAt,
          li.lastMappedAt,
          li.isSpecialPwhUs,
          0 AS edition,
          0 AS audience,
          0 AS duration,
          0 AS part,
          0 AS isClientSpecific,
          0 AS instructionalType,
          c.id AS countryId,
          c.countryName AS countryName,
          'listing' AS listType,
          fi.path AS filePath,
          fi.id AS fileId,
          NULL AS stateName,
          NULL AS stateCode,
          CASE
              WHEN EXISTS (
                  SELECT 1
                  FROM catalogItems c
                  WHERE c.listingId = li.id
                  AND EXISTS (
                      SELECT 1
                      FROM programs p
                      INNER JOIN resources AS resourceP ON p.resourceId = resourceP.id
                      INNER JOIN resourceBundles AS rrb ON resourceP.id = rrb.resourceId AND (rrb.status = 'active' AND rrb.deletedAt IS NULL AND (rrb.bundleId IN (${bundleIds}) OR rrb.bundleId IS NULL))
                      WHERE p.catalogId = c.id AND p.isCustomized = TRUE
                  )
              )
              OR EXISTS (
                  SELECT 1
                  FROM catalogItems c
                  WHERE c.listingId = li.id
                  AND EXISTS (
                      SELECT 1
                      FROM lessons l
                      INNER JOIN resources AS resourceL ON l.resourceId = resourceL.id
                      INNER JOIN resourceBundles AS rrb
                          ON resourceL.id = rrb.resourceId
                          AND (rrb.status = 'active' AND rrb.deletedAt IS NULL AND (rrb.bundleId IN (${bundleIds}) OR rrb.bundleId IS NULL))
                      WHERE l.catalogId = c.id AND l.isCustomized = TRUE
                  )
              )
              THEN TRUE
              ELSE FALSE
          END AS isCustomTag,
          COALESCE((SELECT 
                      JSON_ARRAYAGG(JSON_OBJECT('id', pi.id, 'name', pi.name))
                  FROM
                      listingPillars lp
                          LEFT JOIN
                      socialCapitalPillars pi ON lp.pillarId = pi.id
                  WHERE
                      lp.listingId = li.id),
              JSON_ARRAY()) AS pillars,
          CASE
              WHEN (
                  SELECT COUNT(*)
                  FROM listingIndicators lind
                  WHERE lind.listingId = li.id
              ) = 0 THEN NULL
              ELSE (
                  SELECT 
                      JSON_ARRAYAGG(
                          JSON_OBJECT(
                              'id', ind.id,
                              'name', ind.name,
                              'competency', ind.competency
                          )
                      )
                  FROM
                      listingIndicators lind
                  LEFT JOIN
                      socialCapitalIndicators ind ON lind.indicatorId = ind.id
                  WHERE
                      lind.listingId = li.id
              )
          END AS indicators,
          CASE
              WHEN (
                  SELECT COUNT(*)
                  FROM listingConcepts lcon
                  WHERE lcon.listingId = li.id
              ) = 0 THEN NULL
              ELSE (
                  SELECT 
                      JSON_ARRAYAGG(
                          JSON_OBJECT(
                              'id', con.id,
                              'concept', con.concept
                          )
                      )
                  FROM
                      listingConcepts lcon
                  LEFT JOIN
                      concepts con ON lcon.conceptId = con.id
                  WHERE
                      lcon.listingId = li.id
              )
          END AS concepts,
          COALESCE((SELECT 
              JSON_ARRAYAGG(
                  JSON_OBJECT(
                      'catalogId', ci.id,
                      'edition', ci.edition,
                      'audience', ci.audience,
                      'duration', ci.duration,
                      'part', ci.part,
                      'pwhUsOrder', ci.pwhUsOrder,
                      'isClientSpecific', ci.isClientSpecific,
                      'instructionalType', ci.instructionalType,
                      'stateName', s.stateName,
                      'stateCode', s.stateCode,
                      'programs', COALESCE(
                          (SELECT JSON_ARRAYAGG(JSON_OBJECT(
                              'id', p.id,
                              'name', p.name,
                              'description', p.description,
                              'isBase', p.isBase,
                              'isCustomized', p.isCustomized,
                              'isTimed', p.isTimed,
                              'duration', p.duration,
                              'edition', p.edition,
                              'version', p.version,
                              'build', p.build,
                              'minTimeInMinutes', p.minTimeInMinutes,
                              'accountProgramName', ap.name,
                              'programMappedDate', rrb.createdAt,
                              'downloadedAt', rrb.downloadedAt,
                              'lessons', (
                                    SELECT JSON_ARRAYAGG(
                                        JSON_OBJECT(
                                            'id', les.id,
                                            'title', les.title,
                                            'description', les.description,
                                            'edition', les.edition,
                                            'version', les.version,
                                            'build', les.build,
                                            'requiredMinutes', les.requiredMinutes,
                                            'isCustomized', les.isCustomized,
                                            'hasPolicy', les.hasPolicy,
                                            'accountLessonTitle', al.title
                                        )
                                    )
                                    FROM lessonPrograms lProg
                                    INNER JOIN lessons les ON les.id = lProg.lessonId
                                    LEFT JOIN accountLessons al ON al.lessonId = les.id AND al.accountId = ${accountId}
                                    WHERE lProg.programId = p.id
                                )
                          ))
                          FROM programs p
                          INNER JOIN resources AS resourceP ON p.resourceId = resourceP.id
                          INNER JOIN resourceBundles AS rrb ON resourceP.id = rrb.resourceId AND (rrb.status = 'active' AND rrb.deletedAt IS NULL AND (rrb.bundleId IN (${bundleIds}) OR rrb.bundleId IS NULL))
                          LEFT JOIN accountPrograms ap ON ap.programId = p.id AND ap.accountId=${accountId}
                          WHERE p.catalogId = ci.id
                              AND p.lifecycle = 'publish' AND (ci.isListingAssociate = TRUE OR p.isCustomized = TRUE)),
                            JSON_ARRAY()
                          ),
                      'lessons', COALESCE(
                          (SELECT JSON_ARRAYAGG(JSON_OBJECT(
                                  'id', l.id,
                                  'title', l.title,
                                  'description', l.description,
                                  'isBase', l.isBase,
                                  'isCustomized', l.isCustomized,
                                  'requiredMinutes', l.requiredMinutes,
                                  'edition', l.edition,
                                  'version', l.version,
                                  'build', l.build,
                                  'isStandalone', l.isStandalone,
                                  'hasPolicy', l.hasPolicy,
                                  'accountLessonTitle', al.title,
                                  'lessonMappedDate', rrb.createdAt,
                                  'downloadedAt', rrb.downloadedAt
                              ))
                              FROM lessons l
                              INNER JOIN resources AS resourceL ON l.resourceId = resourceL.id
                              INNER JOIN resourceBundles AS rrb ON resourceL.id = rrb.resourceId AND (rrb.status = 'active' AND rrb.deletedAt IS NULL AND (rrb.bundleId IN (${bundleIds}) OR rrb.bundleId IS NULL))
                              LEFT JOIN accountLessons al ON al.lessonId = l.id AND al.accountId=${accountId}
                              WHERE l.catalogId = ci.id
                                  AND l.lifecycle = 'publish' AND (ci.isListingAssociate = TRUE OR l.isCustomized = TRUE)
                          ), JSON_ARRAY())
                  )
              )
              FROM catalogItems ci
                LEFT JOIN
              states s ON ci.stateId = s.id
              WHERE ci.listingId = li.id
                AND (
                    ci.isListingAssociate = TRUE
                    OR EXISTS (
                        SELECT 1
                        FROM programs p
                        INNER JOIN resources resourceP ON p.resourceId = resourceP.id
                        INNER JOIN resourceBundles rrb
                            ON resourceP.id = rrb.resourceId
                            AND (rrb.status = 'active' AND rrb.deletedAt IS NULL AND (rrb.bundleId IN (${bundleIds}) OR rrb.bundleId IS NULL))
                        WHERE p.catalogId = ci.id AND p.isCustomized = TRUE
                    )
                    OR EXISTS (
                        SELECT 1
                        FROM lessons l
                        INNER JOIN resources resourceL ON l.resourceId = resourceL.id
                        INNER JOIN resourceBundles rrb
                            ON resourceL.id = rrb.resourceId
                            AND (rrb.status = 'active' AND rrb.deletedAt IS NULL AND (rrb.bundleId IN (${bundleIds}) OR rrb.bundleId IS NULL))
                        WHERE l.catalogId = ci.id AND l.isCustomized = TRUE
                    )
                )
            ), JSON_ARRAY()) AS catalogItems,
          Null AS programs,
          Null AS lessons
      FROM
          listings li
            LEFT JOIN 
          catalogItems cItems ON li.id = cItems.listingId
            LEFT JOIN
          countries c ON li.countryId = c.id
            LEFT JOIN
          files fi ON li.thumbnailId = fi.id
      WHERE
          ${whereCondition} 
      GROUP BY li.id , c.id
      HAVING JSON_LENGTH(catalogItems) > 0
      AND (
          JSON_CONTAINS_PATH(catalogItems, 'one', '$[*].programs[0]') 
          OR JSON_CONTAINS_PATH(catalogItems, 'one', '$[*].lessons[0]')
      )
      `;
  return query;
};

const catalogQuery = (accountBundleIds, accountId) => {
  const bundleIds = accountBundleIds.length ? accountBundleIds.join(',') : null;
  const whereCondition = "listingId IS NULL AND (isClientSpecific=true OR (isClientSpecific=false && instructionalType='lesson')) ";
  const query = `
    SELECT 
        ci.id,
        ci.contentType AS type,
        ci.title,
        ci.description,
        0 AS listingRank,
        ci.updatedAt,
        ci.lastMappedAt,
        0 AS isSpecialPwhUs,
        ci.edition,
        ci.audience,
        ci.duration,
        ci.part,
        ci.isClientSpecific AS isClientSpecific,
        ci.instructionalType,
        c.id AS countryId,
        c.countryName AS countryName,
        'catalog' AS listType,
        fi.path AS filePath,
        fi.id AS fileId,
        s.stateName,
        s.stateCode,
        CASE
          WHEN ci.isClientSpecific = TRUE THEN TRUE
          WHEN ci.instructionalType = 'lesson' AND ci.listingId IS NULL AND ci.isClientSpecific = FALSE
            AND EXISTS (
                SELECT 1
                FROM lessons l
                INNER JOIN resources AS resourceL ON l.resourceId = resourceL.id
                INNER JOIN resourceBundles AS rrb ON resourceL.id = rrb.resourceId AND (rrb.status = 'active' AND rrb.deletedAt IS NULL AND (rrb.bundleId IN (${bundleIds}) OR rrb.bundleId IS NULL))
                WHERE l.catalogId = ci.id
                  AND l.isCustomized = TRUE
             )
          THEN TRUE
          ELSE FALSE
        END AS isCustomTag,
        COALESCE((SELECT 
                    JSON_ARRAYAGG(JSON_OBJECT('id', pi.id, 'name', pi.name))
                FROM
                    catalogItemPillars cp
                        LEFT JOIN
                    socialCapitalPillars pi ON cp.socialCapitalPillarId = pi.id
                WHERE
                    cp.catalogItemId = ci.id),
            JSON_ARRAY()) AS pillars,
        CASE
            WHEN (
                SELECT COUNT(*)
                FROM catalogItemIndicators cind
                WHERE cind.catalogItemId = ci.id
            ) = 0 THEN NULL
            ELSE (
                SELECT 
                    JSON_ARRAYAGG(
                        JSON_OBJECT(
                            'id', ind.id,
                            'name', ind.name,
                            'competency', ind.competency
                        )
                    )
                FROM
                    catalogItemIndicators cind
                LEFT JOIN
                    socialCapitalIndicators ind ON cind.socialCapitalIndicatorId = ind.id
                WHERE
                    cind.catalogItemId = ci.id
            )
        END AS indicators,
        CASE
            WHEN (
                SELECT COUNT(*)
                FROM catalogItemConcepts ccon
                WHERE ccon.catalogItemId = ci.id
            ) = 0 THEN NULL
            ELSE (
                SELECT 
                    JSON_ARRAYAGG(
                        JSON_OBJECT(
                            'id', con.id,
                            'concept', con.concept
                        )
                    )
                FROM
                    catalogItemConcepts ccon
                LEFT JOIN
                    concepts con ON ccon.conceptId = con.id
                WHERE
                    ccon.catalogItemId = ci.id
            )
        END AS concepts,
        NULL AS catalogItems,
        CASE
            WHEN
                COUNT(DISTINCT p.id) > 0
            THEN
                (SELECT JSON_ARRAYAGG(JSON_OBJECT(
                    'id',
                    distinct_p.id,
                    'name',
                    distinct_p.name,
                    'description',
                    distinct_p.description,
                    'isBase',
                    distinct_p.isBase,
                    'isCustomized',
                    distinct_p.isCustomized,
                    'isTimed',
                    distinct_p.isTimed,
                    'duration',
                    distinct_p.duration,
                    'minTimeInMinutes',
                    distinct_p.minTimeInMinutes,
                    'edition',
                    distinct_p.edition,
                    'version',
                    distinct_p.version,
                    'build',
                    distinct_p.build,
                    'updatedAt',
                    distinct_p.updatedAt,
                    'fileId',
                    distinct_p.fileId,
                    'filePath',
                    distinct_p.filePath,
                    'accountProgramName',
                    distinct_p.accountProgramName,
                    'programMappedDate',
                    distinct_p.programMappedDate,
                    'downloadedAt',
                    distinct_p.downloadedAt,
                    'lessons',
                    (SELECT JSON_ARRAYAGG(
                        JSON_OBJECT(
                            'id', les.id,
                            'title', les.title,
                            'description', les.description,
                            'edition', les.edition,
                            'version', les.version,
                            'build', les.build,
                            'isStandalone', les.isStandalone,
                            'hasPolicy', les.hasPolicy,
                            'accountLessonTitle', al.title
                        )
                     )
                     FROM lessonPrograms lProg
                     INNER JOIN lessons les ON les.id = lProg.lessonId
                     LEFT JOIN accountLessons al ON al.lessonId = les.id AND al.accountId = ${accountId}
                     WHERE lProg.programId = distinct_p.id)
                    ))
                FROM (
                    SELECT DISTINCT p.id, p.name, p.description, p.isBase, p.isCustomized, p.isTimed, p.duration, p.minTimeInMinutes, p.edition, p.version, p.build, p.updatedAt,p.fileId,fi.path as filePath, ap.name as accountProgramName, rrb.createdAt as programMappedDate, rrb.downloadedAt as downloadedAt
                    FROM programs p
                    INNER JOIN catalogItems ci_sub ON ci_sub.id = p.catalogId
                    INNER JOIN resources AS resourceP ON p.resourceId = resourceP.id
                    INNER JOIN resourceBundles AS rrb ON resourceP.id = rrb.resourceId AND (rrb.status = 'active' AND rrb.deletedAt IS NULL AND (rrb.bundleId IN (${bundleIds}) OR rrb.bundleId IS NULL))
                    LEFT JOIN files fi ON p.fileId = fi.id AND fi.language='en'
                    LEFT JOIN accountPrograms ap ON ap.programId = p.id AND ap.accountId=${accountId}
                    WHERE ci_sub.id = ci.id AND p.lifecycle = 'publish'
                    ORDER BY p.updatedAt DESC
                ) AS distinct_p)
            ELSE NULL
        END AS programs,
        CASE
            WHEN
                COUNT(DISTINCT l.id) > 0
            THEN
                (SELECT JSON_ARRAYAGG(JSON_OBJECT(
                    'id',
                    distinct_l.id,
                    'name',
                    distinct_l.title,
                    'description',
                    distinct_l.description,
                    'isBase',
                    distinct_l.isBase,
                    'isCustomized',
                    distinct_l.isCustomized,
                    'requiredMinutes',
                    distinct_l.requiredMinutes,
                    'edition',
                    distinct_l.edition,
                    'version',
                    distinct_l.version,
                    'build',
                    distinct_l.build,
                    'isStandalone',
                    distinct_l.isStandalone,
                    'updatedAt',
                    distinct_l.updatedAt,
                    'fileId',
                    distinct_l.fileId,
                    'filePath',
                    distinct_l.filePath,
                    'accountLessonTitle',
                    distinct_l.accountLessonTitle,
                    'lessonMappedDate',
                    distinct_l.lessonMappedDate,
                    'hasPolicy',
                    distinct_l.hasPolicy,
                    'downloadedAt',
                    distinct_l.downloadedAt
                  ))
                FROM (
                    SELECT DISTINCT l.id, l.title, l.description, l.isBase, l.isCustomized, l.requiredMinutes, l.edition, l.version, l.build, l.isStandalone, l.updatedAt, fi.id as fileId,fi.path as filePath, al.title as accountLessonTitle, rrb.createdAt as lessonMappedDate, l.hasPolicy as hasPolicy, rrb.downloadedAt as downloadedAt
                    FROM lessons l
                    INNER JOIN catalogItems ci_lsub ON ci_lsub.id = l.catalogId
                    INNER JOIN resources AS resourceL ON l.resourceId = resourceL.id
                    INNER JOIN resourceBundles AS rrb ON resourceL.id = rrb.resourceId AND (rrb.status = 'active' AND rrb.deletedAt IS NULL AND (rrb.bundleId IN (${bundleIds}) OR rrb.bundleId IS NULL))
                    LEFT JOIN files fi ON l.id = fi.fileableId AND fi.language='en' AND fi.fileableId IS NOT NULL AND fi.fileable='lesson'
                    LEFT JOIN accountLessons al ON al.lessonId = l.id AND al.accountId=${accountId}
                    WHERE ci_lsub.id = ci.id
                    AND l.lifecycle = 'publish'
                    ORDER BY l.updatedAt DESC
                ) AS distinct_l)
            ELSE NULL
        END AS lessons
    FROM
        catalogItems ci
          LEFT JOIN
        countries c ON ci.countryId = c.id
          LEFT JOIN
        states s ON ci.stateId = s.id
          LEFT JOIN
        programs p ON ci.id = p.catalogId
            AND p.lifecycle = 'publish'
          LEFT JOIN
        lessons l ON ci.id = l.catalogId
            AND l.lifecycle = 'publish'
          LEFT JOIN
        files fi ON ci.fileId = fi.id
    WHERE
        ${whereCondition}
    GROUP BY c.id , ci.id
    HAVING (JSON_LENGTH(programs) > 0
        OR JSON_LENGTH(lessons) > 0)
        AND (COUNT(p.id) > 0 OR COUNT(l.id) > 0)
    `;
  return query;
};

const saveContentLibrary = async (data, accountId) => {
  try {
    for (const { id, listType, ...rest } of data) {
      await ContentLibrary.upsert({
        ...rest,
        accountId,
        mclContentId: id,
        listType,
      });
    }
  } catch (err) {
    logger.error('Error while saving the contentLibrary:', err);
  }
};

const updateMclData = async () => {
  try {
    const promise = [];
    const accountIds = await getAllActiveAccountIds();
    for await (const account of accountIds) {
      const accountId = account.id;
      logger.info(`MCL triggered for accountId: ${accountId}`);
      const bundleIds = await getResourceBundlesIds(accountId);
      const liQuery = listingQuery(bundleIds, accountId);
      const caQuery = catalogQuery(bundleIds, accountId);
      const finalMclQuery = `(${liQuery}) UNION (${caQuery})`;
      // fetch MCL based on accountId
      const data = await db.sequelize.query(finalMclQuery, {
        type: db.sequelize.QueryTypes.SELECT,
        nest: true,
      });
      if (data.length) {
        // save the MCL data based on accountId
        promise.push(saveContentLibrary(data, accountId));
      }
    }
    await Promise.all(promise);
    return true;
  } catch (err) {
    logger.error('Error in updateMclData:', err);
    throw (err);
  }
};

/**
 * When there is an update for title in mcl this function will get triggered
 * @param {String} accountId 
 * @param {String} contentType 
 * @param {String} contentId 
 * @param {String} newValue 
 */
const updateContentNameInContentLibrary = async (accountId, contentType, contentId, newValue) => {
    contentId = parseInt(contentId, 10);
    const contentLibraries = await ContentLibrary.findAll({
        where: { accountId },
        attributes: ['id', 'programs', 'lessons', 'catalogItems'],
    });

    for (const content of contentLibraries) {
        let updated = false;

        // Update top-level programs or lessons
        let programs = content.programs || [];
        let lessons = content.lessons || [];

        if (contentType === 'program') {
            programs = programs.map(program => {
                if (program.id === contentId) {
                    updated = true;
                    return { ...program, accountProgramName: newValue };
                }
                return program;
            });
        }

        if (contentType === 'lesson') {
            // Update top-level lessons
            lessons = lessons.map(lesson => {
                if (lesson.id === contentId) {
                    updated = true;
                    return { ...lesson, accountLessonTitle: newValue };
                }
                return lesson;
            });

            // Update lessons inside each program
            programs = programs.map(program => {
                if (Array.isArray(program.lessons)) {
                    const updatedLessons = program.lessons.map(lesson => {
                        if (lesson.id === contentId) {
                            updated = true;
                            return { ...lesson, accountLessonTitle: newValue };
                        }
                        return lesson;
                    });
                    return { ...program, lessons: updatedLessons };
                }
                return program;
            });
        }

        // Update catalogItems
        let catalogItems = content.catalogItems || [];
        catalogItems = catalogItems.map(item => {
            let itemChanged = false;

            // Update catalogItem.programs
            let updatedPrograms = (item.programs || []).map(program => {
                let programChanged = false;

                // Update program name
                if (contentType === 'program' && program.id === contentId) {
                    itemChanged = programChanged = true;
                    program = { ...program, accountProgramName: newValue };
                }

                // Update program.lessons
                if (contentType === 'lesson' && Array.isArray(program.lessons)) {
                    const updatedLessons = program.lessons.map(lesson => {
                        if (lesson.id === contentId) {
                            itemChanged = programChanged = true;
                            return { ...lesson, accountLessonTitle: newValue };
                        }
                        return lesson;
                    });

                    if (programChanged) {
                        program = { ...program, lessons: updatedLessons };
                    }
                }

                return program;
            });

            // Update item.lessons (top-level lessons inside catalogItem)
            let updatedLessons = (item.lessons || []).map(lesson => {
                if (contentType === 'lesson' && lesson.id === contentId) {
                    itemChanged = true;
                    return { ...lesson, accountLessonTitle: newValue };
                }
                return lesson;
            });

            if (itemChanged) {
                updated = true;
                return {
                    ...item,
                    programs: updatedPrograms,
                    lessons: updatedLessons,
                };
            }

            return item;
        });
        if (updated) {
            await content.update({
                programs,
                lessons,
                catalogItems,
            });
        }
    }
};

const getListingCatalogInactiveItems = async (accountId, listingId) => {
    const bundleIds = await getResourceBundlesIds(accountId);
    const finalQuery = {
        where: { listingId, isListingAssociate: false },
        attributes: ['id', 'edition', 'audience', 'duration', 'part', 'isClientSpecific', 'instructionalType'],
        include: [
            {
                model: States,
                attributes: ['id', 'stateName', 'stateCode'],
            },
            {
                model: Programs,
                attributes: ['id', 'name', 'description', 'isBase', 'isCustomized', 'isTimed', 'duration', 'minTimeInMinutes', 'edition', 'version', 'build'],
                include: [
                    {
                        model: AccountPrograms,
                        where: { accountId },
                        attributes: ['accountId', 'programId', 'name', 'createdAt'],
                        required: false,
                    },
                    {
                        model: Resources,
                        as: 'resource',
                        attributes: ['id'],
                        include: [
                            {
                                model: ResourceBundles,
                                attributes: ['id', 'downloadedAt', 'createdAt'],
                                where: {
                                    bundleId: {
                                        [Op.in]: bundleIds,
                                    },
                                    status : 'active',
                                },
                            },
                        ],
                        required: true,
                    },
                    {
                        model: Lessons,
                        through: 'lessonPrograms',
                        include: [
                            {
                                model: LessonCard,
                                attributes: ['id', 'cardType', 'list1', 'policyLink'],
                                where: { cardType: 'policyAcknowledgement' },
                                include: [
                                    {
                                        model: AccountLessonCard,
                                        attributes: ['id', 'policyType', 'link'],
                                        where: { accountId },
                                        required: false,
                                        include: [
                                            {
                                                model: Files,
                                                attributes: ['id', 'path'],
                                                as: 'file',
                                                required: false,
                                            },
                                        ],
                                    },
                                ],
                                required: false,
                            }
                        ],
                        required: false,
                    },
                ]
            },
            {
                model: Lessons,
                attributes: ['id', 'title', 'description', 'isBase', 'isCustomized', 'requiredMinutes', 'edition', 'version', 'build', 'isStandalone', 'hasPolicy'],
                include: [
                    {
                        model: AccountLessons,
                        where: { accountId },
                        attributes: ['accountId', 'lessonId', 'title', 'createdAt'],
                        required: false,
                    },
                    {
                        model: Resources,
                        as: 'resource',
                        attributes: ['id'],
                        include: [
                            {
                                model: ResourceBundles,
                                attributes: ['id', 'downloadedAt', 'createdAt'],
                                where: {
                                    bundleId: {
                                        [Op.in]: bundleIds,
                                    },
                                    status : 'active',
                                },
                            },
                        ],
                        required: true,
                    },
                    {
                        model: LessonCard,
                        attributes: ['id', 'cardType', 'list1', 'policyLink'],
                        where: {
                            cardType: 'policyAcknowledgement',
                        },
                        include: [
                            {
                                model: AccountLessonCard,
                                attributes: ['id', 'policyType', 'link'],
                                where: {
                                    accountId,
                                },
                                include: [{
                                    model: Files,
                                    attributes: ['id', 'path'],
                                    as: 'file',
                                    required: false,
                                }],
                                required: false,
                            }],
                        required: false,
                    }
                ]
            },
        ],
    };

    const catalogItems = await CatalogItems.findAll(finalQuery);
    const finalData = [];
    for await (const catalogItem of catalogItems) {
        // Convert to plain JS object
        const plain = catalogItem?.get({ plain: true });

        // add lessonMappedDate & accountLessonTitle
        if (plain?.lessons?.length) {
            plain.lessons = await Promise.all(
                plain.lessons.map(async (lesson) => {
                    const accountLesson = lesson.accountLessons?.[0];
                    const downloadedAt = lesson.resource?.resourceBundles?.[0].downloadedAt || null;
                    const lessonMappedDate = lesson.resource?.resourceBundles?.[0].createdAt || null;
                    delete lesson.accountLessons;
                    delete lesson.resource;
                    // enrich the lesson with deployed data
                    const enrichedLessonArr = await getContentItemsDeployedData([lesson], accountId, 'lesson');
                    const enrichedLesson = enrichedLessonArr[0] || lesson;
                    const finalLesson = checkLessonPolicyConfigured(enrichedLesson);
                    return {
                        ...finalLesson,
                        lessonMappedDate,
                        accountLessonTitle: accountLesson?.title || null,
                        downloadedAt,
                    };
                })
            );
        }
        // add programMappedDate & accountProgramName
        if (plain?.programs?.length) {
            plain.programs = await Promise.all(
                plain.programs.map(async (program) => {
                    const accountProgram = program.accountPrograms?.[0];
                    const downloadedAt = program.resource?.resourceBundles?.[0].downloadedAt || null;
                    const programMappedDate = program.resource?.resourceBundles?.[0].createdAt || null;
                    delete program.accountPrograms;
                    delete program.resource;
                    // enrich the program, with deployed data
                    const enrichedProgramArr = await getContentItemsDeployedData([program], accountId, 'program');
                    const enrichedProgram = enrichedProgramArr[0] || program;
                    const finalProgram = checkProgramPolicyConfigured(enrichedProgram);
                    return {
                        ...finalProgram,
                        programMappedDate,
                        accountProgramName: accountProgram?.name || null,
                        downloadedAt,
                    };
                })
            );
        }
        // catalog state info
        if (plain?.state) {
            plain.stateCode = plain.state.stateCode;
            plain.stateName = plain.state.stateName;
            delete plain.state;
        }
        finalData.push(plain);
    }

    return finalData;
};

const setLessonPolicy = async (accountId, lesson) => {
    const policyData = await lessonLessonCardPolicyData(accountId, lesson.id);
    const newLesson = checkLessonPolicyConfigured(policyData);
    lesson.hasPolicyConfigured = newLesson.hasPolicyConfigured || false;
    lesson.pendingPolicyConfiguration = newLesson.pendingPolicyConfiguration;
};

// check policy configuration
const addPolicyConfigureData = async (accountId, data) => {
    const processLessons = async (lessons = []) => {
        for (const lesson of lessons) {
            await setLessonPolicy(accountId, lesson);
        }
    };

    if (data.catalogItems) {
        for (const catalogData of data.catalogItems) {
            if (catalogData.lessons) {
                await processLessons(catalogData.lessons);
            }
            if (catalogData.programs) {
                for (const program of catalogData.programs) {
                    if (program.lessons) {
                        await processLessons(program.lessons);
                    }
                }
            }
        }
    }

    if (data.lessons) {
        await processLessons(data.lessons);
    }

    if (data.programs) {
        for (const program of data.programs) {
            if (program.lessons) {
                await processLessons(program.lessons);
            }
        }
    }

    return data;
};


const lessonLessonCardPolicyData = async (accountId, lessonId) => {
    const lessonWithPolicy = await Lessons.findOne({
        where: { id: lessonId },
        attributes: ['id', 'hasPolicy'],
        include: [
            {
                model: AccountLessons,
                where: { accountId },
                attributes: ['accountId', 'lessonId', 'title', 'createdAt'],
                required: false,
            },
            {
                model: LessonCard,
                attributes: ['id', 'cardType', 'list1', 'policyLink'],
                where: {
                    cardType: 'policyAcknowledgement',
                },
                include: [
                    {
                        model: AccountLessonCard,
                        attributes: ['id', 'policyType', 'link'],
                        where: {
                            accountId,
                        },
                        include: [{
                            model: Files,
                            attributes: ['id', 'path'],
                            as: 'file',
                            required: false,
                        }],
                        required: false,
                    }],
                required: false,
            }
        ]
    });
    return lessonWithPolicy;
};

const checkLessonPolicyConfigured = (lesson) => {
    let pendingPolicyConfiguration = 0;
    let atLeastOneConfigured = false;

    if (lesson.hasPolicy && Array.isArray(lesson.lessonCards)) {
        const policyCards = lesson.lessonCards.filter(card => card.cardType === 'policyAcknowledgement');

        for (const policyCard of policyCards) {
            const accountCards = policyCard.accountLessonCards || [];

            let isConfigured = false;

            for (const accountCard of accountCards) {
                isConfigured =
                    accountCard.policyType === 'link'
                        ? Boolean(accountCard.link?.length)
                        : Boolean(accountCard.file?.path);

                if (isConfigured) break; // Stop checking more accountCards for this policyCard
            }

            if (isConfigured) {
                atLeastOneConfigured = true;
            } else {
                pendingPolicyConfiguration += 1;
            }
        }

        lesson.hasPolicyConfigured = atLeastOneConfigured;
    }

    lesson.pendingPolicyConfiguration = pendingPolicyConfiguration;
    delete lesson.lessonCards;
    return lesson;
};

const checkProgramPolicyConfigured = (program) => {
    program.lessons.forEach((programLesson) => {
        // Check if the lesson has a policy
        if (programLesson.hasPolicy) {
            // eslint-disable-next-line no-param-reassign
            programLesson.hasPolicyConfigured = true;
            // Ensure the lesson has lesson cards
            if (programLesson.lessonCards.length) {
                if (programLesson.lessonCards.some(lessonCard => lessonCard.accountLessonCards?.length === 0)) {
                    // eslint-disable-next-line no-param-reassign
                    programLesson.hasPolicyConfigured = false;
                } else {
                    for (const lessonPolicyCard of programLesson.lessonCards) {
                        const policyCardData = lessonPolicyCard.accountLessonCards.map((accountCard) => {
                            return accountCard.policyType === 'link'
                                ? Boolean(accountCard.link?.length)
                                : Boolean(accountCard.file?.path);
                        });

                        if (policyCardData && policyCardData.includes(false)) {
                            // eslint-disable-next-line no-param-reassign
                            programLesson.hasPolicyConfigured = false;
                            break;
                        }
                    }
                }
            }
        }
        // eslint-disable-next-line no-param-reassign
        delete programLesson.lessonCards;
    });
    return program;
}

const transformPrograms = (programs) => {
    const listingsMap = new Map();
    const catalog = [];
    const seenProgramIds = new Set();

    for (const program of programs) {
        const catalogItem = program.catalogItem;
        const listing = catalogItem?.listing;

        // Prevent duplicate programs
        if (seenProgramIds.has(program.id)) continue;
        seenProgramIds.add(program.id);

        if (listing) {
            // Group by listing ID
            if (!listingsMap.has(listing.id)) {
                listingsMap.set(listing.id, {
                    id: listing.id,
                    title: listing.title,
                    programs: [],
                });
            }
            listingsMap.get(listing.id).programs.push(program);
        } else if (catalogItem) {
            // No listing, but has catalogItem
            catalog.push(program);
        }
    }

    return {
        listings: Array.from(listingsMap.values()),
        catalog,
    };
}

const getLessonProgramsByLessonId = async (accountId, lessonId) => {
    const bundleIds = await getResourceBundlesIds(accountId);
    const include = {
        model: Programs,
        attributes: ['id', 'name', 'isBase', 'isCustomized', 'isTimed', 'duration', 'minTimeInMinutes', 'edition', 'version', 'build'],
        through: 'lessonPrograms',
        where: { lifecycle: 'publish' },
        include: [
            {
                model: AccountPrograms,
                where: { accountId },
                attributes: ['accountId', 'programId', 'name', 'createdAt'],
                required: false,
            },
            {
                model: Resources,
                as: 'resource',
                attributes: ['id'],
                include: [
                    {
                        model: ResourceBundles,
                        attributes: ['id', 'downloadedAt', 'createdAt'],
                        where: {
                            bundleId: {
                                [Op.in]: bundleIds,
                            },
                            status : 'active',
                        },
                    },
                ],
                required: true,
            },
            {
                model: CatalogItems,
                attributes: ['id', 'title', 'audience', 'duration', 'edition', 'part', 'frequency', 'isListingAssociate', 'isClientSpecific'],
                include: [
                    {
                        model: Listings,
                        attributes: ['id', 'title'],
                        where: { lifecycle: 'published' },
                        required: false,
                    },
                    {
                        model: States,
                        attributes: ['id', 'stateName', 'stateCode'],
                    },
                ]
            }
        ],
        required: false,
    };
    const lessonInstance = await Lessons.findOne({
        attributes: ['id', 'title', 'description', 'isBase', 'isCustomized', 'requiredMinutes', 'edition', 'version', 'build', 'isStandalone', 'hasPolicy'],
        where: {
            id: lessonId,
            lifecycle: 'publish'
        },
        include: [
            include,
            {
                model: AccountLessons,
                where: { accountId },
                attributes: ['accountId', 'lessonId', 'title', 'createdAt'],
                required: false,
            },
            {
                model: Resources,
                as: 'resource',
                attributes: ['id'],
                include: [
                    {
                        model: ResourceBundles,
                        attributes: ['id', 'downloadedAt', 'createdAt'],
                        where: {
                            bundleId: {
                                [Op.in]: bundleIds,
                            },
                            status : 'active',
                        },
                    },
                ],
                required: true,
            },
        ]
    });

    if (!lessonInstance) return null;

    const lessonsData = lessonInstance.toJSON();
    if (lessonsData.programs) {
        lessonsData.programs = transformPrograms(lessonsData.programs);
    }
    return lessonsData;
}

module.exports = {
  updateMclData,
  updateContentNameInContentLibrary,
  getListingCatalogInactiveItems,
  addPolicyConfigureData,
  getLessonProgramsByLessonId,
};

