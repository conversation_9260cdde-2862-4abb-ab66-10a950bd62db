/* eslint-disable max-len */
const _ = require('lodash');

const db = require('../../db');
const config = require('../../config/config');
const logger = require('../../logger');

const { flattenLesson } = require('../../services/utils/lessonUtils');
const { restOperatorsToSequelize } = require('../../services/utils/sequelizeUtils');
const getSearchQuery = require('../../services/elasticsearch/searchQueries');
const { getAllowedPermissions } = require('../../services/acl/acl');
const { getContentStringQuery, getQueryLanguages } = require('../../services/utils/localizationUtils');
const { lookupLessons } = require('./programUtils');
const { Sequelize } = require('../../db');

const { decrypt } = config.encryption;
const Op = db.Sequelize.Op;
const Event = db.events;
const ResourceTopic = db.resourceTopics;
const Tag = db.tags;
const ResourceTag = db.resourceTags;
const Resource = db.resources;
const QuestionAnswer = db.questionAnswers;
const Lesson = db.lessons;
const MediaAsset = db.mediaAssets;
const Program = db.programs;
const Topic = db.topics;
const User = db.users;
const FileModel = db.files;
const LessonCard = db.lessonCards;
const LessonLessonCard = db.lessonLessonCards;
const AnswerCard = db.answerCards;
const UserTopic = db.userTopics;
const UserLesson = db.userLessons;
const Accounts = db.accounts;
const AccountBundles = db.accountBundles;
const ResourceBundles = db.resourceBundles;
const ResourceAsset = db.resourceAssets;
const AccountLessons = db.accountLessons;
const AccountPrograms = db.accountPrograms;
const AccountLessonCard = db.accountLessonCards;
const CatalogItem = db.catalogItems;

const PUBLIC_BUNDLE_ID = 1;
const SEARCH_TEXT_COL_LIMIT = 65000;

/**
 * Creates a sort function based on the sort keys.  Probably could be refactored to a general utility function
 */
const makeSorter = ($sort) => {
  const iteratees = Object.keys($sort);
  const orders = Object.keys($sort).map(key => ($sort[key] === 1 ? 'asc' : 'desc'));
  return function (collection) {
    return _.orderBy(collection, iteratees, orders);
  };
};

/**
 * Preferably unnecessary function that a hook would handle, but the sequelize
 * afterFind hook doesn't affect included models.
 */
/* eslint-disable no-param-reassign */
const decryptExpert = (recordOrRecords) => {
  let data = recordOrRecords;
  if (!Array.isArray(recordOrRecords)) {
    data = [recordOrRecords];
  }
  data.forEach((record) => {
    if (record.hasOwnProperty('resourceObject')) {
      data = record.resourceObject;
    } else {
      data = record;
    }
  });
  return recordOrRecords;
};
/* eslint-enable no-param-reassign */

const getQuestionsIsPrivateClause = (user, allowedPermissions) => {
  let isPrivateClause = {};
  if (!user) {
    isPrivateClause = { isPrivate: false };
  } else if (!allowedPermissions.questionAnswers.includes('readPrivate')) {
    isPrivateClause = { [Op.or]: [{ isPrivate: false }, { isPrivate: true, userId: user.id }] };
  }
  return isPrivateClause;
};

const getMediaAssetsIsPrivateClause = (user, allowedPermissions) => {
  let isPrivateClause = {};
  if (!user || !allowedPermissions.mediaAssets.includes('readPrivate')) {
    isPrivateClause = { isPrivate: false };
  }
  return isPrivateClause;
};
/**
* Converts a REST call's $sort parameter to sequelize.  Generalized function... can be moved.
*/
const restSortToSequelize = (restSort) => {
  return _.map(restSort, (value, key) => {
    return [key, value === '-1' ? 'DESC' : 'ASC'];
  });
};

/**
 * Get the latest status of an event given a list of events of that type
 */
const getEventStatus = (events, eventName) => {
  const latestEvent = events.filter(e => e.type === eventName)[0];
  return latestEvent ? !!latestEvent.action : false;
};

/**
 * Return the userResource formatted object given a list of events
 */
const parseEvents = (events) => {
  return {
    viewed: getEventStatus(events, 'view'),
    shared: getEventStatus(events, 'share'),
    helpful: getEventStatus(events, 'helpful'),
    saved: getEventStatus(events, 'save'),
  };
};

const groupEvents = (events) => {
  // Convert events to a userResource object by grouping the events
  if (events.length === 0) {
    return [];
  }
  // Group by trackableId/resourceId to make parsing easier
  const grouped = _.groupBy(events, r => r.trackableId);
  const resourceIds = Object.keys(grouped);

  const result = [];
  resourceIds.forEach((resourceId) => {
    // Group by userId
    const userEvents = _.groupBy(grouped[resourceId], e => e.userId);
    const userIds = Object.keys(userEvents);
    // Create a result per user
    result.push(...userIds.map(userId => (
      _.merge({
        resourceId: +resourceId,
        userId: +userId,
      }, parseEvents(userEvents[userId]))
    )));
  });
  return result;
};


/**
 * Include event information for this user for each of the resources or digestables.
 * The userResource object will include booleans for helpful, saved, shared, viewed
 *  digestables will have a dataObject.resource.id set, while a resource will just use the dataObject.id
 */
async function includeUserResources(req, data) {
  if (!req.user) {
    return data;
  }
  // Get events with current user and relevant resource Ids
  const resourceIds = data.map(d => (d.hasOwnProperty('resourceId') ? d.resourceId : d.id));
  const events = await Event.findAll({
    where: {
      trackableType: 'resource',
      trackableId: {
        [Op.in]: resourceIds,
      },
      userId: req.user.id,
    },
    order: [['createdAt', 'DESC']],
  });
  const groupedEvents = groupEvents(events);
  // Attach the userResource object to the data
  return data.map((dataObject) => {
    const id = dataObject.hasOwnProperty('resourceId') ? dataObject.resourceId : dataObject.id;
    const userResource = groupedEvents.filter(groupedEvent => groupedEvent.resourceId === id)[0] || null;
    return _.merge({}, dataObject, { userResource });
  });
}

/**
 * Include event information for this user for the single digestable or resource.
 * The userResource object attached to the result will include booleans for helpful, saved, shared, viewed
 * digestable will have a dataObject.resource.id set, while a resource will just use the dataObject.id
 */
async function includeUserResource(req, dataObject) {
  if (!req.user) {
    return dataObject;
  }
  const id = dataObject.hasOwnProperty('resourceId') ? dataObject.resourceId : dataObject.id;
  const events = await Event.findAll({
    where: {
      trackableType: 'resource',
      trackableId: id,
      userId: req.user.id,
    },
    order: [['createdAt', 'DESC']],
  });
  const groupedEvents = groupEvents(events);

  // Attach the userResource object to the data
  const userResource = groupedEvents.filter(groupedEvent => groupedEvent.resourceId === id)[0] || null;
  return _.merge({}, dataObject, { userResource });
}

/**
 * On a patch request, the caller can pass in a list of topic Ids.  This will set up the relationship data
 * between the topics and the resource of the digestable.  No topics are created... they are expected to exist
 */
async function updateAttachedTopics(req, digestable) {
  let topicIds;
  if (typeof req.body.topicIds === 'string') {
    topicIds = JSON.parse(req.body.topicIds);
  } else {
    topicIds = req.body.topicIds;
  }
  if (!topicIds || !topicIds.length) {
    return;
  }

  await ResourceTopic.destroy({
    where: {
      resourceId: digestable.resource.id,
    },
  });
  const createParams = topicIds.map((topicId, i) => {
    return {
      resourceId: digestable.resource.id,
      topicId,
      order: i + 1,
    };
  });

  await ResourceTopic.bulkCreate(createParams, { validate: true });
}

/**
 * On a patch request, the caller can pass in a list of tags (as text).  This will set up the relationship data
 * between the tags and the resource of the digestable.  If the tag doesn't exist, it will be created.
 */
async function updateAttachedTags(req, digestable) {
  let untrimmedTags;
  if (typeof req.body.tags === 'string') {
    untrimmedTags = JSON.parse(req.body.tags);
  } else {
    untrimmedTags = req.body.tags;
  }
  if (!untrimmedTags || !untrimmedTags.length) {
    return;
  }
  const tags = untrimmedTags.map(tag => tag.trim());

  await ResourceTag.destroy({
    where: {
      resourceId: digestable.resource.id,
    },
  });

  const existingTags = await Tag.findAll({
    where: {
      text: {
        [Op.in]: tags,
      },
    },
  });
  const newTags = tags
    .filter(text => !existingTags.find(tag => tag.text === text))
    .map((text) => {
      return {
        text,
      };
    });
  let newTagData = [];
  if (newTags.length > 0) {
    newTagData = await Tag.bulkCreate(newTags, { validate: true });
  }

  const allTags = existingTags.concat(newTagData);
  // TODO: These are ordered by existing before new instead of order passed in

  const createParams = allTags.map((tag, i) => {
    return {
      resourceId: digestable.resource.id,
      tagId: tag.id,
      order: i + 1,
    };
  });

  await ResourceTag.bulkCreate(createParams, { validate: true });
}

const canAccessLesson = (user, permissions, lesson) => {
  const lifecycle = lesson.lifecycle;
  const permission = permissions.lessons;
  if (!permission) {
    return false;
  }
  if (permission.includes('read') && (lifecycle === 'publish' || lifecycle === 'retired')) {
    return true;
  }
  if (permission.includes('review') && lifecycle === 'review') {
    return true;
  }
  if (user && user.id === lesson.userId &&
    (lifecycle === 'draft' || lifecycle === 'review')) {
    return true;
  }
  if (permission.includes('update') &&
    (lifecycle === 'publish' || lifecycle === 'review' || lifecycle === 'draft' || lifecycle === 'closed')) {
    return true;
  }

  return false;
};

const isAnyone = () => {
  return [
    { lifecycle: 'publish' },
    { lifecycle: 'retired' },
  ];
};

const isReviewer = (user, permissions, feature) => {
  if (permissions[feature].includes('review') ||
    permissions[feature].includes('update')) {
    return { lifecycle: 'review' };
  }
  return null;
};

const isEditor = (user, permissions, feature) => {
  if (permissions[feature].includes('update')) {
    return [
      { lifecycle: 'draft' },
      { lifecycle: 'close' },
    ];
  }
  return null;
};

const userFunctions = [isAnyone, isReviewer, isEditor];

const lifecycleByUser = async (req, user, feature) => {
  let query = [];
  const allowedPermissions = await getAllowedPermissions(req, user ? user.id : null, feature, req.tokenPayload);
  userFunctions.forEach((func) => {
    const result = func(user, allowedPermissions, feature);
    if (result) {
      query = query.concat(result);
    }
  });
  return query;
};

// get resource bundle for account
const getAccountBundle = async (accountId) => {
  const accountBundle = await AccountBundles.findOne({
    attributes: ['bundleId'],
    where: {
      accountId,
      bundleId: {
        [Op.ne]: PUBLIC_BUNDLE_ID,
      },
    },
    order: [['updatedAt', 'ASC']],
  });
  return accountBundle;
};

// gets ids of resource bundles for account of user
const getResourceBundlesIds = async (accountId) => {
  let bundleIds = [];
  if (accountId) {
    const bundles = await AccountBundles.findAll({
      attributes: ['bundleId'],
      where: {
        accountId,
      },
    });
    bundleIds = bundles.map(bundle => bundle.bundleId);
  } else {
    bundleIds = [PUBLIC_BUNDLE_ID];
  }
  return bundleIds;
};

// sets or removes bundle access record for modelObj based on isPubic flag
const setPublicAccess = async (modelObj) => {
  let retVal;
  if (modelObj) {
    if (modelObj.isPublic) {
      retVal = ResourceBundles.create({
        bundleId: PUBLIC_BUNDLE_ID,
        resourceId: modelObj.id,
      });
    } else {
      retVal = ResourceBundles.destroy({
        where: {
          resourceId: modelObj.id,
          bundleId: PUBLIC_BUNDLE_ID,
        },
      });
    }
  }
  return retVal;
};

// set bundle access for modelObj based on user account and isPublic flag
const setAccess = async (user, modelObj) => {
  const promises = [];
  if (modelObj) {
    const usersAccountBundle = await getAccountBundle(user.accountId);
    if (user) {
      if (usersAccountBundle) {
        promises.push(ResourceBundles.create({
          bundleId: usersAccountBundle.bundleId,
          resourceId: modelObj.id,
        }));
      }

      // if the question comes from an emtrain user, it goes in the public bundle
      const userAccts = user.accounts.map(ua => ua.name.toLowerCase());
      if (modelObj.digestable === 'questionAnswers' && userAccts.some(acctName => acctName.includes('emtrain'))) {
        promises.push(ResourceBundles.create({
          bundleId: PUBLIC_BUNDLE_ID,
          resourceId: modelObj.id,
        }));
      }
    }
    if (modelObj.isPublic) {
      promises.push(setPublicAccess(modelObj));
    }
    if (modelObj.digestable === 'questionAnswers') {
      // add the questionAnswer to the admin account so that admins can assign the q
      const adminAccount = await Accounts.findOne({ attributes: ['id'], where: { accountType: 'admin' } });
      const accountBundle = await getAccountBundle(adminAccount.id);
      if (accountBundle && accountBundle.bundleId !== usersAccountBundle.bundleId) {
        promises.push(ResourceBundles.create({
          bundleId: accountBundle.bundleId,
          resourceId: modelObj.id,
        }));
      }
    }
  }
  return Promise.all(promises);
};

const setResourceAccess = async (user, resource) => {
  return setAccess(user, resource);
};

// methods moved from countrollers/resources for dashboard

/**
* This is to rename the digestable object to resourceObject,
* since we can't seem to do that directly in sequelize.
* To be compatible, the existing API calls the specific digestable a resourceObject
* no matter the digestable.
*/
const convertToResourceObject = (req, sequelizeRecord) => {
  let newData;
  const record = sequelizeRecord.get({ plain: true });
  if (record.questionAnswer) {
    newData = { ...record, resourceObject: { ...record.questionAnswer } };
    delete newData.questionAnswer;
  } else if (record.mediaAsset) {
    newData = { ...record, resourceObject: { ...record.mediaAsset } };
    delete newData.mediaAsset;
  } else if (record.lesson) {
    const flatLesson = flattenLesson(req, record.lesson, req.i18n.language, true);
    newData = { ...record, resourceObject: { ...flatLesson } };
    delete newData.lesson;
  } else if (record.resourceAsset) {
    newData = { ...record, resourceObject: { ...record.resourceAsset } };
    delete newData.resourceAsset;
  } else {
    newData = { ...record };
  }
  return newData;
};

/**
* Returns an object that defines the general sub-query for the digestable type the caller is querying.
* Note that we include the resource, which will include tags and topics for that resource.  This means
* we include the resource within the digestable within the resource... strictly for backwards compatibility.
*/
const getDigestableInclude = (req, user, digestableType, includeLessonCards) => {
  const include = [
    {
      model: Resource,
      as: 'resource',
      include: [
        {
          model: Topic,
          through: 'resourceTopics',
        },
        {
          model: Tag,
          through: 'resourceTags',
        },
      ],
    },
  ];
  if (digestableType === 'questionAnswers') {
    include.push({
      model: User,
      as: 'user',
      include: [{
        model: Accounts,
      }, {
        model: FileModel,
        as: 'avatar',
      }],
    });
    include.push({
      model: User,
      as: 'expert',
      attributes: ['firstName', 'lastName', 'title', 'description', 'avatarId',
        'bannerId'],
      include: [{
        model: Accounts,
      }],
    });
    include.push(getContentStringQuery(req, getQueryLanguages(req), 'questionAnswer'));
  } else if (digestableType === 'programs') {
    include.push(getContentStringQuery(req, getQueryLanguages(req), 'program'));
  } else if (digestableType === 'lessons') {
    if (user) {
      if (includeLessonCards) {
        include.push({
          model: LessonCard,
          include: [
            {
              model: LessonLessonCard,
              as: 'bindings',
              include: [{
                model: AnswerCard,
                where: {
                  userId: user.id,
                },
                required: false,
              }],
              required: false,
            },
          ],
        });
      }
      include.push({
        model: UserLesson,
        where: {
          userId: user.id,
        },
        required: false,
      });
    } else if (includeLessonCards) {
      include.push({
        model: LessonCard,
      });
    }
    include.push(getContentStringQuery(req, getQueryLanguages(req), 'lesson'));
  } else if (digestableType === 'resourceAssets') {
    include.push({
      association: ResourceAsset.associations.file,
    });
    include.push(getContentStringQuery(req, getQueryLanguages(req), 'resourceAsset'));
  }
  return include;
};

/**
 * Check for search parameters and execute the search
 */
const search = (req, userQuery, filterAccountId) => {
  let searchWhereClause = {};
  let sortedResourceIds = [];
  let getSearchResults = Promise.resolve();

  if (userQuery.search) {
    const searchClient = req.app.get('elasticSearchClient');
    if (searchClient) {
      const searchScheme = config.elasticsearch.searchScheme;
      const startQuery = getSearchQuery(searchScheme, userQuery.search);
      const query = { ...startQuery, from: 0, size: 50 };

      getSearchResults = searchClient.search(query)
        .then((results) => {
          let hits = [];
          if (results && results.hits && results.hits.total > 0) {
            hits = results.hits.hits;
          }
          sortedResourceIds = hits.map(r => +r._id); // eslint-disable-line no-underscore-dangle
          searchWhereClause = {
            id: {
              [Op.in]: sortedResourceIds,
            },
            [Op.and]: [
              {
                [Op.or]: [
                  { isBrowsable: true },
                  {
                    [Op.and]: [
                      { digestable: 'lessons' },
                      // eslint-disable-next-line prefer-template
                      { id: { [Op.in]: db.Sequelize.literal('(SELECT lessons.resourceId FROM lessons, accountLessons ' +
                        'WHERE lessons.id = accountLessons.lessonId AND accountLessons.isBrowsable = true' +
                       (filterAccountId ? ` AND accountLessons.accountId = ${filterAccountId}` : '') +
                        ')') } },
                    ],
                  },
                  {
                    [Op.and]: [
                      { digestable: 'programs' },
                      // eslint-disable-next-line prefer-template
                      { id: { [Op.in]: db.Sequelize.literal('(SELECT programs.resourceId FROM ' +
                        'programs, accountPrograms WHERE programs.id = accountPrograms.programId ' +
                        'AND accountPrograms.isBrowsable = true' +
                        (filterAccountId ? ` AND accountPrograms.accountId = ${filterAccountId}` : '') +
                        ')') } },
                    ],
                  },
                ],
              },
              {
                [Op.and]: [
                  {
                    [Op.or]: [
                      { digestable: { [Op.ne]: 'lessons' } },
                      // eslint-disable-next-line prefer-template
                      { id: { [Op.notIn]: db.Sequelize.literal('(SELECT lessons.resourceId FROM ' +
                        'lessons, accountLessons WHERE lessons.id = accountLessons.lessonId ' +
                        'AND accountLessons.isBrowsable = false' +
                        (filterAccountId ? ` AND accountLessons.accountId = ${filterAccountId}` : '') +
                        ')') } },
                    ],
                  },
                  {
                    [Op.or]: [
                      { digestable: { [Op.ne]: 'programs' } },
                      // eslint-disable-next-line prefer-template
                      { id: { [Op.notIn]: db.Sequelize.literal('(SELECT programs.resourceId FROM ' +
                        'programs, accountPrograms WHERE programs.id = accountPrograms.programId ' +
                        'AND accountPrograms.isBrowsable = false' +
                        (filterAccountId ? ` AND accountPrograms.accountId = ${filterAccountId}` : '') +
                        ')') } },
                    ],
                  },
                ],
              },
            ],
          };
        });
    } else {
      getSearchResults = db.sequelize.query(
        'SELECT id FROM `resources`' +
        'WHERE MATCH(searchText) AGAINST(? IN NATURAL LANGUAGE MODE)',
        {
          replacements: [userQuery.search],
          type: db.sequelize.QueryTypes.SELECT,
          raw: true,
        },
      )
        .then((results) => {
          sortedResourceIds = results.map(r => r.id);
          searchWhereClause = {
            id: {
              [Op.in]: sortedResourceIds,
            },
          };
        });
    }
  }
  return getSearchResults
    .then(() => {
      return { searchWhereClause, sortedResourceIds };
    });
};

const applyMyTopics = async (user) => {
  // Get current user's topics
  const userTopics = await UserTopic.findAll({
    where: {
      userId: user.id,
    },
  });
  if (userTopics.length > 0) {
    return {
      id: {
        [Op.in]: userTopics.map(ut => ut.topicId),
      },
    };
  }
  return null;
};

const applySaved = async (user) => {
  const events = await Event.findAll({
    raw: true,
    where: {
      userId: user.id,
      trackableType: 'resource',
      type: 'save',
    },
  });
  const resourceIds = events.map(e => e.trackableId);
  if (resourceIds.length > 0) {
    return {
      id: {
        [Op.in]: resourceIds,
      },
    };
  }
  return null;
};

const applyShared = async (user) => {
  const events = await Event.findAll({
    raw: true,
    where: {
      userId: user.id,
      trackableType: 'resource',
      type: 'share',
    },
  });
  const resourceIds = events.map(e => e.trackableId);
  if (resourceIds.length > 0) {
    return {
      id: {
        [Op.in]: resourceIds,
      },
    };
  }
  return null;
};

/**
 * Create the initial resource query.
 * Return null to not run any query and finally return 0-length data to the user
 * but no error.
 * Otherwise, return an object with the query and the list of digestable types that
 * we're querying.
 */
const buildResourceQuery = async (req, user, userQuery, initialDigestableTypes, accountId = null) => {
  let digestableTypes = initialDigestableTypes;
  let order = userQuery.$sort ? restSortToSequelize(userQuery.$sort) : [['createdAt', 'DESC']];
  let whereClause = {};
  let topicWhereClause = {};

  // Filter by type
  if (userQuery.type) {
    digestableTypes = userQuery.type.split(',');
  }

  let filterAccountId = accountId;
  if (!filterAccountId && req.user && req.user.accountId) {
    filterAccountId = req.user.accountId;
  }

  // Some of the filters affect the initial resource query... Use them
  switch (userQuery.filter) {
    case 'pending':
    case 'unanswered': {
      digestableTypes = ['questionAnswers'];
      break;
    }
    case 'myTopics': {
      if (!user) {
        return null;
      }
      topicWhereClause = await applyMyTopics(user);
      if (!topicWhereClause) {
        return null;
      }
      break;
    }
    // This query selects programs/lessons that are browsable:
    // The query logic is as follows:
    //   if ((content.isBrowseable || content.account.isBrowseable = true) && (content.isBrowseable != false)) then isBrowseable = true.
    case 'latest': {
      order = [['updatedAt', 'DESC']];
      whereClause = {
        [Op.and]: [
          {
            [Op.or]: [
              { isBrowsable: true },
              {
                [Op.and]: [
                  { digestable: 'lessons' },
                  // eslint-disable-next-line prefer-template
                  { id: { [Op.in]: db.Sequelize.literal('(SELECT lessons.resourceId FROM lessons, accountLessons ' +
                    'WHERE lessons.id = accountLessons.lessonId AND accountLessons.isBrowsable = true' +
                   (filterAccountId ? ` AND accountLessons.accountId = ${filterAccountId}` : '') +
                    ')') } },
                ],
              },
              {
                [Op.and]: [
                  { digestable: 'programs' },
                  // eslint-disable-next-line prefer-template
                  { id: { [Op.in]: db.Sequelize.literal('(SELECT programs.resourceId FROM ' +
                    'programs, accountPrograms WHERE programs.id = accountPrograms.programId ' +
                    'AND accountPrograms.isBrowsable = true' +
                    (filterAccountId ? ` AND accountPrograms.accountId = ${filterAccountId}` : '') +
                    ')') } },
                ],
              },
            ],
          },
          {
            [Op.and]: [
              {
                [Op.or]: [
                  { digestable: { [Op.ne]: 'lessons' } },
                  // eslint-disable-next-line prefer-template
                  { id: { [Op.notIn]: db.Sequelize.literal('(SELECT lessons.resourceId FROM ' +
                    'lessons, accountLessons WHERE lessons.id = accountLessons.lessonId ' +
                    'AND accountLessons.isBrowsable = false' +
                    (filterAccountId ? ` AND accountLessons.accountId = ${filterAccountId}` : '') +
                    ')') } },
                ],
              },
              {
                [Op.or]: [
                  { digestable: { [Op.ne]: 'programs' } },
                  // eslint-disable-next-line prefer-template
                  { id: { [Op.notIn]: db.Sequelize.literal('(SELECT programs.resourceId FROM ' +
                    'programs, accountPrograms WHERE programs.id = accountPrograms.programId ' +
                    'AND accountPrograms.isBrowsable = false' +
                    (filterAccountId ? ` AND accountPrograms.accountId = ${filterAccountId}` : '') +
                    ')') } },
                ],
              },
            ],
          },
        ],
      };
      break;
    }
    case 'library': {
      order = [['updatedAt', 'DESC']];
      break;
    }
    case 'saved': {
      if (!user) {
        return null;
      }
      whereClause = await applySaved(user);
      if (!whereClause) {
        return null;
      }
      break;
    }
    case 'shared': {
      if (!user) {
        return null;
      }
      whereClause = await applyShared(user);
      if (!whereClause) {
        return null;
      }
      break;
    }
    case 'myOrganization': {
      if (!user) {
        return null;
      }
      break;
    }
    default:
      break;
  }
  const resourceQuery = {
    order,
    where: {
      digestable: {
        [Op.in]: digestableTypes,
      },
    },
  };

  // this applies the resource access filter - a user with 'read' gets all the accounts
  // a user with 'readAccount' (the only other option for this api) is constrained to their account
  const allowedPermissions = await getAllowedPermissions(req, user ? user.id : null, 'resources', req.tokenPayload);
  if (!allowedPermissions.resources.includes('read')) {
    const bundleAccountId = accountId || (user ? user.accountId : null);
    const bundleIds = await getResourceBundlesIds(bundleAccountId);
    const include = {
      model: ResourceBundles,
      where: {
        bundleId: {
          [Op.in]: bundleIds,
        },
      },
      required: true,
    };
    Object.assign(resourceQuery, { include });
  }

  const { searchWhereClause, sortedResourceIds } = await search(req, userQuery, filterAccountId);
  resourceQuery.where = Object.assign({}, resourceQuery.where, whereClause, searchWhereClause);
  if (!_.isEmpty(topicWhereClause)) {
    resourceQuery.include = [
      {
        model: Topic,
        through: 'resourceTopics',
        where: topicWhereClause,
      },
    ];
  }
  return {
    resourceQuery,
    digestablesToQuery: digestableTypes,
    sortedResourceIds,
  };
};

/**
 * For each digestable type, we need to add filtering parameters based on what's
 * happening at run-time.
 * This includes passed-in filters, isPrivate, ...
 */
const applyDigestableRuntimeParms = async (req, user, digestable, userQuery) => {
  let filterParams = {};
  if (digestable === 'questionAnswers') {
    if (userQuery.filter === 'unanswered') {
      if (!user) {
        return null;
      }
      filterParams = {
        status: {
          [Op.in]: ['pending', 'assigned'],
        },
        userId: user.id,
      };
    } else if (userQuery.filter === 'pending') {
      if (!user) {
        return null;
      }
      filterParams = {
        status: 'pending',
        userId: user.id,
      };
    } else {
      const allowedPermissions = await getAllowedPermissions(
        req,
        user ? user.id : null, 'questionAnswers', req.tokenPayload,
      );

      filterParams = { ...filterParams, ...getQuestionsIsPrivateClause(user, allowedPermissions) };
    }
  } else if (digestable === 'resourceAssets') {
    filterParams = { ...filterParams };
  } else if (digestable === 'lessons') {
    const lessonLifecycleQuery = await lifecycleByUser(req, user, 'lessons');
    filterParams = { [Op.or]: lessonLifecycleQuery };
  } else if (digestable === 'programs') {
    const programLifecycleQuery = await lifecycleByUser(req, user, 'programs');
    filterParams = { [Op.or]: programLifecycleQuery };
  }
  return filterParams;
};

const buildDigestableQueries = async (req, user, userQuery, digestables, resourceOrResources) => {
  let resources;
  let includeLessonCards = true;
  if (!Array.isArray(resourceOrResources)) {
    resources = [resourceOrResources];
  } else {
    resources = resourceOrResources;
    includeLessonCards = false;
  }


  const topicOrder = [
    { model: Resource, as: 'resource' },
    { model: Topic, through: 'resourceTopics' },
    { model: ResourceTopic },
    'order',
    'asc',
  ];

  const tagOrder = [
    { model: Resource, as: 'resource' },
    { model: Tag, through: 'resourceTags' },
    { model: ResourceTag },
    'order',
    'asc',
  ];

  const lessonCardOrder = [
    { model: LessonCard, through: 'lessonLessonCards' },
    { model: LessonLessonCard },
    'position',
    'asc',
  ];

  const digestableQueries = {
    questionAnswers: {
      model: QuestionAnswer,
      where: {
        status: 'answered',
      },
    },
    lessons: {
      model: Lesson,
    },
    programs: {
      model: Program,
    },
    resourceAssets: {
      model: ResourceAsset,
    },
  };
  if (resources.length === 0) {
    return null;
  }

  for (const digestable of digestables) {
    const ids = resources.filter(r => r.digestable === digestable).map(r => r.id);
    const params = _.get(userQuery, digestable) || {};

    const filterParams = await applyDigestableRuntimeParms(req, user, digestable, userQuery);
    if (!filterParams) {
      digestableQueries[digestable] = null;
    } else {
      const order = [['updatedAt', 'DESC'], topicOrder, tagOrder];
      if (digestable === 'lessons' && includeLessonCards) {
        order.push(lessonCardOrder);
      }
      digestableQueries[digestable] = Object.assign(
        {},
        digestableQueries[digestable], {
          where: Object.assign(
            {},
            digestableQueries[digestable].where,
            restOperatorsToSequelize(params),
            filterParams,
            { resourceId: { [Op.in]: ids } },
          ),
        }, {
          include: getDigestableInclude(req, user, digestable, includeLessonCards),
          order,
        },
      );
    }
    logger.debug(`query for ${digestable}: %j`, digestableQueries[digestable]);
  }
  return digestableQueries;
};

const applySearchSort = (userQuery, sortedResourceIds, data) => {
  let newResult = data;
  if (userQuery.search) {
    newResult = sortedResourceIds.reduce((acc, current) => {
      const resource = data.find(r => r.id === current);
      if (resource) {
        return acc.concat(resource);
      }
      return acc;
    }, []);
  }
  return newResult;
};
// end resource methods

const searchTextFromSearchInfo = (searchInfo) => {
  return `${searchInfo.p1 || ''} ${searchInfo.p2 || ''}`;
};

const updateElasticSearch = (req, resourceId, searchInfo) => {
  const searchClient = req.app.get('elasticSearchClient');
  if (resourceId && searchClient) {
    const searchText = searchTextFromSearchInfo(searchInfo);
    return searchClient.index({
      index: config.elasticsearch.index,
      type: 'document',
      id: resourceId.toString(),
      refresh: 'true',
      body: {
        searchText,
        origSearchText: searchText,
        searchTextP1: `${searchInfo.p1 || ''}`,
        searchTextP2: `${searchInfo.p2 || ''}`,
      },
    });
  }
  return Promise.resolve();
};

const removeFromElasticSearch = (req, resourceId) => {
  const searchClient = req.app.get('elasticSearchClient');
  if (resourceId && searchClient) {
    return searchClient.delete({
      index: config.elasticsearch.index,
      type: 'document',
      id: resourceId.toString(),
      refresh: 'true',
    });
  }
  return Promise.resolve();
};

const getTopicIds = (topics) => {
  const topicIds = topics.map((topic) => {
    const newTopic = topic.get({ plain: true });
    return newTopic.id;
  });
  return topicIds;
};

const addResourceAccessToBundle = async (resourceId, bundleId) => {
  await ResourceBundles.findOrCreate({
    where: {
      bundleId,
      resourceId,
    },
    defaults: {
      bundleId,
      resourceId,
    },
  });
};

const addProgramChildAccess = async (programId, bundleId) => {
  const lessonBindings = await lookupLessons(programId);
  for (const lessonBinding of lessonBindings) {
    await addResourceAccessToBundle(lessonBinding.lesson.resourceId, bundleId);
  }
};

const customSorting = (column, values, direction) => {
  let orderByClause = 'CASE ';
  for (let index = 0; index < values.length; index++) {
    let value = values[index];
    if (typeof value === 'string') { value = `'${value}'`; }
    orderByClause += `WHEN ${column} = ${value} THEN '${index}' `;
  }
  orderByClause += `ELSE ${column} END`;
  return [Sequelize.literal(orderByClause), direction];
};

const handleResourceValidation = async (id, resourceId, bundleIds, i18n, type) => {
  const resource = await Resource.findByPk(resourceId, {
    include: [{
      model: ResourceBundles,
      where: { bundleId: { [Op.in]: bundleIds } },
      required: true,
    }],
  });

  if (!resource) {
    const errorKey = type === 'lesson' ? 'accountLessons.retrieval_Error' : 'accountPrograms.retrieval_Error';
    const err = new Error(i18n.t(errorKey, { id }));
    err.status = 404;
    throw err;
  }
};

const handleAccountContent = async (model, accountId, entityId, updateFields, entityObj, contentType, opt = null) => {
  const whereClause = { accountId, ...entityId };
  const entity = await model.findOne({ where: whereClause });
  if (!entity && !!updateFields?.name) {
    updateFields.hasLastCardMessage = !!entityObj?.completedMessage?.trim();
    updateFields.completedMessage = entityObj?.completedMessage;
    updateFields.downloadInstructions = entityObj?.downloadInstructions;
    updateFields.certificateText = entityObj?.certificateText;
    updateFields.hasCertificate = entityObj?.hasCertificate;
    updateFields.minTimeInMinutes = entityObj?.minTimeInMinutes;
    updateFields.minCardTimeInSeconds = entityObj?.minCardTimeInSeconds;
  }

  if (entity && updateFields && !updateFields.hasLastCardMessage && !!updateFields?.completedMessage) {
    updateFields.completedMessage = '<p><br/></p>';
  }

  if (contentType === 'program') {
    if(!updateFields.name) {
      const { isTimed, minTimeInMinutes } = entityObj?.dataValues;
      if (!entity) {
        Object.assign(
          updateFields,
          opt
            ? { name: entityObj.name }
            : {
              hasCertificate: entityObj.hasCertificate,
              certificateText: entityObj.certificateText,
              downloadInstructions: entityObj.downloadInstructions,
              completedMessage: entityObj.completedMessage,
            },
        );

        if (isTimed && minTimeInMinutes) {
          Object.assign(updateFields, {
            minTimeInMinutes,
            minCardTimeInSeconds: minTimeInMinutes * 60,
          });
        }
      } else if (isTimed) {
        // eslint-disable-next-line no-param-reassign
        delete updateFields.minTimeInMinutes;
      }
    }
  }

  // Create or update the entity
  if (entity) {
    await model.update(updateFields, { where: whereClause });
  } else {
    await model.create({ accountId, ...entityId, ...updateFields });
  }

  // Fetch and add additional field if required
  const resultModel = await model.findOne({ where: whereClause });
  if (contentType === 'program' && resultModel) {
    resultModel.dataValues.isTimed = entityObj.isTimed;
    resultModel.dataValues.isAccountProgramModified = true;
  }

  return resultModel;
};

const updateContentTitle = async (id, contentType, accountId, contentTitle, i18n) => {
  let response = null;
  const bundleIds = await getResourceBundlesIds(accountId);
  if (contentType === 'lesson') {
    const lesson = await Lesson.findOne({ where: { id } });
    if (!lesson) {
      throw new Error(i18n.t('accountLessons.retrieval_Error', { id }));
    }

    await handleResourceValidation(id, lesson.resourceId, bundleIds, i18n, 'lesson');
    response = await handleAccountContent(
      AccountLessons,
      accountId,
      { lessonId: id },
      { title: contentTitle },
      lesson,
      contentType,
    );
  } else if (contentType === 'program') {
    const program = await Program.findOne({ where: { id } });
    if (!program) {
      throw new Error(i18n.t('accountPrograms.retrieval_Error', { id }));
    }

    await handleResourceValidation(id, program.resourceId, bundleIds, i18n, 'program');
    response = await handleAccountContent(
      AccountPrograms,
      accountId,
      { programId: id },
      { name: contentTitle },
      program,
      contentType,
    );
  }
  return response;
};

// Fetch policy lesson cards
const getPolicyLessonCards = async (lessonCardIds, accountId, lessonId=null) => {
  const policyLessonCards = await LessonCard.findAll({
    where: {
      id: { [Op.in]: lessonCardIds },
      cardType: 'policyAcknowledgement',
    },
    attributes: ['id', 'title', 'description', 'cardType', 'list1', 'policyLink'],
    include: [
      {
        model: AccountLessonCard,
        where: { accountId },
        required: false,
        include: [
          {
            model: FileModel,
            as: 'file',
            required: false,
          },
        ],
      },
      {
        model: FileModel,
        as: 'images',
        required: false,
      },
    ],
  });
  return {lessonId, policyLessonCards }
};

const getPolicyLessonCardsMap = async (lessonIds, accountId) => {
  const relatedLessonCards = await LessonLessonCard.findAll({
    where: { lessonId: { [Op.in]: lessonIds } },
  });

  const lessonCardMap = {};
  relatedLessonCards.forEach(({ lessonId, lessonCardId }) => {
    if (!lessonCardMap[lessonId]) {
      lessonCardMap[lessonId] = [];
    }
    lessonCardMap[lessonId].push(lessonCardId);
  });

  const responses = await Promise.all(
    Object.entries(lessonCardMap).map(async ([lessonId, cardIds]) => {
      const { policyLessonCards } = await getPolicyLessonCards(cardIds, accountId, lessonId);
      return { lessonId: parseInt(lessonId), policyLessonCards };
    })
  );

  return responses.reduce((acc, { lessonId, policyLessonCards }) => {
    acc[lessonId] = policyLessonCards;
    return acc;
  }, {});
};

// Fetch Content Details
const getContentDetails = async (id, type) => {
  if (!type) {
    throw new Error('Content type required.');
  }

  const baseAttributes = {
    lesson: ['id', 'published', 'title', 'description', 'resourceId'],
    program: ['id', 'name', 'minTimeInMinutes', 'minCardTimeInSeconds', 'hasCertificate', 'certificateText', 'downloadInstructions', 'completedMessage', 'resourceId', 'isTimed'],
  };

  const includeCatalogItem = {
    model: CatalogItem,
    attributes: ['id', 'code', 'contentType', 'instructionalType', 'audience', 'duration', 'frequency', 'isClientSpecific', 'listingId'],
  };

  const includeLessons = {
    model: Lesson,
    attributes: ['id', 'resourceId', 'title', 'catalogId', 'published'],
    required: false,
  };

  const options = {
    where: { id },
    attributes: baseAttributes[type],
    include: [includeCatalogItem],
  };

  if (type === 'lesson') {
    includeCatalogItem.include = [includeLessons];
  } else if (type === 'program') {
    options.include.push(includeLessons);
  } else {
    throw new Error('Invalid content type.');
  }

  const model = type === 'lesson' ? Lesson : Program;
  return model.findOne(options);
};


module.exports = {
  PUBLIC_BUNDLE_ID,
  SEARCH_TEXT_COL_LIMIT,
  convertToResourceObject,
  buildResourceQuery,
  buildDigestableQueries,
  applySearchSort,
  search,
  applyMyTopics,
  applySaved,
  applyShared,
  applyDigestableRuntimeParms,
  makeSorter,
  getQuestionsIsPrivateClause,
  getMediaAssetsIsPrivateClause,
  restSortToSequelize,
  includeUserResource,
  includeUserResources,
  decryptExpert,
  updateAttachedTags,
  updateAttachedTopics,
  lifecycleByUser,
  canAccessLesson,
  searchTextFromSearchInfo,
  updateElasticSearch,
  removeFromElasticSearch,
  getTopicIds,
  getAccountBundle,
  getResourceBundlesIds,
  setResourceAccess,
  setPublicAccess,
  addResourceAccessToBundle,
  addProgramChildAccess,
  customSorting,
  handleResourceValidation,
  handleAccountContent,
  updateContentTitle,
  getPolicyLessonCards,
  getContentDetails,
  getPolicyLessonCardsMap,
};
