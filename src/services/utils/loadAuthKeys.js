const fs = require('fs');
const config = require('../../config/config');

const env = (process.env.NODE_ENV || '').toLowerCase();
const isDevOrTest = ['development', 'test'].includes(env);

const loadKeys = () => {
  if (isDevOrTest) {
    const publicKey = fs.readFileSync('./src/keys/public.pem', 'utf8');
    const privateKey = fs.readFileSync('./src/keys/private.pem', 'utf8');
    return { publicKey, privateKey };
  } else {
    const { publicKey, privateKey } = config.authKeys;
    return { publicKey, privateKey };
  }
};

// Cache keys once at load time
const keys = loadKeys();
const getPublicKey = () => keys.publicKey;
const getPrivateKey = () => keys.privateKey;

module.exports = {
  getPublicKey,
  getPrivateKey,
};
