const _ = require('lodash');
const sw = require('stopword');
const db = require('../../db');
const logger = require('../../logger');
const { getRandomInt } = require('./jsUtils');

const {
  answerCards: ReportingAnswerCards,
  accounts: ReportingAccounts,
  lessonLessonCards: ReportingLessonLessonCards,
  users: ReportingUsers,
  userLessons: ReportingUserLessons,
  groupAssignments: ReportingGroupAssignments,
  summaryLCResults: ReportingSummaryLCResults,
  insightsData: ReportingInsightsData,
} = db.reportingModels;

const LessonCards = db.lessonCards;
const Op = db.Sequelize.Op;
const reportingDb = db.sequelizeReportingDb;

function calcPercentage(noSelected, total) {
  return total ? (100 * (noSelected / total)).toFixed(1) : '0.0';
}

/**
 * converts a word frequency Set to an array in the format expected by react-wordcloud
 * and limits the # of results to the x amount of most frequent words
 *
 * @param {Map<string,number>} wordFreqObj javascript map of word frequencies
 * @param {number} retLength the number of words to return
 * @param {number} minWCThreshold words with frequency below this threshold will be filtered out
 * @returns {{text: string, value: number}[]} an array of length retLength
 */
const convertFreqObjToArr = (wordFreqObj, retLength, minWCThreshold) => {
  const arr = [];
  for (const [key, value] of wordFreqObj) {
    arr.push({ text: key, value });
  }

  // return the top-100 most frequent words
  // filter out words that occur with frequency below the threshold
  const returnArr = arr
    .sort((a, b) => b.value - a.value)
    .slice(0, retLength);
  return returnArr;
};

// profanity filter for wordcloud, this should maybe be stored in the db (redis?).
// RIP George Carlin
const profanity = ['shit', 'piss', 'fuck', 'cunt', 'cocksucker', 'motherfucker',
  'tits', 'fart', 'turd', 'twat', 'cock', 'fucker', 'asshole', 'bitch'];

// extra stopwords not included in the stopword package
const stopWords = ['not', 'else', 'let', 'so', 'may'];

// Preview Data Sets
const SLIDER = 0;
const SLIDER_POS = 1;
const SLIDER_NEG = 2;
const MULTI_CHOICE = 3;
const COLOR_SPECTRUM = 4;
const COLOR_SPECTRUM_GREEN = 5;
const COLOR_SPECTRUM_YELLOW = 6;
const COLOR_SPECTRUM_ORANGE = 7;
const COLOR_SPECTRUM_RED = 8;
const BOOLEAN = 9;
const BOOLEAN_POS = 10;
const BOOLEAN_NEG = 11;
const SINGLE_CHOICE_2 = 12;
const SINGLE_CHOICE_2_1 = 13;
const SINGLE_CHOICE_2_2 = 14;
const SINGLE_CHOICE_3 = 15;
const SINGLE_CHOICE_3_1 = 16;
const SINGLE_CHOICE_3_2 = 17;
const SINGLE_CHOICE_3_3 = 18;
const SINGLE_CHOICE_4 = 19;
const SINGLE_CHOICE_4_1 = 20;
const SINGLE_CHOICE_4_2 = 21;
const SINGLE_CHOICE_4_3 = 22;
const SINGLE_CHOICE_4_4 = 23;
const SINGLE_CHOICE_5 = 24;
const SINGLE_CHOICE_5_1 = 25;
const SINGLE_CHOICE_5_2 = 26;
const SINGLE_CHOICE_5_3 = 27;
const SINGLE_CHOICE_5_4 = 28;
const SINGLE_CHOICE_5_5 = 29;
const SINGLE_CHOICE_6 = 30;
const SINGLE_CHOICE_6_1 = 31;
const SINGLE_CHOICE_6_2 = 32;
const SINGLE_CHOICE_6_3 = 33;
const SINGLE_CHOICE_6_4 = 34;
const SINGLE_CHOICE_6_5 = 35;
const SINGLE_CHOICE_6_6 = 36;
const SINGLE_CHOICE_7 = 37;
const SINGLE_CHOICE_7_1 = 38;
const SINGLE_CHOICE_7_2 = 39;
const SINGLE_CHOICE_7_3 = 40;
const SINGLE_CHOICE_7_4 = 41;
const SINGLE_CHOICE_7_5 = 42;
const SINGLE_CHOICE_7_6 = 43;
const SINGLE_CHOICE_7_7 = 44;
const SINGLE_CHOICE_8 = 45;
const SINGLE_CHOICE_8_1 = 46;
const SINGLE_CHOICE_8_2 = 47;
const SINGLE_CHOICE_8_3 = 48;
const SINGLE_CHOICE_8_4 = 49;
const SINGLE_CHOICE_8_5 = 50;
const SINGLE_CHOICE_8_6 = 51;
const SINGLE_CHOICE_8_7 = 52;
const SINGLE_CHOICE_8_8 = 53;
const SINGLE_CHOICE_9 = 54;
const SINGLE_CHOICE_9_1 = 55;
const SINGLE_CHOICE_9_2 = 56;
const SINGLE_CHOICE_9_3 = 57;
const SINGLE_CHOICE_9_4 = 58;
const SINGLE_CHOICE_9_5 = 59;
const SINGLE_CHOICE_9_6 = 60;
const SINGLE_CHOICE_9_7 = 61;
const SINGLE_CHOICE_9_8 = 62;
const SINGLE_CHOICE_9_9 = 63;
const SINGLE_CHOICE_10 = 64;
const SINGLE_CHOICE_10_1 = 65;
const SINGLE_CHOICE_10_2 = 66;
const SINGLE_CHOICE_10_3 = 67;
const SINGLE_CHOICE_10_4 = 68;
const SINGLE_CHOICE_10_5 = 69;
const SINGLE_CHOICE_10_6 = 70;
const SINGLE_CHOICE_10_7 = 71;
const SINGLE_CHOICE_10_8 = 72;
const SINGLE_CHOICE_10_9 = 73;
const SINGLE_CHOICE_10_10 = 74;


const previewDataSets = [
  [5, 8, 11, 15, 31, 23, 7], // 0
  [5, 8, 11, 15, 31, 23, 7],
  [7, 23, 31, 15, 11, 8, 5],
  [34, 22, 24, 20],
  [34, 22, 24, 20], // 5
  [70, 20, 8, 2],
  [14, 70, 14, 2],
  [2, 14, 70, 14],
  [2, 8, 20, 70],
  [51, 49], // 10
  [77, 23],
  [23, 77],
  [80, 20],
  [81, 19],
  [19, 81], // 15
  [56, 24, 20],
  [76, 9, 15],
  [15, 76, 9],
  [9, 15, 76],
  [34, 22, 24, 20], // 20
  [71, 12, 15, 2],
  [2, 71, 12, 15],
  [15, 2, 71, 12],
  [12, 15, 2, 71], // 23
  [45, 10, 15, 20, 10], //24
  [70, 10, 5, 5, 10],
  [10, 60, 10, 10, 10],
  [10, 5, 79, 1, 5],
  [1, 4, 15, 75, 5],
  [10, 5, 10, 5, 70],
  [64, 5, 15, 5, 5, 6], //30
  [70, 1, 10, 9, 4, 6],
  [5, 60, 15, 5, 6, 9],
  [10, 5, 70, 5, 4, 6],
  [5, 5, 5, 70,4, 9],
  [5, 6, 15, 65, 5, 4],
  [5, 5, 10, 5, 4, 71], //36
  [64, 5, 1, 10, 5, 6, 9], //37
  [68, 5, 2, 5, 2, 4, 14],
  [5, 70, 5, 1, 4, 5, 10],
  [5, 1, 74, 5, 4, 1, 10],  
  [2, 5, 5, 70, 4, 5, 9],
  [5, 6, 3, 5, 70, 5, 6],
  [5, 5, 5, 5, 4, 75, 1],
  [5, 5, 5, 5, 4, 6, 70], //44
  [65, 1, 5, 10, 5, 1, 4, 9], //45
  [69, 5, 5, 5, 4, 6, 5, 1],
  [5, 65, 1, 6, 5, 4, 9, 5],
  [5, 1, 70, 4, 5, 6, 5, 4],  
  [2, 5, 5, 69, 5, 3, 8, 3],
  [5, 6, 3, 5, 70, 4, 5, 2],
  [5, 5, 10, 4, 67, 6, 2, 1],
  [5, 5, 5, 2, 4, 6, 72, 1],
  [5, 5, 5, 5, 4, 6, 3, 67], //53
  [65, 5, 5, 5, 2, 4, 5, 4, 5], //54
  [80, 2, 1, 1, 4, 4, 5, 1, 2],
  [5, 75, 1, 3, 5, 4, 1, 3, 3],
  [5, 1, 70, 4, 5, 1, 5, 4, 5],  
  [2, 1, 70, 4, 5, 3, 8, 3, 4],
  [5, 1, 3, 72, 5, 4, 5, 2, 3],
  [5, 7, 5, 4, 67, 6, 2, 1, 3],
  [5, 5, 6, 4, 6, 60, 4, 1, 9],
  [5, 5, 2, 5, 4, 6, 3, 62, 8],
  [5, 5, 1, 5, 4, 6, 3, 2, 69], //63 
  [66, 4, 1, 5, 2, 4, 5, 4, 5, 4], //64
  [65, 2, 5, 5, 4, 6, 5, 1, 4, 3],
  [5, 80, 1, 3, 2, 2, 1, 1, 3, 2],
  [5, 1, 70, 4, 5, 1, 2, 4, 5, 3],  
  [2, 1, 65, 4, 5, 3, 8, 3, 4, 5],
  [5, 1, 3, 70, 1, 4, 10, 2, 3, 1],
  [2, 5, 3, 5, 69, 1, 2, 6, 3, 4],
  [5, 8, 5, 2, 4, 66, 2, 1, 2, 5],
  [5, 1, 2, 5, 3, 6, 73, 2, 2, 1],
  [5, 2, 1, 5, 4, 2, 3, 72, 1, 5],    
  [5, 5, 1, 5, 4, 6, 3, 2, 8, 61], //74
];

const previewOrgTextData = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor' +
  ' incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco' +
  ' laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit' +
  ' esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa' +
  ' qui officia deserunt mollit anim id est laborum.' +
  ' At vero eos et accusamus et iusto odio dignissimos ducimus qui blanditiis praesentium' +
  ' voluptatum deleniti atque corrupti quos dolores et quas molestias excepturi sint occaecati cupiditate' +
  ' non provident, similique sunt in culpa qui officia deserunt mollitia animi, id est laborum et dolorum' +
  ' fuga. Et harum quidem rerum facilis est et expedita distinctio. Nam libero tempore, cum soluta nobis est' +
  ' eligendi optio cumque nihil impedit quo minus id quod maxime placeat facere possimus, omnis voluptas' +
  ' assumenda est, omnis dolor repellendus. Temporibus autem quibusdam et aut officiis debitis aut rerum' +
  ' necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae. Itaque earum' +
  ' rerum hic tenetur a sapiente delectus, ut aut reiciendis voluptatibus maiores alias consequatur aut' +
  ' perferendis doloribus asperiores repellat.';

const previewGlobalTextData = 'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium' +
  ' doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto' +
  ' beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut' +
  ' fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam' +
  ' est, qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit, sed quia non numquam eius modi' +
  ' tempora incidunt ut labore et dolore magnam aliquam quaerat voluptatem. Ut enim ad minima veniam, quis nostrum' +
  ' exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur? Quis autem vel' +
  ' eum iure reprehenderit qui in ea voluptate velit esse quam nihil molestiae consequatur, vel illum qui dolorem' +
  ' eum fugiat quo voluptas nulla pariatur?';

const prepStringForWordCloud = (text) => {
  // get rid of punctuation
  const sentenceString = text.replace(/[^\w\s]|_/g, '').split(/\s+/);

  // get rid of common words like 'and' 'the' 'it' etc...
  // https://www.npmjs.com/package/stopword
  // language support goes like this: sw.removeStopwords(oldString, sw.es) <- for spanish
  return sw.removeStopwords(sentenceString, [...sw.en, ...profanity, ...stopWords]);
};

function countWords(text) {
  const freqMap = new Map();
  const processedString = prepStringForWordCloud(text);
  processedString.forEach((strWord) => {
    const word = strWord.toLowerCase();
    const wordCount = freqMap.get(word);
    if (wordCount === undefined) {
      freqMap.set(word, 1);
    } else {
      freqMap.set(word, wordCount + 1);
    }
  });
  return freqMap;
}

async function wordCloudReport(user, lessonCard, llcIds, publishOnly, startDate, endDate, campaignId, lessonLifecycle) {
  if (lessonLifecycle && lessonLifecycle === 'preview') {
    // eslint-disable-next-line
    const previewwordCloudReport = await getPreviewAnswerReport(
      user,
      lessonCard,
      lessonCard.id,
      publishOnly,
    );
    return { globalWords: previewwordCloudReport.globalWords, accountWords: previewwordCloudReport.accountWords };
  }
  const numberOfWords = 100; // number of words to display in the wordcloud

  const userAccountId = user.accountId;
  const account = await db.accounts.findOne({ where: { id: userAccountId } });

  const minAnswersThreshold = account.responseThreshold || 5; // number of answerCards required before displaying the wordcloud
  const minWordCoundThreshold = account.responseThreshold || 5; // number of occurences of a word required before it can be displayed

  const useGlobalCache = publishOnly && !startDate && !endDate;

  const acQuery = {
    attributes: ['textAnswer', 'accountId'],
    where: {
      lessonLessonCardId: {
        [Op.in]: llcIds,
      },
      textAnswer: {
        [Op.ne]: null,
      },
    },
  };
  if (useGlobalCache) {
    // only query for account data. we'll get global data separately.
    acQuery.where = {
      ...acQuery.where,
      accountId: userAccountId,
    };
  }

  if (publishOnly) {
    acQuery.where = {
      ...acQuery.where,
      sourceLifecycle: 'publish',
    };
  }
  // date filter on org
  if (startDate || endDate) {
    if (startDate && endDate) {
      acQuery.where = {
        ...acQuery.where,
        createdAt: {
          [Op.and]: [
            { [Op.gte]: startDate },
            { [Op.lte]: endDate },
          ],
        },
      };
    } else if (startDate) {
      acQuery.where = {
        ...acQuery.where,
        createdAt: {
          [Op.gte]: startDate,
        },
      };
    } else if (endDate) {
      acQuery.where = {
        ...acQuery.where,
        createdAt: {
          [Op.lte]: endDate,
        },
      };
    }
  }

  const answerCards = await ReportingAnswerCards.findAll(acQuery);
  const accountWordFrequency = new Map();
  const globalWordFrequency = new Map();

  if (answerCards.length < minAnswersThreshold) {
    return { globalWords: [], accountWords: [] };
  }

  if (useGlobalCache) {
    const summary = await ReportingSummaryLCResults.findOne({
      attributes: ['lessonCardId', 'wordCounts'],
      where: {
        lessonCardId: lessonCard.id,
      },
    });
    summary.wordCounts.forEach((wc) => {
      const glwc = globalWordFrequency.get(wc.t);
      if (glwc === undefined) {
        globalWordFrequency.set(wc.t, wc.v);
      } else {
        globalWordFrequency.set(wc.t, glwc + wc.v);
      }
    });
  }

  for (let i = 0; i < answerCards.length; i++) {
    const answerCard = answerCards[i];
    const isOwnAccount = answerCard.accountId === userAccountId;

    const processedString = prepStringForWordCloud(answerCard.textAnswer);
    processedString.forEach((strWord) => {
      const word = strWord.toLowerCase();
      if (!useGlobalCache) {
        const glwc = globalWordFrequency.get(word);
        if (glwc === undefined) {
          globalWordFrequency.set(word, 1);
        } else {
          globalWordFrequency.set(word, glwc + 1);
        }
      }
      if (isOwnAccount) {
        const awc = accountWordFrequency.get(word);
        if (awc === undefined) {
          accountWordFrequency.set(word, 1);
        } else {
          accountWordFrequency.set(word, awc + 1);
        }
      }
    });
  }

  // put in the formatting for wordcloud package
  // https://github.com/chrisrzhou/react-wordcloud
  const globalWordArray = convertFreqObjToArr(globalWordFrequency, numberOfWords, minWordCoundThreshold);
  const accontWordArray = convertFreqObjToArr(accountWordFrequency, numberOfWords, minWordCoundThreshold);

  return { globalWords: globalWordArray, accountWords: accontWordArray };
}

async function percentageReport(
  user, lessonCard, llcIds, publishOnly,
  campaignId, programId, startDate, endDate, globalOnly = false, isInsightReport = false,
) {
  const report = [];
  const useGlobalCache = publishOnly && !startDate && !endDate;
  // This is only needed if we're querying on a campaignId or a programId
  const baseQueryIncludeIfNeeded = {
    model: ReportingUsers,
    attributes: [],
    required: true,
    include: [{
      model: ReportingAccounts,
      attributes: [],
      where: {
        id: user.accountId,
      },
    }],
  };

  try {
    let a1TrueOrg = 0;
    let a1TrueAll = 0;
    let a2TrueOrg = 0;
    let a2TrueAll = 0;
    let a3TrueOrg = 0;
    let a3TrueAll = 0;
    let a4TrueOrg = 0;
    let a4TrueAll = 0;
    let a5TrueOrg = 0;
    let a5TrueAll = 0;
    let a6TrueOrg = 0;
    let a6TrueAll = 0;
    let a7TrueOrg = 0;
    let a7TrueAll = 0;
    let a8TrueOrg = 0;
    let a8TrueAll = 0;
    let a9TrueOrg = 0;
    let a9TrueAll = 0;
    let a10TrueOrg = 0;
    let a10TrueAll = 0;
    let allTotal = 0;
    let orgTotal = 0;

    if (user && !globalOnly) {
      const baseQuery = {
        attributes: [
          [reportingDb.fn('SUM', reportingDb.col('answer1')), 'answer1'],
          [reportingDb.fn('SUM', reportingDb.col('answer2')), 'answer2'],
          [reportingDb.fn('SUM', reportingDb.col('answer3')), 'answer3'],
          [reportingDb.fn('SUM', reportingDb.col('answer4')), 'answer4'],
          [reportingDb.fn('SUM', reportingDb.col('answer5')), 'answer5'],
          [reportingDb.fn('SUM', reportingDb.col('answer6')), 'answer6'],
          [reportingDb.fn('SUM', reportingDb.col('answer7')), 'answer7'],
          [reportingDb.fn('SUM', reportingDb.col('answer8')), 'answer8'],
          [reportingDb.fn('SUM', reportingDb.col('answer9')), 'answer9'],
          [reportingDb.fn('SUM', reportingDb.col('answer10')), 'answer10'],
          [reportingDb.fn('COUNT', '*'), 'total'],
        ],
        where: {
          lessonLessonCardId: {
            [Op.in]: llcIds,
          },
          accountId: user.accountId,
        },
        includeIgnoreAttributes: false, // we need this because it's the only way to eliminate attributes on the through table for acccountUsers
        raw: true,
      };

      if (publishOnly) {
        baseQuery.where = {
          ...baseQuery.where,
          sourceLifecycle: 'publish',
        };
      }

      // campaign filter on org
      if (campaignId) {
        baseQuery.include = baseQueryIncludeIfNeeded;
        baseQuery.include.include.push({
          model: ReportingUserLessons,
          attributes: [],
          required: true,
          include: {
            model: ReportingGroupAssignments,
            attributes: [],
            where: {
              campaignId,
            },
          },
          where: {
            enrollmentId: { [Op.col]: 'answerCards.enrollmentId' },
          },
        });
      } else if (programId) {
        baseQuery.include = baseQueryIncludeIfNeeded;
        baseQuery.include.include.push({
          model: ReportingUserLessons,
          required: true,
          attributes: [],
          include: {
            model: ReportingGroupAssignments,
            attributes: [],
            where: {
              programId,
            },
          },
          where: {
            enrollmentId: { [Op.col]: 'answerCards.enrollmentId' },
          },
        });
      }
      // date filter on org
      if (startDate || endDate) {
        if (startDate && endDate) {
          baseQuery.where = {
            ...baseQuery.where,
            createdAt: {
              [Op.and]: [
                { [Op.gte]: startDate },
                { [Op.lte]: endDate },
              ],
            },
          };
        } else if (startDate) {
          baseQuery.where = {
            ...baseQuery.where,
            createdAt: {
              [Op.gte]: startDate,
            },
          };
        } else if (endDate) {
          baseQuery.where = {
            ...baseQuery.where,
            createdAt: {
              [Op.lte]: endDate,
            },
          };
        }
      }
      const orgCounts = await ReportingAnswerCards.findAll(baseQuery);
      if (orgCounts && orgCounts[0]) {
        const orgCount = orgCounts[0];
        a1TrueOrg = orgCount.answer1 || 0;
        a2TrueOrg = orgCount.answer2 || 0;
        a3TrueOrg = orgCount.answer3 || 0;
        a4TrueOrg = orgCount.answer4 || 0;
        a5TrueOrg = orgCount.answer5 || 0;
        a6TrueOrg = orgCount.answer6 || 0;
        a7TrueOrg = orgCount.answer7 || 0;
        a8TrueOrg = orgCount.answer8 || 0;
        a9TrueOrg = orgCount.answer9 || 0;
        a10TrueOrg = orgCount.answer10 || 0;
        orgTotal = orgCount.total || 0;
      }
    }
    if (useGlobalCache) {
      const summary = await ReportingSummaryLCResults.findOne({
        where: {
          lessonCardId: lessonCard.id,
        },
        raw: true,
      });
      if (summary) {
        const keys = Object.keys(summary).filter(key => key.startsWith('val') && summary[key]);
        const sums = keys.reduce((acc, key) => {
          return {
            ...acc,
            [key]: summary[key],
          };
        }, {});
        allTotal = summary.total || 0;
        a1TrueAll = sums.val1 || 0;
        a2TrueAll = sums.val2 || 0;
        a3TrueAll = sums.val3 || 0;
        a4TrueAll = sums.val4 || 0;
        a5TrueAll = sums.val5 || 0;
        a6TrueAll = sums.val6 || 0;
        a7TrueAll = sums.val7 || 0;
        a8TrueAll = sums.val8 || 0;
        a9TrueAll = sums.val9 || 0;
        a10TrueAll = sums.val10 || 0;
      }
    } else {
      const publishClause = publishOnly ? ' AND answerCards.sourceLifecycle = \'publish\'' : '';

      // date filter on global
      let createdAtClause = '';
      if (startDate) {
        createdAtClause = `${createdAtClause} AND answerCards.createdAt >= '${startDate}'`;
      }
      if (endDate) {
        createdAtClause = `${createdAtClause} AND answerCards.createdAt <= '${endDate}'`;
      }

      const globalAnswersQuery = 'SELECT ' +
        'SUM(answer1) as `answer1`, ' +
        'SUM(answer2) as `answer2`, ' +
        'SUM(answer3) as `answer3`, ' +
        'SUM(answer4) as `answer4`, ' +
        'SUM(answer5) as `answer5`, ' +
        'SUM(answer6) as `answer6`, ' +
        'SUM(answer7) as `answer7`, ' +
        'SUM(answer8) as `answer8`, ' +
        'SUM(answer9) as `answer9`, ' +
        'SUM(answer10) as `answer10`, ' +
        'COUNT(*) as `total` ' +
        'FROM answerCards ' +
        'WHERE answerCards.lessonLessonCardId IN (?) ' +
        `${publishClause}` +
        `${createdAtClause}`;

      const allCounts = await db.sequelize.query(
        globalAnswersQuery,
        {
          replacements: [llcIds],
          type: db.sequelize.QueryTypes.SELECT,
          raw: true,
        },
      );

      if (allCounts && allCounts[0]) {
        const allCount = allCounts[0];
        a1TrueAll = allCount.answer1;
        a2TrueAll = allCount.answer2;
        a3TrueAll = allCount.answer3;
        a4TrueAll = allCount.answer4;
        a5TrueAll = allCount.answer5;
        a6TrueAll = allCount.answer6;
        a7TrueAll = allCount.answer7;
        a8TrueAll = allCount.answer8;
        a9TrueAll = allCount.answer9;
        a10TrueAll = allCount.answer10;
        allTotal = allCount.total;
      }
    }
    if ((orgTotal === 0 && !globalOnly) && allTotal === 0) {
      if (isInsightReport) {
        report.push({
          yourOrg: globalOnly ? 0 : calcPercentage(a1TrueOrg, orgTotal),
          allResponses: calcPercentage(a1TrueAll, allTotal),
          orgTotal,
          allTotal,
        });
      } else {
        // nothing to report
        return undefined;
      }
    }
    // order dependant! answer1 @ index 0, answer2 @ index 1, etc...
    if (!_.isNil(lessonCard.list1) || !_.isNil(lessonCard.question1)) {
      report.push({
        yourOrg: globalOnly ? 0 : calcPercentage(a1TrueOrg, orgTotal),
        allResponses: calcPercentage(a1TrueAll, allTotal),
        orgTotal,
        allTotal,
      });
    }

    if (
      (!_.isNil(lessonCard.list1) || !_.isNil(lessonCard.question1)) &&
      (!_.isNil(lessonCard.list2) || !_.isNil(lessonCard.question2))
    ) {
      report.push({
        yourOrg: globalOnly ? 0 : calcPercentage(a2TrueOrg, orgTotal),
        allResponses: calcPercentage(a2TrueAll, allTotal),
        orgTotal,
        allTotal,
      });
    }

    if (
      (!_.isNil(lessonCard.list1) || !_.isNil(lessonCard.question1)) &&
      (!_.isNil(lessonCard.list2) || !_.isNil(lessonCard.question2)) &&
      (!_.isNil(lessonCard.list3))
    ) {
      report.push({
        yourOrg: globalOnly ? 0 : calcPercentage(a3TrueOrg, orgTotal),
        allResponses: calcPercentage(a3TrueAll, allTotal),
        orgTotal,
        allTotal,
      });
    }

    if (
      (!_.isNil(lessonCard.list1) || !_.isNil(lessonCard.question1)) &&
      (!_.isNil(lessonCard.list2) || !_.isNil(lessonCard.question2)) &&
      (!_.isNil(lessonCard.list3)) &&
      (!_.isNil(lessonCard.list4))
    ) {
      report.push({
        yourOrg: globalOnly ? 0 : calcPercentage(a4TrueOrg, orgTotal),
        allResponses: calcPercentage(a4TrueAll, allTotal),
        orgTotal,
        allTotal,
      });
    }

    if (
      (!_.isNil(lessonCard.list1) || !_.isNil(lessonCard.question1)) &&
      (!_.isNil(lessonCard.list2) || !_.isNil(lessonCard.question2)) &&
      (!_.isNil(lessonCard.list3)) &&
      (!_.isNil(lessonCard.list4)) && 
      (!_.isNil(lessonCard.list5))
    ) {
      report.push({
        yourOrg: globalOnly ? 0 : calcPercentage(a5TrueOrg, orgTotal),
        allResponses: calcPercentage(a5TrueAll, allTotal),
        orgTotal,
        allTotal,
      });
    }

    if (
      (!_.isNil(lessonCard.list1) || !_.isNil(lessonCard.question1)) &&
      (!_.isNil(lessonCard.list2) || !_.isNil(lessonCard.question2)) &&
      (!_.isNil(lessonCard.list3)) &&
      (!_.isNil(lessonCard.list4)) && 
      (!_.isNil(lessonCard.list5)) && 
      (!_.isNil(lessonCard.list6))
    ) {
      report.push({
        yourOrg: globalOnly ? 0 : calcPercentage(a6TrueOrg, orgTotal),
        allResponses: calcPercentage(a6TrueAll, allTotal),
        orgTotal,
        allTotal,
      });
    }

    if (
      (!_.isNil(lessonCard.list1) || !_.isNil(lessonCard.question1)) &&
      (!_.isNil(lessonCard.list2) || !_.isNil(lessonCard.question2)) &&
      (!_.isNil(lessonCard.list3)) &&
      (!_.isNil(lessonCard.list4)) && 
      (!_.isNil(lessonCard.list5)) && 
      (!_.isNil(lessonCard.list6)) &&
      (!_.isNil(lessonCard.list7))
    ) {
      report.push({
        yourOrg: globalOnly ? 0 : calcPercentage(a7TrueOrg, orgTotal),
        allResponses: calcPercentage(a7TrueAll, allTotal),
        orgTotal,
        allTotal,
      });
    }

    if (
      (!_.isNil(lessonCard.list1) || !_.isNil(lessonCard.question1)) &&
      (!_.isNil(lessonCard.list2) || !_.isNil(lessonCard.question2)) &&
      (!_.isNil(lessonCard.list3)) &&
      (!_.isNil(lessonCard.list4)) && 
      (!_.isNil(lessonCard.list5)) && 
      (!_.isNil(lessonCard.list6)) &&
      (!_.isNil(lessonCard.list7)) &&
      (!_.isNil(lessonCard.list8))
    ) {
      report.push({
        yourOrg: globalOnly ? 0 : calcPercentage(a8TrueOrg, orgTotal),
        allResponses: calcPercentage(a8TrueAll, allTotal),
        orgTotal,
        allTotal,
      });
    }

    if (
      (!_.isNil(lessonCard.list1) || !_.isNil(lessonCard.question1)) &&
      (!_.isNil(lessonCard.list2) || !_.isNil(lessonCard.question2)) &&
      (!_.isNil(lessonCard.list3)) &&
      (!_.isNil(lessonCard.list4)) && 
      (!_.isNil(lessonCard.list5)) && 
      (!_.isNil(lessonCard.list6)) &&
      (!_.isNil(lessonCard.list7)) &&
      (!_.isNil(lessonCard.list8)) &&
      (!_.isNil(lessonCard.list9))
    ) {
      report.push({
        yourOrg: globalOnly ? 0 : calcPercentage(a9TrueOrg, orgTotal),
        allResponses: calcPercentage(a9TrueAll, allTotal),
        orgTotal,
        allTotal,
      });
    }

    if (
      (!_.isNil(lessonCard.list1) || !_.isNil(lessonCard.question1)) &&
      (!_.isNil(lessonCard.list2) || !_.isNil(lessonCard.question2)) &&
      (!_.isNil(lessonCard.list3)) &&
      (!_.isNil(lessonCard.list4)) && 
      (!_.isNil(lessonCard.list5)) && 
      (!_.isNil(lessonCard.list6)) &&
      (!_.isNil(lessonCard.list7)) &&
      (!_.isNil(lessonCard.list8)) &&
      (!_.isNil(lessonCard.list9)) && 
      (!_.isNil(lessonCard.list10))
    ) {
      report.push({
        yourOrg: globalOnly ? 0 : calcPercentage(a10TrueOrg, orgTotal),
        allResponses: calcPercentage(a10TrueAll, allTotal),
        orgTotal,
        allTotal,
      });
    }
  } catch (err) {
    logger.error(err);
  }
  return report;
}

async function distributionReport(
  user, lessonCard, llcIds, publishOnly,
  campaignId, programId, startDate, endDate,
  globalOnly = false,
) {
  const useGlobalCache = publishOnly && !startDate && !endDate;

  const publishClause = publishOnly ? ' AND answerCards.sourceLifecycle = \'publish\'' : '';
  const report = [];
  const numPoints = lessonCard.list3;
  const orgData = {
    level: 'org',
    data: new Array(numPoints),
    numResponses: 0,
  };
  const allData = {
    level: 'all',
    data: new Array(numPoints),
    numResponses: 0,
  };

  // This is only needed if we're querying on a campaignId or a programId
  const baseQueryIncludeIfNeeded = {
    model: ReportingUsers,
    attributes: [],
    required: true,
    include: [{
      model: ReportingAccounts,
      attributes: [],
      where: {
        id: user.accountId,
      },
    }],
  };

  try {
    if (numPoints > 0) {
      for (let i = 0; i < numPoints; i++) {
        orgData.data[i] = { response: i + 1, perc: '0.0' };
        allData.data[i] = { response: i + 1, perc: '0.0' };
      }
      if (user && !globalOnly) {
        const baseQuery = {
          attributes: [
            'answer1',
            [reportingDb.fn('COUNT', reportingDb.col('answer1')), 'count'],
          ],
          group: ['answer1'],
          where: {
            lessonLessonCardId: {
              [Op.in]: llcIds,
            },
            accountId: user.accountId,
          },
          includeIgnoreAttributes: false, // we need this because it's the only way to eliminate attributes on the through table for acccountUsers
          raw: true,
        };

        if (publishOnly) {
          baseQuery.where = {
            ...baseQuery.where,
            sourceLifecycle: 'publish',
          };
        }

        if (campaignId) {
          baseQuery.include = baseQueryIncludeIfNeeded;
          baseQuery.include.include.push({
            model: ReportingUserLessons,
            required: true,
            attributes: [],
            include: {
              model: ReportingGroupAssignments,
              attributes: [],
              where: {
                campaignId,
              },
            },
            where: {
              enrollmentId: { [Op.col]: 'answerCards.enrollmentId' },
            },
          });
        } else if (programId) {
          baseQuery.include = baseQueryIncludeIfNeeded;
          baseQuery.include.include.push({
            model: ReportingUserLessons,
            required: true,
            attributes: [],
            include: {
              model: ReportingGroupAssignments,
              attributes: [],
              where: {
                programId,
              },
            },
            where: {
              enrollmentId: { [Op.col]: 'answerCards.enrollmentId' },
            },
          });
        }
        // date filter on org
        if (startDate || endDate) {
          if (startDate && endDate) {
            baseQuery.where = {
              ...baseQuery.where,
              createdAt: {
                [Op.and]: [
                  { [Op.gte]: startDate },
                  { [Op.lte]: endDate },
                ],
              },
            };
          } else if (startDate) {
            baseQuery.where = {
              ...baseQuery.where,
              createdAt: {
                [Op.gte]: startDate,
              },
            };
          } else if (endDate) {
            baseQuery.where = {
              ...baseQuery.where,
              createdAt: {
                [Op.lte]: endDate,
              },
            };
          }
        }

        const orgDistribution = await ReportingAnswerCards.findAll(baseQuery);

        const orgTotal = orgDistribution.reduce((acc, val) => { return acc + val.count; }, 0);
        for (const dist of orgDistribution) {
          // Weird bug in the front end on redux forms some where that causes an answer
          // once in a while to go into the wrong field.  This prevents that from killing
          // the results page
          if (dist.answer1) {
            if (typeof orgData.data[dist.answer1 - 1] !== 'undefined') {
              orgData.data[dist.answer1 - 1].perc = calcPercentage(dist.count, orgTotal);
            }
          }
        }
        orgData.numResponses = orgTotal;
      }
      let allTotal = 0;
      if (useGlobalCache) {
        // lesson cards across all lessons for global data
        const summary = await ReportingSummaryLCResults.findOne({
          where: {
            lessonCardId: lessonCard.id,
          },
          raw: true,
        });
        if (summary) {
          const keys = Object.keys(summary).filter(key => key.startsWith('val') && !_.isNil(summary[key]));
          allTotal = summary.total;

          keys.forEach((key) => {
            const index = parseInt(key.substr(3));
            allData.data[index - 1].perc = calcPercentage(summary[key], allTotal);
            allData.data[index - 1].response = index;
          });
        }
      } else {
        let createdAtClause = '';
        if (startDate) {
          createdAtClause = `${createdAtClause} AND answerCards.createdAt >= '${startDate}'`;
        }
        if (endDate) {
          createdAtClause = `${createdAtClause} AND answerCards.createdAt <= '${endDate}'`;
        }

        const globalAnswersQuery = 'SELECT answer1, ' +
          'COUNT(answer1) as `count` ' +
          'FROM answerCards ' +
          'WHERE answerCards.lessonLessonCardId IN (?) ' +
          `${publishClause}` +
          `${createdAtClause}` +
          'GROUP BY answer1';

        const allDistribution = await db.sequelize.query(
          globalAnswersQuery,
          {
            replacements: [llcIds],
            type: db.sequelize.QueryTypes.SELECT,
            raw: true,
          },
        );
        allTotal = allDistribution.reduce((acc, val) => { return acc + val.count; }, 0);
        for (const dist of allDistribution) {
          // Weird bug in the front end on redux forms some where that causes an answer
          // once in a while to go into the wrong field.  This prevents that from killing
          // the results page
          if (dist.answer1) {
            allData.data[dist.answer1 - 1].perc = calcPercentage(dist.count, allTotal);
          }
        }
      }
      allData.numResponses = allTotal;

      report.push(orgData);
      report.push(allData);
    }
  } catch (err) {
    logger.error(err);
  }
  return report;
}

async function percentageReportInsights(
  user, lessonCard, llcIds, publishOnly,
  campaignId, programId, periodCategory, periodId, globalOnly = false, isInsightReport = false,
) {
 
  const report = [];
  const useGlobalCache = publishOnly && periodCategory === 'All Time';

  let a1TrueOrg = 0, a2TrueOrg = 0, a3TrueOrg = 0, a4TrueOrg = 0, a5TrueOrg = 0;
  let a6TrueOrg = 0, a7TrueOrg = 0, a8TrueOrg = 0, a9TrueOrg = 0, a10TrueOrg = 0;
  let a1TrueAll = 0, a2TrueAll = 0, a3TrueAll = 0, a4TrueAll = 0, a5TrueAll = 0;
  let a6TrueAll = 0, a7TrueAll = 0, a8TrueAll = 0, a9TrueAll = 0, a10TrueAll = 0;
  let orgTotal = 0;
  let allTotal = 0;

  try {
    if (useGlobalCache) {
      const summary = await ReportingSummaryLCResults.findOne({
        where: { lessonCardId: lessonCard.id },
        raw: true,
      });

      if (summary) {
        const keys = Object.keys(summary).filter(key => key.startsWith('val') && summary[key]);
        const sums = keys.reduce((acc, key) => ({ ...acc, [key]: summary[key] }), {});
        allTotal = summary.total || 0;
        a1TrueAll = sums.val1 || 0;
        a2TrueAll = sums.val2 || 0;
        a3TrueAll = sums.val3 || 0;
        a4TrueAll = sums.val4 || 0;
        a5TrueAll = sums.val5 || 0;
        a6TrueAll = sums.val6 || 0;
        a7TrueAll = sums.val7 || 0;
        a8TrueAll = sums.val8 || 0;
        a9TrueAll = sums.val9 || 0;
        a10TrueAll = sums.val10 || 0;
      }
    } else {
     
      const whereGlobal = {
        lessonCardId: lessonCard.id,
        myOrgOrGlobal: 'global',
      };

      if (periodId && periodCategory) {
        whereGlobal.periodId = periodId;
        whereGlobal.periodCategory = periodCategory;
      }

      const whereOrg = {
        lessonCardId: lessonCard.id,
        accountId: user.accountId,
        myOrgOrGlobal: 'myOrg',
      };

      if (periodId && periodCategory) {
        whereOrg.periodId = periodId;
        whereOrg.periodCategory = periodCategory;
      }

      // Fetch from insightsData
      const [globalData, orgData] = await Promise.all([
        ReportingInsightsData.findOne({ where: whereGlobal, raw: true }),
        globalOnly ? null : ReportingInsightsData.findOne({ where: whereOrg, raw: true }),
      ]);

      if (globalData) {
        allTotal = globalData.total || 0;
        a1TrueAll = globalData.val1_pct || 0;
        a2TrueAll = globalData.val2_pct || 0;
        a3TrueAll = globalData.val3_pct || 0;
        a4TrueAll = globalData.val4_pct || 0;
        a5TrueAll = globalData.val5_pct || 0;
        a6TrueAll = globalData.val6_pct || 0;
        a7TrueAll = globalData.val7_pct || 0;
        a8TrueAll = globalData.val8_pct || 0;
        a9TrueAll = globalData.val9_pct || 0;
        a10TrueAll = globalData.val10_pct || 0;
      }

      if (orgData) {
        orgTotal = orgData.total || 0;
        a1TrueOrg = orgData.val1_pct || 0;
        a2TrueOrg = orgData.val2_pct || 0;
        a3TrueOrg = orgData.val3_pct || 0;
        a4TrueOrg = orgData.val4_pct || 0;
        a5TrueOrg = orgData.val5_pct || 0;
        a6TrueOrg = orgData.val6_pct || 0;
        a7TrueOrg = orgData.val7_pct || 0;
        a8TrueOrg = orgData.val8_pct || 0;
        a9TrueOrg = orgData.val9_pct || 0;
        a10TrueOrg = orgData.val10_pct || 0;
      }
    }

    if ((orgTotal === 0 && !globalOnly) && allTotal === 0) {
      if (isInsightReport) {
        report.push({
          yourOrg: globalOnly ? 0 : a1TrueOrg,
          allResponses: a1TrueAll,
          orgTotal,
          allTotal,
        });
      } else {
        return undefined;
      }
    }

    const hasValue = (key) => !_.isNil(lessonCard[key]);

    const checkConditions = (i) => {
      if (i === 1) {
        return hasValue('list1') || hasValue('question1');
      }
    
      if (i === 2) {
        return (
          (hasValue('list1') || hasValue('question1')) &&
          (hasValue('list2') || hasValue('question2'))
        );
      }
    
      for (let j = 1; j <= 2; j++) {
        if (!(hasValue(`list${j}`) || hasValue(`question${j}`))) return false;
      }
      for (let j = 3; j <= i; j++) {
        if (!hasValue(`list${j}`)) return false;
      }
      return true;
    };

    for (let i = 1; i <= 10; i++) {
      if (checkConditions(i)) {
        report.push({
          yourOrg: globalOnly
            ? 0
            : useGlobalCache
              ? calcPercentage(eval(`a${i}TrueOrg`), orgTotal)
              : eval(`a${i}TrueOrg`),
        
          allResponses: useGlobalCache
            ? calcPercentage(eval(`a${i}TrueAll`), allTotal)
            : eval(`a${i}TrueAll`),
        
          orgTotal,
          allTotal,
        });
      }
    }
  } catch (err) {
    logger.error(err);
  }
  return report;
}

async function distributionReportInsights(
  user, lessonCard, llcIds, publishOnly,
  campaignId, programId, periodCategory, periodId,
  globalOnly = false,
) {
  const useGlobalCache = publishOnly && periodCategory === 'All Time';
  const report = [];
  const numPoints = lessonCard.list3;

  const orgData = {
    level: 'org',
    data: Array.from({ length: numPoints }, (_, i) => ({ response: i + 1, perc: '0.0' })),
    numResponses: 0,
  };

  const allData = {
    level: 'all',
    data: Array.from({ length: numPoints }, (_, i) => ({ response: i + 1, perc: '0.0' })),
    numResponses: 0,
  };

  try {
    if (numPoints > 0) {
      let allTotal = 0;

      // --- ORG DATA ---
      if (!globalOnly && !useGlobalCache) {
        const whereClause = {
          lessonCardId: lessonCard.id,
          myOrgOrGlobal: 'myOrg',
          accountId: user.accountId,
        };

        if (periodId) {
          whereClause.periodId = periodId;
        }

        if (periodCategory) {
          whereClause.periodCategory = periodCategory;
        }

        const orgRow = await ReportingInsightsData.findOne({ where: whereClause, raw: true });

        if (orgRow) {
          for (let i = 1; i <= numPoints; i++) {
            const pctKey = `val${i}_pct`;
            if (orgData.data[i - 1]) {
              orgData.data[i - 1].perc = parseFloat(orgRow[pctKey] || 0).toFixed(1);
            }
          }
          orgData.numResponses = orgRow.total || 0;
        }
      }

      // --- GLOBAL DATA ---
      if (useGlobalCache) {
        // Use pre-aggregated summary table for global cache
        const summary = await ReportingSummaryLCResults.findOne({
          where: {
            lessonCardId: lessonCard.id,
          },
          raw: true,
        });

        if (summary) {
          const keys = Object.keys(summary).filter(key => key.startsWith('val') && !_.isNil(summary[key]));
          allTotal = summary.total;

          keys.forEach((key) => {
            const index = parseInt(key.substr(3), 10);
            if (allData.data[index - 1]) {
              allData.data[index - 1].perc = calcPercentage(summary[key], allTotal);
              allData.data[index - 1].response = index;
            }
          });
        }
      } else {
        const whereClause = {
          lessonCardId: lessonCard.id,
          myOrgOrGlobal: 'global',
        };

        if (periodId) {
          whereClause.periodId = periodId;
        }

        const globalRow = await ReportingInsightsData.findOne({ where: whereClause, raw: true });

        if (globalRow) {
          for (let i = 1; i <= numPoints; i++) {
            const pctKey = `val${i}_pct`;
            if (allData.data[i - 1]) {
              allData.data[i - 1].perc = parseFloat(globalRow[pctKey] || 0).toFixed(1);
            }
          }
          allData.numResponses = globalRow.total || 0;
        }
      }
      if (!globalOnly) report.push(orgData);
      report.push(allData);
    }
  } catch (err) {
    logger.error(err);
  }

  return report;
}

async function wordCloudReportInsights(user, lessonCard, llcIds, publishOnly, periodCategory, periodId, campaignId, lessonLifecycle) {
  if (lessonLifecycle && lessonLifecycle === "preview") {
    const previewwordCloudReport = await getPreviewAnswerReport(
      user,
      lessonCard,
      lessonCard.id,
      publishOnly
    );
    return {
      globalWords: previewwordCloudReport.globalWords,
      accountWords: previewwordCloudReport.accountWords,
    };
  }

  const numberOfWords = 100;
  const userAccountId = user.accountId;
  const account = await db.accounts.findOne({ where: { id: userAccountId } });

  const minAnswersThreshold = account.responseThreshold || 5;
  const minWordCountThreshold = account.responseThreshold || 5;

  const useGlobalCache = publishOnly && periodCategory === "All Time";

  const accountWordFrequency = new Map();
  const globalWordFrequency = new Map();

  if (useGlobalCache) {
    const summary = await ReportingSummaryLCResults.findOne({
      attributes: ['lessonCardId', 'wordCounts'],
      where: {
        lessonCardId: lessonCard.id,
      },
    });
    summary.wordCounts.forEach((wc) => {
      const glwc = globalWordFrequency.get(wc.t);
      if (glwc === undefined) {
        globalWordFrequency.set(wc.t, wc.v);
      } else {
        globalWordFrequency.set(wc.t, glwc + wc.v);
      }
    });
  } else {
    const orgInsight = await ReportingInsightsData.findOne({
      where: {
        lessonCardId: lessonCard.id,
        myOrgOrGlobal: "myOrg",
        accountId: userAccountId,
        periodId,
        periodCategory,
      },
      raw: true,
    });

    const globalInsight = await ReportingInsightsData.findOne({
      where: {
        lessonCardId: lessonCard.id,
        myOrgOrGlobal: "global",
        periodId,
        periodCategory,
      },
      raw: true,
    });

    if (orgInsight?.wordCounts) {
      let parsed = {};
      if (typeof orgInsight.wordCounts === 'string') {
        try {
          parsed = JSON.parse(orgInsight.wordCounts);
        } catch (e) {
          parsed = {};
        }
      } else {
        parsed = orgInsight.wordCounts;
      }
      for (const [word, count] of Object.entries(parsed)) {
        accountWordFrequency.set(word, count);
      }
    }
    
    if (globalInsight?.wordCounts) {
      let parsed = {};
      if (typeof globalInsight.wordCounts === 'string') {
        try {
          parsed = JSON.parse(globalInsight.wordCounts);
        } catch (e) {
          parsed = {};
        }
      } else {
        parsed = globalInsight.wordCounts;
      }
      for (const [word, count] of Object.entries(parsed)) {
        globalWordFrequency.set(word, count);
      }
    }
  }

  // Check minimum threshold for account responses
  const totalResponses = Array.from(accountWordFrequency.values()).reduce(
    (a, b) => a + b,
    0
  );
  if (totalResponses < minAnswersThreshold) {
    return { globalWords: [], accountWords: [] };
  }

  const globalWordArray = convertFreqObjToArr(
    globalWordFrequency,
    numberOfWords,
    minWordCountThreshold
  );
  const accountWordArray = convertFreqObjToArr(
    accountWordFrequency,
    numberOfWords,
    minWordCountThreshold
  );

  return { globalWords: globalWordArray, accountWords: accountWordArray };
}

function getPreviewDataset(lessonCard) {
  const { cardType, healthyResponse } = lessonCard;

  switch (cardType) {
    case 'quizSlider': {
      let dsidx = SLIDER;
      if (healthyResponse === 'positive') {
        dsidx = SLIDER_POS;
      } else if (healthyResponse === 'negative') {
        dsidx = SLIDER_NEG;
      }
      return previewDataSets[dsidx];
    }
    case 'quizMultiChoice':
      return previewDataSets[MULTI_CHOICE];
    case 'quizBoolean': {
      let dsidx = BOOLEAN;
      if (healthyResponse === 'positive') {
        dsidx = BOOLEAN_POS;
      } else if (healthyResponse === 'negative') {
        dsidx = BOOLEAN_NEG;
      }
      return previewDataSets[dsidx];
    }
    case 'quizSingleChoice': {
      if (lessonCard.list3 === null) {
        let dsidx = SINGLE_CHOICE_2;
        if (healthyResponse === '1') {
          dsidx = SINGLE_CHOICE_2_1;
        } else if (healthyResponse === '2') {
          dsidx = SINGLE_CHOICE_2_2;
        }
        return previewDataSets[dsidx];
      } else if (lessonCard.list4 === null) {
        let dsidx = SINGLE_CHOICE_3;
        if (healthyResponse === '1') {
          dsidx = SINGLE_CHOICE_3_1;
        } else if (healthyResponse === '2') {
          dsidx = SINGLE_CHOICE_3_2;
        } else if (healthyResponse === '3') {
          dsidx = SINGLE_CHOICE_3_3;
        }
        return previewDataSets[dsidx];
      } else if (lessonCard.list5 === null) {
        let dsidx = SINGLE_CHOICE_4;
        if (healthyResponse === '1') {
          dsidx = SINGLE_CHOICE_4_1;
        } else if (healthyResponse === '2') {
          dsidx = SINGLE_CHOICE_4_2;
        } else if (healthyResponse === '3') {
          dsidx = SINGLE_CHOICE_4_3;
        } else if (healthyResponse === '4') {
          dsidx = SINGLE_CHOICE_4_4;
        }
        return previewDataSets[dsidx];
      } else if (lessonCard.list6 === null) {
        let dsidx = SINGLE_CHOICE_5;
        if (healthyResponse === '1') {
          dsidx = SINGLE_CHOICE_5_1;
        } else if (healthyResponse === '2') {
          dsidx = SINGLE_CHOICE_5_2;
        } else if (healthyResponse === '3') {
          dsidx = SINGLE_CHOICE_5_3;
        } else if (healthyResponse === '4') {
          dsidx = SINGLE_CHOICE_5_4;
        } else if (healthyResponse === '5') {
          dsidx = SINGLE_CHOICE_5_5;
        }
        return previewDataSets[dsidx];
      } else if (lessonCard.list7 === null) {
        let dsidx = SINGLE_CHOICE_6;
        if (healthyResponse === '1') {
          dsidx = SINGLE_CHOICE_6_1;
        } else if (healthyResponse === '2') {
          dsidx = SINGLE_CHOICE_6_2;
        } else if (healthyResponse === '3') {
          dsidx = SINGLE_CHOICE_6_3;
        } else if (healthyResponse === '4') {
          dsidx = SINGLE_CHOICE_6_4;
        } else if (healthyResponse === '5') {
          dsidx = SINGLE_CHOICE_6_5;
        } else if (healthyResponse === '6') {
          dsidx = SINGLE_CHOICE_6_6;
        }
        return previewDataSets[dsidx];
      } else if (lessonCard.list8 === null) {
        let dsidx = SINGLE_CHOICE_7;
        if (healthyResponse === '1') {
          dsidx = SINGLE_CHOICE_7_1;
        } else if (healthyResponse === '2') {
          dsidx = SINGLE_CHOICE_7_2;
        } else if (healthyResponse === '3') {
          dsidx = SINGLE_CHOICE_7_3;
        } else if (healthyResponse === '4') {
          dsidx = SINGLE_CHOICE_7_4;
        } else if (healthyResponse === '5') {
          dsidx = SINGLE_CHOICE_7_5;
        } else if (healthyResponse === '6') {
          dsidx = SINGLE_CHOICE_7_6;
        } else if (healthyResponse === '7') {
          dsidx = SINGLE_CHOICE_7_7;
        }
        return previewDataSets[dsidx];
      } else if (lessonCard.list9 === null) {
        let dsidx = SINGLE_CHOICE_8;
        if (healthyResponse === '1') {
          dsidx = SINGLE_CHOICE_8_1;
        } else if (healthyResponse === '2') {
          dsidx = SINGLE_CHOICE_8_2;
        } else if (healthyResponse === '3') {
          dsidx = SINGLE_CHOICE_8_3;
        } else if (healthyResponse === '4') {
          dsidx = SINGLE_CHOICE_8_4;
        } else if (healthyResponse === '5') {
          dsidx = SINGLE_CHOICE_8_5;
        } else if (healthyResponse === '6') {
          dsidx = SINGLE_CHOICE_8_6;
        } else if (healthyResponse === '7') {
          dsidx = SINGLE_CHOICE_8_7;
        } else if (healthyResponse === '8') {
          dsidx = SINGLE_CHOICE_8_8;
        }
        return previewDataSets[dsidx];
      } else if (lessonCard.list10 === null) {
        let dsidx = SINGLE_CHOICE_9;
        if (healthyResponse === '1') {
          dsidx = SINGLE_CHOICE_9_1;
        } else if (healthyResponse === '2') {
          dsidx = SINGLE_CHOICE_9_2;
        } else if (healthyResponse === '3') {
          dsidx = SINGLE_CHOICE_9_3;
        } else if (healthyResponse === '4') {
          dsidx = SINGLE_CHOICE_9_4;
        } else if (healthyResponse === '5') {
          dsidx = SINGLE_CHOICE_9_5;
        } else if (healthyResponse === '6') {
          dsidx = SINGLE_CHOICE_9_6;
        } else if (healthyResponse === '7') {
          dsidx = SINGLE_CHOICE_9_7;
        } else if (healthyResponse === '8') {
          dsidx = SINGLE_CHOICE_9_8;
        } else if (healthyResponse === '9') {
          dsidx = SINGLE_CHOICE_9_9;
        }
        return previewDataSets[dsidx];
      }
      let dsidx = SINGLE_CHOICE_10;
      if (healthyResponse === '1') {
        dsidx = SINGLE_CHOICE_10_1;
      } else if (healthyResponse === '2') {
        dsidx = SINGLE_CHOICE_10_2;
      } else if (healthyResponse === '3') {
        dsidx = SINGLE_CHOICE_10_3;
      } else if (healthyResponse === '4') {
        dsidx = SINGLE_CHOICE_10_4;
      } else if (healthyResponse === '5') {
        dsidx = SINGLE_CHOICE_10_5;
      } else if (healthyResponse === '6') {
        dsidx = SINGLE_CHOICE_10_6;
      } else if (healthyResponse === '7') {
        dsidx = SINGLE_CHOICE_10_7;
      } else if (healthyResponse === '8') {
        dsidx = SINGLE_CHOICE_10_8;
      } else if (healthyResponse === '9') {
        dsidx = SINGLE_CHOICE_10_4;
      } else if (healthyResponse === '10') {
        dsidx = SINGLE_CHOICE_10_10;
      }
      return previewDataSets[dsidx];
    }
    case 'quizColorSpectrum':
      if (lessonCard.list4 === '1') {
        return previewDataSets[COLOR_SPECTRUM_GREEN];
      } else if (lessonCard.list4 === '2') {
        return previewDataSets[COLOR_SPECTRUM_YELLOW];
      } else if (lessonCard.list4 === '3') {
        return previewDataSets[COLOR_SPECTRUM_ORANGE];
      } else if (lessonCard.list4 === '4') {
        return previewDataSets[COLOR_SPECTRUM_RED];
      }
      return previewDataSets[COLOR_SPECTRUM];
    default:
      logger.warn(`Can't find preview data set for lessonCard: ${lessonCard.id}`);
      return [];
  }
}

const MIN_GLOBAL_RESPONSES = 94;
const MIN_ORG_RESPONSES = 23;
const ORG_FRACTION_OF_GLOBAL = 0.2;
async function getPreviewAnswerReport(
  user, lessonCard, lessonId,
  publishOnly, campaignId, programId, startDate, endDate,
) {
  let report;
  const cardType = lessonCard.cardType;
  const globalOnly = true;
  const previewOrgData = getPreviewDataset(lessonCard);

  // include lessonCard across all lessons
  const llcs = await ReportingLessonLessonCards.findAll({
    where: {
      lessonCardId: lessonCard.id,
    },
  });
  const llcIds = llcs.map(llc => llc.id);

  if (cardType === 'quizBoolean' || cardType === 'quizSingleChoice' || cardType === 'quizMultiChoice') {
    report = await percentageReport(
      user, lessonCard, llcIds, publishOnly,
      campaignId, programId, startDate, endDate,
      globalOnly,
    );

    for (let i = 0; i < report.length; i++) {
      const dataPoint = report[i];
      if (dataPoint.allTotal < 100) {
        dataPoint.allTotal = MIN_GLOBAL_RESPONSES;
        dataPoint.allResponses = previewOrgData[i];
        dataPoint.orgTotal = MIN_ORG_RESPONSES;
      } else {
        // org response count is 20% of global
        dataPoint.orgTotal = Math.floor(dataPoint.allTotal * ORG_FRACTION_OF_GLOBAL);
      }
      dataPoint.yourOrg = previewOrgData[i];
    }
  } else if (cardType === 'quizSlider' || cardType === 'quizColorSpectrum') {
    report = await distributionReport(
      user, lessonCard, llcIds, publishOnly,
      campaignId, programId, startDate, endDate,
      globalOnly,
    );
    const orgData = report[0];
    const globalData = report[1];
    // global
    if (globalData.numResponses < 100) {
      // fake response counts
      globalData.numResponses = MIN_GLOBAL_RESPONSES;
      // use preview data for global
      for (let i = 0; i < globalData.data.length; i++) {
        const dataPoint = globalData.data[i];
        dataPoint.perc = previewOrgData[i];
      }
    }
    // org
    if (globalData.numResponses < 100) {
      orgData.numResponses = MIN_ORG_RESPONSES;
    } else {
      // org response count is 20% of global
      orgData.numResponses = Math.floor(globalData.numResponses * ORG_FRACTION_OF_GLOBAL);
    }
    // use preview data for org
    for (let i = 0; i < orgData.data.length; i++) {
      const dataPoint = orgData.data[i];
      dataPoint.perc = previewOrgData[i];
    }
  } else if (cardType === 'quizFreeformText') {
    const numberOfWords = 100; // number of words to display in the wordcloud
    const minWordCoundThreshold = 23; // number of occurences of a word required before it can be displayed
    const orgWordCount = countWords(previewOrgTextData);
    const globalWordCount = countWords(previewGlobalTextData);

    // randomize word counts
    for (const key of orgWordCount.keys()) {
      let value = orgWordCount.get(key);
      if (value >= 2) {
        value = getRandomInt(94);
        orgWordCount.set(key, value);
      }
    }
    for (const key of globalWordCount.keys()) {
      let value = globalWordCount.get(key);
      if (value >= 2) {
        value = getRandomInt(94);
        globalWordCount.set(key, value);
      }
    }

    const globalWordArray = convertFreqObjToArr(globalWordCount, numberOfWords, minWordCoundThreshold);
    const accontWordArray = convertFreqObjToArr(orgWordCount, numberOfWords, minWordCoundThreshold);
    return { globalWords: globalWordArray, accountWords: accontWordArray };
  }
  return report;
}

async function getAnswerReport(
  user, lessonCard, lessonId,
  publishOnly, campaignId, programId, startDate, endDate, lessonLifecycle, isInsightReport = false,
) {
  let report;
  // include lessonCard across all lessons
  const llcs = await ReportingLessonLessonCards.findAll({
    where: {
      lessonCardId: lessonCard.id,
    },
  });
  const llcIds = llcs.map(llc => llc.id);

  if (!_.isNil(llcIds)) {
    if (lessonCard.cardType === 'quizBoolean' ||
      lessonCard.cardType === 'quizSingleChoice' ||
      lessonCard.cardType === 'quizMultiChoice') {
      if (lessonLifecycle && lessonLifecycle === 'preview') {
        const previewOrgData = getPreviewDataset(lessonCard);
        report = await percentageReport(
          user, lessonCard, llcIds, publishOnly,
          campaignId, programId, startDate, endDate,
          true, isInsightReport,
        );
        for (let i = 0; i < report.length; i++) {
          const dataPoint = report[i];
          if (dataPoint.allTotal < 100) {
            dataPoint.allTotal = MIN_GLOBAL_RESPONSES;
            dataPoint.allResponses = previewOrgData[i];
            dataPoint.orgTotal = MIN_ORG_RESPONSES;
          } else {
            // org response count is 20% of global
            dataPoint.orgTotal = Math.floor(dataPoint.allTotal * ORG_FRACTION_OF_GLOBAL);
          }
          dataPoint.yourOrg = previewOrgData[i];
        }
      } else {
        report = await percentageReport(
          user, lessonCard, llcIds, publishOnly,
          campaignId, programId, startDate, endDate, false, isInsightReport,
        );
      }
    } else if (lessonCard.cardType === 'quizSlider' ||
      lessonCard.cardType === 'quizColorSpectrum') {
      if (lessonLifecycle && lessonLifecycle === 'preview') {
        const previewOrgData = getPreviewDataset(lessonCard);
        report = await distributionReport(
          user, lessonCard, llcIds, publishOnly,
          campaignId, programId, startDate, endDate,
          true,
        );
        const orgData = report[0];
        const globalData = report[1];
        // global
        if (globalData.numResponses < 100) {
          // fake response counts
          globalData.numResponses = MIN_GLOBAL_RESPONSES;
          // use preview data for global
          for (let i = 0; i < globalData.data.length; i++) {
            const dataPoint = globalData.data[i];
            dataPoint.perc = previewOrgData[i];
          }
        }
        if (globalData.numResponses < 100) {
          orgData.numResponses = MIN_ORG_RESPONSES;
        } else {
          // org response count is 20% of global
          orgData.numResponses = Math.floor(globalData.numResponses * ORG_FRACTION_OF_GLOBAL);
        }
        // use preview data for org
        for (let i = 0; i < orgData.data.length; i++) {
          const dataPoint = orgData.data[i];
          dataPoint.perc = previewOrgData[i];
        }
      } else {
        report = await distributionReport(
          user, lessonCard, llcIds, publishOnly,
          campaignId, programId, startDate, endDate,
        );
      }
    } else if (lessonCard.cardType === 'quizFreeformText') {
      report = await wordCloudReport(user, lessonCard, llcIds, publishOnly, startDate, endDate, campaignId, lessonLifecycle);
    }
  }
  return report;
}

async function getAnswerReportInsights(
  user, lessonCard, lessonId,
  publishOnly, campaignId, programId, periodCategory, periodId, lessonLifecycle, isInsightReport = false,
) {
  let report;
  // include lessonCard across all lessons
  const llcs = await ReportingLessonLessonCards.findAll({
    where: {
      lessonCardId: lessonCard.id,
    },
  });
  const llcIds = llcs.map(llc => llc.id);

  if (periodCategory) {
    periodCategory = decodeURIComponent(periodCategory);
  }
  
  if (!_.isNil(llcIds)) {
    if (lessonCard.cardType === 'quizBoolean' ||
      lessonCard.cardType === 'quizSingleChoice' ||
      lessonCard.cardType === 'quizMultiChoice') {
      if (lessonLifecycle && lessonLifecycle === 'preview') {
        const previewOrgData = getPreviewDataset(lessonCard);
        report = await percentageReportInsights(
          user, lessonCard, llcIds, publishOnly,
          campaignId, programId, periodCategory, periodId,
          true, isInsightReport,
        );
        for (let i = 0; i < report.length; i++) {
          const dataPoint = report[i];
          if (dataPoint.allTotal < 100) {
            dataPoint.allTotal = MIN_GLOBAL_RESPONSES;
            dataPoint.allResponses = previewOrgData[i];
            dataPoint.orgTotal = MIN_ORG_RESPONSES;
          } else {
            // org response count is 20% of global
            dataPoint.orgTotal = Math.floor(dataPoint.allTotal * ORG_FRACTION_OF_GLOBAL);
          }
          dataPoint.yourOrg = previewOrgData[i];
        }
      } else {
        report = await percentageReportInsights(
          user, lessonCard, llcIds, publishOnly,
          campaignId, programId, periodCategory, periodId, false, isInsightReport,
        );
      }
    } else if (lessonCard.cardType === 'quizSlider' ||
      lessonCard.cardType === 'quizColorSpectrum') {
      if (lessonLifecycle && lessonLifecycle === 'preview') {
        const previewOrgData = getPreviewDataset(lessonCard);
        report = await distributionReportInsights(
          user, lessonCard, llcIds, publishOnly,
          campaignId, programId, periodCategory, periodId,
          true,
        );
        const orgData = report[0];
        const globalData = report[1];
        // global
        if (globalData.numResponses < 100) {
          // fake response counts
          globalData.numResponses = MIN_GLOBAL_RESPONSES;
          // use preview data for global
          for (let i = 0; i < globalData.data.length; i++) {
            const dataPoint = globalData.data[i];
            dataPoint.perc = previewOrgData[i];
          }
        }
        if (globalData.numResponses < 100) {
          orgData.numResponses = MIN_ORG_RESPONSES;
        } else {
          // org response count is 20% of global
          orgData.numResponses = Math.floor(globalData.numResponses * ORG_FRACTION_OF_GLOBAL);
        }
        // use preview data for org
        for (let i = 0; i < orgData.data.length; i++) {
          const dataPoint = orgData.data[i];
          dataPoint.perc = previewOrgData[i];
        }
      } else {
        report = await distributionReportInsights(
          user, lessonCard, llcIds, publishOnly,
          campaignId, programId, periodCategory, periodId,
        );
      }
    } else if (lessonCard.cardType === 'quizFreeformText') {
      report = await wordCloudReportInsights(user, lessonCard, llcIds, publishOnly, periodCategory, periodId, campaignId, lessonLifecycle);
    }
  }
  return report;
}

// find and move answerCard from it binding to the legacy location off of lessonCard
// if there is enough info to identify answerCard, will return non-null value
const findAnswerCard = (lessonId, lessonCard, enrollmentId = 1) => {
  let answerCard;
  if (!lessonCard || !lessonCard.bindings) {
    return answerCard;
  }
  if (lessonId) {
    // find binding for Lesson
    for (const binding of lessonCard.bindings) {
      let theACs = [];
      // find answerCard for enrollmentId
      if (binding.lessonId === lessonId) {
        theACs = binding.answerCards.filter(ac => ac.enrollmentId === enrollmentId);
      }
      if (theACs.length > 0) {
        answerCard = theACs[0];
      }
    }
  } else {
    // attempt to find answer card
    // if lessonCard belongs to only one lesson AND there's only one answer card, then use it, otherwise return null
    const answerCardBindings = lessonCard.bindings.filter(binding => binding.answerCards.length === 1);
    if (answerCardBindings.length === 1) {
      answerCard = answerCardBindings[0].answerCards[0];
    }
  }
  return answerCard;
};

// Add a hasAnswers flag to the lesson card data returned to the client.
// The flag is set to TRUE if the lesson card has any saved answers in publish or closed mode.
// The flag is used by the Lesson Authoring front-end.
async function includeHasAnswer(lessonCard, lessonId = undefined) {
  let answerCount = 0;
  // find all lessonLessonCard bindings with lesson card and lessons
  const llcQuery = {
    attributes: ['id'],
    where: {
      lessonCardId: lessonCard.id,
    },
  };
  if (lessonId) {
    llcQuery.where.lessonId = lessonId;
  }
  const LLCs = await ReportingLessonLessonCards.findAll(llcQuery);
  const LLCIDs = LLCs.map(LLC => LLC.id);

  // see if there are one or more answerCards associated with lessonLessonCard bindings
  answerCount = await ReportingAnswerCards.findOne({
    attributes: ['id'],
    where: {
      lessonLessonCardId: {
        [Op.in]: LLCIDs,
      },
      sourceLifecycle: {
        [Op.or]: ['publish', 'close'],
      },
    },
  });
  const hasAnswers = !!answerCount;
  const retVal = _.merge({}, lessonCard, { hasAnswers });
  return retVal;
}

async function includeAnswer(
  req, lessonCard, lessonId,
  campaignId = null, programId = null, startDate = null, endDate = null, lessonLifecycle = null,
) {
  if (lessonCard.answerCard || req.user) {
    const results = await getAnswerReport(
      req.user,
      lessonCard,
      lessonId,
      req.publishOnly,
      campaignId,
      programId,
      startDate,
      endDate,
      lessonLifecycle,
    );
    if (lessonCard.answerCard) {
      Object.assign(lessonCard.answerCard, { results });
    }
    if (req.user) {
      Object.assign(lessonCard, { results });
    }
  }
  return lessonCard;
}

async function includeAnswerReport(req, answerCard, lessonId, lessonCardId, campaignId = null) {
  let retVal = answerCard;
  if (answerCard) {
    const lessonCard = await LessonCards.findByPk(lessonCardId);
    let results;
    if (answerCard.sourceLifecycle === 'preview') {
      results = await getPreviewAnswerReport(
        req.user,
        lessonCard,
        lessonId,
        req.publishOnly,
        campaignId,
      );
    } else {
      results = await getAnswerReport(
        req.user,
        lessonCard,
        lessonId,
        req.publishOnly,
        campaignId,
      );
    }
    retVal = Object.assign(answerCard, { results });
  }
  return retVal;
}

module.exports = {
  convertFreqObjToArr,
  findAnswerCard,
  includeAnswer,
  getAnswerReport,
  includeAnswerReport,
  includeHasAnswer,
  prepStringForWordCloud,
  getAnswerReportInsights,
};
