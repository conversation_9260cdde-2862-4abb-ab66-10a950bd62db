/* eslint-disable max-len */
const _ = require('lodash');
const moment = require('moment');
const csvtojson = require('csvtojson');
const fs = require('fs');
const { S3Client, GetObjectCommand } = require("@aws-sdk/client-s3");
const ES = require('event-stream');
const { v4: uuidv4 } = require('uuid');
const logger = require('../../logger');
const db = require('../../db');
const config = require('../../config/config');
const { NotFoundError } = require('../../services/utils/errors');
const { getCsvS3Bucket, listFiles, moveFile,
  removeFileByPath, pgpDecryptFile } = require('../../services/utils/fileUtils');
const { processCampaignsForUserComingOffLeave } = require('../../services/utils/campaignUtils');
const { propagateUserDeactivation, getValidUserStatus, updateEmtrainRoles } = require('../../services/utils/userUtils');
const { isValidDate, isValidNumber, isValidAttritionDate, formatDateToYYYYMMDD, isValidBoolean, booleanToNumeric } = require('../utils/jsUtils');
const { getValidStateCode, getValidCountryCode } = require('../utils/iso3166Utils');
const { isValidEmailAddress } = require('../email');
const { format, differenceInMinutes, differenceInMilliseconds } = require('date-fns');
const { addUserToAccountRoster } = require('../utils/groupUtils');
const { currentlySupportedLanguages } = require('../i18n/i18n');

const CsvColumns = db.csvColumns;
const CsvErrors = db.csvErrors;
const Users = db.users;
const Accounts = db.accounts;
const AccountUsers = db.accountUsers;
const Roles = db.roles;
const UserRoles = db.userRoles;
const AccountFields = db.accountFields;
const UserAccountFields = db.userAccountFields;
const CsvImports = db.csvImports;
const CsvSettings = db.csvSettings;
const Op = db.Sequelize.Op;

const s3Client = new S3Client({
  region: "us-east-1",
  credentials: {
    accessKeyId: config.s3.accessKeyId,
    secretAccessKey: config.s3.secretAccessKey,
  },
});


/* eslint-disable no-param-reassign */

const isLocalTestMode = () => {
  return (process.env.NODE_ENV === 'test' && (!config.s3.secretAccessKey || !config.s3.accessKeyId));
};

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

const convertToBoolean = (s) => {
  if (!s) {
    return false;
  }
  const upper = s.toUpperCase();
  if (upper === 'Y' || upper === 'YES' || upper === 'T' || upper === 'TRUE' || upper === '1') {
    return true;
  }
  return false;
};

const convertToYesNo = (s) => {
  if (convertToBoolean(s)) {
    return 'Yes';
  }
  return 'No';
};

// eslint-disable-next-line no-unused-vars
const getReadStream = async (filePath, accountId, fileLocation) => {

  if (isLocalTestMode()) {
    return fs.createReadStream(filePath);
  }
  // For now, files in the SFTP location will have already been moved to csv-uploads.
  // const subFolder = fileLocation === 'S3' ? 'csv-uploads' : 'csv-sftp';
  const subFolder = "csv-uploads";
  const { bucket, keyPrefix } = getCsvS3Bucket(accountId, subFolder);
  const fileName = filePath.replace(/^.*[\\/]/, '');
  const params = {
    Bucket: bucket,
    Key: `${keyPrefix}${fileName}`,
  };
  try {
    const command = new GetObjectCommand(params);
    const response = await s3Client.send(command);

    if (!response.Body) {
      throw new Error('Failed to get object stream');
    }
    return response.Body;
  } catch (error) {
    logger.error('Error getting S3 read stream: %j', error);
    throw error;
  }
};

const updateImportStatus = async (workingData, csvImportData) => {
  if (workingData.row % 5 === 0) {
    await csvImportData.update({
      recordsProcessed: workingData.row,
      updatedUserCount: workingData.updatedUsers,
      newUserCount: workingData.newUsers,
      ignoredUserCount: workingData.ignoredUsers,
    });
  }
};

const updateOrCreateCustomFields = async (customFields, accountId, user, csvImportData, workingData, data, csvSetting) => {
  for (const cf of Object.keys(customFields)) {
    try {
      /* eslint-disable no-await-in-loop */
      const accountField = await AccountFields.findOne({
        where: {
          fieldName: cf,
          accountId,
        },
      });
      if (accountField) {
        // validate custom field value for types
        if (customFields[cf] !== null && accountField.fieldType === 'date'
          && !isValidDate(customFields[cf])) {
          const error = new Error(`<b>${cf}</b> should be formatted as YYYY-MM-DD. ${customFields[cf]} is not valid for this field.`);
          error.status = 400;
          throw error;
        } else if (customFields[cf] !== null && accountField.fieldType === 'number'
          && !isValidNumber(customFields[cf])) {
          const error = new Error(`<b>${cf}</b> must be a number such as 0, 1, 1.5, or 100. ${customFields[cf]} is not valid for this field.`);
          error.status = 400;
          throw error;
        } else if (customFields[cf] !== null && accountField.fieldType === 'email'
          && !isValidEmailAddress(customFields[cf])) {
          const error = new Error(`<b>${cf}</b> must be <NAME_EMAIL>, and may contain ! $ & * - = \` ' | ~ # + / ? _ { }. ${customFields[cf]} is not valid for this field.`);
          error.status = 400;
          throw error;
        }
        const userCF = await UserAccountFields.findOne({
          where: {
            userId: user.id,
            accountFieldId: accountField.id,
          },
        });

        const fieldToUpdate = customFields[cf];

        // update or create user's cf value
        if (userCF) {
          await userCF.update({ value: fieldToUpdate });
        } else {
          await UserAccountFields.create({
            userId: user.id,
            accountFieldId: accountField.id,
            value: fieldToUpdate,
          });
        }
      }
    } catch (err) {
      logger.error('** Error in custom field: %j', err);
      const msg = (err && err.errors && err.errors[0] && err.errors[0].message) || (err && err.message);
      logger.error('custom field data is :%j', customFields);
      await CsvErrors.create({
        csvImportId: csvImportData.id,
        row: csvSetting.firstRowColumnNames ? workingData.row + 1 : workingData.row,
        message: `${msg}`,
      });
    }
  }
};

const getManagerUser = async (csvImportData, data, accountId, workingData, csvSetting) => {
  let managerUser;
  if (data.managerEmail) { // this field holds the manager's email right now
    const managerEmail = data.managerEmail.toLowerCase();
    managerUser = await Users.findOne({
      where: {
        email: managerEmail,
      },
      include: [{
        model: Accounts,
        where: {
          id: accountId,
        },
      }],
    });
  }
  return managerUser;
};

const getUser = async (syncField, standardData, accountId) => {
  let user = null;

  if (syncField === 'email') {
    user = await Users.findOne({
      where: {
        email: standardData.email.toLowerCase(),
      },
      include: [{
        model: Accounts,
        where: {
          id: accountId,
        },
      }],
      paranoid: false,
    });
  } else if (syncField === 'scormId') {
    // Find user by scormId if CSV contain a scormId.
    if (standardData.scormId) {
      user = await Users.findOne({
        where: {
          scormId: standardData.scormId,
        },
        include: [{
          model: Accounts,
          where: {
            id: accountId,
          },
        }],
        paranoid: false,
      });
    }
  } else {
    user = await Users.findOne({
      where: {
        employeeId: standardData.employeeId,
      },
      include: [{
        model: Accounts,
        where: {
          id: accountId,
        },
      }],
      paranoid: false,
    });
  }
  return user;
};

const validateBoolean = (supervisor) => {
  let isValid = false;
  supervisor = supervisor.toLowerCase();
  const acceptedValues = ['yes', 'no', 'y', 'n', '1', '0', 'true', 'false', 't', 'f'];
  if (acceptedValues.includes(supervisor)) {
    isValid = true;
  }
  return isValid;
};

// Get all scorm users for given data
const getAllScormUsers = async (standardData, accountId) => {
  let users = null;
  users = await Users.findAll({
    where: {
      scormId: standardData.scormId,
    },
    include: [
      {
        model: Accounts,
        where: {
          id: accountId,
        },
      },
    ],
    paranoid: false,
  });
  return users;
};

/*
* If multiple users with the same scormId AND scormId is formatted as an email address,
* the first user created with that scormId will have users.email set to the same value as scormId,
* and the rest of the users with that scormId will have users.email = NULL.
*/
const updateScormUsersEmail = async (standardData, accountId) => {
  const scormUsers = await getAllScormUsers(standardData, accountId);
  scormUsers.forEach(async (userData, index) => {
    if (index === 0 && userData.email !== userData.scormId) {
      await Users.update({
        email: userData.scormId,
      }, {
        where: {
          id: userData.id,
        },
      });
    } else if (index !== 0 && userData.email === userData.scormId) {
      await Users.update({
        email: null,
      }, {
        where: {
          id: userData.id,
        },
      });
    }
  });
};

//  update user attributes
const overrideUser = async (user, localStart, managerUser, data, standardData, accountId, customFields, csvImportData, workingData, syncField, csvSetting) => {
  logger.info(`>>>>> identified existing user id= ${user.id}, elapsed = ${differenceInMilliseconds(new Date(), localStart)}ms.`);
  if (managerUser) {
    standardData.managerEmail = managerUser.email;
  } else if(data.managerEmail) {
    standardData.managerEmail = data.managerEmail;
  }
  if (data.status === 'onLeave') {
    standardData.leaveStartedAt = Date.now();
  } else if (data.status && data.status !== 'onLeave' && user.leaveStartedAt) {
    processCampaignsForUserComingOffLeave(user.id, user.leaveStartedAt);
    standardData.leaveStartedAt = null;
  }

  if (syncField === 'scormId' && standardData) {
    delete standardData.email;
    delete standardData.scormId;
    delete standardData.employeeId;
  }

  await user.update(standardData, { accountId });

  if (data.status === 'active' && user.deletedAt) {
    await user.restore();
    await addUserToAccountRoster(accountId, user.id);
  } else if (data.status === 'onLeave' && user.deletedAt) {
    await user.restore();
    await addUserToAccountRoster(accountId, user.id);
  } else if (data.status === 'inactive' && !user.deletedAt) {
    await propagateUserDeactivation(user.id);
    await user.destroy();
  }
  // Update custom fields
  logger.debug(`>>>>> user updated, updating custom fields, elapsed = ${differenceInMilliseconds(new Date(), localStart)}ms.`);
  await updateOrCreateCustomFields(customFields, accountId, user, csvImportData, workingData, data, csvSetting);
};

const processRow = async ({
  csvImportData,
  csvSetting,
  data,
  syncField,
  accountId,
  workingData,
  standardFields,
  userRoleId,
}) => {
  const localStart = new Date();
  logger.info(`>>>>> Entering processRow #${workingData.row}`);
  if ( (!data.firstName || !data.firstName.length) && syncField != 'scormId' ) {
    logger.error('Missing First Name in record: %j', data);
    const message = '<b>First Name</b> is required for all Users.';
    throw new Error(message);
  }
  if ( (!data.lastName || !data.lastName.length) && syncField != 'scormId') {
    logger.error('Missing Last Name in record: %j', data);
    const message = '<b>Last Name</b> is required for all Users.';
    throw new Error(message);
  }
  if ((syncField === 'email' && (!data.email || !data.email.length))) {
    logger.error('Missing Email Address in record: %j', data);
    const message = '<b>Email Address</b> is required for all Users.';
    throw new Error(message);
  }
  if ((syncField === 'employeeId' && (!data.employeeId || !data.employeeId.length))) {
    logger.error('Missing EmployeeId in record: %j', data);
    const message = '<b>Employee ID</b> is required for all Users.';
    throw new Error(message);
  }
  // check if hire date is valid before doing any calculations with it.
  if (data.hireDate && !isValidDate(data.hireDate)) {
    logger.error(`Hire date must be a valid date: ${data.hireDate}`);
    const message = `<b>Hire Date</b> must be formatted as YYYY-MM-DD. ${data.hireDate} is not valid for this field.`;
    throw new Error(message);
  }
  // check if managerEmail is valid
  if (data.managerEmail && !isValidEmailAddress(data.managerEmail)) {
    logger.error(`Manager Email must be a valid email: ${data.managerEmail}`);
    const validEmailChars = "! $ & * - = ` ' | ~ # + / ? _ { }$";
    const message = `<b>Manager Email</b> must be <NAME_EMAIL>, and may contain ${validEmailChars}. ${data.managerEmail} is not valid for this field.`;
    throw new Error(message);
  }
  // check if notificationEmail is valid
  if (data.notificationEmail && !isValidEmailAddress(data.notificationEmail)) {
    logger.error(`Nofitication Email must be a valid email: ${data.notificationEmail}`);
    const validEmailChars = "! $ & * - = ` ' | ~ # + / ? _ { }$";
    const message = `<b>Notification Email</b> must be <NAME_EMAIL>, and may contain ${validEmailChars}. ${data.notificationEmail} is not valid for this field.`;
    throw new Error(message);
  }
  if (data.remote && !isValidBoolean(data.remote)) {
    logger.error(`Remote? should be Yes or No: ${data.remote}`);
    const message = `<b>Remote?</b> should be Yes or No. ${data.remote} is not valid for this field.`;
    throw new Error(message);
  } else {
    data.remote = booleanToNumeric(data.remote);
  }
  if (data.attritionDate && !isValidAttritionDate(data.attritionDate)) {
    logger.error(`Attrition Date must be a valid date: ${data.attritionDate}`);
    const message = `<b>Attrition Date</b> should be formatted as YYYY-MM-DD. ${data.attritionDate} is not valid for this field.`;
    throw new Error(message);
  } else {
    data.attritionDate = formatDateToYYYYMMDD(data.attritionDate);
  }
  if (data.voluntaryAttrition && !isValidBoolean(data.voluntaryAttrition)) {
    logger.error(`Voluntary Attrition? should be Yes or No: ${data.voluntaryAttrition}`);
    const message = `<b>Voluntary Attrition?</b> should be Yes or No. ${data.voluntaryAttrition} is not valid for this field.`;
    throw new Error(message);
  } else {
    data.voluntaryAttrition = booleanToNumeric(data.voluntaryAttrition);
  }
  if (data.stateCode) {
    const validStateCode = getValidStateCode(data.stateCode);
    if (validStateCode) {
      data.stateCode = validStateCode;
    } else {
      const twoStateCode = 'https://pe.usps.com/text/pub28/28apb.htm';
      const message = `<b>US State Code</b> must be formatted as a standard <a href="${twoStateCode}"> 2-letter state code</a> such as CA, NY, or DC. ${data.stateCode} is not valid for this field.`;
      logger.error(message);
      throw new Error(message);
    }
  }
  if (data.countryCode) {
    const validCountryCode = getValidCountryCode(data.countryCode);
    if (validCountryCode) {
      data.countryCode = validCountryCode;
    } else {
      const countryCodesLink = 'https://en.wikipedia.org/wiki/ISO_3166-1_alpha-3#Officially_assigned_code_elements';
      const message = `<b>Country Code</b> must be formatted as a standard <a href="${countryCodesLink}">3-letter country code</a> such as USA, CAN, or GBR. ${data.countryCode} is not valid for this field`;
      logger.error(message);
      throw new Error(message);
    }
  }
  if (data.status) {
    const userStatusData = getValidUserStatus(data.status);
    if (userStatusData.isValid) {
      data.status = userStatusData.status;
      if (data.status === 'onLeave') {
        data.leaveStartedAt = Date.now();
      }
    } else {
      const message = `<b>User Status</b> must be Active, Inactive or On Leave. ${data.status} is not valid for this field.`;
      logger.error(message);
      throw new Error(message);
    }
  }
  if (data.language) {
    data.language = data.language.toLowerCase();
    if (!currentlySupportedLanguages.includes(data.language)) {
      const languageLink = 'https://answers-support.emtrain.com/hc/en-us/articles/*************';
      const message = `<b>Language</b> must be a <a href="${languageLink}">standard ISO code for a language supported by the platform</a>. ${data.language} is not valid for this field.`;
      logger.error(message);
      throw new Error(message);
    }
  }
  logger.debug(`>>>>> Data valid, getting manager, elapsed = ${differenceInMilliseconds(new Date(), localStart)}ms.`);
  // see if the manager exists in the account
  const managerUser = await getManagerUser(csvImportData, data, accountId, workingData, csvSetting);
  // Transform boolean fields
  if (data.exempt) {
    if (validateBoolean(data.exempt)) {
      data.exempt = convertToBoolean(data.exempt); // eslint-disable-line no-param-reassign
    } else {
      const message = `<b>Exempt</b> should be Yes or No. ${data.exempt} is not valid for this field.`;
      logger.error(message);
      throw new Error(message);
    }
  } else if (data.exempt === '') {
    data.exempt = null;
  }

  if (data.supervisor) {
    if (validateBoolean(data.supervisor)) {
      data.supervisor = convertToYesNo(data.supervisor);
    } else {
      const message = `<b>Supervisor?</b> should be Yes or No. ${data.supervisor} is not valid for this field.`;
      logger.error(message);
      throw new Error(message);
    }
  } else if (data.supervisor === '') {
    data.supervisor = null;
  }
  if (data.status === 'onLeave') {
    data.leaveStartedAt = Date.now(); // eslint-disable-line no-param-reassign
  }
  const customFields = {};
  const standardData = Object.keys(data).reduce((acc, fieldName) => {
    if (fieldName === 'status') {
      return acc;
    }
    if (!standardFields.includes(fieldName)) {
      customFields[fieldName] = data[fieldName];
      return acc;
    }
    return { ...acc, [fieldName]: data[fieldName] };
  }, {});
  let scormIdIsValidEmailAddress = false;
  if (syncField === 'scormId') {
    // Check scormId for email address
    scormIdIsValidEmailAddress = isValidEmailAddress(data.scormId);
  }

  logger.debug(`>>>>> got manager, getting user, elapsed = ${differenceInMilliseconds(new Date(), localStart)}ms.`);
  // Check for existing user in the account and create if not there.
  const user = await getUser(syncField, standardData, accountId);
  if (user) {
    if (syncField === 'scormId' && scormIdIsValidEmailAddress) {
      await updateScormUsersEmail(standardData, accountId);
    }
    if (csvSetting.overrideCurrent) {
      if (syncField === 'scormId') {
        workingData.updatedUsers = workingData.updatedUsers !== 0 ? workingData.updatedUsers : 0;
        const scormUsers = await getAllScormUsers(standardData, accountId);
        scormUsers.forEach(async (userData) => {
          await overrideUser(userData, localStart, managerUser, data, standardData, accountId, customFields, csvImportData, workingData, syncField, csvSetting);
        });
        workingData.updatedUsers += scormUsers.length;
      } else {
        await overrideUser(user, localStart, managerUser, data, standardData, accountId, customFields, csvImportData, workingData, syncField, csvSetting);
        workingData.updatedUsers += 1;
      }
    } else {
      workingData.ignoredUsers += 1;
    }
  } else if (syncField !== 'scormId') {
    logger.debug(`>>>>> new user, elapsed = ${differenceInMilliseconds(new Date(), localStart)}ms.`);
    // For each new user, we create the user record, the accountUser record, and a userRole record,
    // and then add the new user into the acl for permissions.
    const newUserData = {
      ...standardData,
      password: Math.random().toString(36).replace('0.', ''),
      managerEmail: managerUser ? managerUser.email : data.managerEmail,
      deletedAt: data.status === 'inactive' ? new Date() : null,
      leaveStartedAt: data.status === 'onLeave' ? new Date() : null,
    };
    // do not import scormId even if the csv has for non scrom accounts
    delete newUserData.scormId;
    let newUser = null;
    await db.sequelize.transaction(async (transact) => {
      newUser = await Users.create(newUserData, { accountId }, { transaction: transact });
      await AccountUsers.create({
        userId: newUser.id,
        accountId,
        roleId: 2,
      }, { transaction: transact });
      await UserRoles.create({
        userId: newUser.id,
        roleId: userRoleId,
      }, { transaction: transact });
    });
    if (!newUser.deletedAt) {
      // add active user to account roster
      await addUserToAccountRoster(accountId, newUser.id);
    }

    workingData.newUsers += 1;
    logger.info(`>>>>> created newUser = ${newUser.id}, elapsed = ${differenceInMilliseconds(new Date(), localStart)}ms.`);
    await updateOrCreateCustomFields(customFields, accountId, newUser, csvImportData, workingData, data, csvSetting);
    // update emtrainroles
    await updateEmtrainRoles(newUser.id);
  }
  else {
    workingData.ignoredUsers += 1;
    // CAN SURFACE ERROR MESSAGE HERE IN FUTURE IF DESIRED
  }
  logger.info(`>>>>> Exiting processRow, elapsed = ${differenceInMilliseconds(new Date(), localStart)}ms.`);
};

// The workingData object consists of fields that change and we need to pass in and out.
// The 2nd parameter object is fixed (non-changing) data that is necessary.
const doAChunk = (workingData, {
  csvSetting,
  csvImportData,
  readStream,
  rowsToSkip,
  colHeaders,
  chunkSize,
  syncField,
  accountId,
  standardFields,
  userRoleId,
}) => {
  return new Promise(async (resolve, reject) => {
    let recordOffset = 0;
    let fullChunkProcessed = false;
    let firstRowColumnNamesSkipped = false;

    const pipeline = readStream
      .pipe(ES.split())
      .pipe(ES.mapSync(async (nextLine) => {
        logger.debug('Getting next line from readStream. nextLine: ', nextLine);
        pipeline.pause();
        if (csvSetting.firstRowColumnNames && !firstRowColumnNamesSkipped) {
          firstRowColumnNamesSkipped = true;
          pipeline.resume();
          return;
        }
        if (recordOffset < rowsToSkip) {
          recordOffset += 1;
          // eslint-disable-next-line max-len
          logger.debug('Iterating up to the where we were. recordOffset: ', recordOffset, ', rowsToSkip: ', rowsToSkip);
          pipeline.resume();
          return;
        }

        let data = null;
        try {
          // Not sure why I need to recreate this for each line, but whatever
          const parser = csvtojson({
            delimeter: ',',
            noheader: true,
            headers: colHeaders,
            ignoreColumns: /none/,
            ignoreEmpty: csvSetting.ignoreBlankFields,
            trim: true,
          });

          await parser.fromString(nextLine)
            .then((nextData) => {
              data = nextData && nextData[0] ? nextData[0] : null;
            });
          logger.debug('data: ', data);
          workingData.row += 1;
          if (!data) {
            logger.debug('Blank row detected...');
            workingData.blankRowAtEnd = true; // maybe true, but will reset if not true.
          } else {
            workingData.blankRowAtEnd = false;
            logger.debug('Processing row number: ', workingData.row);
            await processRow({
              csvImportData,
              csvSetting,
              data,
              syncField,
              accountId,
              workingData,
              standardFields,
              userRoleId,
            });
          }
          await updateImportStatus(workingData, csvImportData);
          if ((workingData.row - rowsToSkip) === chunkSize) {
            fullChunkProcessed = true;
            logger.debug('pipeline.end() because chunk size done');
            pipeline.end();
          }
        } catch (err) {
          const msg = (err && err.errors && err.errors[0] && err.errors[0].message) || (err && err.message);
          logger.error('** Error in CSV record - skipping: %j', msg);
          logger.error('catch for single record, data is :%j', data);
          await CsvErrors.create({
            csvImportId: csvImportData.id,
            // for csv's with first row as column names add +1 to match row numbers on sheet
            row: csvSetting.firstRowColumnNames ? workingData.row + 1 : workingData.row,
            message: `${msg}`,
          });
          await updateImportStatus(workingData, csvImportData);
          if ((workingData.row - rowsToSkip) === chunkSize) {
            fullChunkProcessed = true;
            pipeline.end();
            logger.debug('resolving because chunk size done');
            // resolve(workingData);
          }
        } finally {
          logger.debug('finally... resuming pipeline');
          pipeline.resume();
        }
      }))
      .on('error', async (err) => {
        logger.error('** CSV Parse error: %j', err);
      })
      .on('end', async (err) => {
        logger.debug('Processing onEnd...');

        if (fullChunkProcessed) {
          logger.debug('******* onEnd... full chunk completed. Resolving...');
          resolve(workingData);
          return;
        }
        // We might not get an EOL on the last row, but we got an 'end', so we're done
        workingData.endOfFileReached = true;
        if (workingData.blankRowAtEnd) {
          workingData.row -= 1; // undo the last empty row
        }
        const totalRecords = csvSetting.firstRowColumnNames ? workingData.row + 1 : workingData.row;
        if (err) {
          logger.error('** CSV processing error: %j', err);
          await csvImportData.update({
            totalRecords,
            recordsProcessed: workingData.row,
            updatedUserCount: workingData.updatedUsers,
            newUserCount: workingData.newUsers,
            ignoredUserCount: workingData.ignoredUsers,
            status: 'failed',
            importFinishedAt: new Date(),
          });

          reject(err); // TODO: We may not want to do an API fail if only certain records fail.
        } else {
          logger.info('** CSV processing completed');
          await csvImportData.update({
            totalRecords,
            recordsProcessed: workingData.row,
            updatedUserCount: workingData.updatedUsers,
            newUserCount: workingData.newUsers,
            ignoredUserCount: workingData.ignoredUsers,
            status: 'imported',
            importFinishedAt: new Date(),
          });
          resolve(workingData);
        }
      });
  });
};

const importCsv = async (res, i18n, accountId, csvImportData) => {
  const { csvSetting, filePath: csvFilePath, fileLocation } = csvImportData;
  const standardFields = ['email', 'notificationEmail', 'lastName', 'firstName', 'supervisor', 'title',
    'language', 'role', 'city', 'location', 'hireDate', 'employeeId', 'exempt', 'managerEmail',
    'stateCode', 'countryCode', 'scormId', 'jobGroup', 'department', 'orgLevel', 'remote', 'raceEthnicity',
    'gender', 'birthDate', 'veteranStatus', 'sexualOrientation', 'disabilityStatus', 'attritionDate', 'voluntaryAttrition'];

  try {
    await csvImportData.update({ status: 'importing', importStartedAt: new Date() });
    await CsvErrors.destroy({
      where: {
        csvImportId: csvImportData.id,
      },
    });

    // Get the columns. Maybe we want to do this in importById, but do it here for now.
    const csvColumns = await CsvColumns.findAll({
      where: {
        csvSettingId: csvSetting.id,
      },
      order: [['columnNumber', 'ASC']],
    });
    let colHeaders = [];
    if (csvColumns) {
      // fill in any missing columns as 'don't import' column headers.
      colHeaders = csvColumns.reduce((acc, col) => {
        let idx = acc.length;
        const empties = [];
        while (idx + 1 < col.columnNumber) {
          empties.push('none');
          idx += 1;
        }
        return acc.concat(empties).concat(col.userTableColumn);
      }, []);
    }

    const account = await Accounts.findOne({
      where: {
        id: accountId,
      },
    });
    const syncField = account.csvImportSyncField;
    const requiredHeaders = [];
    if (syncField === 'employeeId') {
      requiredHeaders.push('employeeId', 'firstName', 'lastName');
    } else if (syncField === 'scormId') {
      requiredHeaders.push('scormId');
    } else {
      requiredHeaders.push('email', 'firstName', 'lastName' );
    }

    if (colHeaders.length === 0 || _.difference(requiredHeaders, colHeaders).length !== 0) {
      const startCaseRequiredHeaders = requiredHeaders.map(header => _.startCase(header));
      const headers = startCaseRequiredHeaders.join(', ');
      throw new NotFoundError(i18n.t('csvImport.missing_column_header_Error', { headers }));
    }

    // Get the role id for a user. We'll use this to create userRole records for each user.
    const roleRecord = await Roles.findOne({
      where: {
        name: 'user',
      },
    });
    const userRoleId = roleRecord.id;

    // Loop through the next part. We need to recreate read streams because of timeouts from AWS. This is not pretty.
    // We want the API to return after initial setup and not wait for the whole thing to be done. This is done
    // via the res.json call after the first readstream is created (to make sure there are no obvious errors on initialization).

    let readStream = await getReadStream(csvFilePath, accountId, fileLocation);
    readStream.on('error', (err) => {
      // AWS will give us timeouts after 2 minutes even when we destroy the stream. At least we get an error that
      // we can log and ignore. Also, throwing an error in this error callback doesn't work
      logger.error('CSV File Open error: %j', err);
    });
    if (res) {
      res.json('started');
    }

    const chunkSize = (config.csvImport && config.csvImport.chunkSize) || 50;
    // workingData is updated within the doAChunk function
    const workingData = {
      row: 0,
      newUsers: 0,
      ignoredUsers: 0,
      updatedUsers: 0,
      endOfFileReached: false,
      blankRowAtEnd: false,
    };

    while (!workingData.endOfFileReached) {
      /* eslint-disable no-loop-func */
      /* eslint-disable no-await-in-loop */
      logger.debug('about to call doAChunk');
      await doAChunk(workingData, {
        csvSetting,
        csvImportData,
        readStream,
        rowsToSkip: workingData.row,
        colHeaders,
        chunkSize,
        syncField,
        accountId,
        standardFields,
        userRoleId,
      });
      await sleep(0); // wait for next tick so we don't get a "write after destroy" error
      logger.debug('readStream destroy');
      readStream.destroy();
      if (!workingData.endOfFileReached) {
        logger.debug('getting new readStream');
        readStream = await getReadStream(csvFilePath, accountId, fileLocation);
        readStream.on('error', (err) => {
          logger.error('CSV File Open error: %j', err);
        });
      }
    }
  } catch (err) {
    logger.error(`Exception caught while executing csvImport: ${err}.`);
    const failReason = err.name === 'NotFoundError' ? 'Invalid Values in Data' : 'Emtrain System Error';
    // We have an error. The status of the import may or may not have been updated,
    // but it's a fail, so make sure we update the status if we can
    await csvImportData.reload();
    if (csvImportData.status !== 'failed') {
      await csvImportData.update({
        status: 'failed',
        importFinishedAt: new Date(),
        failReason,
      });
    }
    throw err; // continue up the chain and allow the calling application to handle the catch().
  }
};

const readCsv = async(csvImportData, accountId, maxRecords = 10) => {
  const { filePath: csvFilePath, fileLocation } = csvImportData;

  return new Promise(async (resolve, reject) => {
    try {
      const records = [];
      let row = 0;

      const readStream = await getReadStream(csvFilePath, accountId, fileLocation);
      readStream.on('error', (err) => {
        logger.error('CSV File Open error: %j', err);
        reject(err);
      });
      const parser = csvtojson({
        delimeter: ',',
        noheader: true,
        trim: true,
      });

      parser
        .subscribe((data) => {
          return new Promise(async (resolveRecord, rejectRecord) => {
            try {
              row += 1;
              if (row > maxRecords) {
                parser.emit('done');
                rejectRecord();
              } else {
                records.push(data);
                resolveRecord();
              }
            } catch (err) {
              rejectRecord(err);
            }
          });
        })
        .on('error', (err) => {
          logger.error('** CSV Parse error: %j', err);
        })
        .on('done', async (err) => {

          if (err) {
            logger.error('** CSV processing error: %j', err);
            reject(err); // TODO: We may not want to do an API fail if only certain records fail.
          } else {
            resolve(records);
          }
        });
      readStream.pipe(parser);
    } catch (err) {
      reject(err);
    }
  });
};

// return count of pending sftp import jobs
const countSFTPJobs = async () => {
  return CsvImports.count({
    where: {
      fileLocation: 'sftp',
      status: 'notstarted',
    },
    include: [{
      model: CsvSettings,
      include: [{
        model: Accounts,
        where: {
          status: 'active',
        },
        required: true,
      }],
      required: true,
    }],
  });
};

// return the next pending sftp import job according to priority
const getNextSFTPJob = async (excludeIds) => {
  return CsvImports.findOne({
    where: {
      id: {
        [Op.notIn]: excludeIds,
      },
      fileLocation: 'sftp',
      status: 'notstarted',
    },
    include: [{
      model: db.csvSettings,
      include: [{
        model: Accounts,
        where: {
          status: 'active',
        },
        required: true,
      }],
      required: true,
    }],
    order: [['priority', 'DESC'], ['fileSize', 'ASC']],
  });
};

// return count of pending scheduled import jobs
const countS3Jobs = async () => {
  return CsvImports.count({
    where: {
      fileLocation: 'S3',
      status: 'scheduled',
    },
    include: [{
      model: CsvSettings,
      include: [{
        model: Accounts,
        where: {
          status: 'active',
        },
        required: true,
      }],
      required: true,
    }],
    order: [['priority', 'DESC'], ['fileSize', 'ASC']],
  });
};

// return the next pending scheduled import job according to priority
const getNextS3Job = async (excludeIds) => {
  return CsvImports.findOne({
    where: {
      id: {
        [Op.notIn]: excludeIds,
      },
      fileLocation: 'S3',
      status: 'scheduled',
    },
    include: [{
      model: CsvSettings,
      include: [{
        model: Accounts,
        where: {
          status: 'active',
        },
        required: true,
      }],
      required: true,
    }],
    order: [['priority', 'DESC'], ['fileSize', 'ASC']],
  });
};

/*
* Checks for csv files at the target directory in S3 (accounts/:id/csv-sftp) and creates and executes a
* csvImport job for each file.
* Moves file to accounts/:id/csv-uploads folder once processed.
*/
async function checkUserUpdates(app) {
  const csvSettings = await CsvSettings.findAll();
  const timestamp = format(new Date(), 'YYYY-MM-DD HH:mm:ss');

  logger.info('Entering checkUserUpdates');
  logger.info(`Phase I - checking sftp for files for ${csvSettings.length} accounts.`);
  for (const csvSetting of csvSettings) {
    const account = await Accounts.findOne({
      where: {
        id: csvSetting.accountId,
        status: 'active',
      },
    });

    if (!account) {
      // eslint-disable-next-line no-continue
      continue;
    }
    const Bucket = config.s3.bucket;
    const csvBucketDetails = getCsvS3Bucket(csvSetting.accountId, 'csv-sftp');

    // First we will process all the files that require pgp decryption
    if (account.pgpEncryptedImports) {
      const files = await listFiles(csvBucketDetails.keyPrefix);
      if (files.length > 0) {
        logger.info(`Performing pgp decryption on account id = ${account.id}`);
      }
      for (const file of files) {
        if (file.name && file.name.toLowerCase().endsWith('.gpg')) {
          try {
            await pgpDecryptFile(file.path);
          } catch (err) {
            logger.error(`Exception caught while decrypting csvImport file for accountId ${account.id}: ${err}.`);
            const dest = getCsvS3Bucket(account.id, `upload-errors/${uuidv4(file.name)}_${file.name}`);
            const destination = `${dest.bucket}/${dest.keyPrefix}`;
            const csvJobFilePath = destination.replace(`${Bucket}/`, '');

            // create a csvImport to track failure
            const newJob = await CsvImports.create({
              csvSettingId: csvSetting.id,
              fileLocation: 'sftp',
              filePath: csvJobFilePath,
              fileSize: file.size,
              origFilename: file.name,
              status: 'failed',
              failReason: 'decryption',
              createdAt: timestamp,
              updatedAt: timestamp,
            });
            logger.info(`Created failed csvImport job id = ${newJob.id}, for account id = ${account.id}, file= ${file.name}, failReason=decryption`);

            // move the encrypted file into the destination directory
            await moveFile(file.path, destination);
            // eslint-disable-next-line no-continue
            continue;
          }
          // Move the encrypted file into the destination directory
          // The decrypted file will be processed, but deleted after processing.
          const dest = getCsvS3Bucket(account.id, `csv-uploads/${uuidv4(file.name)}_${file.name}`);
          const destination = `${dest.bucket}/${dest.keyPrefix}`;
          await moveFile(file.path, destination);
        }
      }
    }

    // Encrypted files have been decrypted, so get a new listing from the directory.
    const files = await listFiles(csvBucketDetails.keyPrefix);
    logger.debug(`Found ${files.length} files for account id = ${account.id}`);
    // create csvImport job for each file found in target directory
    for (const file of files) {
      if (file.name && file.name !== '' && !file.name.startsWith('.') && file.name.toLowerCase().endsWith('.csv')) {
        // move the file into the csv-uploads directory for parsing.
        const dest = getCsvS3Bucket(csvSetting.accountId, `csv-uploads/${uuidv4(file.name)}_${file.name}`);
        const destination = `${dest.bucket}/${dest.keyPrefix}`;
        await moveFile(file.path, destination);
        const csvJobFilePath = destination.replace(`${Bucket}/`, '');

        const newJob = await CsvImports.create({
          csvSettingId: csvSetting.id,
          fileLocation: 'sftp',
          filePath: csvJobFilePath,
          fileSize: file.size,
          origFilename: file.name,
          status: 'notstarted',
          createdAt: timestamp,
          updatedAt: timestamp,
        });
        logger.info(`Creating csvImport job id = ${newJob.id}, for account id = ${account.id}, file= ${file.name}`);
      } else if (file.name) {
        // Invalid file, move it to error bucket.
        const dest = getCsvS3Bucket(account.id, `upload-errors/${uuidv4(file.name)}_${file.name}`);
        const destination = `${dest.bucket}/${dest.keyPrefix}`;
        const csvJobFilePath = destination.replace(`${Bucket}/`, '');

        // create a csvImport to track failure
        const newJob = await CsvImports.create({
          csvSettingId: csvSetting.id,
          fileLocation: 'sftp',
          filePath: csvJobFilePath,
          fileSize: file.size,
          origFilename: file.name,
          status: 'failed',
          failReason: 'invalid-file-extension',
          createdAt: timestamp,
          updatedAt: timestamp,
        });
        logger.info(`Created failed csvImport job id = ${newJob.id}, for account id = ${account.id}, file= ${file.name}, failReason=invalid-file-extension`);

        await moveFile(file.path, destination);
      }
    }
  }

  const numSFTPJobs = await countSFTPJobs();
  const processedJobIds = [];
  logger.info(`Phase II - checking for ready sftp csvImport jobs:  ${numSFTPJobs} jobs found.`);
  // execute new sftp jobs
  for (let i = 0; i < numSFTPJobs; i++) {
    try {
      const sftpJob = await getNextSFTPJob(processedJobIds);
      if (sftpJob && !processedJobIds.includes(sftpJob.id)) {
        const jobStart = new Date();
        const accountId = sftpJob.csvSetting.accountId;
        const i18n = app.get('i18n');
        processedJobIds.push(sftpJob.id);
        logger.info(`Executing sftpJob ${sftpJob.id} for account ${accountId}.`);
        await importCsv(null, i18n, accountId, sftpJob);
        logger.info(`Completed sftpJob ${sftpJob.id} for account ${accountId} in ${differenceInMinutes(new Date(), jobStart)} minutes.`);
      }
    } catch (err) {
      logger.error(`Exception caught while executing csvImport: ${err}.`);
    }
  }

  // clean up pgp files
  const completedJobs = await CsvImports.findAll({
    where: {
      fileLocation: 'sftp',
      status: {
        [Op.in]: ['imported'],
      },
      updatedAt: {
        [Op.gte]: moment().subtract(1, 'days').toDate(),
      },
    },
    include: [{
      model: db.csvSettings,
      include: [{
        model: db.accounts,
        attributes: ['id', 'pgpEncryptedImports'],
      }],
    }],
  });
  for (const completedJob of completedJobs) {
    if (completedJob.csvSetting.account.pgpEncryptedImports) {
      removeFileByPath(completedJob.filePath);
    }
  }
  return numSFTPJobs;
}

/*
* Checks for csv files at the target directory in S3 (accounts/:id/csv-sftp) and creates and executes a
* csvImport job for each file.
* Moves file to accounts/:id/csv-uploads folder once processed.
*/
async function checkScheduledImports(app) {
  // mark as failed any import jobs that have been stuck on 'importing' for more than one day.
  db.sequelize.query(
    'UPDATE csvImports SET status = ?, failReason = ? ' +
    'WHERE importStartedAt < DATE_SUB(NOW(), INTERVAL 2 DAY) AND status = ? ',
    {
      replacements: ['failed', 'Import timed out', 'importing'],
      type: db.sequelize.QueryTypes.UPDATE,
      raw: true,
    },
  );

  const numS3Jobs = await countS3Jobs();
  const processedJobIds = [];
  logger.info(`Entering checkScheduledImports(), ${numS3Jobs} scheduled jobs found.`);
  // execute new scheduled import jobs
  for (let i = 0; i < numS3Jobs; i++) {
    try {
      const s3Job = await getNextS3Job(processedJobIds);
      if (s3Job && !processedJobIds.includes(s3Job.id)) {
        const jobStart = new Date();
        const accountId = s3Job.csvSetting.accountId;
        const i18n = app.get('i18n');
        processedJobIds.push(s3Job.id);
        logger.info(`Executing job ${s3Job.id} for account ${accountId}.`);
        await importCsv(null, i18n, accountId, s3Job);
        logger.info(`Completed job ${s3Job.id} for account ${accountId} in ${differenceInMinutes(new Date(), jobStart)} minutes.`);
      }
    } catch (err) {
      logger.error(`Exception caught while executing csvImport for scheduled import: ${err}.`);
    }
  }
  return numS3Jobs;
}

module.exports = {
  checkUserUpdates,
  checkScheduledImports,
  importCsv,
  readCsv,
};
