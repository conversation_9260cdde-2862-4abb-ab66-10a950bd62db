const _ = require('lodash');
const { S3Client, DeleteObjectCommand, PutObjectCommand, HeadObjectCommand, ListObjectsV2Command } = require("@aws-sdk/client-s3");
const { v4: uuidv4 } = require('uuid');

const config = require('../../config/config');
const logger = require('../../logger');
const db = require('../../db');
const { initPolly, getLanguage, getSpeechParams, synthesizeSpeech, convertToSsml } = require('./polly');

const Op = db.Sequelize.Op;

const htmlRE = /(<([^>]+)>)/ig; // regular expression for finding html tags

const s3 = new S3Client({
  region: 'us-east-1',
  credentials: {
    accessKeyId: config.s3.accessKeyId,
    secretAccessKey: config.s3.secretAccessKey,
  },
});

// Get lesson cards with cardType (text, textImage)
// TBD - only published cards?, any other constraints
const getLessonCards = async (limit, offset) => {
  const lessonCards = await db.lessonCards.findAll({
    limit,
    offset,
    include: [
      {
        model: db.contentStrings,
        where: {
          model: 'lessonCard',
        },
        required: false,
      },
      {
        model: db.speechAudios,
        where: {
          model: 'lessonCard',
        },
        required: false,
      },
      // Uncomment this if we only want published lessons
      // {
      //   model: db.lessons,
      //   where: {
      //     lifecycle: 'publish',
      //   },
      //   required: true,
      // },
    ],
    order: [['id', 'ASC']],
  });
  return lessonCards;
};

const useSsml = () => {
  return !!config.textToSpeech.useSsml;
};

const postProcessText = (s) => {
  if (!s || s.length === 0) {
    return s;
  }

  const processedString = s
    .replace(/<li>|<\/li>|<\/p>/ig, '. ') // <li>x</li> is a bullet, and <p></p> might need some extra time for a pause. do before removing html tags.
    .replace(htmlRE, '') // remove HTML tags
    .replace(/&nbsp;/g, ' ') // get rid of pesky &nbsp; thingies that are lurking in production database.
    .replace(/\.(?!\.)/g, '. ') // replace a period followed by 'not period' with period space, so Polly doesn't say "dot" for the period
    .replace(/Tishman Speyer/g, 'Tishman Spire')
    .replace(/Pasha/g, 'Payshuh');

  const final = useSsml() ? convertToSsml(processedString) : processedString;
  return final;
};

const addPauseAfterText = (text) => {
  return text && text.length > 0 ? `${text}. ` : ''; // add a period and space after the text
};

const getTextFromCard = (lessonCard, field, language = 'en', contentStrings) => {
  let text = '';
  if (language === 'en') {
    switch (lessonCard.cardType) {
      case 'text':
      case 'textImage':
      case 'borderlessTextImage':
      case 'textOverlayImage':
      case 'video':
      case 'policyAcknowledgement':
        text = `${addPauseAfterText(lessonCard.title)} ${lessonCard.description || ''}`;
        break;
      case 'lessonComplete':
        text = lessonCard.description || '';
        break;
      case 'quizColorSpectrum':
      case 'quizSlider':
      case 'quizFreeformText':
      case 'quizBoolean':
        text = `${addPauseAfterText(lessonCard.title)} ${lessonCard.question1 || ''}`;
        break;
      case 'listNumbers':
      case 'listBullets':
        text = `${addPauseAfterText(lessonCard.title)} ${addPauseAfterText(lessonCard.list1)} ` +
          `${addPauseAfterText(lessonCard.list2)} ${addPauseAfterText(lessonCard.list3)} ` +
          `${addPauseAfterText(lessonCard.list4)} ${addPauseAfterText(lessonCard.list5)} ` +
          `${addPauseAfterText(lessonCard.list6)} ${addPauseAfterText(lessonCard.list7)} ` +
          `${addPauseAfterText(lessonCard.list8)} ${addPauseAfterText(lessonCard.list9)} ` +
          `${lessonCard.list10 || ''}`;
        break;
      case 'listCheckboxes':
        text = `${addPauseAfterText(lessonCard.title)} ${addPauseAfterText(lessonCard.list1)} ` +
          `${addPauseAfterText(lessonCard.list2)} ${addPauseAfterText(lessonCard.list3)} ` +
          `${addPauseAfterText(lessonCard.list4)} ${lessonCard.list5 || ''}`;
        break;
      case 'quizMultiChoice':
      case 'quizSingleChoice':
        text = field
          ? lessonCard[field] || ''
          : `${addPauseAfterText(lessonCard.title)} ${lessonCard.question1 || ''} ` +
             `${addPauseAfterText(lessonCard.list1)} ${addPauseAfterText(lessonCard.list2)} ` +
             `${addPauseAfterText(lessonCard.list3)} ${addPauseAfterText(lessonCard.list4)}` +
             `${addPauseAfterText(lessonCard.list5)} ${addPauseAfterText(lessonCard.list6)}` +
             `${addPauseAfterText(lessonCard.list7)} ${addPauseAfterText(lessonCard.list8)}` +
             `${addPauseAfterText(lessonCard.list9)} ${addPauseAfterText(lessonCard.list10)}`;
        break;
      case 'clickExpand':
        text = field
          ? lessonCard[field] || ''
          : `${addPauseAfterText(lessonCard.title)} ${lessonCard.description || ''} ` +
             `${addPauseAfterText(lessonCard.list1)} ${addPauseAfterText(lessonCard.list2)} ` +
             `${addPauseAfterText(lessonCard.list3)} ${addPauseAfterText(lessonCard.list4)}`;
        break;
      case 'statesMap':
        text = lessonCard.title || '';
        break;
      default:
        break;
    }
  } else if (contentStrings) {
    switch (lessonCard.cardType) {
      case 'text':
      case 'textImage':
      case 'borderlessTextImage':
      case 'textOverlayImage':
      case 'video':
      case 'policyAcknowledgement': {
        const title = contentStrings.find(cs => cs.field === 'title');
        const description = contentStrings.find(cs => cs.field === 'description');
        text = `${addPauseAfterText(title && title.value)} ${(description && description.value) || ''}`;
        break;
      }
      case 'lessonComplete': {
        const description = contentStrings.find(cs => cs.field === 'description');
        text = (description && description.value) || '';
        break;
      }
      case 'quizColorSpectrum':
      case 'quizSlider':
      case 'quizFreeformText':
      case 'quizBoolean': {
        const title = contentStrings.find(cs => cs.field === 'title');
        const question1 = contentStrings.find(cs => cs.field === 'question1');
        text = `${addPauseAfterText(title && title.value)} ${(question1 && question1.value) || ''}`;
        break;
      }
      case 'listBullets':
      case 'listNumbers': {
        const title = contentStrings.find(cs => cs.field === 'title');
        const list1 = contentStrings.find(cs => cs.field === 'list1');
        const list2 = contentStrings.find(cs => cs.field === 'list2');
        const list3 = contentStrings.find(cs => cs.field === 'list3');
        const list4 = contentStrings.find(cs => cs.field === 'list4');
        const list5 = contentStrings.find(cs => cs.field === 'list5');
        const list6 = contentStrings.find(cs => cs.field === 'list6');
        const list7 = contentStrings.find(cs => cs.field === 'list7');
        const list8 = contentStrings.find(cs => cs.field === 'list8');
        const list9 = contentStrings.find(cs => cs.field === 'list9');
        const list10 = contentStrings.find(cs => cs.field === 'list10');
        text = `${addPauseAfterText(title && title.value)} ${addPauseAfterText(list1 && list1.value)} ` +
          `${addPauseAfterText(list2 && list2.value)} ${addPauseAfterText(list3 && list3.value)} ` +
          `${addPauseAfterText(list4 && list4.value)} ${addPauseAfterText(list5 && list5.value)} ` +
          `${addPauseAfterText(list6 && list6.value)} ${addPauseAfterText(list7 && list7.value)} ` +
          `${addPauseAfterText(list8 && list8.value)} ${addPauseAfterText(list9 && list9.value)} ` +
          `${(list10 && list10.value) || ''}`;
        break;
      }
      case 'listCheckboxes': {
        const title = contentStrings.find(cs => cs.field === 'title');
        const list1 = contentStrings.find(cs => cs.field === 'list1');
        const list2 = contentStrings.find(cs => cs.field === 'list2');
        const list3 = contentStrings.find(cs => cs.field === 'list3');
        const list4 = contentStrings.find(cs => cs.field === 'list4');
        const list5 = contentStrings.find(cs => cs.field === 'list5');
        text = `${addPauseAfterText(title && title.value)} ${addPauseAfterText(list1 && list1.value)} ` +
          `${addPauseAfterText(list2 && list2.value)} ${addPauseAfterText(list3 && list3.value)} ` +
          `${addPauseAfterText(list4 && list4.value)} ${(list5 && list5.value) || ''}`;
        break;
      }
      case 'quizMultiChoice':
      case 'quizSingleChoice': {
        const title = contentStrings.find(cs => cs.field === 'title');
        const question = contentStrings.find(cs => cs.field === 'question1');
        const list1 = contentStrings.find(cs => cs.field === 'list1');
        const list2 = contentStrings.find(cs => cs.field === 'list2');
        const list3 = contentStrings.find(cs => cs.field === 'list3');
        const list4 = contentStrings.find(cs => cs.field === 'list4');
        const list5 = contentStrings.find(cs => cs.field === 'list5');
        const list6 = contentStrings.find(cs => cs.field === 'list6');
        const list7 = contentStrings.find(cs => cs.field === 'list7');
        const list8 = contentStrings.find(cs => cs.field === 'list8');
        const list9 = contentStrings.find(cs => cs.field === 'list9');
        const list10 = contentStrings.find(cs => cs.field === 'list10');
        text = `${addPauseAfterText(title && title.value)} ${(question && question.value) || ''}` +
          `${addPauseAfterText(list1 && list1.value)} ${addPauseAfterText(list2 && list2.value)} ` +
          `${addPauseAfterText(list3 && list3.value)} ${addPauseAfterText(list4 && list4.value)}` +
          `${addPauseAfterText(list5 && list5.value)} ${addPauseAfterText(list6 && list6.value)}` +
          `${addPauseAfterText(list7 && list7.value)} ${addPauseAfterText(list8 && list8.value)}` +
          `${addPauseAfterText(list9 && list9.value)} ${addPauseAfterText(list10 && list10.value)}`;
        break;
      }
      case 'clickExpand': {
        if (field) {
          const contentField = contentStrings.find(cs => cs.field === field);
          text = (contentField && contentField.value) || '';
        } else {
          const title = contentStrings.find(cs => cs.field === 'title');
          const description = contentStrings.find(cs => cs.field === 'description');
          const list1 = contentStrings.find(cs => cs.field === 'list1');
          const list2 = contentStrings.find(cs => cs.field === 'list2');
          const list3 = contentStrings.find(cs => cs.field === 'list3');
          const list4 = contentStrings.find(cs => cs.field === 'list4');
          text = `${addPauseAfterText(title && title.value)} ${(description && description.value) || ''}` +
          `${addPauseAfterText(list1 && list1.value)} ${addPauseAfterText(list2 && list2.value)} ` +
          `${addPauseAfterText(list3 && list3.value)} ${addPauseAfterText(list4 && list4.value)}`;
        }
        break;
      }
      case 'statesMap': {
        const title = contentStrings.find(cs => cs.field === 'title');
        text = (title && title.value) || '';
        break;
      }
      default:
        break;
    }
  }
  const final = postProcessText(text);
  logger.debug('getTextFromCard:', final);
  return final;
};

const getTextFromMapCard = (protectedTrait) => {
  const retVal = protectedTrait.traits && postProcessText(protectedTrait.traits);
  logger.debug('getTextFromMapCard:', retVal);
  return retVal;
};

const getS3KeyFromPath = (path) => {
  const idx = path.search('/lesson-cards');
  const key = idx !== -1 ? path.slice(idx + 1) : null;
  return key;
};

const removeS3Object = async (s3, Key) => {
  const params = {
    Bucket: config.s3.bucket,
    Key,
  };
  logger.info('removing s3 file:', `${config.s3.bucket}:/${Key}`);
  try {
    const data = await s3.send(new DeleteObjectCommand(params));
    return data;
  } catch (err) {
    throw new Error(err.stack);
  }
};

// Function to update the database as appropriate
// We're dealing with file and speechAudio records
// If we get an empty { Location, ContentLength }, it means no audio, so we need
// to delete existing records if they're there.
const upsertRecords = async (
  contentId,
  model,
  existingSpeechAudio,
  { Location, ContentLength },
  lang,
  specialId,
  isMap,
) => {
  const fileParams = {
    path: Location,
    type: 'audio/mpeg',
    language: lang,
    kb: Math.round(ContentLength / 1024),
  };
  if (existingSpeechAudio) {
    // if we're modifying a lesson card, make sure mp3 file isn't one of the defaultProtectedTraits files
    let ptdSameFile;
    if (model !== 'protectedTraitsDefault' && isMap) {
      ptdSameFile = await db.speechAudios.findOne({
        where: {
          fileId: existingSpeechAudio.fileId,
          model: 'protectedTraitsDefault',
        },
      });
    }
    let origFile;
    if (!ptdSameFile) {
      origFile = await db.files.findByPk(existingSpeechAudio.fileId);
    }

    if (origFile) {
      const origPath = origFile.path;
      const key = getS3KeyFromPath(origFile.path);
      if (Location) {
        origFile.update({
          path: Location,
          kb: Math.round(ContentLength / 1024),
        });
        existingSpeechAudio.updatedAt = new Date(); // eslint-disable-line no-param-reassign
        existingSpeechAudio.changed('updatedAt', true);
        await existingSpeechAudio.save();
      } else {
        await origFile.destroy();
      }
      // check if the path has records in files, to handle duplicate lesson break
      const filesCount = await db.files.count({ where: { path: origPath, type: fileParams.type } });
      if (key && !filesCount) {
        await removeS3Object(s3, key);
      }
    } else if (Location) {
      const newFile = await db.files.create(fileParams);
      await existingSpeechAudio.update({
        fileId: newFile.id,
      });
    }
    if (!Location) {
      await existingSpeechAudio.destroy();
    }
  } else if (Location) {
    const newFile = await db.files.create(fileParams);
    await db.speechAudios.create({
      contentId,
      model,
      language: lang,
      fileId: newFile.id,
      specialId,
    });
  }
};

const upsertSpeechRecordsForLessonCard = async (
  lessonCardId,
  { Location, ContentLength = 0 },
  lang,
  specialId = null,
  isMap,
) => {
  const existingSpeechAudio = await db.speechAudios.findOne({
    where: {
      contentId: lessonCardId,
      model: 'lessonCard',
      language: lang,
      specialId,
    },
  });
  await upsertRecords(
    lessonCardId, 'lessonCard', existingSpeechAudio, { Location, ContentLength },
    lang, specialId, isMap,
  );
};

const upsertSpeechRecordsForDpt = async (dpt, { Location, ContentLength = 0 }) => {
  await upsertRecords(
    dpt.id, 'protectedTraitsDefault', dpt.speechAudio, { Location, ContentLength },
    dpt.language, dpt.abbr, false,
  );
};

// S3 upload using a promise and retrieval of content length
const uploadFile = async (fileNamePrefix, Body) => {
  const Bucket = `${config.s3.bucket}`;
  const Key = `lesson-cards/audio/${fileNamePrefix}_${uuidv4()}.mp3`;
  const ContentType = 'audio/mpeg';

  // Upload the file
  await s3.send(
    new PutObjectCommand({ Bucket, Key, Body, ContentType })
  );
  const metadata = await s3.send(new HeadObjectCommand({ Bucket, Key }));
  const location = `https://${Bucket}.s3.amazonaws.com/${Key}`;
  return { Bucket, Key, ContentLength: metadata.ContentLength, Location: location };
};

//
// generate the mp3 file and upload it to S3
//
const createAndUploadAudioFileFromText = async (fileNamePrefix, text, language = 'en') => {
  if (text && text.length > 0) {
    const params = getSpeechParams(text, language, useSsml());
    if (params) {
      let mp3Data;
      try {
        mp3Data = await synthesizeSpeech(params);
      } catch (err) {
        logger.error('Could not generate audio for fileNamePrefix', fileNamePrefix);
        logger.error(err);
      }
      if (mp3Data && mp3Data.AudioStream) {
        const uploadResult = await uploadFile(fileNamePrefix, mp3Data.AudioStream);
        return uploadResult;
      }
      return false;
    }
  }
  return {};
};

//
// Points protectedTrait's speechAudio record to the default protected trait's file record.
//
const pointToDefault = async (lessonCardId, dpt) => {
  const { id: dptId, language, abbr } = dpt;
  const existingSpeechAudio = await db.speechAudios.findOne({
    where: {
      contentId: lessonCardId,
      model: 'lessonCard',
      language,
      specialId: abbr,
    },
  });
  let defaultSpeechAudio = await db.speechAudios.findOne({
    where: {
      contentId: dptId,
      model: 'protectedTraitsDefault',
      language,
      specialId: abbr,
    },
  });
  if (!defaultSpeechAudio) {
    // no default... this is odd. Create one. Probably means we've created new defaults and are now in
    // lesson card authoring UI clicking on the Generate Audio button
    const result = await createAndUploadAudioFileFromText(
      `ptd_${language}_${abbr || ''}_${dptId}`,
      getTextFromMapCard(dpt),
      language,
    );
    await upsertSpeechRecordsForDpt(dpt, result);
    defaultSpeechAudio = await db.speechAudios.findOne({
      where: {
        contentId: dptId,
        model: 'protectedTraitsDefault',
        language,
        specialId: abbr,
      },
    });
  }
  if (defaultSpeechAudio) {
    if (!existingSpeechAudio) {
      await db.speechAudios.create({
        contentId: lessonCardId,
        model: 'lessonCard',
        language,
        fileId: defaultSpeechAudio.fileId,
        specialId: abbr,
      });
    } else {
      // about to change the fileId. If the current fileId isn't used anymore, need to delete the file record and the mp3 file
      const origFileId = existingSpeechAudio.fileId;
      await existingSpeechAudio.update({
        fileId: defaultSpeechAudio.fileId,
      });
      const sa = await db.speechAudios.findOne({
        where: {
          fileId: origFileId,
          model: 'lessonCard',
          language,
          specialId: abbr,
        },
      });
      if (!sa) {
        const origFile = await db.files.findByPk(origFileId);
        if (origFile) {
          const key = getS3KeyFromPath(origFile.path);
          if (key) {
            await removeS3Object(s3, key);
          }
          await origFile.destroy();
        }
      }
    }
  }
};

const needToUpdateDpt = (dpt) => {
  return (!dpt.speechAudio || dpt.updatedAt > dpt.speechAudio.updatedAt);
};

// Calculate if we need to update the mp3.
// This will be true if we don't have anything yet or the appropriate record has been updated
// since the last time we generated the audio.
const needToUpdate = (lessonCard, language, specialId) => {
  if (!lessonCard.speechAudios || lessonCard.speechAudios.length === 0) {
    return true;
  }
  const speechAudio = lessonCard.speechAudios.find(sa => sa.language === language && sa.specialId === specialId);
  if (!speechAudio) {
    return true;
  }
  if (language === 'en' && lessonCard.updatedAt > speechAudio.updatedAt) {
    return true;
  }
  if (language !== 'en' && lessonCard.contentStrings && lessonCard.contentStrings.length > 0) {
    const contentStrings = lessonCard.contentStrings.filter(cs => cs.language === language);
    if (contentStrings.some(cs => cs.updatedAt > speechAudio.updatedAt)) {
      return true;
    }
  }
  return false;
};

// Calculate if we need to update any of the state/protectedTrait mp3s.
// This will be true if we don't have anything yet or the particular state for a language
// has been updated since the last time we generated the audio.
// This card's speechAudio could be associated with either the default protected trait or its own,
// and they might have changed to the other.
// If either of them have changed, it's best to be safe and update.
const needToUpdateState = async (lessonCard, dpt, cardTrait) => {
  if (!lessonCard.speechAudios || lessonCard.speechAudios.length === 0) {
    return true;
  }
  const speechAudio = lessonCard.speechAudios.find(sa => sa.language === dpt.language
    && sa.specialId === dpt.abbr);
  if (!speechAudio) {
    return true;
  }
  if (!cardTrait || cardTrait.updatedAt > speechAudio.updatedAt || dpt.updatedAt > speechAudio.updatedAt) {
    return true;
  }
  return false;
};

//
// Generate audio for a lesson card. This will include all languages and all of the
// expandText fields. We'll do maps separately.
//
const genAudioForCard = async (genAll, lessonCard, language = null, fieldName = null, specialId = null, fixAudio = false) => {
  try {
    // First create the english one
    if ((!language || language === 'en') && (genAll || needToUpdate(lessonCard, 'en', specialId))) {
      const result = await createAndUploadAudioFileFromText(
        `lc_en_${lessonCard.id}`,
        getTextFromCard(lessonCard, fieldName),
      );
      if (result) {
        await upsertSpeechRecordsForLessonCard(lessonCard.id, result, 'en', specialId, false);
      }
    }
    // Now handle all the other languages
    if (lessonCard.contentStrings) {
      const languageGroups = _.groupBy(lessonCard.contentStrings, 'language');
      const langs = Object.keys(languageGroups);
      for (const lang of langs) {
        if ((!language || lang === language) && (genAll || needToUpdate(lessonCard, lang, specialId) || fixAudio)) {
          const result1 = await createAndUploadAudioFileFromText(
            `lc_${lang}_${lessonCard.id}`,
            getTextFromCard(lessonCard, fieldName, lang, languageGroups[lang]),
            lang,
          );
          if (result1) {
            await upsertSpeechRecordsForLessonCard(lessonCard.id, result1, lang, specialId, false);
          }
        }
      }
    }
  } catch (err) {
    logger.error('Bummer, could not generate audio for lesson card:', lessonCard.id);
    logger.error(err);
  }
};

//
// create separate audios for each state or province.
// If a defined protected Trait doesn't exist, use the default one.
//
const genAudioForMapCard = async (genAll, lessonCard, language = null) => {
  try {
    const protectedTraits = await db.protectedTraits.findAll({
      where: {
        lessonCardId: lessonCard.id,
      },
    });
    const defaultProtectedTraits = await db.protectedTraitsDefaults.findAll({
      where: {
        country: lessonCard.list1,
      },
    });

    // Generate for languages used in defaults and existing protectedTraits.
    let languagesUsed;
    if (language) {
      languagesUsed = [language];
    } else {
      const languagesUsedSet = new Set();
      defaultProtectedTraits.forEach((dpt) => {
        if (getLanguage(dpt.language)) {
          languagesUsedSet.add(dpt.language);
        }
      });
      protectedTraits.forEach((pt) => {
        if (getLanguage(pt.language)) {
          languagesUsedSet.add(pt.language);
        }
      });
      languagesUsed = Array.from(languagesUsedSet);
    }
    logger.debug('languages used for this map:', languagesUsed); // includes -mt languages
    for (const dpt of defaultProtectedTraits) {
      // see if we have a real trait. If not, use default. If real trait matches default, use default
      // We still need the original real trait independent of whether it's the same as the default to determine if
      // it should be updated.
      const cardTrait = protectedTraits.find(pt => pt.language === dpt.language
        && pt.abbr === dpt.abbr);
      const traitToUse = protectedTraits.find(pt => pt.language === dpt.language
        && pt.abbr === dpt.abbr && pt.traits !== dpt.traits)
        || dpt;

      if ((!language || traitToUse.language === language)
        && (genAll || await needToUpdateState(lessonCard, dpt, cardTrait))) {
        logger.debug('using this trait', traitToUse.abbr, traitToUse.language, traitToUse.traits);
        if (traitToUse !== dpt) {
          const result = await createAndUploadAudioFileFromText(
            `pt_${traitToUse.language}_${traitToUse.abbr || ''}_${lessonCard.id}`,
            getTextFromMapCard(traitToUse),
            traitToUse.language,
          );
          if (result) {
            await upsertSpeechRecordsForLessonCard(lessonCard.id, result, traitToUse.language, traitToUse.abbr, true);
          }
        } else {
          await pointToDefault(lessonCard.id, dpt);
        }
      }
    }
  } catch (err) {
    logger.error('Bummer, could not generate map audio for lesson card:', lessonCard.id);
    logger.error(err);
  }
};

//
// Main entry point for a single lesson card.
// Could be for one language or all the languages associated with a card.
//
const genTextToSpeechLessonCard = async (genAll, lessonCard, language = null, fixAudio = false) => {
  await initPolly(); // initialize here because this can be called by external API

  await genAudioForCard(genAll, lessonCard, language, null, null, fixAudio);
  if (lessonCard.cardType === 'clickExpand') {
    // create separate audios for each of the expandable fields.
    for (let i = 1; i < 5; i++) {
      await genAudioForCard(genAll, lessonCard, language, `list${i}Detail`, i.toString(), fixAudio);
    }
  } else if (lessonCard.cardType === 'statesMap') {
    await genAudioForMapCard(genAll, lessonCard, language);
  } else if (lessonCard.cardType === 'quizSingleChoice' && lessonCard.isGated) {
    await genAudioForCard(genAll, lessonCard, language, 'choiceFeedback', 'feedback', fixAudio);
  }
};

const genTextToSpeechProtectedTraitsDefaults = async (genAll) => {
  await initPolly();
  const defaultProtectedTraits = await db.protectedTraitsDefaults.findAll({
    include: [{
      model: db.speechAudios,
      where: {
        model: 'protectedTraitsDefault',
      },
      required: false,
    }],
  });
  for (const dpt of defaultProtectedTraits) {
    if (genAll || needToUpdateDpt(dpt)) {
      const result = await createAndUploadAudioFileFromText(
        `ptd_${dpt.language}_${dpt.abbr || ''}_${dpt.id}`,
        getTextFromMapCard(dpt),
        dpt.language,
      );
      if (result) {
        await upsertSpeechRecordsForDpt(dpt, result);
      }
    }
  }
};

//
// Main entry point for the sweeper and the script
// For each card, create the English audio file, create a speechAudio record and a file record
// Then do the same for each translation in contentStrings
//
const genTextToSpeech = async (genAll = false) => {
  // Keep this number fairly small, as the outer joins
  // of speechAudios and contentStrings on map cards will explode the number of records
  const MAX_RECS_PER_ITERATION = 5;
  await genTextToSpeechProtectedTraitsDefaults(genAll);
  const limit = MAX_RECS_PER_ITERATION;
  let offset = 0;
  let lessonCards;
  do {
    lessonCards = await getLessonCards(limit, offset);
    offset += limit;
    for (const lessonCard of lessonCards) {
      await genTextToSpeechLessonCard(genAll, lessonCard);
    }
  } while (lessonCards && lessonCards.length > 0);
};

const getS3FolderListing = async (s3, extraParams) => {
  const params = {
    ...extraParams,
    Bucket: config.s3.bucket,
    Prefix: 'lesson-cards/audio/',
  };
  try {
    const data = await s3.send(new ListObjectsV2Command(params));
    return data;
  } catch (err) {
    console.error("Error listing objects:", err);
    throw err;
  }
};

//
// Removes dangling mp3 files from S3 that don't have a file record
//
const cleanS3Mp3s = async () => {
  try {
    let s3FileListing;
    const params = { MaxKeys: 1000 };
    do {
      s3FileListing = await getS3FolderListing(s3, params);
      params.ContinuationToken = s3FileListing && s3FileListing.NextContinuationToken;

      logger.debug(s3FileListing && s3FileListing.Contents && s3FileListing.Contents.map(f => f.Key));
      if (s3FileListing && s3FileListing.Contents) {
        for (const s3File of s3FileListing.Contents) {
          const file = await db.files.findOne({
            attributes: ['path'],
            where: {
              type: 'audio/mpeg',
              path: {
                [Op.like]: `%${s3File.Key}`,
              },
            },
          });
          if (!file) {
            await removeS3Object(s3, s3File.Key);
          }
        }
      }
    } while (s3FileListing && s3FileListing.IsTruncated);
  } catch (err) {
    logger.error(err);
  }
};

module.exports = {
  genTextToSpeech,
  cleanS3Mp3s,
  genTextToSpeechLessonCard,
  removeS3Object,
};
