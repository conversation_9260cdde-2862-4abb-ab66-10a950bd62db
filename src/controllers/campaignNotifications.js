const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { restQueryToSequelize, findUserIdByEmailAndAccount } = require('../services/utils/userUtils');
const { executeTestNotification, calcCampaignTriggerDate } = require('../services/utils/notificationsUtils');
const {
  mapNotificationTypeToTemplateId,
  getScheduledNotificationRecipients,
  returnCampaignNotification,
  getParticipantStartDate,
  getParticipants,
} = require('../services/utils/campaignUtils');
const { isValidEmailAddress } = require('../services/email');
const { differenceInDays, addDays } = require('date-fns');

const Campaigns = db.campaigns;
const CampaignNotifications = db.campaignNotifications;
const Notifications = db.notifications;
const NotificationRecipients = db.notificationRecipients;
const Users = db.users;

const Op = db.Sequelize.Op;

async function addScheduledNotifications(campaign, notification) {
  const newNotifications = [];
  const participants = await getParticipants(campaign);
  const existingRecipients = await getScheduledNotificationRecipients(notification.id);
  for (const participant of participants) {
    const start = await getParticipantStartDate(campaign, participant);
    const end = addDays(start, campaign.duration);
    const newNots = returnCampaignNotification(start, end, campaign, notification, [participant], existingRecipients);
    for (const n of newNots) {
      newNotifications.push(n);
    }
  }
  await Notifications.bulkCreate(newNotifications, {
    include: [{
      model: NotificationRecipients,
      as: 'recipients',
    }],
  });
}

async function cancelScheduledNotifications(notificationId) {
  // cancel any pending notifications for this campaign notification
  await Notifications.update({
    status: 'cancelled',
  }, {
    where: {
      refId: notificationId,
      status: {
        [Op.or]: ['pending', 'onhold'],
      },
    },
  });
}

async function updateScheduledNotifications(campaignNotification, triggerDateDiff, oldType, newType) {
  let promises = [];
  if (campaignNotification && campaignNotification.campaign &&
      campaignNotification.campaign.type === 'open' &&
      campaignNotification.campaign.status === 'inProgress') {
    if (triggerDateDiff === 0) {
      promises.push(Notifications.update({
        templateId: mapNotificationTypeToTemplateId(campaignNotification.notificationType),
        messageText: campaignNotification.notificationText,
        subjectText: campaignNotification.notificationSubject,
        replyToEmail: campaignNotification.notificationReplyTo,
        fromEmail: campaignNotification.notificationFrom,
        fromName: campaignNotification.notificationFromName,
      }, {
        where: {
          refId: campaignNotification.id,
          status: 'pending',
        },
      }));
    } else {
      const scheduledNotifications = await Notifications.findAll({
        where: {
          refId: campaignNotification.id,
          status: 'pending',
        },
      });
      promises = scheduledNotifications.map((notification) => {
        const newSendDate = addDays(notification.sendDate, triggerDateDiff);
        return Notifications.update({
          templateId: mapNotificationTypeToTemplateId(campaignNotification.notificationType),
          messageText: campaignNotification.notificationText,
          subjectText: campaignNotification.notificationSubject,
          replyToEmail: campaignNotification.notificationReplyTo,
          fromEmail: campaignNotification.notificationFrom,
          fromName: campaignNotification.notificationFromName,
          sendDate: newSendDate,
        }, {
          where: {
            id: notification.id,
          },
        });
      });
    }
    if (oldType === 'completion' && newType !== 'completion') {
      // need to schedule notifications of the newType since completions aren't scheduled
      promises.push(addScheduledNotifications(campaignNotification.campaign, campaignNotification));
    } else if (oldType !== 'completion' && newType === 'completion') {
      // need to remove scheduled notifications of the oldType since completions aren't scheduled
      promises.push(cancelScheduledNotifications(campaignNotification.id));
    }
  }
  return promises;
}

/**
 * @openapi
 * /campaigns/{campaignId}/notifications?$limit={limit}&$skip={skip}:
 *   get:
 *     summary: Campaign Notifications
 *     tags:
 *       - Campaigns
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         description: Campaign ID
 *         schema:
 *           type: integer
 *       - name: $limit
 *         in: query
 *         required: true
 *         description: Number of records per page
 *         schema:
 *           type: integer
 *           example: 10
 *       - name: $skip
 *         in: query
 *         required: true
 *         description: Number of records to skip
 *         schema:
 *           type: integer
 *           example: 0
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/campaignNotifications'
 *                       - type: object
 *                         properties:
 *                           campaign:
 *                             $ref: '#/components/schemas/campaigns'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  const campaign = req.campaign;
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
    where: {
      campaignId: campaign.id,
    },
    include: [
      {
        model: Campaigns,
      },
    ],
  };
  const queryParams = restQueryToSequelize(req.query, defaults);
  queryParams.order = [[...queryParams.order]];

  const finalQuery = {
    ...queryParams,
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  const countQuery = {
    where: _.pick(finalQuery, ['where']).where,
  };

  try {
    const count = await CampaignNotifications.count(countQuery);
    const data = await CampaignNotifications.findAll(finalQuery);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = (req, res) => {
  res.json(req.campaignNotification);
};

/**
 * @openapi
 * /campaigns/{campaignId}/notifications:
 *   post:
 *     summary: Add Notification To Campaign
 *     tags: [Campaigns]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notificationType:
 *                 type: string
 *                 example: "welcome"
 *               notificationTiming:
 *                 type: string
 *                 example: "beforeStart"
 *               timingDays:
 *                 type: integer
 *                 example: 0
 *               notificationText:
 *                 type: string
 *                 example: "<p>You matter! To strengthen our workplace culture...</p>"
 *               notificationSubject:
 *                 type: string
 *                 example: "Test Notification"
 *               notificationFrom:
 *                 type: string
 *                 example: "<EMAIL>"
 *               notificationFromName:
 *                 type: string
 *                 example: "Test User"
 *               notificationReplyTo:
 *                 type: string
 *                 example: "<EMAIL>"
 *               notificationCCManager:
 *                 type: boolean
 *                 example: false
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/campaignNotifications'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  const campaign = req.campaign;
  try {
    req.body.campaignId = campaign.id;
    req.body.triggerDate = calcCampaignTriggerDate(campaign, req.body);
    const newNotification = await CampaignNotifications.create(req.body);
    // for inProgress, open campaigns, schedule new notification for each user
    if (campaign && campaign.type === 'open' &&
        campaign.status === 'inProgress' &&
        newNotification.notificationType !== 'completion') {
      await addScheduledNotifications(campaign, newNotification);
    }
    res.json(newNotification);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /campaigns/{campaignId}/notifications/{campaignNotificationId}:
 *   patch:
 *     summary: Update Notification To Campaign
 *     tags: [Campaigns]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: campaignNotificationId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notificationType:
 *                 type: string
 *                 example: "welcome"
 *               notificationTiming:
 *                 type: string
 *                 example: "beforeStart"
 *               timingDays:
 *                 type: integer
 *                 example: 0
 *               notificationText:
 *                 type: string
 *                 example: "<p>You matter! To strengthen our workplace culture...</p>"
 *               notificationSubject:
 *                 type: string
 *                 example: "Test Notification"
 *               notificationFrom:
 *                 type: string
 *                 example: "<EMAIL>"
 *               notificationFromName:
 *                 type: string
 *                 example: "Test User"
 *               notificationReplyTo:
 *                 type: string
 *                 example: "<EMAIL>"
 *               notificationCCManager:
 *                 type: boolean
 *                 example: false
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               allOf:
 *                 - $ref: '#/components/schemas/campaignNotifications'
 *                 - type: object
 *                   properties:
 *                     campaign:
 *                       $ref: '#/components/schemas/campaigns'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.patch = async (req, res, next) => {
  try {
    const campaign = req.campaignNotification.campaign;
    const oldNotificationType = req.campaignNotification.notificationType;
    const newNotificationType = req.body.notificationType;

    const flattenedRecord = req.campaignNotification.get({ plain: true });
    const oldTriggerDate = req.campaignNotification.triggerDate;
    const newTriggerDate = calcCampaignTriggerDate(campaign, { ...flattenedRecord, ...req.body });
    req.body.triggerDate = newTriggerDate;
    delete req.body.notificationId; // make sure notificationId is not changed
    const updatedNotification = await req.campaignNotification.update(req.body);
    await updateScheduledNotifications(
      updatedNotification,
      differenceInDays(newTriggerDate, oldTriggerDate),
      oldNotificationType,
      newNotificationType,
    );
    res.json(updatedNotification);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /campaigns/{campaignId}/notifications/{campaignNotificationId}:
 *   delete:
 *     summary: Delete Notification To Campaign
 *     tags: [Campaigns]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: campaignNotificationId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               allOf:
 *                 - $ref: '#/components/schemas/campaignNotifications'
 *                 - type: object
 *                   properties:
 *                     campaign:
 *                       $ref: '#/components/schemas/campaigns'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */


module.exports.delete = async (req, res, next) => {
  const campaignNotification = req.campaignNotification;

  try {
    if (campaignNotification.campaign.type === 'open') {
      // cancel any pending notifications for this campaign notification
      await cancelScheduledNotifications(campaignNotification.id);
    } else if (campaignNotification.notificationId !== null) {
      // cancel any pending notifications for this campaign notification
      await Notifications.update({
        status: 'cancelled',
      }, {
        where: {
          id: campaignNotification.notificationId,
          status: {
            [Op.or]: ['pending', 'onhold'],
          },
        },
      });
    }
    const data = await campaignNotification.destroy();

    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.test = async (req, res, next) => {
  try {
    let emails = req.body.emails;
    // fall back to logged in user's email if emails not properly specified
    if (_.isNil(emails) || emails.length <= 0) {
      emails = [req.user.email];
    }
    // validate email address syntax
    emails.forEach((email) => {
      if (!isValidEmailAddress(email)) {
        const err = new Error(req.i18n.t('campaigns.invalid_email_Error', { email }));
        err.status = 400;
        throw err;
      }
    });
    // only send test email to addresses that are associated with the account or with superAdmins on account 1
    const validAccountEmails = [];
    for (const email of emails) {
      const user = await findUserIdByEmailAndAccount(req, email, req.user.accountId, true);
      if (user) {
        validAccountEmails.push(email);
      } else {
        const superAdminUser = await Users.findOne({
          paranoid: true,
          attributes: ['email'],
          where: {
            email: email.toLowerCase(),
          },
          include: {
            model: db.accountUsers,
            required: true,
            where: {
              roleId: {
                [Op.in]: [5, 6],
              },
              accountId: 1,
            },
          },
        });
        if (superAdminUser) {
          validAccountEmails.push(email);
        }
      }
    }

    const templateId = mapNotificationTypeToTemplateId(req.body.notificationType);

    let promises = [];
    for (const email of validAccountEmails) {
      const singleEmailArray = [email];
      promises.push(executeTestNotification(
        singleEmailArray, templateId, req.body.notificationText,
        req.body.notificationSubject, req.body.notificationFrom,
        req.body.notificationFromName, req.body.notificationReplyTo, req.user.accountId,
      ));
      if (promises.length > 20) {
        // clear promises every 20
        await Promise.all(promises);
        promises = [];
      }
    }
    const data = await Promise.all(promises);
    res.json(data);
  } catch (err) {
    next(err);
  }
};
// Middleware to retrieve the campaignNotification when an id is passed in the route
module.exports.campaignNotificationById = async function (req, res, next, id) {
  try {
    const campaignNotification = await CampaignNotifications.findByPk(id, {
      include: [
        {
          model: Campaigns,
        },
      ],
    });
    if (!campaignNotification) {
      const err = new Error(req.i18n.t('campaigns.notification_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.campaignNotification = campaignNotification;
    next();
  } catch (err) {
    next(err);
  }
};
