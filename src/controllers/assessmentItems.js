const _ = require('lodash');
const db = require('../db');

const config = require('../config/config');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { restSortToSequelize } = require('../services/utils/resourceUtils');
const { validateQuestionType, validateHealthyResponses,
  validateSjt } = require('../services/utils/assessmentItemUtils');

const AssessmentItem = db.assessmentItems;
const Indicators = db.socialCapitalIndicators;

const getIncludeParams = async () => {
  const includeParams = [
    {
      model: db.socialCapitalIndicators,
      attributes: ['id', 'name'],
    },
    {
      model: db.users,
      attributes: ['id', 'firstName', 'lastName'],
      as: 'assessmentItemCreatedBy',
    },
    {
      model: db.users,
      attributes: ['id', 'firstName', 'lastName'],
      as: 'assessmentItemUpdatedBy',
    },
  ];
  return includeParams;
};

const validateAssessmentItem = async (assessmentId) => {
  const assessmentItemData = await AssessmentItem.findOne({ where: { id: assessmentId } });
  if (!assessmentItemData) {
    const error = new Error('AssessmentItem not found');
    error.status = 404;
    throw error;
  }
};

/**
 * converts a rest query from the client to sequelize params
 */
const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize(_.omit(
    query,
    ['$limit', '$skip', '$sort', 'accountId', 'downloadLanguage', 'includeLessons', 'startDate', 'endDate'],
  ));
  const newQuery = {
    ...defaults,
  };
  // eslint-disable-next-line no-undef
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    const paginateMax = 700;
    const limit = parseInt(query.$limit);
    newQuery.limit = Math.min(paginateMax, limit);
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  return newQuery;
};

/**
 * @openapi
 * /assessment-items:
 *   post:
 *     summary: Create Assessment Item
 *     tags:
 *       - AssessmentItems
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/createAssessmentItem'
 *     responses:
 *       200:
 *         description: AssessmentItem added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 message: "AssessmentItem created successfully"
 *
 * components:
 *   schemas:
 *     createAssessmentItem:
 *       type: object
 *       description: Model to add AssessmentItem
 *       properties:
 *         assessmentType:
 *           type: string
 *           description: The assessment Type of the Assessment item (Benchmark/Knowledge/Survey/Climate)
 *         questionType:
 *           type: string
 *           description: The question type of the Assessment item (quizBoolean/quizSlider/quizSingleChoice/quizMultiChoice/quizFreeformText/quizColorSpectrum)
 *         questionText:
 *           type: string
 *           description: The question text of the Assessment item
 *         auditQuestion:
 *           type: boolean
 *           description: auditQuestion of the Assessment item
 *         response1:
 *           type: string
 *           description: response1 of the Assessment item
 *         response2:
 *           type: string
 *           description: response2 of the Assessment item
 *         response3:
 *           type: string
 *           description: response3 of the Assessment item
 *         response4:
 *           type: string
 *           description: response4 of the Assessment item
 *         healthyResponse:
 *           type: string
 *           description: healthyResponse of the Assessment item
 *         indicatorId:
 *           type: number
 *           description: indicatorId of the Assessment item
 *         isSJT:
 *           type: boolean
 *           description: isSJT of the Assessment item
 *       required:
 *         - questionType
 */
module.exports.create = async (req, res, next) => {
  let newAssessmentItem;
  try {
    await db.sequelize.transaction(async (transact) => {
      validateQuestionType(req.body.assessmentType, req.body.questionType);
      validateHealthyResponses(req.body.assessmentType, req.body.questionType, req.body.healthyResponse);
      validateSjt(req.body.isSJT, req.body.assessmentType);
      req.body.createdBy = req.user.id;
      newAssessmentItem = await AssessmentItem.create(req.body, {
        transaction: transact,
      });
    });
    res.json(newAssessmentItem);
  } catch (err) {
    if (err.message === 'Validation error') {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /assessment-items/{assessmentId}:
 *   post:
 *     summary: Update Assessment Item
 *     tags:
 *       - AssessmentItems
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: assessmentId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the Assessment Item to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/updateAssessmentItem'
 *     responses:
 *       200:
 *         description: AssessmentItem updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 message: "AssessmentItem updated successfully"
 *
 * components:
 *   schemas:
 *     updateAssessmentItem:
 *       type: object
 *       description: Model to update AssessmentItem
 *       properties:
 *         assessmentType:
 *           type: string
 *           description: The assessment Type of the Assessment item (Benchmark/Knowledge/Survey/Climate)
 *         questionType:
 *           type: string
 *           description: The question type of the Assessment item (quizBoolean/quizSlider/quizSingleChoice/quizMultiChoice/quizFreeformText/quizColorSpectrum)
 *         questionText:
 *           type: string
 *           description: The question text of the Assessment item
 *         auditQuestion:
 *           type: boolean
 *           description: Whether it's an audit question
 *         response1:
 *           type: string
 *           description: Response option 1
 *         response2:
 *           type: string
 *           description: Response option 2
 *         response3:
 *           type: string
 *           description: Response option 3
 *         response4:
 *           type: string
 *           description: Response option 4
 *         healthyResponse:
 *           type: string
 *           description: Healthy response for the item
 *         indicatorId:
 *           type: number
 *           description: Related indicator ID
 *         isSJT:
 *           type: boolean
 *           description: Whether the item is a Situational Judgment Test (SJT)
 *       required:
 *         - questionType
 */
module.exports.patch = async (req, res, next) => {
  try {
    const { assessmentId } = req.params;
    await validateAssessmentItem(assessmentId);
    validateQuestionType(req.body.assessmentType, req.body.questionType);
    validateHealthyResponses(req.body.assessmentType, req.body.questionType, req.body.healthyResponse);
    validateSjt(req.body.isSJT, req.body.assessmentType);
    req.body.updatedBy = req.user.id;
    await AssessmentItem.update(req.body, {
      where: {
        id: assessmentId,
      },
    });
    const finalAssessmentItem = await AssessmentItem.findByPk(assessmentId, {
      include: await getIncludeParams(),
    });
    res.json(finalAssessmentItem);
  } catch (err) {
    if (err.message === 'Validation error') {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /assessmentitems:
 *   get:
 *     summary: Fetch all the Assessment Item Values
 *     tags:
 *       - AssessmentItems
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successfully fetched all Assessment Items
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/AssessmentItem'
 * components:
 *   schemas:
 *     AssessmentItem:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         assessmentType:
 *           type: string
 *           description: Type of assessment
 *         questionType:
 *           type: string
 *           description: Type of the question
 *         questionText:
 *           type: string
 *           description: Question content
 *         auditQuestion:
 *           type: boolean
 *         response1:
 *           type: string
 *         response2:
 *           type: string
 *         response3:
 *           type: string
 *         response4:
 *           type: string
 *         healthyResponse:
 *           type: string
 *         indicatorId:
 *           type: number
 *         isSJT:
 *           type: boolean
 */
module.exports.list = async (req, res, next) => {
  try {
    const defaults = {
      order: [['updatedAt', 'DESC']],
      limit: config.paginate.default,
      offset: 0,
    };
    if (req.query.isSJT) {
      req.query.isSJT = (req.query.isSJT === 'true');
    }
    if (req.query.auditQuestion) {
      req.query.auditQuestion = (req.query.auditQuestion === 'true');
    }
    const queryParams = restQueryToSequelize(req.query, defaults);
    queryParams.order = [[...queryParams.order]];
    const finalQuery = {
      ...queryParams,
      where: queryParams.where,
      include: await getIncludeParams(),
    };
    const pagedResult = {
      limit: queryParams.limit,
      skip: queryParams.offset,
    };
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
      include: _.pick(finalQuery, ['include']).include,
      distinct: true,
    };
    const count = await AssessmentItem.count(countQuery);
    const assessmentItemData = await AssessmentItem.findAll(finalQuery);
    for (const assessmentItem of assessmentItemData) {
      if (assessmentItem && assessmentItem.assessmentItemCreatedBy) {
        assessmentItem.createdByUser = assessmentItem.assessmentItemCreatedBy;
      }
      if (assessmentItem && assessmentItem.assessmentItemUpdatedBy) {
        assessmentItem.updatedByUser = assessmentItem.assessmentItemUpdatedBy;
      }
    }
    res.json({ total: count, ...pagedResult, assessmentItemData });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /assessment-items/{assessmentId}:
 *   get:
 *     summary: Fetch Assessment Item based on assessmentId
 *     tags:
 *       - AssessmentItems
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: assessmentId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the assessment item to fetch
 *     responses:
 *       200:
 *         description: Successfully fetched particular Assessment Item based on its ID
 *       404:
 *         description: Assessment item not found
 *       400:
 *         description: Bad request
 */
module.exports.read = async (req, res, next) => {
  try {
    const assessmentItemData = req.assessmentItem.get({ plain: true });
    res.json(assessmentItemData);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the assessmentItem when an id is passed in the route
module.exports.assessmentItemById = async function (req, res, next, id) {
  const queryParams = {
    where: { id },
    include: await getIncludeParams(),
  };
  try {
    const finalQuery = {
      ...queryParams,
      where: { ...queryParams.where },
    };
    const assessmentItem = await AssessmentItem.findOne(finalQuery);
    if (assessmentItem && assessmentItem.assessmentItemCreatedBy) {
      assessmentItem.createdByUser = assessmentItem.assessmentItemCreatedBy;
    }
    if (assessmentItem && assessmentItem.assessmentItemUpdatedBy) {
      assessmentItem.updatedByUser = assessmentItem.assessmentItemUpdatedBy;
    }
    if (!assessmentItem) {
      const err = new Error(req.i18n.t('assessmentItems.assessmentItem_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.assessmentItem = assessmentItem;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /assessment-items/indicatorsMasterData:
 *   get:
 *     summary: Fetch all the Indicators Values
 *     tags:
 *       - AssessmentItems
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successfully fetched all Indicators data
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 */
module.exports.getAllIndicators = async (req, res, next) => {
  try {
    const indicatorsList = await Indicators.findAll({
      attributes: ['id', 'name'],
      order: [['name', 'ASC']],
    });
    res.json(indicatorsList);
  } catch (err) {
    next(err);
  }
};
