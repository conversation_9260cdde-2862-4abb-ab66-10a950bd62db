const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { restQueryToSequelize } = require('../services/utils/userUtils');
const { reorderLLCs } = require('../services/utils/lessonLessonCardUtils');
const { launchedByUsersCount } = require('../services/utils/lessonUtils');

const LessonLessonCards = db.lessonLessonCards;
const LessonCards = db.lessonCards;
const Lessons = db.lessons;
const SupportedLanguages = db.supportedLanguages;
const ContentStrings = db.contentStrings;
const LastViewedLessonCard = db.lastViewedLessonCards;
const Sequelize = db.Sequelize;

// fetches the lessonLessonCard record with id = bindingId
// joins in Lesson and LessonCard
async function lookuplessonCardBinding(bindingId) {
  return LessonLessonCards.findByPk(bindingId, {
    include: [{
      model: Lessons,
    }, {
      model: LessonCards,
    }],
  });
}
module.exports.list = async (req, res, next) => {
  const lesson = req.lesson;
  const defaults = {
    order: [['position', 'ASC']],
    limit: config.paginate.default,
    offset: 0,
    where: { lessonId: lesson.id },
  };
  const queryParams = restQueryToSequelize(req.query, defaults);
  queryParams.order = [[...queryParams.order]];

  const finalQuery = {
    ...queryParams,
    include: [{
      model: Lessons,
    }, {
      model: LessonCards,
    }],
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };

  try {
    const count = await LessonLessonCards.count({ where: { lessonId: lesson.id } });
    const data = await LessonLessonCards.findAll(finalQuery);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res) => {
  res.json(req.lessonCardBinding);
};

module.exports.create = async (req, res, next) => {
  const newBinding = {
    lessonId: req.lesson.id,
    lessonCardId: req.body.cardId,
    position: req.body.position,
    isOwner: req.body.isOwner,
  };

  const lessonCompleteCardBindingId = req.body.lessonCompleteCardBindingId;

  try {
    const oldBinding = await LessonLessonCards.findOne({
      where:
        {
          lessonId: newBinding.lessonId,
          lessonCardId: newBinding.lessonCardId,
        },
    });

    if (oldBinding) {
      const id = newBinding.lessonId;
      let err = null;
      if (oldBinding.dateRemoved) {
        err = new Error(req.i18n.t('lessonCards.card_removed_cannot_be_added_Error', { id }));
      } else {
        err = new Error(req.i18n.t('lessonCards.card_already_exists_Error', { id }));
      }
      err.status = 400;
      throw err;
    }

    // if this lesson has already been published, we should record the dateAdded for the lessonCard
    const launchByUsersCount = await launchedByUsersCount(req.lesson.id);
    if (launchByUsersCount > 0) {
      newBinding.dateAdded = Date.now();
      // delete lastViewedLessonCards records
      await LastViewedLessonCard.destroy({ where: { lessonId: req.lesson.id } });
    }
    const lessonCard = await LessonCards.findOne({ where: { id: req.body.cardId } });

    if (lessonCard) {
      if (lessonCard.cardType === 'lessonComplete') {
        const err = new Error(req.i18n.t('lessonCards.cannot_add_lessonComplete_Error'));
        err.status = 400;
        throw err;
      }
    } else {
      const id = req.body.cardId;
      const err = new Error(req.i18n.t('lessonCards.lessonCard_load_Error', { id }));
      err.status = 400;
      throw err;
    }

    const oldItems = await LessonLessonCards.findAll({
      where: {
        lessonId: newBinding.lessonId,
      },
      order: [['position', 'ASC']],
    });

    if (_.isNil(newBinding.position)) {
      // figure out where to stick the newbie
      if (_.isNil(newBinding.position) || newBinding.position > oldItems.length) {
        newBinding.position = oldItems.length + 1;
      } else if (newBinding.position < 1) {
        newBinding.position = 1;
      }

      if (newBinding.position < oldItems.length) {
        await reorderLLCs(req.lesson.id, oldItems.length + 1, newBinding.position);
      }
    }

    const createdBinding = await LessonLessonCards.create(newBinding);
    const createdItem = await lookuplessonCardBinding(createdBinding.id);

    if (createdBinding) {
      // add lesson card supportedLanguages to LESSON
      const contentStringLangs = await ContentStrings.findAll({
        where: {
          model: 'lessonCard',
          contentId: req.body.cardId,
        },
        attributes: [Sequelize.fn('DISTINCT', Sequelize.col('language')), 'language'],
      });
      contentStringLangs.forEach(async (cs) => {
        const exsitingSupportedLanguage = await SupportedLanguages.findOne({
          where: {
            language: cs.language,
            langSupportable: 'lesson',
            langSupportableId: req.lesson.id,
          },
        });
        if (!exsitingSupportedLanguage) {
          await SupportedLanguages.create({
            language: cs.language,
            langSupportable: 'lesson',
            langSupportableId: req.lesson.id,
          });
        }
      });
      // move lessonCompleteCard to end
      const lessonCompleteCardBinding = await LessonLessonCards.findByPk(lessonCompleteCardBindingId);
      await lessonCompleteCardBinding.update({ position: lessonCompleteCardBinding.position + 1 });
    }

    res.json(createdItem);
  } catch (err) {
    next(err);
  }
};

module.exports.patch = async (req, res) => {
  const lessonId = req.lesson.id;
  const newItem = req.body;
  const oldItem = req.lessonCardBinding;

  await reorderLLCs(lessonId, oldItem.position, newItem.position);
  const updatedItem = await oldItem.update(newItem);
  const data = await lookuplessonCardBinding(updatedItem.id);
  res.json(data);
};

module.exports.delete = async (req, res, next) => {
  try {
    if (req.lessonCardBinding.dateAdded) {
      const id = req.lessonCardBinding.lessonId;
      const err = new Error(req.i18n.t('lessonCards.card_added_cannot_be_removed_Error', { id }));
      err.status = 400;
      throw err;
    }
    await reorderLLCs(req.lesson.id, req.lessonCardBinding.position);
    let data = null;
    const launchByUsersCount = await launchedByUsersCount(req.lesson.id);
    if (launchByUsersCount > 0) {
      req.lessonCardBinding.dateRemoved = Date.now();
      data = req.lessonCardBinding.save();
    } else {
      data = await req.lessonCardBinding.destroy();
    }
    res.json(data);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve a specific campaign member when an id is passed in the route
module.exports.cardById = async function (req, res, next, id) {
  const lessonId = req.lesson.id;
  try {
    const queryParams = {
      where: {
        lessonCardId: id,
        lessonId,
      },
      include: [{
        model: Lessons,
      }, {
        model: LessonCards,
      }],
    };
    const lessonCardBinding = await LessonLessonCards.findOne(queryParams);
    if (!lessonCardBinding) {
      const err = new Error(req.i18n.t('lessonCards.lessonCard_binding_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.lessonCardBinding = lessonCardBinding;
    next();
  } catch (err) {
    next(err);
  }
};

module.exports.byId = async function (req, res, next, bindingId) {
  const lessonId = req.lesson.id;
  try {
    const queryParams = {
      where: {
        id: bindingId,
        lessonId,
      },
      include: [{
        model: Lessons,
      }, {
        model: LessonCards,
      }],
    };
    const lessonCardBinding = await LessonLessonCards.findOne(queryParams);
    if (!lessonCardBinding) {
      const id = bindingId;
      const err = new Error(req.i18n.t('lessonCards.lessonCard_binding_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.lessonCardBinding = lessonCardBinding;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * definitions:
 *  lessonLessonCards:
 *    type: object
 *    properties:
 *      id:
 *        type: integer
 *      lessonId:
 *        type: integer
 *      lessonCardId:
 *        type: integer
 *      position:
 *        type: integer
 *      createdAt:
 *        type: string
 *      updatedAt:
 *        type: string
 *      isOwner:
 *        type: boolean
 *      dateAdded:
 *        type: string
 *      dateRemoved:
 *        type: string
 *      contentStrings:
 *        type: array
 *      userId:
 *        type: integer
 *      enrollmentId:
 *        type: integer
 */
