const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { getAllowedPermissions } = require('../services/acl/acl');

const Topic = db.topics;
const ResourceTopic = db.resourceTopics;

const restSortToSequelize = (restSort) => {
  return _.map(restSort, (value, key) => {
    return [key, value === '-1' ? 'DESC' : 'ASC'];
  });
};

const restQueryToSequelize = (query, defaults) => {
  const whereClause = _.omit(query, ['$limit', '$skip', '$sort']);

  const newQuery = {
    ...defaults,
  };
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  return newQuery;
};

/**
 * @openapi
 * /topics:
 *   get:
 *     summary: Get All Topics
 *     tags:
 *       - Topics
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *         description: limit (integer) for number of records
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *         description: skip (integer) for number of records
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                   description: total number of records.
 *                 data:
 *                   type: array
 *                   description: Array of report data object.
 *                   items:
 *                     $ref: '#/components/schemas/topics'
 */
module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };
  const queryParams = restQueryToSequelize(req.query, defaults);

  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };

  try {
    const count = await Topic.count();
    const data = await Topic.findAll(queryParams);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /topics/{topicId}:
 *   get:
 *     summary: View Topic By topicId
 *     tags: [Topics]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: topicId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/topics'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.read = (req, res) => {
  res.json(req.topic);
};

/**
 * @openapi
 * /topics:
 *   post:
 *     summary: Add new topic
 *     tags:
 *       - Topics
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Required parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Test Topic title"
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/topics'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  const title = req.body.title;

  try {
    const newTopic = await Topic.create({ title });
    res.json(newTopic);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /topics/{topicId}:
 *   patch:
 *     summary: Update Topic
 *     tags:
 *       - Topics
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: topicId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Update topic."
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/topics'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.patch = async (req, res, next) => {
  const topic = req.topic;

  try {
    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'topics', req.tokenPayload);
    if (!allowedPermissions.topics.includes('update')) {
      const err = new Error(req.i18n.t('topics.topic_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    const updatedTopic = await topic.update(req.body);
    res.json(updatedTopic);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /topics/{topicId}:
 *   delete:
 *     summary: Delete Topic
 *     tags:
 *       - Topics
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: topicId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/topics'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  const topic = req.topic;

  try {
    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'topics', req.tokenPayload);
    if (!allowedPermissions.topics.includes('update')) {
      const err = new Error(req.i18n.t('topics.topic_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    const data = await topic.destroy();

    // remove relationship records since onDelete: 'cascade' doesn't work without creating the tables with
    // the relevant constraints.
    await ResourceTopic.destroy({
      where: {
        topicId: topic.id,
      },
    });
    res.json(data);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the topic when an id is passed in the route
module.exports.topicById = async function (req, res, next, id) {
  try {
    const topic = await Topic.findByPk(id);
    if (!topic) {
      const err = new Error(req.i18n.t('topics.topic_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.topic = topic;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * components:
 *   schemas:
 *     topics:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         title:
 *           type: string
 *         resourceTopics:
 *           $ref: '#/components/schemas/resourceTopics'

 *     resourceTopics:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         resourceId:
 *           type: integer
 *         topicId:
 *           type: integer
 *         order:
 *           type: integer
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 */
