const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { getAllowedPermissions } = require('../services/acl/acl');

const Tag = db.tags;
const ResourceTag = db.resourceTags;

const restSortToSequelize = (restSort) => {
  return _.map(restSort, (value, key) => {
    return [key, value === '-1' ? 'DESC' : 'ASC'];
  });
};

const restQueryToSequelize = (query, defaults) => {
  const whereClause = _.omit(query, ['$limit', '$skip', '$sort']);

  const newQuery = {
    ...defaults,
  };
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  return newQuery;
};

module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };
  const queryParams = restQueryToSequelize(req.query, defaults);

  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };

  try {
    const count = await Tag.count();
    const data = await Tag.findAll(queryParams);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = (req, res) => {
  res.json(req.tag);
};

module.exports.create = async (req, res, next) => {
  const text = req.body.text;

  try {
    const newTag = await Tag.create({ text });
    res.json(newTag);
  } catch (err) {
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  const tag = req.tag;

  try {
    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'tags', req.tokenPayload);
    if (!allowedPermissions.tags.includes('update')) {
      const err = new Error(req.i18n.t('tags.tag_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    const updatedTag = await tag.update(req.body);
    res.json(updatedTag);
  } catch (err) {
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  const tag = req.tag;

  try {
    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'tags', req.tokenPayload);
    if (!allowedPermissions.tags.includes('update')) {
      const err = new Error(req.i18n.t('tags.tag_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    const data = await tag.destroy();

    // remove relationship records since onDelete: 'cascade' doesn't work without creating the tables with
    // the relevant constraints.
    await ResourceTag.destroy({
      where: {
        tagId: tag.id,
      },
    });
    res.json(data);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the tag when an id is passed in the route
module.exports.tagById = async function (req, res, next, id) {
  try {
    const tag = await Tag.findByPk(id);
    if (!tag) {
      const err = new Error(req.i18n.t('tags.tag_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.tag = tag;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * definitions:
 *  tags:
 *    type: object
 *    properties:
 *      id:
 *        type: integer
 *      text:
 *        type: string
 *      createdAt:
 *        type: string
 *      updatedAt:
 *        type: string
 *      resourceTags:
 *        $ref: '#/definitions/resourceTags'
 *
 *  resourceTags:
 *    type: object
 *    properties:
 *      id:
 *        type: integer
 *      resourceId:
 *        type: integer
 *      tagId:
 *        type: integer
 *      order:
 *        type: integer
 *      createdAt:
 *        type: string
 *      updatedAt:
 *        type: string
 */
