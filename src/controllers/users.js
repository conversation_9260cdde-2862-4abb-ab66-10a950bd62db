/* eslint-disable max-len */
const _ = require('lodash');
const fs = require('fs');
const moment = require('moment');
const csvWriter = require('csv-write-stream');
const puppeteer = require('puppeteer');
const userUtils = require('../services/utils/userUtils');
const { UnprocessableEntityError } = require('../services/utils/errors');
const { isSCORMIntegrationType, isScormAccount } = require('../services/utils/accountUtils');
const { restSortToSequelize } = require('../services/utils/resourceUtils');
const config = require('../config/config');
const db = require('../db');
const { getAllowedPermissions } = require('../services/acl/acl');
const { processCampaignsForUserComingOffLeave,
  deactivateOrphanedUserLessonsForDeletedUser } = require('../services/utils/campaignUtils');
const { validatePassword } = require('../services/utils/authManagementUtils');
const { genNewCSV } = require('../services/utils/reportingUtils');
const differenceInSeconds = require('date-fns/difference_in_seconds');
const { getValidStateCode, getValidCountryCode } = require('../services/utils/iso3166Utils');
const { addUserToAccountRoster } = require('../services/utils/groupUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { getTranscriptReport } = require('../services/pdf/transcript');
const { sendSFPlatformEvent } = require('../services/utils/salesforceUtils');
const { getLaunchBrowser } = require('../services/utils/fileUtils');

const User = db.users;
const Role = db.roles;
const UserRole = db.userRoles;
const Accounts = db.accounts;
const AccountUser = db.accountUsers;
const AccountFields = db.accountFields;
const UserAccountFields = db.userAccountFields;
const CsvSetting = db.csvSettings;
const CsvColumn = db.csvColumns;
const Group = db.groups;
const RoleGroup = db.roleGroups;
const marketingEmailOptin = db.marketingEmailsOptIns;
const QuestionAnswers = db.questionAnswers;
const UserLessons = db.userLessons;
const LastViewedLessonCards = db.lastViewedLessonCards;
const Events = db.events;
const ViewLessonCardEvents = db.viewLessonCardEvents;
const Op = db.Sequelize.Op;

const defaultCSVSettingColHeaders = ['firstName', 'lastName', 'email', 'notificationEmail', 'status', 'title', 'role',
  'city', 'location', 'supervisor', 'hireDate', 'employeeId', 'managerEmail'];

const segmentationFieldsColHeaders = ['jobGroup', 'department', 'orgLevel', 'remote', 'gender', 'birthDate', 'raceEthnicity', 'veteranStatus',
  'sexualOrientation', 'disabilityStatus', 'attritionDate', 'voluntaryAttrition'];

async function checkDeletePermissions(req) {
  const accountId = req.requestedUser.accounts[0].id;
  // To delete user, you need one of the following:
  // 'delete' permissions
  // 'deleteAccount' permissions AND the requested user is in the same account
  // 'deleteOwn' permissions AND you're the owner
  const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'users', req.tokenPayload);
  if (!(allowedPermissions.users.includes('delete') ||
    (allowedPermissions.users.includes('deleteAccount') && req.user.accountId === accountId) ||
    (allowedPermissions.users.includes('deleteOwn')
      && userUtils.isOwner(req)))) {
    const err = new Error(req.i18n.t('users.user_no_permissions_Error'));
    err.status = 401;
    throw err;
  }
}

async function validateDelete(req) {
  const force = !!(req.query.force && req.query.force === 'true');
  const hard = !!(req.query.hard && req.query.hard === 'true');
  const userId = req.requestedUser.id;
  if (userUtils.isOwner(req)) {
    // can't delete yourself
    const err = new Error(req.i18n.t('users.cant_delete_yourself'));
    err.status = 401;
    throw err;
  }
  if (force || hard) {
    // can't wipe user with completed assignments
    const completedAssignments = await userUtils.getUsersCompletedAssignments(userId);
    // eslint-disable-next-line no-use-before-define
    const eligibleForDeletion = await determineDeletionEligibility(userId);
    if (completedAssignments.length > 0 || !eligibleForDeletion) {
      const err = new Error(req.i18n.t('users.failed_to_delete_user'));
      err.status = 401;
      throw err;
    }
  }
}

const lessonCardTitleHelper = (cardId, title, cardType) => {
  const retVal = (title === null || title === '') ? _.startCase(cardType) : title;
  return `(${cardId}) ${retVal}`;
};

const decodeQueryParams = (queryParams) => {
  for (const qp of Object.keys(queryParams)) {
    if (typeof (queryParams[qp]) === 'object' && !['lastActivityAt', 'hireDate', 'createdAt', 'attritionDate'].includes(qp)) {
      let value = queryParams[qp][Object.getOwnPropertySymbols(queryParams[qp])[0]] || queryParams[qp].$like;
      if (Array.isArray(value)) {
        // eslint-disable-next-line no-param-reassign
        queryParams[qp] = { [Op.in]: value };
      } else if (value) {
        value = value.slice(1, -1);
        value = decodeURI(value);
        // eslint-disable-next-line no-param-reassign
        queryParams[qp] = { [Op.like]: `%${value}%` };
      }
    }
  }
  return queryParams;
};

// sorting based on userStatus
const userStatusSorting = (queryOrder) => {
  const statusFilter = [];
  // eslint-disable-next-line no-param-reassign, array-callback-return, consistent-return
  queryOrder.filter((el) => {
    if (el[0] === 'status') {
      // eslint-disable-next-line no-param-reassign
      statusFilter.push(['leaveStartedAt', el[1]]);
      statusFilter.push(['deletedAt', el[1]]);
    } else {
      return el;
    }
  });
  return statusFilter;
};

/**
 * Calculates time spent per lessonCard viewing
 * TODO: finish docs
 *
 * @param {{createdAt: Date, type: String, trackableId: Number, 'lessonCard.cardType': String, 'lessonCard.title': String, 'lessonCard.lessons.title': String}[]} lcEvents a series of events in
 * reverse chronological order. See calcTimePerLessonCard function documentation for more info on
 * lessonCard view events.
 * @returns {{timeStamp: Date, type: 'viewLessonCard', metaData: {start: Date, end: Date, lessonCard: {title: String, lessonTitle: String}}}[]}
 */
const calcLessonCardViews = (lcEvents) => {
  const returnData = [];
  const workingEvents = {};

  for (const event of lcEvents) {
    if (event.type === 'viewcontinue') {
      if (!Object.keys(workingEvents).includes(event.lessonCardId.toString())) {
        workingEvents[event.lessonCardId] = {
          end: event.updatedAt,
        };
      }
    }
    if (event.type === 'view') {
      if (workingEvents[event.lessonCardId]) {
        workingEvents[event.lessonCardId].start = event.createdAt;
      } else {
        // orphaned event
        workingEvents[event.lessonCardId] = {
          start: event.createdAt,
        };
      }
      workingEvents[event.lessonCardId].lessonCard = {
        title: lessonCardTitleHelper(event.lessonCardId, event['lessonCard.title'], event['lessonCard.cardType']),
        lessonTitle: event['lessonCard.lessons.title'],
      };
      returnData.push({
        timeStamp: workingEvents[event.lessonCardId].start,
        type: 'viewLessonCard',
        metaData: workingEvents[event.lessonCardId],
      });
      delete workingEvents[event.lessonCardId];
    }
  }
  return returnData;
};
/**
 * Calculates time spent per Video viewing
 * TODO: finish docs
 *
 * videoEvents: a series of events in chronological order.
 * @param {{createdAt: Date, type: String, trackableId: Number, 'lessonCard.cardType': String, 'lessonCard.title': String, 'lessonCard.lessons.title': String}[]}
 * @returns {{timeStamp: Date, type: 'viewVideo', metaData: {start: Date, end: Date, duration: Number, lessonCard: {title: String, lessonTitle: String}}}[]}
 */
const calcVideoViews = (videoEvents) => {
  const returnData = [];
  const workingEvents = {};

  for (const event of videoEvents) {
    if (event.type === 'start') {
      if (!Object.keys(workingEvents).includes(event.trackableId.toString())) {
        workingEvents[event.trackableId] = {
          start: event.createdAt,
          lastStart: event.createdAt,
          duration: 0,
        };
      } else {
        workingEvents[event.trackableId].start = event.createdAt;
      }
    }
    if (event.type === 'pause') {
      if (workingEvents[event.trackableId] && workingEvents[event.trackableId].lastStart) {
        workingEvents[event.trackableId].duration += differenceInSeconds(event.createdAt, workingEvents[event.trackableId].lastStart);
      }
    }
    if (event.type === 'resume') {
      if (workingEvents[event.trackableId]) {
        workingEvents[event.trackableId].lastStart = event.createdAt;
      }
    }
    if (event.type === 'stop') {
      if (workingEvents[event.trackableId]) {
        workingEvents[event.trackableId].end = event.createdAt;
        workingEvents[event.trackableId].duration += differenceInSeconds(event.createdAt, workingEvents[event.trackableId].lastStart);
        workingEvents[event.trackableId].lessonCard = {
          title: lessonCardTitleHelper(event.trackableId, event['lessonCard.title'], event['lessonCard.cardType']),
          lessonTitle: event['lessonCard.lessons.title'],
        };
        returnData.push({
          timeStamp: workingEvents[event.trackableId].start,
          type: 'viewVideo',
          metaData: workingEvents[event.trackableId],
        });
        delete workingEvents[event.trackableId];
      }
    }
  }
  return returnData;
};

/**
 * convert the event.type to the type expected by the client
 *
 * @param {string} type the event type
 * @param {boolean} action whether the resource was created or deleted
 * @returns
 */
const getType = (type, action) => {
  if (type === 'userLessonAssigned') {
    if (action) {
      return 'assigned';
    }
    return 'deleted';
  }
  if (type === 'userLessonStarted') {
    return 'continued';
  }
  if (type === 'userLessonCompleted') {
    return 'completed';
  }
  return 'resourceView';
};

const updateUserRole = async (user, role) => {
  const { hrPersonnelRiskId, businessComplianceRiskId } = await userUtils.analyticsUserRoleData();
  const excludedRoleIds = [hrPersonnelRiskId, businessComplianceRiskId].filter(id => id !== null);

  const roleRecord = await Role.findOne({
    where: {
      name: role,
    },
  });
  if (roleRecord) {
    const roleId = roleRecord.id;
    const userRole = await UserRole.update(
      {
        roleId,
      },
      {
        where: {
          userId: user.id,
          roleId: {
            [Op.notIn]: excludedRoleIds,
          },
        },
        fields: ['roleId'],
      },
    );
    const accountUserRecord = await AccountUser.findOne({
      where: {
        userId: user.id,
      },
    });
    if (accountUserRecord) {
      await accountUserRecord.update({
        roleId,
      });
    }
    return userRole;
  }
  return null;
};

const restQueryToSequelize = async (reqUser, query) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };
  const unpaginatedQuery = restOperatorsToSequelize(_.omit(query, ['$limit', '$skip', '$sort', 'roles', 'filterEmtrainAdmin', 'userTimeZone']));
  let newQuery = {};
  let includes;
  if (query) {
    newQuery = { ...defaults, ...unpaginatedQuery };
    // override defaults for limit, skip, sort if passed in by caller
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }

    // filter query with any user subset to which requesting user is restricted
    const userScope = await userUtils.getAdminRoleUserScope(reqUser);
    if (userScope) {
      unpaginatedQuery.id = { [Op.in]: [...userScope] };
    }
    if (unpaginatedQuery.accountId) {
      includes = {
        model: AccountUser,
        where: {
          accountId: unpaginatedQuery.accountId,
        },
      };
      delete unpaginatedQuery.accountId;
    }

    userUtils.getDateFilters(unpaginatedQuery, newQuery, query.userTimeZone);
    newQuery.where = unpaginatedQuery;

    if (query.roles) {
      const rolesRet = await Role.findAll({ where: { name: { [Op.in]: query.roles } }, attributes: ['id'] });
      if (rolesRet) {
        newQuery.roleIds = rolesRet.map(({ id }) => { return id; });
      }
    }
  }
  return [newQuery, { where: unpaginatedQuery }, includes];
};

const getParentGroups = async (groupIds, isReturnArray = false) => {
  const {
    groups: rGroups,
    userGroups: rUserGroups,
  } = db.reportingModels;
  const groupsMemberships = await rUserGroups.findAll({
    where: {
      memberId: {
        [Op.in]: groupIds,
      },
      memberType: 'group',
    },
    required: false,
    include: {
      model: rGroups,
      as: 'parent',
      attributes: ['id', 'name', 'groupType'],
    },
  });
  if (!isReturnArray) {
    return groupsMemberships
      .filter(userGroup => userGroup.parent && userGroup.parent.name)
      .map(userGroup => userGroup.parent.name).join(', ');
  }
  return groupsMemberships;
};

/**
 * A user list of an account's users. Goes a bit more in depth than the standard user listing
 * and can be used to generate a csv file.
 *
 * @param {number} accountId
 * @param {object} queryParams
 * @returns promise object that represents all of the accounts users and their extended fields
 */
const generateUserReport = async (queryParams, accountId) => {
  const statusSql = queryParams && queryParams.where && queryParams.where.userStatus
    ? userUtils.userStatusSelectHelper(queryParams.where.userStatus)
    : '';
  // eslint-disable-next-line no-param-reassign
  /* eslint-disable no-param-reassign */
  if (queryParams.where && queryParams.where.userStatus) {
    delete (queryParams.where.userStatus);
  }
  const {
    accountFields: rAccountFields,
    accounts: rAccounts,
    accountUsers: rAccountUsers,
    groups: rGroups,
    roles: rRoles,
    userAccountFields: rUserAccountFields,
    userGroups: rUserGroups,
    users: rUsers,
  } = db.reportingModels;

  const requestingAccount = await rAccounts.findOne({
    where: {
      id: accountId,
    },
  });
  let activeUserWhereClause = {};
  if (!queryParams.where || (queryParams.where && !queryParams.where.id)) {
    // eslint-disable-next-line no-param-reassign
    queryParams.where = { ...queryParams.where, id: { [Op.ne]: requestingAccount.accountOwnerId } };
    // eslint-disable-next-line no-param-reassign
    activeUserWhereClause = { ...queryParams.where, id: { [Op.ne]: requestingAccount.accountOwnerId } };
  }

  if (queryParams.where && queryParams.where.id) {
    activeUserWhereClause = { ...queryParams.where };
  }

  if (queryParams.where && statusSql !== '') {
    // eslint-disable-next-line no-param-reassign
    queryParams.where = {
      ...queryParams.where,
      [Op.and]: [
        db.Sequelize.literal(statusSql),
      ],
    };
  }

  const accountUsersWhere = { accountId };
  if (queryParams.roleIds) {
    accountUsersWhere.roleId = { [Op.in]: queryParams.roleIds };
  }
  const usersInclude = [
    {
      model: rAccountUsers,
      attributes: ['id', 'roleId'],
      where: accountUsersWhere,
      include: [{
        model: rRoles,
        as: 'role',
        attributes: ['name'],
      },
      {
        model: rAccounts,
        attributes: ['id', 'accountOwnerId', 'clientAccountOwnerId'],
      }],
    },
    {
      model: rUserGroups,
      attributes: ['memberId', 'groupId'],
      where: {
        memberType: 'user',
      },
      required: false,
      include: {
        model: rGroups,
        as: 'parent',
        attributes: ['id', 'name'],
      },
    },
    {
      model: rUserAccountFields,
      attributes: ['value'],
      include: {
        model: rAccountFields,
        required: false,
        attributes: ['fieldName'],
        where: {
          accountId,
        },
      },
    },
  ];
  const { col } = db.Sequelize;
  const attributes = [
    'id',
    'firstName',
    'lastName',
    'email',
    'scormId',
    'title',
    'location',
    'language',
    'role',
    'city',
    'hireDate',
    'supervisor',
    'employeeId',
    'exempt',
    'leaveStartedAt',
    'managerEmail',
    'updatedAt',
    'deletedAt',
    'stateCode',
    'countryCode',
    'isVerified',
    'createdAt',
    'lastActivityAt',
    'notificationEmail',
    'emtrainUserRole',
    'emtrainAdminRole',
    'jobGroup',
    'department',
    'orgLevel',
    'remote',
    'gender',
    'birthDate',
    'raceEthnicity',
    'veteranStatus',
    'sexualOrientation',
    'disabilityStatus',
    'attritionDate',
    'voluntaryAttrition',
  ];
  // filter
  if (queryParams.where && queryParams.where.group) {
    const groupWhere = {
      id: { [Op.in]: queryParams.where.group },
    };
    usersInclude[1].include.where = groupWhere;
    // eslint-disable-next-line no-param-reassign
    delete queryParams.where.group; delete queryParams.group; delete activeUserWhereClause.group;
  }
  // sort
  if (JSON.stringify(queryParams.order).indexOf('group') > -1) {
    const groupOrder = queryParams.order[0][0] === 'group' ? queryParams.order[0][1] : queryParams.order[0][0][1];
    // eslint-disable-next-line no-param-reassign
    queryParams.order = [[rUserGroups, 'parent', col('name'), groupOrder]];
  } else if (JSON.stringify(queryParams.order).indexOf('status') > -1) {
    queryParams.order = userStatusSorting(queryParams.order);
  }
  const [
    accountFields,
    { rows: users, count },
    activeCount, allUsersCount,
  ] = await Promise.all([
    // find custom account fields
    rAccountFields.findAll({
      attributes: ['fieldName'],
      where: {
        accountId,
        isStandard: false,
      },
      raw: true,
    }),
    // get userdata
    rUsers.findAndCountAll({
      attributes,
      distinct: true, // needed for an accurate 'total' count
      paranoid: false, // include 'deactivated' users
      ...queryParams,
      include: usersInclude,
    }),
    // get active user count
    rUsers.count({
      where: activeUserWhereClause,
      include: [
        {
          model: rAccounts,
          where: {
            id: accountId,
          },
        },
      ],
      distinct: true,
    }),
    // get all user count
    rUsers.count({
      where: { id: { [Op.ne]: requestingAccount.accountOwnerId } },
      include: [
        {
          model: rAccounts,
          where: {
            id: accountId,
          },
        },
      ],
      distinct: true,
      paranoid: false,
    }),
  ]);

  const initializeCustomFields = {};
  accountFields.forEach((row) => {
    initializeCustomFields[row.fieldName] = null;
  });
  const formattedUsers = [];
  for await (const user of users) {
    const userCustomFields = Object.assign({}, initializeCustomFields);
    for (const customField of user.userAccountFields) {
      const name = customField?.accountField?.fieldName;
      if (name) {
        userCustomFields[name] = customField?.value;
      }
    }
    let group = user.userGroups
      .filter(userGroup => userGroup.parent && userGroup.parent.name)
      .map(userGroup => userGroup.parent.name).join(', ');
    const groupIds = user.userGroups.map(userGroup => userGroup.groupId);
    if (groupIds.length) {
      // find parent group's of child groups
      const parentGroups = await getParentGroups(groupIds);
      if (parentGroups) {
        group = `${group}, ${parentGroups}`;
      }
    }
    const hasAnalyticsAccess = await userUtils.checkAnalyticsAccess(user.id);
    const obj = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      emtrainUserRole: user.emtrainAdminRole ? `${user.emtrainUserRole} (${user.emtrainAdminRole})` : user.emtrainUserRole,
      email: user.email,
      notificationEmail: user.notificationEmail,
      scormId: user.scormId,
      title: user.title,
      role: user.role,
      city: user.city,
      location: user.location,
      stateCode: user.stateCode,
      countryCode: user.countryCode,
      language: user.language,
      hireDate: user.hireDate,
      supervisor: user.supervisor,
      employeeId: user.employeeId,
      // eslint-disable-next-line no-nested-ternary
      exempt: user.exempt === true ? 'Yes' : user.exempt === false ? 'No' : '',
      group,
      managerEmail: user.managerEmail,
      lastActive: user.lastActivityAt,
      status: userUtils.userStatusHelper(user),
      createdAt: user.createdAt,
      jobGroup: user.jobGroup,
      department: user.department,
      orgLevel: user.orgLevel,
      // eslint-disable-next-line no-nested-ternary
      remote: user.remote === true ? 'Yes' : user.remote === false ? 'No' : '',
      gender: user.gender ? '*****' : '',
      birthDate: user.birthDate ? '*****' : '',
      raceEthnicity: user.raceEthnicity ? '*****' : '',
      veteranStatus: user.veteranStatus ? '*****' : '',
      sexualOrientation: user.sexualOrientation ? '*****' : '',
      disabilityStatus: user.disabilityStatus ? '*****' : '',
      attritionDate: user.attritionDate,
      // eslint-disable-next-line no-nested-ternary
      voluntaryAttrition: user.voluntaryAttrition === true ? 'Yes' : user.voluntaryAttrition === false ? 'No' : '',
      ...userCustomFields,
      hasAnalyticsAccess,
    };
    formattedUsers.push(obj);
  }
  return { data: formattedUsers, total: count, totalActive: activeCount, totalUsers: allUsersCount };
};

const getUserAccountFields = async (userId, accountId) => {
  const userAccountFields = await AccountFields.findAll({
    where: {
      accountId,
      isStandard: false,
    },
    include: {
      required: false,
      model: UserAccountFields,
      where: {
        userId,
      },
      attributes: [],
    },
    attributes: [
      'id',
      'fieldName',
      'fieldType',
      'userEditable',
      'userAccountFields.value',
      'userAccountFields.createdAt',
      'userAccountFields.updatedAt'],
    raw: true,
  });

  return userAccountFields;
};

const getAccountRole = async (userId, accountId) => {
  const accountUser = await AccountUser.findOne({
    where: { accountId, userId },
    include: [{
      model: Role,
      as: 'role',
      required: true,
      include: [{
        model: Group,
        through: RoleGroup,
      }],
    }],
  });

  if (accountUser) {
    return accountUser.role;
  }
  return null;
};

const getUserOptInInfo = async (userId) => {
  const optInInfo = await marketingEmailOptin.findOne({
    where: { userId },
    attributes: ['optInStatus', 'prompted'],
  });

  return optInInfo;
};

// Determine whether or not a user is eligible for deletion based on certain criteria
const determineDeletionEligibility = async (userId) => {
  // The user must not have any started or completed lessons
  const startedUserLessons = await UserLessons.findOne({
    where: {
      userId,
      status: {
        [Op.in]: ['inProgress', 'completed'],
      },
    },
    attributes: ['id', 'userId'],
  });

  // The user must not have accessed any content
  const accessEvents = await Events.findOne({
    where: {
      userId,
      type: {
        [Op.in]: ['userLessonStarted', 'userLessonCompleted', 'userLessonStartedSD', 'userLessonCompletedSD'],
      },
    },
    attributes: ['id', 'userId'],
  });

  // The user must not have submitted any ask the expert questions
  const askedQuestions = await QuestionAnswers.findOne({
    where: { userId },
    attributes: ['id', 'userId'],
  });

  // The user must not have viewed any lesson cards
  const lastViewedLessonCards = await LastViewedLessonCards.findOne({
    where: {
      userId,
    },
    attributes: ['id', 'userId'],
  });

  const viewLessonCardEvents = await ViewLessonCardEvents.findOne({
    where: {
      userId,
    },
    attributes: ['id', 'userId'],
  });

  const hasAskedQuestion = !!askedQuestions;
  const hasStartedUserLessons = !!startedUserLessons;
  const hasLastViewedLessonCards = !!lastViewedLessonCards;
  const hasViewLessonCardEvents = !!viewLessonCardEvents;
  const hasAccessEvents = !!accessEvents;

  return !hasAskedQuestion && !hasStartedUserLessons && !hasLastViewedLessonCards && !hasViewLessonCardEvents && !hasAccessEvents;
};

/**
 * @swagger
 * /users?$limit={limit}&$skip={skip}:
 *   get:
 *     summary: Fetch all users
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         schema:
 *           type: integer
 *         description: Limit number of records
 *         required: true
 *       - name: $skip
 *         in: query
 *         schema:
 *           type: integer
 *         description: Offset for records
 *         required: true
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   description: Array of user data
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/users'
 *                       - type: object
 *                         properties:
 *                           roles:
 *                             type: array
 *                             items:
 *                               allOf:
 *                                 - $ref: '#/components/schemas/Role'
 *                                 - type: object
 *                                   properties:
 *                                     userRoles:
 *                                       $ref: '#/components/schemas/UserRole'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 */

module.exports.list = async (req, res, next) => {
  try {
    const [queryParams, unpaginatedQuery, accountInclude] = await restQueryToSequelize(req.user.id, req.query);
    const pagedResult = {
      limit: queryParams.limit,
      skip: queryParams.offset,
    };
    const includes = [];

    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'users', req.tokenPayload);

    // only generalized 'read' permission allows cross account users to be included
    if (!allowedPermissions.users.includes('read')) {
      includes.push({
        model: AccountUser,
        where: {
          accountId: req.user.accountId,
        },
      });
    } else if (accountInclude) {
      // add account filter if indicated
      includes.push(accountInclude);
    }

    const promises = [];

    unpaginatedQuery.include = includes;
    promises.push(User.count(unpaginatedQuery)); // count total in the set

    includes.push({
      model: Role,
      through: 'userRoles',
    });

    queryParams.attributes = ['id', 'firstName', 'lastName', 'email', 'language', 'stateCode', 'countryCode', 'updatedAt',
      'lastActivityAt', 'deletedAt', 'isVerified', 'leaveStartedAt'];
    queryParams.include = includes;

    // run the actual limited query
    promises.push(User.findAll(queryParams));

    const [userCount, userData] = await Promise.all(promises);
    res.json({ total: userCount, ...pagedResult, data: userData });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /users/me:
 *   get:
 *     summary: Get Current User Details. Fetch Details Based On Auth Token
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/users'
 *               properties:
 *                 accounts:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/accounts'
 *                     properties:
 *                       accountUsers:
 *                         $ref: '#/components/schemas/accountUsers'
 *                       integrations:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/integrations'
 *                 groups:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/groups'
 *                     properties:
 *                       roleGroups:
 *                         $ref: '#/components/schemas/roleGroups'
 *                 accountFields:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/accountFields'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.me = async (req, res, next) => {
  try {
    // should always return the requested users info so no extra permissions checking necessary
    const user = await userUtils.findUserAndAssociations(req, req.user.id);

    const permissions = await getAllowedPermissions(req, req.user.id, null, req.tokenPayload);
    const flattenedUser = user.get({ plain: true });
    flattenedUser.permissions = permissions;
    flattenedUser.accountFields = await getUserAccountFields(req.user.id, req.user.accountId);
    res.json(flattenedUser);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /users/{userId}:
 *   get:
 *     summary: Fetch User Information
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/users'
 *               properties:
 *                 accounts:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/accounts'
 *                     properties:
 *                       accountUsers:
 *                         $ref: '#/components/schemas/accountUsers'
 *                       integrations:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/integrations'
 *                 groups:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/groups'
 *                     properties:
 *                       roleGroups:
 *                         $ref: '#/components/schemas/roleGroups'
 *                 permissions:
 *                   type: object
 *                 accountFields:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/accountFields'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.read = async (req, res, next) => {
  try {
    const accountId = req.requestedUser.accounts[0].id;
    // To read user, you need one of the following:
    // 'read' permissions
    // 'readAccount' permissions AND the requested user is in the same account
    // 'readOwn' permissions AND you're the owner
    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'users', req.tokenPayload);
    if (!(allowedPermissions.users.includes('read') ||
      (allowedPermissions.users.includes('readAccount') && req.user.accountId === accountId) ||
      (allowedPermissions.users.includes('readOwn')
        && userUtils.isOwner(req)))) {
      const err = new Error(req.i18n.t('users.user_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    const permissions = await getAllowedPermissions(req, req.requestedUser.id, null, req.tokenPayload);
    const flattenedUser = req.requestedUser.get({ plain: true });

    flattenedUser.permissions = permissions;
    flattenedUser.accountFields = await getUserAccountFields(req.requestedUser.id, accountId);

    if (flattenedUser && flattenedUser.accounts && flattenedUser.accounts.length > 0 && flattenedUser.accounts[0].integrations) {
      const scormIntegrations =
        flattenedUser.accounts[0].integrations.filter(intg => isSCORMIntegrationType(intg.integrationType));
      const scormIntegration = scormIntegrations[0]; // only one scorm integration per account for now
      if (scormIntegration) {
        flattenedUser.accounts[0].scorm = scormIntegration.integrationType;
      }
    }
    flattenedUser.accountRole = await getAccountRole(req.requestedUser.id, accountId);
    flattenedUser.userOptInInfo = await getUserOptInInfo(req.requestedUser.id);
    flattenedUser.canBeDeleted = await determineDeletionEligibility(req.requestedUser.id);
    if (flattenedUser?.groups) {
      const groupIds = flattenedUser.groups.map(userGroup => userGroup.id);
      // find parent group's of child groups
      const parentGroups = await getParentGroups(groupIds, true);
      const groups = parentGroups.filter(userGroup => userGroup.parent && userGroup.parent.name)
        .map((userGroup) => {
          return userGroup.parent;
        });
      if (groups.length) {
        Array.prototype.push.apply(flattenedUser.groups, groups);
      }
    }
    if (flattenedUser.managerEmail) {
      flattenedUser.manager = await userUtils.getUserByEmail(flattenedUser.managerEmail, accountId);
    }
    flattenedUser.analyticsRolesData = await userUtils.getUserAnalyticsRolesData(req.requestedUser.id);
    res.json(flattenedUser);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /users/{userId}:
 *   patch:
 *     summary: Update User Information.
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       description: Required parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *               language:
 *                 type: string
 *               location:
 *                 type: string
 *               noConsent:
 *                 type: integer
 *               role:
 *                 type: string
 *               city:
 *                 type: string
 *               description:
 *                 type: string
 *               title:
 *                 type: string
 *               accountFields:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     fieldName:
 *                       type: string
 *                     value:
 *                       type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/users'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.patch = async (req, res, next) => {
  try {
    const accountRecord = req.requestedUser.accounts[0];
    const accountId = accountRecord.id;
    let createTempPassword = false;
    if (req.body && req.body.generateTempPassword) {
      createTempPassword = true;
      delete req.body.generateTempPassword;
    }
    // To patch user, you need one of the following:
    // 'update' permissions
    // 'updateAccount' permissions AND the requested user is in the same account
    // 'updateOwn' permissions AND you're the owner
    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'users', req.tokenPayload);

    if (!(allowedPermissions.users.includes('update') ||
      (allowedPermissions.users.includes('updateAccount') && req.user.accountId === accountId))) {
      // Not any kind of admin. See if it's a regular user.
      if (!(allowedPermissions.users.includes('updateOwn')
        && userUtils.isOwner(req))) {
        const err = new Error(req.i18n.t('users.user_no_permissions_Error'));
        err.status = 401;
        throw err;
      } else {
        // see if any custom fields want to be set but are not user editable
        await userUtils.checkEditableCustomFields(req.body, accountId);
      }
    }

    await userUtils.validateUserCustomFields(req.body, accountId);
    const {
      coreUserFields,
      accountFieldPromises,
    } = await userUtils.separateUserFields(req.body, req.requestedUser.id, accountId);

    // Check permissions on specific fields
    await userUtils.checkEditableFields(req, 'update', coreUserFields, allowedPermissions);

    const user = req.requestedUser;

    const existingUser = await User.findOne({
      where: {
        id: user.id,
      },
      include: [{
        model: Accounts,
      }],
      paranoid: false,
    });

    if (req.requestedUser.password) {
      await validatePassword(req, existingUser, existingUser.accounts[0], req.requestedUser.password);
    }
    if (createTempPassword) {
      coreUserFields.resetPasswordRequired = true;
      if (existingUser.accountLocked) {
        coreUserFields.failedLoginAttempts = 0;
        coreUserFields.accountLocked = 0;
        coreUserFields.failedLoginAt = null;
      }
    }
    if ((req.body.onLeave || (req.body.status && req.body.status === 'onLeave')) && !existingUser.leaveStartedAt) {
      user.leaveStartedAt = Date.now();
      coreUserFields.leaveStartedAt = user.leaveStartedAt;
    } else if ((req.body.hasOwnProperty('onLeave') && req.body.onLeave === false) && existingUser.leaveStartedAt) {
      processCampaignsForUserComingOffLeave(user.id, user.leaveStartedAt);
      user.leaveStartedAt = null;
      coreUserFields.leaveStartedAt = user.leaveStartedAt;
    }

    const otherUser = await userUtils.findUserByEmployeeIdOrEmailAndAccount({
      email: req.body.email,
      employeeId: req.body.employeeId,
      accountId,
      paranoid: false,
      excludeId: user.id,
    });
    if (otherUser) {
      throw new UnprocessableEntityError(req.i18n.t('users.user_already_exists_Error'));
    }

    const removingEmail = !req.body.email && req.body.email !== undefined;
    const removingEmployeeId = !req.body.employeeId && req.body.employeeId !== undefined;

    // don't allow removing of email if it's required.
    if (accountRecord.authField === 'email' && removingEmail) {
      const error = new Error(req.i18n.t('users.email_required_Error'));
      error.status = 422;
      throw error;
    }
    // don't allow removing of employeeId if it's required.
    if (accountRecord.authField === 'employeeId' && removingEmployeeId) {
      throw new UnprocessableEntityError(req.i18n.t('users.employeeId_required_Error'));
    }

    // There needs to be either an employeeId or email, either existing or being passed in
    const incorrectlyRemovingEmailOrEmployeeId = (removingEmail && removingEmployeeId) || (removingEmail && !req.body.employeeId && !req.requestedUser.employeeId) || (removingEmployeeId && !req.body.email && !req.requestedUser.email);
    if (accountRecord.authField === 'emailOrEmployeeId' && incorrectlyRemovingEmailOrEmployeeId) {
      throw new UnprocessableEntityError(req.i18n.t('users.email_or_employeeId_required_Error'));
    }

    if (req.body.stateCode) {
      const validCode = getValidStateCode(req.body.stateCode);
      if (validCode) {
        coreUserFields.stateCode = validCode;
      } else {
        throw new UnprocessableEntityError(req.i18n.t('validation.invalidStateCode_Error', { stateCode: req.body.stateCode }));
      }
    }
    if (req.body.countryCode) {
      const validCode = getValidCountryCode(req.body.countryCode);
      if (validCode) {
        coreUserFields.countryCode = validCode;
      } else {
        throw new UnprocessableEntityError(req.i18n.t('validation.invalidCountryCode_Error', { countryCode: req.body.countryCode }));
      }
    }

    const updatedUser = await user.update(coreUserFields, { accountId });

    const promises = [];

    // would like to move this to a beforeUpdate hook but hooks only have access to the user instance itself not the request.
    if (Object.keys(req.body).includes('topicIds')) {
      promises.push(updatedUser.setTopics(req.body.topicIds));
    }

    if (Object.keys(req.body).includes('motivationIds')) {
      promises.push(updatedUser.setMotivations(req.body.motivationIds));
    }

    if (accountFieldPromises.length) {
      promises.push(accountFieldPromises);
    }

    await Promise.all(promises);

    updatedUser.password = undefined;
    const permissions = await getAllowedPermissions(req, updatedUser.id, null, req.tokenPayload);
    const flattenedUser = updatedUser.get({ plain: true });
    flattenedUser.permissions = permissions;
    flattenedUser.accountFields = await getUserAccountFields(updatedUser.id, accountId);
    res.json(flattenedUser);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /users/{userId}:
 *   delete:
 *     summary: Deactivate User Accounts
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: User account deactivated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "User deactivated successfully"
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       5XX:
 *         description: Unexpected error
 */

/**
 * @swagger
 * /users/{userId}?force=true:
 *   delete:
 *     summary: Deduplicate User
 *     description: Permanently deletes a user if `force=true` is passed as a query parameter.
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: force
 *         in: query
 *         required: false
 *         schema:
 *           type: boolean
 *           default: false
 *         description: If true, the user will be permanently deleted
 *     responses:
 *       200:
 *         description: User deduplicated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "User deduplicated successfully"
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  const force = !!(req.query.force && req.query.force === 'true');
  const hard = !!(req.query.hard && req.query.hard === 'true');
  const tbdUser = req.requestedUser;
  try {
    let deletedUser = tbdUser;
    await checkDeletePermissions(req);
    await validateDelete(req);
    if (hard || force) {
      deletedUser = await userUtils.wipeUserData(req);
    } else {
      await userUtils.propagateUserDeactivation(tbdUser.id);
      await tbdUser.changed('updatedAt', true);
      await tbdUser.update({ updatedAt: new Date() });
      await User.destroy({ where: { id: tbdUser.id }, individualHooks: true });
      await deactivateOrphanedUserLessonsForDeletedUser(tbdUser.id);
    }
    res.json(deletedUser);
  } catch (err) {
    next(err);
  }
};

module.exports.undelete = async (req, res, next) => {
  try {
    const accountId = req.requestedUser.accounts[0].id;
    await checkDeletePermissions(req);
    await User.restore({ where: { id: req.requestedUser.id }, individualHooks: true });
    const userWithRoles = await req.requestedUser.reload({
      include: {
        model: Role,
        through: 'userRoles',
      },
    });
    await addUserToAccountRoster(accountId, userWithRoles.id);
    res.json(userWithRoles);
  } catch (err) {
    next(err);
  }
};

/**
 * Gets roles specific to a user.  It does not get a user's roles specific
 * to membership in any group.
 *
 * return data looks like...
 * {
 *   role: 'name of user role',
 *   account { // if exists
 *     accountId: id,
 *     role: 'name of account role, probably accountAdmin'
 *   }
 * }
 */
module.exports.getRoles = async (req, res, next) => {
  try {
    const user = req.requestedUser;
    const accountId = user.accounts[0].id;

    // To read user, you need one of the following:
    // 'read' permissions
    // 'readAccount' permissions AND the requested user is in the same account
    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'users', req.tokenPayload);
    if (!(allowedPermissions.users.includes('read') ||
      (allowedPermissions.users.includes('readAccount') && req.user.accountId === accountId))) {
      const err = new Error(req.i18n.t('users.user_no_permissions_Error'));
      err.status = 401;
      throw err;
    }
    // Get the user role
    const userRole = await UserRole.findOne({
      where: {
        userId: user.id,
      },
    });

    // Get the account role
    const accountRole = await AccountUser.findOne({
      where: {
        userId: user.id,
      },
    });
    const result = {};
    if (userRole) {
      const role = await Role.findByPk(userRole.roleId);
      if (role) {
        result.role = role.name;
      }
    }
    if (accountRole) {
      const role = await Role.findByPk(accountRole.roleId);
      if (role) {
        result.account = {
          accountId: accountRole.accountId,
          role: role.name,
        };
      }
    }
    res.json(result);
  } catch (err) {
    next(err);
  }
};

/**
 * req.body looks like...
 * {
 *   role: 'name of user role',
 *   account {
 *     accountId: id,
 *     role: 'name of account role, probably accountAdmin'
 *   }
 * }
 */

/**
 * @swagger
 * /users/{userId}/roles:
 *   post:
 *     summary: Change User Role
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               role:
 *                 type: string
 *                 example: systemAdmin
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/users'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

module.exports.setRoles = async (req, res, next) => {
  try {
    const { account } = req.body;
    let { role } = req.body;
    const user = req.requestedUser;
    const userId = user.id;
    const accountId = user.accounts[0].id;

    // email required for all accountAdmins
    if (!user.email && role === 'accountAdmin') {
      const error = new Error(req.i18n.t('users.email_required_Error'));
      error.status = 422;
      throw error;
    }

    // To patch user, you need one of the following:
    // 'update' permissions
    // 'updateAccount' permissions AND the requested user is in the same account
    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'users', req.tokenPayload);
    if (!(allowedPermissions.users.includes('update') ||
      (allowedPermissions.users.includes('updateAccount') && req.user.accountId === accountId))) {
      const err = new Error(req.i18n.t('users.user_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    // Assume the user already has a user role in userRoles table.  Replace it if role is set
    if (role) {
      // The user role can only be set by and admin account and admin read permissions.
      const requestingUserAccount = await Accounts.findOne({
        where: {
          id: account?.accountId,
        },
      });
      const allowedAdminPermissions = await getAllowedPermissions(req, req.user.id, 'users', req.tokenPayload);
      if (!allowedAdminPermissions.users.includes('update') || requestingUserAccount.accountType !== 'admin') {
        role = 'user';
      }
      await updateUserRole(user, role);
    }

    // If there's an account, upsert the record in accountUsers.
    if (account) {
      let roleRecord;
      if (account.role) {
        // If it's a system role, accountId should be null.
        const systemRoles = await Role.findAll({
          where: {
            accountId: null,
          },
        });
        const accountIdToQuery = systemRoles.find(sr => sr.name === account.role) ? null : account.accountId;
        roleRecord = await Role.findOne({
          where: {
            name: account.role,
            accountId: accountIdToQuery,
          },
        });
      }
      if (roleRecord) {
        const accountUser = await AccountUser.findOne({
          where: {
            userId,
            accountId: account.accountId,
          },
        });
        if (accountUser) {
          await accountUser.update({
            roleId: roleRecord.id,
          });
        } else {
          await AccountUser.create({
            userId,
            accountId: account.accountId,
            roleId: roleRecord.id,
          });
          await addUserToAccountRoster(account.accountId, user.id);
        }
      }
    }

    const permissions = await getAllowedPermissions(req, userId, null, req.tokenPayload);
    // Get the user with only the interesting bits... mostly the same as list users Probably will need account user stuff later.
    const updatedUser = await User.findByPk(userId, {
      attributes: ['id', 'firstName', 'lastName', 'email', 'language', 'updatedAt'],
      include: {
        model: Role,
        through: 'userRoles',
      },
      paranoid: false,
    });
    const flattenedUser = updatedUser.get({ plain: true });
    flattenedUser.permissions = permissions;
    // update emtrainroles
    await userUtils.updateEmtrainRoles(flattenedUser.id);
    res.json(flattenedUser);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /users/{userId}/userActivity:
 *   get:
 *     summary: Fetch User Activity
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records.
 *                 limit:
 *                   type: integer
 *                   description: Number of records for page.
 *                 skip:
 *                   type: integer
 *                   description: Skip number for page number.
 *                 data:
 *                   type: array
 *                   description: Array of group object.
 *                   items:
 *                     type: object
 *                     properties:
 *                       timeStamp:
 *                         type: string
 *                       type:
 *                         type: string
 *                       metaData:
 *                         type: object
 *                         properties:
 *                           end:
 *                             type: string
 *                           start:
 *                             type: string
 *                           lessonCard:
 *                             type: object
 *                             properties:
 *                               title:
 *                                 type: string
 *                               lessonTitle:
 *                                 type: string
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

module.exports.activity = async (req, res, next) => {
  try {
    const promises = [];
    // user signed in or out
    promises.push(req.requestedUser.getEvents({
      attributes: ['createdAt', 'action'],
      where: {
        type: 'signin',
      },
      order: [['createdAt', 'DESC']],
      raw: true,
    }));

    // user viewed a lesson card
    promises.push(req.requestedUser.getViewLessonCardEvents({
      attributes: ['createdAt', 'updatedAt', 'type', 'lessonCardId', 'lessonId'],
      where: {
        sourceLifecycle: 'publish',
      },
      include: [{
        model: db.lessonCards,
        attributes: ['title', 'cardType'],
        required: true,
        include: [{
          model: db.lessons,
          attributes: ['title'],
          required: true,
        }],
      }],
      order: [['createdAt', 'DESC']],
      raw: true,
    }));

    // user viewed a video
    promises.push(req.requestedUser.getEvents({
      attributes: ['createdAt', 'type', 'trackableId', 'trackableData'],
      where: {
        trackableType: 'video',
        sourceLifecycle: 'publish',
      },
      include: [{
        model: db.lessonCards,
        attributes: ['title', 'cardType'],
        required: true,
        include: [{
          model: db.lessons,
          attributes: ['title'],
          required: true,
        }],
      }],
      order: [['createdAt', 'ASC']],
      raw: true,
    }));

    // user answered a lessonCard
    promises.push(req.requestedUser.getAnswerCards({
      where: {
        sourceLifecycle: 'publish',
      },
      attributes: ['createdAt'],
      include: [{
        model: db.lessonLessonCards,
        include: [
          {
            model: db.lessons,
            attributes: ['title'],
          },
          {
            model: db.lessonCards,
            attributes: ['title', 'cardType'],
          },
        ],
        required: true,
      }],
      order: [['createdAt', 'DESC']],
      raw: true,
    }));

    for (const resourceType of ['lessons', 'questionAnswers', 'programs', 'resourceAssets']) {
      let include; // include resources for the progam so we can get a title
      let resourceTitle; // properly formatted sequelize/SQL literal to get the title

      if (resourceType === 'programs') {
        include = {
          model: db.programs,
          attributes: [],
          required: true,
        };
        resourceTitle = [db.sequelize.col('program.name'), 'title'];
      } else {
        include = {
          model: db.resources,
          required: true,
          attributes: ['digestable'],
          where: {
            digestable: resourceType,
          },
          include: {
            model: db[resourceType],
            attributes: [],
            required: true,
          },
        };
        if (resourceType === 'questionAnswers') {
          resourceTitle = [db.sequelize.col('`resource->questionAnswer`.`questionEdited`'), 'title'];
        } else {
          resourceTitle = [db.sequelize.col(`\`resource->${resourceType.slice(0, -1)}\`.\`title\``), 'title'];
        }
      }

      promises.push(req.requestedUser.getEvents({
        attributes: ['createdAt', 'trackableData', 'type', 'action', resourceTitle],
        where: {
          trackableType: resourceType === 'programs' ? 'program' : 'resource',
          sourceLifecycle: 'publish',
        },
        include,
        order: [['createdAt', 'DESC']],
        raw: true,
      }));
    }

    const [
      signupEvents,
      lessonCardViews,
      videoEvents,
      answerCards,
      todoLessons,
      todoQAs,
      todoMAs,
      todoPrograms,
    ] = await Promise.all(promises);

    // format all the data
    const formattedSignups = signupEvents.map((event) => {
      const returnEvent = {
        timeStamp: event.createdAt,
        metaData: {},
      };
      if (event.action) {
        returnEvent.type = 'userSignin';
      } else {
        returnEvent.type = 'userSignout';
      }
      return returnEvent;
    });

    // If the lessonCard is shared, filter out duplicate view events created by the join query
    const filteredLCViews = lessonCardViews.filter(lcv => lcv['lessonCard.lessons.id'] === lcv.lessonId);
    const formattedLCViews = calcLessonCardViews(filteredLCViews);

    const filteredVideoEvents = videoEvents.filter(lcv => lcv['lessonCard.lessons.id'] === lcv.trackableData.lessonId);
    const formattedVideoViews = calcVideoViews(filteredVideoEvents);

    const formattedAnswers = answerCards.map((ac) => {
      const title = lessonCardTitleHelper(
        ac['lessonLessonCard.lessonCard.id'],
        ac['lessonLessonCard.lessonCard.title'],
        ac['lessonLessonCard.lessonCard.cardType'],
      );
      return {
        timeStamp: ac.createdAt,
        type: 'answerCard',
        metaData: {
          lessonCard: {
            title,
            lessonTitle: ac['lessonLessonCard.lesson.title'],
          },
        },
      };
    });

    const formattedEvents = [...todoLessons, ...todoQAs, ...todoMAs, ...todoPrograms]
      // self-directed userLesson events don't produce useful data in the user activity list
      .filter(td => !(['userLessonStarted', 'userLessonAssigned', 'userLessonCompleted']
        .includes(td.type) && td.trackableData.selfDirected))
      .map((td) => {
        return {
          timeStamp: td.createdAt,
          type: getType(td.type, td.action),
          metaData: {
            campaignName: td.trackableData && td.trackableData.campaignName,
            title: td.title,
          },
        };
      });

    const returnData = [
      ...formattedSignups,
      ...formattedLCViews,
      ...formattedVideoViews,
      ...formattedAnswers,
      ...formattedEvents,
    ].sort((a, b) => b.timeStamp - a.timeStamp);

    res.json({
      total: returnData.length,
      limit: 0,
      skip: 0,
      data: returnData,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /users/extendedList:
 *   get:
 *     summary: Extended User List
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *         description: Limit for number of records
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *         description: Offset or skip position before beginning to return results
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records
 *                 data:
 *                   type: array
 *                   description: Array of user data objects
 *                   items:
 *                     $ref: '#/components/schemas/users'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.extendedUserList = async (req, res, next) => {
  const timeZone = req.query.timeZone;
  delete req.query.timeZone;
  try {
    const [queryParams] = await restQueryToSequelize(req.user.id, req.query);
    const actualLimit = parseInt(req.query.$limit);
    queryParams.limit = actualLimit > queryParams.limit ? actualLimit : queryParams.limit;
    const { data, total, totalActive, totalUsers } = await generateUserReport(queryParams, req.user.accountId, timeZone);

    res.json({
      total,
      totalActive,
      limit: queryParams.limit,
      skip: queryParams.offset,
      data,
      totalUsers,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Make sure the user is requesting allowable fields
 *
 * @param {string[]} params user requested attributes
 */
const filterAllowedAttributes = async (req, params) => {
  if (!params || params.length < 1) {
    const err = new Error(req.i18n.t('users.missing_attribute_Error'));
    err.status = 400;
    throw err;
  }

  let accountFields = [];
  if (req.user && req.user.accountId) {
    accountFields = await db.accountFields.findAll({
      attributes: ['fieldName'],
      where: {
        accountId: req.user.accountId,
      },
      raw: true,
    });
  }

  const allowed = ['id', 'firstName', 'lastName', 'email', 'role', 'title',
    'location', 'emtrainUserRole', 'group', 'status', 'lastActive', 'city', 'supervisor', 'hireDate',
    'employeeId', 'exempt', 'managerEmail', 'leaveStartedAt', 'scormId', 'createdAt',
    'countryCode', 'stateCode', 'notificationEmail', 'language', 'jobGroup', 'department', 'orgLevel', 'remote',
    'gender', 'birthDate', 'raceEthnicity', 'veteranStatus', 'sexualOrientation', 'disabilityStatus',
    'attritionDate', 'voluntaryAttrition', ...accountFields.map(af => af.fieldName)];

  const allowedAttributes = params.filter(param => allowed.includes(param));
  return allowedAttributes;
};

/**
 * downloadable csv endpoint for extended user list
 *
 * @param {{}} req
 * @param {{}} res
 * @param {{}} next
 * @todo GDPR
 */
module.exports.generateUserListCSV = async (req, res, next) => {
  const defaults = {
    order: [['id', 'DESC']],
    limit: 5000,
    offset: 0,
  };

  // filter list with any user subset to which requesting user is restricted
  const userScope = await userUtils.getAdminRoleUserScope(req.user.id);
  if (userScope) {
    defaults.where = { id: { [Op.in]: [...userScope] } };
  }

  const timeZone = req.query.timeZone;
  delete req.query.timeZone;

  let keepAttributes = req.query.attributes;
  try {
    keepAttributes = await filterAllowedAttributes(req, keepAttributes);

    // user can't specify limit or skip for the csv.
    delete req.query.$limit;
    delete req.query.$skip;
    delete req.query.attributes;
    const account = await Accounts.findOne({
      where: {
        id: req.user.accountId,
      },
      attributes: ['name'],
    });
    const dynamicQueryData = [];
    if (req.query.group) {
      const groupData = await Group.findAll({
        where: {
          id: { [Op.in]: req.query.group },
        },
      }, { attributes: ['name'], raw: true });

      let groupNames = groupData.map(c => c.name);
      groupNames = groupNames.join(',');
      dynamicQueryData.push([`Group: ${groupNames}`]);
    }
    let accountName = account.name || '';
    // remove all non alphanumeric and space characters from the account name
    accountName = accountName.replace(/[^a-zA-Z0-9 ]/g, '');
    accountName = accountName.split(' ').join('_');
    const filename = `Emtrain_${accountName}_User_Roster_${Date.now()}`;
    const [queryParams] = await restQueryToSequelize(req.user.id, req.query, defaults);
    queryParams.order = queryParams.order ? [[...queryParams.order]] : [[...defaults.order]];
    queryParams.where = decodeQueryParams(queryParams.where);
    queryParams.limit = defaults.limit;
    genNewCSV(keepAttributes, queryParams, filename, res, generateUserReport, timeZone, req.query, dynamicQueryData, 'users')(req.user.accountId);
  } catch (err) {
    next(err);
  }
};

/**
 * downloadable csv endpoint for user roster using CSV settings as headings
 *
 * @param {{}} req
 * @param {{}} res
 * @param {{}} next
 * @todo GDPR
 */
module.exports.generateUserRosterCSV = async (req, res, next) => {
  const writer = csvWriter();
  const defaults = {
    order: [['id', 'ASC']],
    limit: 1000,
    offset: 0,
  };

  const accountId = req.user.accountId;
  const scormAccount = await isScormAccount(accountId);
  // Find the settings record based on the accountId.
  const csvSetting = await CsvSetting.findOne({
    where: {
      accountId,
    },
  });

  let colHeaders = [];
  let noImportColumns = [];

  if (csvSetting) {
    // Get the columns.
    const csvColumns = await CsvColumn.findAll({
      where: {
        csvSettingId: csvSetting.id,
      },
      order: [['columnNumber', 'ASC']],
    });

    if (csvColumns) {
      colHeaders = csvColumns.reduce((acc, col) => {
        let idx = acc.length;
        const empties = [];
        while (idx + 1 < col.columnNumber) {
          empties.push(`emptyColumn_${idx + 1}`);
          idx += 1;
        }
        return acc.concat(empties).concat(col.userTableColumn);
      }, []);

      noImportColumns = csvColumns.reduce((acc, col) => {
        if (!col.shouldImport) {
          return acc.concat(col.userTableColumn);
        }
        return acc;
      }, []);

      const allColHeaders = scormAccount ? new Set([...colHeaders, 'scormId']) : new Set([...colHeaders]);
      colHeaders = [...allColHeaders];    
    }
  } else {
    colHeaders = defaultCSVSettingColHeaders;
  }

  try {
    // filter list with any user subset to which requesting user is restricted
    const userScope = await userUtils.getAdminRoleUserScope(req.user.id);
    if (userScope) {
      defaults.where = { id: { [Op.in]: [...userScope] } };
    }

    // user can't specify limit or skip for the csv.
    delete req.query.$limit;
    delete req.query.$skip;
    delete req.query.attributes;

    const timestampString = moment().format('MMDDYYYY_hh:mm:ss');
    const account = await Accounts.findByPk(accountId);
    let accountName = account.name;
    // remove all non alphanumeric and space characters from the account name
    accountName = accountName.replace(/[^a-zA-Z0-9 ]/g, '');
    accountName = accountName.split(' ').join('_');
    res.set({
      'Content-Disposition': `attachment; filename=${accountName}_UserRoster_${timestampString}.csv`,
      'Content-Type': 'text/csv',
    });
    res.setHeader('Access-Control-Expose-Headers', 'content-disposition');
    writer.pipe(res);

    let offset = defaults.offset;
    let loopLimit = 1; // ensure the loop runs at least once and then set it to the real limit once we have the count
    for (let loopIdx = 0; loopIdx < loopLimit; loopIdx++) {
      // get user reports in batches of 1000 and stream them back as a csv
      const userReports = await generateUserReport({ ...defaults, offset }, req.user.accountId);
      userReports.data = userReports.data.map((nextUser) => {
        if (nextUser.hireDate) {
          // eslint-disable-next-line no-param-reassign
          nextUser.hireDate = moment(nextUser.hireDate).format('YYYY-MM-DD');
        }
        return nextUser;
      });

      loopLimit = Math.ceil(userReports.total / defaults.limit);

      // only send the csv column attributes
      for (const usr of userReports.data) {
        const outputUser = {};
        for (let colHeadersIdx = 0; colHeadersIdx < colHeaders.length; colHeadersIdx++) {
          if (usr[colHeaders[colHeadersIdx]] && !noImportColumns.includes(colHeaders[colHeadersIdx])) {
            outputUser[colHeaders[colHeadersIdx]] = usr[colHeaders[colHeadersIdx]];
          } else {
            outputUser[colHeaders[colHeadersIdx]] = '';
          }
          // invited is not a valid status for CSV uploads.
          if (usr.status && usr.status === 'invited') {
            outputUser.status = 'active';
          }
        }
        writer.write(_.pick(outputUser, colHeaders));
      }


      offset += defaults.limit;
    }
    writer.end();
  } catch (err) {
    next(err);
  }
};

/**
 * downloadable csv endpoint for user roster template
 *
 * @param {{}} req
 * @param {{}} res
 * @param {{}} next
 * @todo GDPR
 */
module.exports.generateUserRosterTemplate = async (req, res, next) => {
  const writer = csvWriter({ sendHeaders: false });
  const accountId = req.user.accountId;
  const scormAccount = await isScormAccount(accountId);
  defaultCSVSettingColHeaders[2] = scormAccount ? 'scormId' : 'email';
  // Find the settings record based on the accountId.
  const csvSetting = await CsvSetting.findOne({
    where: {
      accountId,
    },
  });

  let colHeaders = [];
  if (csvSetting) {
    // Get the columns.
    const csvColumns = await CsvColumn.findAll({
      where: {
        csvSettingId: csvSetting.id,
      },
      order: [['columnNumber', 'ASC']],
    });

    if (csvColumns) {
      colHeaders = csvColumns.reduce((acc, col) => {
        let idx = acc.length;
        const empties = [];
        while (idx + 1 < col.columnNumber) {
          empties.push(`emptyColumn_${idx + 1}`);
          idx += 1;
        }
        return acc.concat(empties).concat(col.userTableColumn);
      }, []);
    }

    const allColHeaders = scormAccount ? new Set([...colHeaders, 'scormId']) : new Set([...colHeaders]);
    colHeaders = [...allColHeaders];
  } else {
    colHeaders = [...defaultCSVSettingColHeaders, ...segmentationFieldsColHeaders];
  }

  try {
    const account = await Accounts.findByPk(accountId);
    let accountName = account.name;
    // remove all non alphanumeric and space characters from the account name
    accountName = accountName.replace(/[^a-zA-Z0-9 ]/g, '');
    accountName = accountName.split(' ').join('_');
    res.set({
      'Content-Disposition': `attachment; filename=${accountName}_UserRoster_Template.csv`,
      'Content-Type': 'text/csv',
    });
    res.setHeader('Access-Control-Expose-Headers', 'content-disposition');
    writer.pipe(res);
    // csv-write-steam doesn't seem to have a way to write out a line without specifying
    // the headers, so I need to create an object for the headers.
    const headersObject = {};
    for (let colHeadersIdx = 0; colHeadersIdx < colHeaders.length; colHeadersIdx++) {
      headersObject[colHeaders[colHeadersIdx]] = colHeaders[colHeadersIdx];
    }
    writer.write(headersObject);
    writer.end();
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the resource when an id is passed in the route
module.exports.userById = async function (req, res, next, id) {
  try {
    const checkAccess = await userUtils.checkUserAccess(req.user.id, parseInt(id));
    if (!checkAccess) {
      const err = new Error(req.i18n.t('users.user_no_permissions_Error'));
      err.status = 401;
      throw err;
    }
    const paranoid = false;
    const user = await userUtils.findUserAndAssociations(req, id, paranoid);
    req.requestedUser = user;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * components:
 *   schemas:
 *     users:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         email:
 *           type: string
 *         password:
 *           type: string
 *         supervisor:
 *           type: string
 *         firstName:
 *           type: string
 *         lastName:
 *           type: string
 *         title:
 *           type: string
 *         description:
 *           type: string
 *         avatarId:
 *           type: integer
 *         bannerId:
 *           type: integer
 *         isVerified:
 *           type: boolean
 *         verifyToken:
 *           type: string
 *         verifyExpires:
 *           type: string
 *         verifyChanges:
 *           type: string
 *         resetToken:
 *           type: string
 *         resetExpires:
 *           type: string
 *         canAskExpert:
 *           type: boolean
 *         deletedAt:
 *           type: string
 *         noConsent:
 *           type: boolean
 *         linkToken:
 *           type: string
 *         unsubscribe:
 *           type: boolean
 *         language:
 *           type: string
 *         role:
 *           type: string
 *         location:
 *           type: string
 *         hireDate:
 *           type: string
 *         employeeId:
 *           type: string
 *         exempt:
 *           type: boolean
 *         managerEmail:
 *           type: integer
 *         samlToken:
 *           type: string
 *         samlExpires:
 *           type: string
 *         scormId:
 *           type: string
 *         lastActivityAt:
 *           type: string
 *         leaveStartedAt:
 *           type: string
 *         internalScormId:
 *           type: string
 *         audio:
 *           type: boolean
 *         authToken:
 *           type: string
 *         authExpires:
 *           type: string
 *         resetDirectLogin:
 *           type: boolean
 *         stateCode:
 *           type: string
 *         countryCode:
 *           type: string
 *         resetPasswordRequired:
 *           type: boolean
 *         notificationEmail:
 *           type: string
 *         slackId:
 *           type: string
 *         slackTeamId:
 *           type: string
 *         demographicDataRequested:
 *           type: boolean
 *         gender:
 *           type: string
 *         transgender:
 *           type: string
 *         heterosexual:
 *           type: string
 *         raceEthnicity:
 *           type: string
 *         disabilityStatus:
 *           type: string
 *         veteranStatus:
 *           type: string
 *         hireDateBkp:
 *           type: string
 *         accounts:
 *           type: array
 *           items:
 *             type: object
 *         groups:
 *           type: array
 *           items:
 *             type: object
 *         roles:
 *           type: array
 *           items:
 *             type: object
 *         accountFields:
 *           type: array
 *           items:
 *             type: object
 *         topicIds:
 *           type: array
 *           items:
 *             type: string

 *     Role:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string

 *     UserRole:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         userId:
 *           type: integer
 *         roleId:
 *           type: integer
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string

 *     userRoles:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         groupId:
 *           type: integer
 *         memberId:
 *           type: integer
 *         memberType:
 *           type: string
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string

 *     Permission:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string

 *     UserMotivation:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         userId:
 *           type: integer
 *         motivationId:
 *           type: string
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 */

/**
 * @swagger
 * /users/transcriptDownload:
 *   post:
 *     summary: Download user transcript report as PDF
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Transcript download parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: integer
 *                 description: ID of the user to generate transcript for
 *                 example: 123
 *               includePreview:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to include userLessons with sourceLifecycle preview
 *             required:
 *               - userId
 *     responses:
 *       200:
 *         description: PDF file download
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: User not found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.transcriptDownload = async (req, res, next) => {
  const defaults = {
    order: [['id', 'DESC']],
    limit: 500,
    offset: 0,
  };
  const includePreview = req?.body?.includePreview;
  let browser;
  try {
    const reportData = await userUtils.getTranscriptDownloadReport(req, defaults, includePreview);
    browser = await getLaunchBrowser(browser);

    const { pdfFilename, clientFilename } = await getTranscriptReport(reportData, browser);
    res.setHeader('Access-Control-Expose-Headers', 'content-disposition');
    res.download(pdfFilename, clientFilename, async (err) => {
      if (err) {
        throw err;
      }
      await fs.unlinkSync(pdfFilename);
    });
  } catch (err) {
    next(err);
  } finally { 
    setTimeout(async () => {
      if (browser) {
        await browser.close();
        browser = null;
      }
    }, 1000);
  }
};

/**
 * @swagger
 * /users/marketingEmailInfo:
 *   post:
 *     summary: Store user's marketing email prompt info and send platform event to SF.
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Prompt Info
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/users'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.marketingEmailInfo = async (req, res, next) => {
  try {
    const { prompted, subscribed } = req.body;
    const userId = req.user.id;
    await marketingEmailOptin.update({ prompted, optInStatus: subscribed }, { where: { userId } });
    if (subscribed) {
      const sfEventBody = {};
      sfEventBody.AI_Account_Instance_ID__c = req.user.accounts[0].id;
      sfEventBody.Email__c = req.user.email;
      sfEventBody.Emtrain_User_ID__c = userId;
      sfEventBody.First_Name__c = req.user.firstName;
      sfEventBody.Last_Name__c = req.user.lastName;
      sfEventBody.Marketing_Opt_In__c = subscribed;
      await sendSFPlatformEvent('Emtrain_Opt_In_Event__e', sfEventBody);
    }
    res.json({ status: 'ok' });
  } catch (err) {
    next(err);
  }
};
