const db = require('../db');

const Op = db.Sequelize.Op;
const { getAllowedPermissions } = require('../services/acl/acl');

const isOwner = (user, qa) => {
  return (user ? user.id === qa.userId : false);
};

const mostRecentEvent = (e1, e2) => {
  if (!e1) {
    return e2;
  }
  if (!e2) {
    return e1;
  }
  return (e1.createdAt <= e2.createdAt ? e2 : e1);
};

module.exports.read = async (req, res, next) => {
  // TODO: GDPR?
  try {
    const selectResourceEvent = await db.events.findOne({
      attributes: ['trackableId', 'trackableType', 'createdAt'],
      where: {
        userId: req.questionAnswer.userId,
        createdAt: {
          [Op.lte]: req.questionAnswer.createdAt,
        },
        trackableId: {
          [Op.ne]: null,
        },
        trackableType: {
          [Op.in]: ['resource'],
        },
      },
      order: [['createdAt', 'DESC']],
    });
    const selectViewLessonCardEvent = await db.viewLessonCardEvents.findOne({
      attributes: ['lessonCardId', 'createdAt'],
      where: {
        userId: req.questionAnswer.userId,
        createdAt: {
          [Op.lte]: req.questionAnswer.createdAt,
        },
        lessonCardId: {
          [Op.ne]: null,
        },
      },
      order: [['createdAt', 'DESC']],
    });

    const selectEvent = mostRecentEvent(selectResourceEvent, selectViewLessonCardEvent);

    let resource;
    let resourceObject;
    if (selectEvent.trackableType === 'resource') {
      resource = await selectEvent.getResource({ raw: true });
      resourceObject = await db[resource.digestable].findOne({
        where: {
          resourceId: resource.id,
        },
        raw: true,
      });
    } else {
      const lessonCard = await selectEvent.getLessonCard({
        include: {
          model: db.lessons,
        },
        raw: true,
        nest: true,
      });
      resourceObject = {};
      resource = {
        ...lessonCard.lessons,
        lessonCards: [],
      };
    }
    res.json({ ...resource, digestableId: resourceObject.id, resourceObject });
  } catch (err) {
    next(err);
  }
};

module.exports.questionById = async (req, res, next, id) => {
  try {
    const [qa, allowedPermissions] = await Promise.all([
      db.questionAnswers.findByPk(id),
      getAllowedPermissions(req, req.user.id, 'questionAnswers', req.tokenPayload),
    ]);

    if (!qa) {
      const err = new Error(req.i18n.t('questionAnswers.qa_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    if (qa.deletedAt !== null &&
      !allowedPermissions.questionAnswers.includes('readArchived')) {
      const err = new Error(req.i18n.t('questionAnswers.qa_no_permissions_Error'));
      err.status = 401;
      throw err;
    }
    if (qa.isPrivate &&
      !(allowedPermissions.questionAnswers.includes('readPrivate')
        || isOwner(req.user, req.questionAnswer))) {
      const err = new Error(req.i18n.t('questionAnswers.qa_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    req.questionAnswer = qa;
    next();
  } catch (err) {
    next(err);
  }
};
