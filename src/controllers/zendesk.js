const logger = require('../logger');
const { get } = require('lodash');
const { Readable } = require('stream');
const config = require('../config/config');
const zendesk = require('node-zendesk');

const ZENDESK_BASE_URL = get(config, 'zendesk.apiBaseURL');
const ZENDESK_LOGIN = get(config, 'zendesk.login');
const ZENDESK_API_KEY = get(config, 'zendesk.apiKey');


async function createAttachment(client, name, buffer) {
  const stream = new Readable();
  stream.push(buffer);
  stream.push(null);


  const attachment = await client.attachments.upload(stream, { binary: true, filename: name });
  return attachment;
}

async function createSupportTicket(client, name, email, subject, message, url, uploads) {
  const messageWithURL = `${message}\n\n---------------------\n\nSubmitted from: ${url}`;
  const ticket = {
    ticket:
      {
        title: subject,
        subject,
        comment: {
          body: messageWithURL,
          ...(uploads && { uploads }),
        },
        requester: { name, email },
      },
  };

  const result = await client.tickets.create(ticket);
  return result;
}

async function createZendeskTicket(req, res, next) {
  try {
    const client = zendesk.createClient({
      username: ZENDESK_LOGIN,
      token: ZENDESK_API_KEY,
      subdomain: ZENDESK_BASE_URL,
    });
    const ticketsData = Array.isArray(req.body) ? req.body : [req.body];
    const result = [];

    for (const ticket of ticketsData) {
      // =======================
      // Attachment handling skipped
      // =======================
      // const attachments = [];
      // Check to see if the ticket contains any attachments.
      // if (req.files && req.files.length > 0) {
      //   for (const nextFile of req.files) {
      //     const attachment = await createAttachment(client, nextFile.originalname, nextFile.buffer);
      //     attachments.push(attachment);
      //   }
      // }
      // const attachmentUploadTokens = attachments.map(attachment => attachment.upload.token);

      const supportTicket = await createSupportTicket(
        client,
        ticket.name,
        ticket.email,
        ticket.subject,
        ticket.message,
        ticket.url,
        // Attachment upload is currently disabled, so we pass null.
        null,
        // attachmentUploadTokens.length > 0 ? attachmentUploadTokens : null,
      );
      result.push({ id: supportTicket?.result?.id });
    }

    res.json(result);
  } catch (e) {
    logger.error(`Could not create zendesk ticket: ${e}`);
    next(e);
  }
}

module.exports = {
  createZendeskTicket,
};
