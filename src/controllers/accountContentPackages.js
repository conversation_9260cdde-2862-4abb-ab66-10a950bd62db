const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { restSortToSequelize } = require('../services/utils/resourceUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { addContentPackagesToAccount } = require('../services/utils/contentPackageUtils');

const AccountContentPackages = db.accountContentPackages;
const ContentPackages = db.contentPackages;

const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize({
    ..._.omit(query, [
      '$limit', '$skip', '$sort',
    ]),
  });
  const includeParams = [{
    model: ContentPackages,
  }];
  const newQuery = {
    ...defaults,
    where: { ...defaults.where, ...whereClause },
    include: includeParams,
  };

  // override defaults for limit, skip, and sort if passed in by caller
  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }
  return newQuery;
};

module.exports.list = async (req, res, next) => {
  try {
    const account = req.account;
    const defaults = {
      order: [['updatedAt', 'DESC']],
      limit: config.paginate.default,
      offset: 0,
      where: {
        accountId: account.id,
      },
    };
    const finalQuery = restQueryToSequelize(req.query, defaults);

    const accountContentPackages = await AccountContentPackages.findAll(finalQuery);
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
    };
    const count = await AccountContentPackages.count(countQuery);

    const data = accountContentPackages.map(cpr => cpr.contentPackage);

    res.json({ total: count, limit: finalQuery.limit, skip: finalQuery.skip, data });
  } catch (err) {
    next(err);
  }
};

module.exports.create = async (req, res, next) => {
  try {
    const contentPackageIds = req.body.contentPackageIds;
    const account = req.account;
    const status = await addContentPackagesToAccount(account.id, contentPackageIds);
    res.json({ status });
  } catch (err) {
    next(err);
  }
};
module.exports.delete = async (req, res, next) => {
  try {
    const { contentPackageId } = req.params;
    const account = req.account;
    await AccountContentPackages.destroy({
      where: {
        accountId: account.id,
        contentPackageId,
      },
    });
    res.json({ status: 'ok' });
  } catch (err) {
    next(err);
  }
};
