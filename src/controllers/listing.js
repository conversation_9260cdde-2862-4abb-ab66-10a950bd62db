/* eslint-disable no-param-reassign */
const _ = require('lodash');
const moment = require('moment');

const db = require('../db');
const config = require('../config/config');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { restSortToSequelize, customSorting } = require('../services/utils/resourceUtils');
const { uploadFile } = require('../services/utils/fileUtils');
const { Op } = require('sequelize');
const { getCatalogGeography } = require('../services/utils/catalogUtils');
const { getProgramLessonByCatalogItems, getLessonByCatalogItems, formatListingLessonsList } = require('../services/utils/listingUtils');

const CatalogItems = db.catalogItems;
const Listing = db.listings;
const Countries = db.countries;
const Experts = db.experts;
const Concepts = db.concepts;
const SocialCapitalPillars = db.socialCapitalPillars;
const SocialCapitalIndicators = db.socialCapitalIndicators;
const States = db.states;
const ListingConcepts = db.listingConcepts;
const ListingExperts = db.listingExperts;
const ListingIndicators = db.listingIndicators;
const ListingPillars = db.listingPillars;
const ListingLessonList = db.listingLessonListItems;
const Categories = db.categories;

const getIncludeParams = async (isListingAssociate = false) => {
  const includeParams = [
    {
      association: Listing.associations.thumbnail,
    },
    {
      model: Countries,
      attributes: ['id', 'countryName', 'countryCode'],
    },
    {
      model: Concepts,
      attributes: ['id', 'concept'],
      include: [
        {
          model: Categories,
          attributes: ['id', 'priority', 'name'],
        }
      ]
    },
    {
      model: Experts,
      attributes: ['id', 'title', 'firstName', 'lastName', 'bio'],
    },
    {
      model: SocialCapitalPillars,
      attributes: ['id', 'name'],
    },
    {
      model: SocialCapitalIndicators,
      attributes: ['id', 'name'],
    },
    {
      model: CatalogItems,
      attributes: ['id', 'audience', 'duration', 'edition', 'part', 'frequency', 'isListingAssociate', 'pwhUsOrder'],
      include: [
        {
          model: States,
          attributes: ['id', 'stateName', 'stateCode'],
        },
      ],
    },
    {
      model: db.users,
      attributes: ['id', 'firstName', 'lastName'],
      as: 'listingCreatedBy',
    },
    {
      model: db.users,
      attributes: ['id', 'firstName', 'lastName'],
      as: 'listingUpdatedBy',
    }
  ];
  if (isListingAssociate) {
    includeParams[6].where = {
      isListingAssociate: true,
    }
    includeParams[6].required = false
  }
  return includeParams;
};

/**
 * converts a rest query from the client to sequelize params
 */
const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize(_.omit(
    query,
    ['$limit', '$skip', '$sort'],
  ));
  const newQuery = {
    ...defaults,
  };
  // eslint-disable-next-line no-undef
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    const paginateMax = 700;
    const limit = parseInt(query.$limit);
    newQuery.limit = Math.min(paginateMax, limit);
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  return newQuery;
};

const handleFormValues = (data) => {
  data.rank = parseInt(data.rank) || null;
  data.microLessonPreviewId = parseInt(data.microLessonPreviewId) || null;
  data.lifecycle = data.lifecycle || 'draft';
  return data;
};

const formatSocialData = (id, data, key) => {
  const finalArray = data.map((n) => {
    const createObj = {};
    createObj.listingId = id;
    createObj[key] = n;
    return createObj;
  });
  return finalArray;
};

const saveAssociations = async (id, associationData, key, modelName, transact) => {
  try {
    const saveData = formatSocialData(id, associationData, key);
    await db[modelName].bulkCreate(saveData, { transaction: transact });
  } catch (err) {
    throw err;
  }
};

const updateAssociations = async (id, associationData, key, modelName, transact) => {
  try {
    const listingAssociation = await db[modelName].findAll({
      attributes: ['listingId', key],
      where: {
        listingId: id,
      },
    });
    let existingAssociation = listingAssociation.map(cpr => cpr[key]);
    existingAssociation = [...new Set(existingAssociation)];
    const newAssociation = associationData.filter((cId) => {
      return !existingAssociation.find(cc => cc === cId);
    });
    const deletedAssociations = existingAssociation.filter((cId) => {
      return !associationData.find(cc => cc === cId);
    });
    if (deletedAssociations.length) {
      const delObj = { listingId: id };
      delObj[key] = deletedAssociations;
      await db[modelName].destroy({ where: delObj, transaction: transact });
    }
    if (newAssociation.length) {
      const saveData = formatSocialData(id, newAssociation, key);
      await db[modelName].bulkCreate(saveData, { transaction: transact });
    }
  } catch (err) {
    throw err;
  }
};

const getNextListingRank = async () => {
  const lastCategory = await Listing.findOne({ attributes: ['rank'], order: [['rank', 'desc']] });
  return lastCategory?.rank ? lastCategory?.rank + 1 : 1;
};

const saveListingUpdatedFields = async (lId, userId) => {
  const data = {
    updatedBy: userId,
    updatedAt: moment.utc().format("YYYY-MM-DD HH:mm:ss")
  };
  await Listing.update(data, {
    where: { id: lId }
  });
}

/**
 * @openapi
 * /listing:
 *   post:
 *     summary: Create Listing Item
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       description: The model for the parameters are described below.
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/createListing'
 *           example:
 *             type: course
 *             rank: 1
 *             lifecycle: published
 *             title: Sample Listing
 *             description: Description of the listing
 *             subtitle: Subtitle of the listing
 *             marketingCopy: Marketing copy here
 *             featuredSnippet: Featured snippet here
 *             webUrl: https://example.com
 *             countryId: 91
 *             listingExperts: [1, 2]
 *             listingConcepts: [1]
 *             listingIndicators: [1]
 *             listingPillars: [1, 2]
 *     responses:
 *       200:
 *         description: Listing added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Listing added successfully
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     createListing:
 *       type: object
 *       required:
 *         - type
 *         - rank
 *         - lifecycle
 *         - countryId
 *       properties:
 *         type:
 *           type: string
 *           description: The listing type (course/microlesson/diagnostic)
 *         rank:
 *           type: integer
 *           description: The rank of the listing item
 *         lifecycle:
 *           type: string
 *           description: The lifecycle of the listing item (draft/published/retired)
 *         title:
 *           type: string
 *           description: Title of the listing item
 *         description:
 *           type: string
 *           description: Description of the listing item
 *         subtitle:
 *           type: string
 *           description: Subtitle of the listing item
 *         marketingCopy:
 *           type: string
 *           description: Marketing copy of the listing item
 *         featuredSnippet:
 *           type: string
 *           description: Featured snippet of the listing item
 *         webUrl:
 *           type: string
 *           description: Web URL of the listing item
 *         countryId:
 *           type: integer
 *           description: Country ID of the listing item
 *         listingExperts:
 *           type: array
 *           items:
 *             type: integer
 *           example: [1, 2]
 *         listingConcepts:
 *           type: array
 *           items:
 *             type: integer
 *           example: [1]
 *         listingIndicators:
 *           type: array
 *           items:
 *             type: integer
 *           example: [1]
 *         listingPillars:
 *           type: array
 *           items:
 *             type: integer
 *           example: [1, 2]
 */
module.exports.create = async (req, res, next) => {
  let newListing;
  const data = req.body;
  try {
    const newFile = await uploadFile(req, 'listing');
    if (newFile) {
      data.thumbnailId = newFile.id;
    }
    if (!data.rank) {
      data.rank = await getNextListingRank();
    }
    data.createdBy = req.user.id;
    await db.sequelize.transaction(async (transact) => {
      newListing = await Listing.create(handleFormValues(data), {
        transaction: transact,
      });
      // association save
      if (newListing.id) {
        const promise = [];
        if (data.listingConcepts) {
          promise.push(saveAssociations(newListing.id, data.listingConcepts, 'conceptId', 'listingConcepts', transact));
        }
        if (data.listingExperts) {
          promise.push(saveAssociations(newListing.id, data.listingExperts, 'expertId', 'listingExperts', transact));
        }
        if (data.listingIndicators) {
          promise.push(saveAssociations(newListing.id, data.listingIndicators, 'indicatorId', 'listingIndicators', transact));
        }
        if (data.listingPillars) {
          promise.push(saveAssociations(newListing.id, data.listingPillars, 'pillarId', 'listingPillars', transact));
        }
        if (promise.length) {
          await Promise.all(promise);
        }
      }
    });
    res.json(newListing);
  } catch (err) {
    if (err.message === 'Validation error') {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /listing/{listingId}:
 *   patch:
 *     summary: Update Listing Item
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: listingId
 *         in: path
 *         required: true
 *         description: ID of the listing to update
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       description: The model for the parameters are described below.
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/updateListing'
 *           example:
 *             type: microlesson
 *             rank: 2
 *             lifecycle: draft
 *             title: Updated Listing Title
 *             description: Updated description
 *             subtitle: Updated subtitle
 *             marketingCopy: Updated marketing copy
 *             featuredSnippet: Updated snippet
 *             webUrl: https://updated.example.com
 *             countryId: 91
 *             listingExperts: [2]
 *             listingConcepts: [1, 2]
 *             listingIndicators: [1, 2]
 *             listingPillars: [1]
 *     responses:
 *       200:
 *         description: Listing updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Listing updated successfully
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Listing not found
 *       5XX:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     updateListing:
 *       type: object
 *       required:
 *         - type
 *         - rank
 *         - lifecycle
 *         - countryId
 *       properties:
 *         type:
 *           type: string
 *           description: The listing type (course/microlesson/diagnostic)
 *         rank:
 *           type: integer
 *           description: The rank of the listing item
 *         lifecycle:
 *           type: string
 *           description: The lifecycle of the listing item (draft/published/retired)
 *         title:
 *           type: string
 *           description: Title of the listing item
 *         description:
 *           type: string
 *           description: Description of the listing item
 *         subtitle:
 *           type: string
 *           description: Subtitle of the listing item
 *         marketingCopy:
 *           type: string
 *           description: Marketing copy of the listing item
 *         featuredSnippet:
 *           type: string
 *           description: Featured snippet of the listing item
 *         webUrl:
 *           type: string
 *           description: Web URL of the listing item
 *         countryId:
 *           type: integer
 *           description: Country ID of the listing item
 *         listingExperts:
 *           type: array
 *           items:
 *             type: integer
 *           example: [2]
 *         listingConcepts:
 *           type: array
 *           items:
 *             type: integer
 *           example: [1, 2]
 *         listingIndicators:
 *           type: array
 *           items:
 *             type: integer
 *           example: [1, 2]
 *         listingPillars:
 *           type: array
 *           items:
 *             type: integer
 *           example: [1]
 */
module.exports.patch = async (req, res, next) => {
  try {
    const { listingId } = req.params;
    const existingListing = req.listing;
    const modifiedListing = req.body;
    if (modifiedListing.associatedCatalogItems && typeof (modifiedListing.associatedCatalogItems) === 'string') {
      modifiedListing.associatedCatalogItems = JSON.parse(modifiedListing.associatedCatalogItems);
    }
    const newFile = await uploadFile(req, 'listing');
    if (newFile) {
      modifiedListing.thumbnailId = newFile.id;
    }
    const isLifecycleChangeOnly = Object.keys(modifiedListing).length === 1 && modifiedListing.lifecycle;
    modifiedListing.rank = existingListing.rank;
    if (isLifecycleChangeOnly) {
      modifiedListing.microLessonPreviewId = existingListing.microLessonPreviewId;
    } else {
      modifiedListing.lifecycle = existingListing.lifecycle;
    }
    modifiedListing.updatedBy = req.user.id;
    await db.sequelize.transaction(async (transact) => {
      await Listing.update(handleFormValues(modifiedListing), {
        where: {
          id: listingId,
        }
      });
      // catalogItem association update
      if (!isLifecycleChangeOnly) {
        const modifiedListingCatalog = modifiedListing.associatedCatalogItems;
        let isModifiedListingCatalogs = [];
        if (modifiedListingCatalog) {
          isModifiedListingCatalogs = modifiedListingCatalog.filter(({ isListingAssociate }) => isListingAssociate === true || isListingAssociate === 'true');
        }
        if (!isModifiedListingCatalogs?.length && existingListing.lifecycle === 'published') {
          const err = new Error('Published Listings must have at least one active Catalog Item association');
          err.status = 400;
          throw err;
        } else if (modifiedListingCatalog) {
          const promise = [];
          for (const catalog of modifiedListingCatalog) {
            const pwhUsOrder = String(modifiedListing.isSpecialPwhUs) === 'true' && String(catalog.isListingAssociate) === 'true'? catalog.pwhUsOrder !== '-1' ? catalog.pwhUsOrder : null: null;

            promise.push(CatalogItems.update({ isListingAssociate: catalog.isListingAssociate, pwhUsOrder }, {
              where: {
                id: catalog.id,
              },
            }));
          }
          await Promise.all(promise);
        }
      }
      // association save
      const promise = [];
      if (modifiedListing.listingConcepts) {
        promise.push(updateAssociations(listingId, modifiedListing.listingConcepts, 'conceptId', 'listingConcepts', transact));
      } else if (!isLifecycleChangeOnly) {
        promise.push(ListingConcepts.destroy({ where: { listingId } }));
      }
      if (modifiedListing.listingExperts) {
        promise.push(updateAssociations(listingId, modifiedListing.listingExperts, 'expertId', 'listingExperts', transact));
      } else if (!isLifecycleChangeOnly) {
        promise.push(ListingExperts.destroy({ where: { listingId } }));
      }
      if (modifiedListing.listingIndicators) {
        promise.push(updateAssociations(listingId, modifiedListing.listingIndicators, 'indicatorId', 'listingIndicators', transact));
      } else if (!isLifecycleChangeOnly) {
        promise.push(ListingIndicators.destroy({ where: { listingId } }));
      }
      if (modifiedListing.listingPillars) {
        promise.push(updateAssociations(listingId, modifiedListing.listingPillars, 'pillarId', 'listingPillars', transact));
      } else if (!isLifecycleChangeOnly) {
        promise.push(ListingPillars.destroy({ where: { listingId } }));
      }
      if (promise.length) {
        await Promise.all(promise);
      }
    });
    const updatedListing = await Listing.findByPk(listingId, {
      include: await getIncludeParams(),
    });
    res.json(updatedListing);
  } catch (err) {
    if (err.message === 'Validation error') {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /listing:
 *   get:
 *     summary: Fetch all the Listing Item Values
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successfully fetched all Listing Items
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/listingItem'
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     listingItem:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         title:
 *           type: string
 *         description:
 *           type: string
 *         type:
 *           type: string
 *         lifecycle:
 *           type: string
 *         rank:
 *           type: integer
 *         countryId:
 *           type: integer
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */
module.exports.list = async (req, res, next) => {
  try {
    const defaults = {
      order: [['updatedAt', 'DESC']],
      limit: config.paginate.default,
      offset: 0,
    };
    const queryParams = restQueryToSequelize(req.query, defaults);
    queryParams.order = [[...queryParams.order]];
    const finalQuery = {
      ...queryParams,
      where: queryParams.where,
      include: await getIncludeParams(true),
    };
    const pagedResult = {
      limit: queryParams.limit,
      skip: queryParams.offset,
    };
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
      include: _.pick(finalQuery, ['include']).include,
      distinct: true,
    };
    const count = await Listing.count(countQuery);
    const data = await Listing.findAll(finalQuery);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /listing/{listingId}:
 *   get:
 *     summary: Fetch Listing Item based on listingId
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: listingId
 *         in: path
 *         required: true
 *         description: The ID of the existing Listing
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successfully fetched particular Listing Item based on its ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/listingItem'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Listing not found
 *       5XX:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     listingItem:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         title:
 *           type: string
 *         description:
 *           type: string
 *         type:
 *           type: string
 *         lifecycle:
 *           type: string
 *         rank:
 *           type: integer
 *         countryId:
 *           type: integer
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */
module.exports.read = async (req, res, next) => {
  try {
    const listingData = req.listing.get({ plain: true });
    res.json(listingData);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /listing/{listingId}:
 *   delete:
 *     summary: Delete the existing Listing by ID
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: listingId
 *         in: path
 *         required: true
 *         description: The ID of the existing Listing to be deleted
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successfully deleted Listing record
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Listing deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Listing not found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  try {
    const { listingId } = req.params;
    const data = await Listing.destroy({
      where: {
        id: listingId,
      },
    });
    await Promise.all([
      ListingConcepts.destroy({ where: { listingId } }),
      ListingExperts.destroy({ where: { listingId } }),
      ListingIndicators.destroy({ where: { listingId } }),
      ListingPillars.destroy({ where: { listingId } }),
    ]);
    res.json(data);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /listing/listingMasterData:
 *   get:
 *     summary: Fetch all the dropdown values for the listing page
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successfully fetched all the dropdown values
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 listingTypes:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["course", "microlesson", "diagnostic"]
 *                 lifecycles:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["draft", "published", "retired"]
 *                 countries:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       name:
 *                         type: string
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 */
module.exports.listingMasterData = async (req, res, next) => {
  try {
    const { forlist } = req.query;
    if (!forlist) {
      const countryList = await getCatalogGeography();
      const conceptList = Concepts.findAll({
        attributes: ['id', 'concept'],
        order: [['concept', 'ASC']],
      });
      const expertList = Experts.findAll({
        attributes: ['id', 'title', 'firstName', 'lastName'],
        order: [['title', 'ASC']],
      });
      const indicatorsList = SocialCapitalIndicators.findAll({
        attributes: ['id', 'name'],
        order: [['name', 'ASC']],
      });
      const pillarsList = SocialCapitalPillars.findAll({
        attributes: ['id', 'name'],
        order: [['name', 'ASC']],
      });
      const masterData = await Promise.all([
        countryList, conceptList, expertList, indicatorsList, pillarsList,
      ]);
      res.json({
        countryList: masterData[0],
        conceptList: masterData[1],
        expertList: masterData[2],
        indicatorsList: masterData[3],
        pillarsList: masterData[4],
      });
    } else if (forlist === 'country') {
      const countryList = await getCatalogGeography();
      res.json({
        countryList,
      });
    } else if (forlist === 'concept') {
      const conceptList = await Concepts.findAll({
        attributes: ['id', 'concept'],
        order: [['concept', 'ASC']],
      });
      res.json({
        conceptList,
      });
    } else if (forlist === 'expert') {
      const expertList = await Experts.findAll({
        attributes: ['id', 'title', 'firstName', 'lastName'],
        order: [['title', 'ASC']],
      });
      res.json({
        expertList,
      });
    } else if (forlist === 'indicators') {
      const indicatorsList = await SocialCapitalIndicators.findAll({
        attributes: ['id', 'name'],
        order: [['name', 'ASC']],
      });
      res.json({
        indicatorsList,
      });
    } else if (forlist === 'pillars') {
      const pillarsList = await SocialCapitalPillars.findAll({
        attributes: ['id', 'name'],
        order: [['name', 'ASC']],
      });
      res.json({
        pillarsList,
      });
    } else {
      res.status(400).send({
        message: 'invalid value for forlist',
      });
    }
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /listing/rankListings:
 *   post:
 *     summary: Update Listings rank
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: The model for the parameters are described below.
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/updateListingRank'
 *           example:
 *             listings:
 *               - id: 1
 *                 rank: 1
 *     responses:
 *       200:
 *         description: Listing updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *
 * components:
 *   schemas:
 *     updateListingRank:
 *       type: object
 *       description: Model to update Listing rank
 *       properties:
 *         listings:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: integer
 *               rank:
 *                 type: integer
 *           example:
 *             - id: 1
 *               rank: 1
 *       required:
 *         - listings
 */
module.exports.rankListings = async (req, res, next) => {
  try {
    const data = req.body.listings;
    if (data.length) {
      const promise = [];
      for (const listing of data) {
        promise.push(Listing.update({ rank: listing.rank }, {
          where: {
            id: listing.id,
          },
        }));
      }
      await Promise.all(promise);
    }
    res.json(data);
  } catch (err) {
    if (err.message === 'Validation error') {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};


/**
 * @openapi
 * /listing/{listingId}/lessonList:
 *   get:
 *     summary: Fetch all the lessons list for a listing item
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: listingId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the listing item
 *     responses:
 *       200:
 *         description: Successfully fetched all the listing lesson list
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 lessons:
 *                   type: array
 *                   items:
 *                     type: object
 *                     # Define lesson item schema here or $ref
 *                     example:
 *                       id: 123
 *                       title: "Sample Lesson"
 */
module.exports.listingLessonList = async (req, res, next) => {
  try {
    const { listingId } = req.params;
    const catalogItemIds = req.listing.catalogItems ? req.listing.catalogItems.map(l => l.id) : [];
    if (!catalogItemIds.length) {
      const err = new Error(`Listing ID ${listingId} is not mapped to any catalogitem`);
      err.status = 400;
      throw err;
    }
    const defaults = {
      order: [['id', 'ASC']],
    };
    const queryParams = restQueryToSequelize(req.query, defaults);
    const promise = [];
    // program lessons based on catalogId
    promise.push(getProgramLessonByCatalogItems(catalogItemIds, queryParams));
    // lessons based on catalogId
    promise.push(getLessonByCatalogItems(catalogItemIds, queryParams));
    promise.push(ListingLessonList.count({ where: { listingId }, paranoid: false }));
    const lessonsData = await Promise.all(promise);

    const finalData = formatListingLessonsList(lessonsData, queryParams);
    const listingData = {
      id: req.listing.id,
      title: req.listing.title,
    };
    res.json({ listingData, listingLessonData: finalData, listingLessonSavedCount: lessonsData[2] });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /listing/{listingId}/listingLessonList:
 *   post:
 *     summary: Create Listing Lesson Item
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: listingId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the listing item
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/createListingList'
 *           example:
 *             id: "lesson123"
 *             rank: 1
 *             title: "Sample Lesson Title"
 *             description: "Optional description of the lesson item"
 *     responses:
 *       200:
 *         description: Listing added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               # Define response schema or $ref here if needed
 *
 * components:
 *   schemas:
 *     createListingList:
 *       type: object
 *       required:
 *         - id
 *         - rank
 *         - title
 *       properties:
 *         id:
 *           type: string
 *           description: The lessonId
 *         rank:
 *           type: integer
 *           description: The rank of the Listing Lesson item
 *         title:
 *           type: string
 *           description: Title of the Listing Lesson item
 *         description:
 *           type: string
 *           description: Description of the Listing Lesson item
 */
module.exports.createListingLessonList = async (req, res, next) => {
  let data = req.body;
  const listingId = req.listing.id;
  try {
    const catalogItemIds = req.listing.catalogItems ?
      req.listing.catalogItems.filter((l) => l.isListingAssociate === true).map(o => o.id)
        .join(',') : null;
    const lData = data.map((d, i) => {
      d.listingId = listingId;
      d.catalogItemIds = catalogItemIds;
      d.lessonId = d.id;
      d.rank = i + 1;
      delete d.id;
      return d;
    });
    const promiseData = await Promise.all([ListingLessonList.bulkCreate(lData),
    saveListingUpdatedFields(listingId, req.user.id)
    ]);
    res.json(promiseData[0]);
  } catch (err) {
    if (err.message === 'Validation error') {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /listing/{listingId}/listingLessonList:
 *   get:
 *     summary: Fetch All the lessons list for saved listing list Item
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: listingId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the listing item
 *       - name: Authorization
 *         in: header
 *         required: true
 *         schema:
 *           type: string
 *         description: The authentication token of a user
 *     responses:
 *       200:
 *         description: Successfully fetched all the saved listing lesson list
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/lessons'  # Adjust this ref to your actual lesson schema
 */
module.exports.listListingLessons = async (req, res, next) => {
  try {
    const { listingId } = req.params;
    const listingListData = await ListingLessonList.findAll({ where: { listingId }, paranoid: false, order: [['rank', 'ASC']] });
    const listingData = {
      id: req.listing.id,
      title: req.listing.title,
      catalogItems: req.listing.catalogItems.filter(l => l.isListingAssociate === true),
    }
    res.json({ listingData, listingListData });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /listing/{listingId}/listingLessonList:
 *   patch:
 *     summary: Update Listing List Lesson Item
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: listingId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the listing item
 *       - name: Authorization
 *         in: header
 *         required: true
 *         schema:
 *           type: string
 *         description: The authentication token of a user
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/updateListingLessonList'
 *           examples:
 *             example1:
 *               summary: Example update payload
 *               value:
 *                 type: "course"
 *                 id: 123
 *                 title: "Updated Lesson Title"
 *                 description: "Updated description"
 *                 catalogItemIds: "456,789"
 *     responses:
 *       200:
 *         description: Listing lesson list updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Listing lesson list updated successfully
 *
 * components:
 *   schemas:
 *     updateListingLessonList:
 *       type: object
 *       description: Model to update Lesson Listing List
 *       properties:
 *         type:
 *           type: string
 *           description: The listing Type of the Listing item (course/microlesson/diagnostic)
 *         id:
 *           type: integer
 *           description: The id of the Listing Lesson item (optional)
 *         title:
 *           type: string
 *           description: The title of the Listing item
 *         description:
 *           type: string
 *           description: Description of the Listing Lesson item
 *         catalogItemIds:
 *           type: string
 *           description: catalogItemIds of the Listing Lesson item
 *       required:
 *         - type
 *         - title
 */
module.exports.patchListingLessons = async (req, res, next) => {
  try {
    const { listingId } = req.params;
    const data = req.body;
    let rank = 1;
    await db.sequelize.transaction(async (transact) => {
      for await (const lData of data) {
        lData.rank = rank;
        lData.listingId = listingId;
        if (lData.catalogItemIds && typeof (lData.catalogItemIds) === 'object') {
          lData.catalogItemIds = lData.catalogItemIds.join(',');
        }
        if (lData.id) {
          await ListingLessonList.update(lData, { where: { id: lData.id } });
        } else {
          await ListingLessonList.create(lData, { transaction: transact });
        }
        rank++;
      }
    });
    const promiseData = await Promise.all([ListingLessonList.findAll({ where: { listingId }, paranoid: false, order: [['rank', 'ASC']] }),
    saveListingUpdatedFields(listingId, req.user.id)
    ]);
    res.json(promiseData[0]);
  } catch (err) {
    if (err.message === 'Validation error') {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /listing/{listingId}/listingLessonList:
 *   delete:
 *     summary: Delete the existing listing lessons based on listingId
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: listingId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the listing item
 *       - name: Authorization
 *         in: header
 *         required: true
 *         schema:
 *           type: string
 *         description: The authentication token of a user
 *     responses:
 *       200:
 *         description: Successfully deleted listing lessons list
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Successfully deleted listing lessons list
 */
module.exports.deleteAllListingLessons = async (req, res, next) => {
  try {
    const { listingId } = req.params;
    const promiseData = await Promise.all([ListingLessonList.destroy({ where: { listingId } }),
    saveListingUpdatedFields(listingId, req.user.id)
    ]);
    res.json(promiseData[0]);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /listing/{listingId}/listingLessonList/{lessonId}:
 *   delete:
 *     summary: Delete the existing listing lesson based on listingId and lessonId
 *     tags:
 *       - Listings
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: listingId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the listing item
 *       - name: lessonId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the lesson to delete
 *       - name: Authorization
 *         in: header
 *         required: true
 *         schema:
 *           type: string
 *         description: The authentication token of a user
 *     responses:
 *       200:
 *         description: Successfully deleted listing lesson
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Successfully deleted listing lesson
 */
module.exports.deleteListingLessons = async (req, res, next) => {
  try {
    const { listingId, lessonId } = req.params;
    const listingLessonData = await ListingLessonList.findOne({ where: { listingId, lessonId }, attributes: ['rank'] });
    if (!listingLessonData) {
      const err = new Error(`Failed to load listing lesson with Listing ID ${listingId} & lesson ID ${lessonId} `);
      err.status = 400;
      throw err;
    }
    const promiseData = await Promise.all([ListingLessonList.destroy({ where: { listingId, lessonId } }),
    saveListingUpdatedFields(listingId, req.user.id)
    ]);
    // reorder of rank
    ListingLessonList.decrement('rank', { by: 1, where: { listingId, rank: { [Op.gt]: listingLessonData.rank } } });
    res.json(promiseData[0]);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the Listing when an id is passed in the route
module.exports.listingById = async function (req, res, next, id) {
  const queryParams = {
    where: { id },
    include: await getIncludeParams(),
  };
  try {
    const finalQuery = {
      ...queryParams,
      where: { ...queryParams.where },
    };
    const listing = await Listing.findOne(finalQuery);
    if (!listing) {
      const err = new Error(req.i18n.t('listings.listing_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.listing = listing;
    next();
  } catch (err) {
    next(err);
  }
};
