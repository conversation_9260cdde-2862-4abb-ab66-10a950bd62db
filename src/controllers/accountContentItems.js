const { omit } = require('lodash');

const db = require('../db');

const Op = db.Sequelize.Op;
const { restSortToSequelize } = require('../services/utils/resourceUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');

const AccountContentItems = db.accountContentItems;


const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize({
    ...omit(query, [
      '$limit', '$skip', '$sort',
    ]),
  });

  const newQuery = {
    ...defaults,
    where: { ...defaults.where, ...whereClause },
  };

  // override defaults for limit, skip, and sort if passed in by caller
  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = parseInt(query.$limit);
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }

  return newQuery;
};

/**
 * @swagger
 * /account-content-items:
 *  get:
 *    summary: Fetch Account Content Items List
 *    tags: [Account]
 *    security:
 *      - JWT: []
 *    parameters:
 *      - name: $limit
 *        in: query
 *        description: limit (integer) for number of records
 *        required: true
 *        schema:
 *          type: integer
 *          example: 10
 *      - name: $skip
 *        in: query
 *        description: offset or skip position before beginning to return result
 *        required: true
 *        schema:
 *          type: integer
 *          example: 0
 *    responses:
 *      200:
 *        description: Successfully fetched accounts list
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              properties:
 *                total:
 *                  type: integer
 *                  description: Total number of records.
 *                limit:
 *                  type: integer
 *                  description: Number of records for page.
 *                skip:
 *                  type: integer
 *                  description: Skip number for page number.
 *                data:
 *                  type: array
 *                  description: Array of account content items object.
 *                  items:
 *                    type: object
 *      404:
 *        description: Not Found
 *      401:
 *        description: Unauthorized
 *      400:
 *        description: Bad Request
 *      5XX:
 *        description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  try {
    const accountId = req.user.accountId;
    const defaults = {
      order: [['createdAt', 'DESC']],
      where: {
        accountId,
        lifecycle: { [Op.in]: ['publish', 'retired'] },
      },
    };

    const finalQuery = restQueryToSequelize(req.query, defaults);
    finalQuery.attributes = ['id', 'resourceName', 'resourceId', 'resourceType'];
    const { count, rows } = await AccountContentItems.findAndCountAll(finalQuery);
    res.json({ total: count, limit: finalQuery.limit, skip: finalQuery.offset, data: rows });
  } catch (error) {
    next(error);
  }
};
