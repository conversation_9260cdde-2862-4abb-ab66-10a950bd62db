const db = require('../db');
const { uploadFile } = require('../services/utils/fileUtils');
const { updateElasticSearch } = require('../services/utils/resourceUtils');
const { deriveSearch } = require('../services/utils/lessonUtils');

const AccountLessonCards = db.accountLessonCards;
const LessonCards = db.lessonCards;
const Lessons = db.lessons;
const Programs = db.programs;
const Files = db.files;

const updateLessonSearch = async (req, lessonCardId) => {
  const lessons = await Lessons.findAll({
    include: [
      {
        model: LessonCards,
        where: { id: lessonCardId },
        required: true,
      },
    ],
  });

  if (lessons) {
    for (const lesson of lessons) {
      const searchInfo = await deriveSearch(lesson);
      await updateElasticSearch(req, lesson.resourceId, searchInfo);
    }
  }
};

module.exports.list = async (req, res, next) => {
  try {
    const lessonId = req.query.lessonId;
    const programId = req.query.programId;
    const accountId = req.user.accountId;

    let accountLessonCards = [];

    if (lessonId) {
      accountLessonCards = await AccountLessonCards.findAll({
        where: { accountId },
        include: [
          {
            model: Files,
            as: 'file',
            required: false,
          },
          {
            model: LessonCards,
            include: [
              {
                model: Lessons,
                where: { id: lessonId },
              },
            ],
          },
        ],
      });
    } else if (programId) {
      accountLessonCards = await AccountLessonCards.findAll({
        where: { accountId },
        include: [
          {
            model: Files,
            as: 'file',
            required: false,
          },
          {
            model: LessonCards,
            include: [
              {
                model: Lessons,
                include: [
                  {
                    model: Programs,
                    where: { id: programId },
                  },
                ],
              },
            ],
          },
        ],
      });
    }
    res.json({ accountLessonCards });
  } catch (err) {
    next(err);
  }
};

module.exports.create = async (req, res, next) => {
  try {
    if (!req.body.accountId) {
      req.body.accountId = req.user.accountId;
    }
    if (['null', 'undefined', ''].includes(req.body.fileId)) {
      req.body.fileId = null;
    }

    req.body.link = req.body.policyType === 'file' ? null : req.body.link;
    req.body.fileId = req.body.policyType === 'link' ? null : req.body.fileId;

    const fileFolder = `accounts/${req.body.accountId}/documents`;
    const policyFile = await uploadFile(req, fileFolder, 'en');
    if (policyFile && req.body.policyType === 'file') {
      req.body.fileId = policyFile.id;
    }

    const existingAccountLessonCard = await
    AccountLessonCards.findOne({ where: { accountId: req.body.accountId, lessonCardId: req.body.lessonCardId } });

    let accountLessonCard;
    if (existingAccountLessonCard) {
      req.body.id = existingAccountLessonCard.id;
      accountLessonCard = await existingAccountLessonCard.update(req.body);
    } else {
      accountLessonCard = await AccountLessonCards.create(req.body);
    }
    await updateLessonSearch(req, req.body.lessonCardId);
    res.json(accountLessonCard);
  } catch (err) {
    if (err.name === 'SequelizeValidationError') {
      // statements to handle TypeError exceptions
      err.status = 422;
    }
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  const existingAccountLessonCard = req.accountLessonCard;
  const modifiedAccountLessonCard = req.body;

  try {
    const fileFolder = `accounts/${existingAccountLessonCard.accountId}/documents`;
    const policyFile = await uploadFile(req, fileFolder, 'en');
    modifiedAccountLessonCard.accountId = existingAccountLessonCard.accountId;
    if (policyFile && req.body.policyType === 'file') {
      modifiedAccountLessonCard.link = null;
      modifiedAccountLessonCard.fileId = policyFile.id;
    } else if (req.body.policyType === 'link') {
      req.body.fileId = null;
    }

    const updatedAccountLessonCard = await existingAccountLessonCard.update(modifiedAccountLessonCard);
    const finalAccountLessonCard = await AccountLessonCards.findByPk(updatedAccountLessonCard.id);
    await updateLessonSearch(req, finalAccountLessonCard.lessonCardId);
    res.json(finalAccountLessonCard);
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  try {
    res.json(req.accountLessonCard);
  } catch (err) {
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  const accountLessonCard = req.accountLessonCard;

  try {
    const data = await accountLessonCard.destroy();
    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.accountLessonCardByLessonCardId = async (req, res, next, id) => {
  try {
    const accountId = req.user.accountId;
    const accountLessonCard = await AccountLessonCards.findOne({
      where: { accountId, lessonCardId: id },
      association: AccountLessonCards.associations.file,
    });
    if (!accountLessonCard) {
      const err = new Error(req.i18n.t('accountLessonCards.retrieval_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.accountLessonCard = accountLessonCard;
    next();
  } catch (err) {
    next(err);
  }
};
