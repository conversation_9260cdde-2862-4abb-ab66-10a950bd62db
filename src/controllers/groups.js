const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const logger = require('../logger');
const { createAuditLog } = require('./auditLogs');
const userUtils = require('../services/utils/userUtils');
const { getAccountFields } = require('../services/utils/accountUtils');
const acl = require('../services/acl/acl');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const {
  includeUserCount,
  loadGroup,
  resetDynamicGroupMembers,
  parseRule,
  lockDynamicGroup,
  destroyGroup,
} = require('../services/utils/groupUtils');

const Groups = db.groups;
const GroupRoles = db.groupRoles;
const Roles = db.roles;
const UserGroups = db.userGroups;
const GroupRules = db.groupRules;
const GroupRuleValues = db.groupRuleValues;
const Campaigns = db.campaigns;
const GroupAssignments = db.groupAssignments;
const Op = db.Sequelize.Op;

const setGroupRole = async (group, role) => {
  const roleRecord = await Roles.findOne({
    where: {
      name: role,
    },
  });
  if (roleRecord) {
    const groupRole = await GroupRoles.create({
      groupId: group.id,
      roleId: roleRecord.id,
    });
    return groupRole;
  }
  return null;
};

const updateGroupRoles = async (group, roles) => {
  const role = roles[0]; // not supporting multiple roles on update yet
  const roleRecord = await Roles.findOne({
    where: {
      name: role,
    },
  });
  if (roleRecord) {
    const roleId = roleRecord.id;
    const groupRole = await GroupRoles.update(
      {
        roleId,
      },
      {
        where: {
          groupId: group.id,
        },
        fields: ['roleId'],
      }
    );
    return groupRole;
  }
  return null;
};

const resetRuleValues = async (rule) => {
  await GroupRuleValues.destroy({
    where: {
      ruleId: rule.id,
    },
  });
  return rule.values.map((value) => {
    return GroupRuleValues.create({
      ruleId: rule.id,
      value: value.value,
    });
  });
};
const getObjectDiff = (obj1, obj2, compareRef = false) => {
  return Object.keys(obj1).reduce((result, key) => {
    if (!obj2.hasOwnProperty(key)) {
      result.push(key);
    } else if (_.isEqual(obj1[key], obj2[key])) {
      const resultKeyIndex = result.indexOf(key);

      if (compareRef && obj1[key] !== obj2[key]) {
        result[resultKeyIndex] = `${key} (ref)`; // eslint-disable-line no-param-reassign
      } else {
        result.splice(resultKeyIndex, 1);
      }
    }
    return result;
  }, Object.keys(obj2));
};
const lockGroup = async (req, group) => {
  let groupIsLocked = false;
  if (group && group.groupType === 'dynamic') {
    groupIsLocked = await lockDynamicGroup(group.id);
    if (!groupIsLocked) {
      // if a dynamic group is already updating, exit to avoid concurrent updates.
      const id = group.id;
      logger.error(`Cannot modify locked group: ${id}`);
      const err = new Error(req.i18n.t('groups.group_modify_Error', { id }));
      err.status = 423;
      throw err;
    }
  }
  return groupIsLocked;
};

const unlockGroup = async (groupId) => {
  if (groupId) {
    await Groups.update({ status: 'current' }, { where: { id: groupId } });
  }
};
/*
 * add, modify, and delete any rules for the group
 * in this case, all rules should be there
 */
const updateGroupRules = async (req, group, rules = [], accountFields) => {
  // new rules don't have an id
  const newRules = rules
    .filter((r) => !r.id)
    .map((nr) => {
      return { ...nr, groupId: group.id };
    });
  const oldRules = rules.filter((r) => r.id);
  // Delete rules that don't exist in the rules array
  const toDelete = _.differenceBy(group.groupRules, oldRules, 'id');
  const toUpdate = oldRules.filter((rule) => !toDelete.find((del) => del.id === rule.id));

  const baseLogData = {
    feature: 'groups',
    object: 'groups',
    objectId: group.id,
    childObject: 'groupRules',
  };
  // process any new rules
  if (newRules.length > 0) {
    const promises = [];
    newRules.forEach((nr) => {
      const customFieldAttributes = accountFields.find((af) => af.fieldName === nr.field && !af.isStandard);
      // eslint-disable-next-line no-param-reassign
      nr.clause = parseRule(req, nr, customFieldAttributes);

      const create = GroupRules.create(nr, {
        validate: true,
        include: [
          {
            model: GroupRuleValues,
            as: 'values',
            required: false,
          },
        ],
      });
      promises.push(create);
      const logData = { ...baseLogData };
      logData.action = 'add-group-rule';
      logData.updatedData = { field: nr.field, operator: nr.operator, clause: nr.clause };
      promises.push(createAuditLog(logData, req));
    });

    await Promise.all(promises);
  }
  // delete any old rules no longer in the array
  if (toDelete.length > 0) {
    const delPromises = [];
    // remove the rules
    delPromises.push(
      GroupRules.destroy({
        where: {
          id: { [Op.in]: toDelete.map((del) => del.id) },
        },
      })
    );
    // remove associated rule values
    delPromises.push(
      GroupRuleValues.destroy({
        where: {
          ruleId: { [Op.in]: toDelete.map((del) => del.id) },
        },
      })
    );
    // log the deletions
    toDelete.forEach((del) => {
      const logData = { ...baseLogData };
      logData.action = 'delete-group-rule';
      logData.childObjectId = del.id;
      logData.updatedData = { field: del.field, operator: del.operator, clause: del.clause };
      delPromises.push(createAuditLog(logData, req));
    });
    await Promise.all(delPromises);
  }
  // update the rest
  if (toUpdate.length > 0) {
    const promises = [];
    for (const upd of toUpdate) {
      const existingRule = group.groupRules.find((r) => r.id === upd.id);

      const customFieldAttributes = accountFields.find((af) => af.fieldName === upd.field && !af.isStandard);
      if (!_.isNil(upd.values)) {
        await resetRuleValues(upd);
      }
      const existingData = {
        field: existingRule.field,
        operator: existingRule.operator,
        refId: existingRule.refId,
        clause: existingRule.clause,
      };
      const data = {
        field: upd.field,
        operator: upd.operator,
        refId: upd.refId,
        clause: parseRule(req, upd, customFieldAttributes),
      };
      promises.push(
        GroupRules.update(data, {
          where: {
            id: upd.id,
          },
          fields: ['field', 'operator', 'clause', 'refId'],
        })
      );
      const logData = { ...baseLogData };
      logData.action = 'edit-group-rule';
      logData.childObjectId = upd.id;
      const diff = getObjectDiff(data, existingData);
      if (diff.length > 0) {
        diff.forEach((k) => {
          logData.originalData = {};
          logData.updatedData = {};
          logData.originalData[k] = existingData[k] || 'null';
          logData.updatedData[k] = data[k] || 'null';
        });
        promises.push(createAuditLog(logData, req));
      }
    }
    await Promise.all(promises);
  }
};

const duplicate = async (req) => {
  const sourceId = req.body.sourceId;
  const hooks = !(req.body.isArchived || false);
  const sourceGroup = await Groups.findByPk(sourceId, {
    include: [
      {
        model: GroupRules,
        as: 'groupRules',
        required: false,
        include: [
          {
            model: GroupRuleValues,
            as: 'values',
            required: false,
          },
        ],
      },
      {
        model: UserGroups,
        as: 'userGroups',
        required: false,
      },
    ],
    hooks,
  });

  if (!sourceGroup) {
    const id = sourceId;
    const err = new Error(req.i18n.t('groups.group_load_Error', { id }));
    err.status = 404;
    throw err;
  }

  // formulate name
  const nameCount = await Groups.count({
    where: {
      name: {
        [Op.like]: `${sourceGroup.name}%`,
      },
    },
  });
  const newName = `${sourceGroup.name} copy ${nameCount}`;

  // compose new group
  const newGroup = {
    accountId: sourceGroup.accountId,
    name: newName,
    groupType: sourceGroup.groupType,
    status: 'current',
  };
  // create new group
  const copiedGroup = await Groups.create(newGroup);
  await setGroupRole(copiedGroup, 'user');

  if (sourceGroup.groupType === 'dynamic') {
    if (sourceGroup.groupRules) {
      const groupRulesCopy = sourceGroup.groupRules.map((rule) => {
        const ruleValuesCopy = rule.values.map((nextRuleValue) => {
          return { value: nextRuleValue.value };
        });
        return {
          groupId: copiedGroup.id,
          field: rule.field,
          operator: rule.operator,
          clause: rule.clause,
          values: ruleValuesCopy,
        };
      });

      if (sourceGroup.groupRules && sourceGroup.groupRules.length > 0) {
        await GroupRules.bulkCreate(groupRulesCopy, {
          validate: true,
          include: [
            {
              model: GroupRuleValues,
              as: 'values',
              required: false,
            },
          ],
        });
      }
    }
  }

  if (sourceGroup.userGroups) {
    const userGroupsCopy = sourceGroup.userGroups.map((userGroup) => {
      return { groupId: copiedGroup.id, memberId: userGroup.memberId, memberType: userGroup.memberType };
    });

    await UserGroups.bulkCreate(userGroupsCopy, { validate: true });
  }

  const groupWithIncludes = await loadGroup(copiedGroup.id);
  return groupWithIncludes;
};

const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize(_.omit(query, ['$limit', '$skip', '$sort']));

  const newQuery = {
    ...defaults,
  };
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = userUtils.restSortToSequelize(query.$sort);
  }
  return newQuery;
};

/**
 * @openapi
 * /groups?$limit={limit}&$skip={skip}:
 *   get:
 *     summary: Get all groups
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Limit (integer) for number of records
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *         description: Skip (integer) for number of records
 *     responses:
 *       '200':
 *         description: Successfully fetched groups list
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records.
 *                 limit:
 *                   type: integer
 *                   description: Number of records for page.
 *                 skip:
 *                   type: integer
 *                   description: Skip number for page number.
 *                 data:
 *                   type: array
 *                   description: Array of group objects.
 *                   items:
 *                     $ref: '#/components/schemas/groups'
 *       '401':
 *         description: Unauthorized
 *       '404':
 *         description: Not Found
 *       '400':
 *         description: Bad Request
 *       '5XX':
 *         description: Unexpected error
 */

/**
 * @openapi
 * /groups?$limit={limit}&$skip={skip}&groupType={groupType}:
 *   get:
 *     summary: Filter Group (Dynamic/Static)
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Limit (integer) for number of records
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *         description: Skip (integer) for number of records
 *       - name: groupType
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           example: static
 *         description: Group type filter. Value may be `static` or `dynamic`
 *     responses:
 *       '200':
 *         description: Successfully fetched group list
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records.
 *                 limit:
 *                   type: integer
 *                   description: Number of records per page.
 *                 skip:
 *                   type: integer
 *                   description: Records skipped for pagination.
 *                 data:
 *                   type: array
 *                   description: Array of group objects.
 *                   items:
 *                     $ref: '#/components/schemas/groups'
 *       '401':
 *         description: Unauthorized
 *       '404':
 *         description: Not Found
 *       '400':
 *         description: Bad Request
 *       '5XX':
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };
  const queryParams = restQueryToSequelize(req.query, defaults);
  queryParams.order = [[...queryParams.order]];

  const finalQuery = {
    ...queryParams,
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };

  try {
    const allowedPermissions = await acl.getAllowedPermissions(req, req.user.id, 'groups', req.tokenPayload);

    // only generalized 'read' permission allows cross account groups to be included
    if (!allowedPermissions.groups.includes('read')) {
      if (finalQuery.where) {
        finalQuery.where.accountId = req.user.accountId;
      } else {
        const where = { accountId: req.user.accountId };
        Object.assign(finalQuery, { where });
      }
    } else if (!finalQuery.where) {
      const where = { accountId: req.user.accountId };
      Object.assign(finalQuery, { where });
    } else if (!finalQuery.where.accountId) {
      finalQuery.where.accountId = req.user.accountId;
    }
    const archivedGroup = !!(req.query.archivedGroup && req.query.archivedGroup === 'true');
    const includeArchivedGroups = !!(req.query.includeArchivedGroups && req.query.includeArchivedGroups === 'true');
    if (archivedGroup) {
      finalQuery.hooks = false;
      finalQuery.where = {
        ...finalQuery.where,
        isArchived: true,
      };
    } else if (includeArchivedGroups) {
      finalQuery.hooks = false;
      finalQuery.where = {
        ...finalQuery.where,
      };
    }
    delete finalQuery.where?.archivedGroup;
    delete finalQuery.where?.includeArchivedGroups;
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
    };
    const count = await Groups.count(countQuery);
    const archiveCountQuery = {
      where: {
        accountId: req.user.accountId,
        isArchived: true,
      },
      paranoid: false,
    };
    const archiveCount = await Groups.count(archiveCountQuery);
    const results = await Groups.findAll(finalQuery);
    const flattenedData = results.map((record) => record.get({ plain: true }));
    const data = await includeUserCount(req, flattenedData);

    res.json({ total: count, archiveCount, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  const group = req.group;
  try {
    const flattenedGroup = group.get({ plain: true });
    const data = await includeUserCount(req, flattenedGroup);
    res.json(data);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /groups:
 *   post:
 *     summary: Add Dynamic/Static Group
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Test Group"
 *               groupType:
 *                 type: string
 *                 enum: [static, dynamic]
 *                 example: "dynamic"
 *     responses:
 *       '200':
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/groups'
 *                 - type: object
 *                   properties:
 *                     roles:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/roles'
 *                           - type: object
 *                             properties:
 *                               groupRoles:
 *                                 $ref: '#/components/schemas/groupRoles'
 *                     userGroups:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/userGroups'
 *                           - type: object
 *                             properties:
 *                               user:
 *                                 $ref: '#/components/schemas/users'
 *                     groupRules:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/groupRules'
 *       '401':
 *         description: Unauthorized
 *       '404':
 *         description: Not Found
 *       '400':
 *         description: Bad Request
 *       '5XX':
 *         description: Unexpected error
 */

/**
 * @openapi
 * /groups/:
 *   post:
 *     summary: Add Duplicate Group
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Required parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sourceId:
 *                 type: integer
 *                 example: 5
 *     responses:
 *       '200':
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/groups'
 *                 - type: object
 *                   properties:
 *                     roles:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/roles'
 *                           - type: object
 *                             properties:
 *                               groupRoles:
 *                                 $ref: '#/components/schemas/groupRoles'
 *                     userGroups:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/userGroups'
 *                           - type: object
 *                             properties:
 *                               user:
 *                                 $ref: '#/components/schemas/users'
 *                     groupRules:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/groupRules'
 *       '401':
 *         description: Unauthorized
 *       '404':
 *         description: Not Found
 *       '400':
 *         description: Bad Request
 *       '5XX':
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  const group = req.body;
  try {
    let groupWithIncludes;
    if (req.body.sourceId) {
      groupWithIncludes = await duplicate(req);
    } else {
      group.accountId = req.user.accountId;
      const groupRules = group.groupRules || [];
      delete group.groupRules;
      const newGroup = await Groups.create(group);
      const logData = {
        feature: 'groups',
        object: 'groups',
        action: `add ${newGroup.groupType} group`,
        objectId: newGroup.id,
        updatedData: { name: `${newGroup.name}` },
      };
      await createAuditLog(logData, req);
      await setGroupRole(newGroup, 'user');
      const groupRulesWithGroupId = groupRules.map((rule) => {
        return { ...rule, groupId: newGroup.id };
      });

      if (groupRules && groupRules.length > 0) {
        await GroupRules.bulkCreate(groupRulesWithGroupId, { validate: true });
      }
      groupWithIncludes = await loadGroup(newGroup.id);
    }
    resetDynamicGroupMembers(groupWithIncludes);
    res.json(groupWithIncludes);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /groups/{groupId}:
 *   patch:
 *     summary: Edit Static Group
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: groupId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: 'Test Edit Group'
 *     responses:
 *       '200':
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/groups'
 *                 - type: object
 *                   properties:
 *                     roles:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/roles'
 *                           - type: object
 *                             properties:
 *                               groupRoles:
 *                                 $ref: '#/components/schemas/groupRoles'
 *                     userGroups:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/userGroups'
 *                           - type: object
 *                             properties:
 *                               user:
 *                                 $ref: '#/components/schemas/users'
 *                     groupRules:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/groupRules'
 *       '401':
 *         description: Unauthorized
 *       '404':
 *         description: Not Found
 *       '400':
 *         description: Bad Request
 *       '5XX':
 *         description: Unexpected error
 */

/**
 * @openapi
 * /groups/{groupId}/:
 *   patch:
 *     summary: Edit Group - Dynamic
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: groupId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: 'Test Edit Group'
 *               groupRules:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     field:
 *                       type: string
 *                       example: 'firstName'
 *                     operator:
 *                       type: string
 *                       example: 'isNot'
 *                     values:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           value:
 *                             type: string
 *                             example: 'TestFirstName'
 *     responses:
 *       '200':
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/groups'
 *                 - type: object
 *                   properties:
 *                     roles:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/roles'
 *                           - type: object
 *                             properties:
 *                               groupRoles:
 *                                 $ref: '#/components/schemas/groupRoles'
 *                     userGroups:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/userGroups'
 *                           - type: object
 *                             properties:
 *                               user:
 *                                 $ref: '#/components/schemas/users'
 *                     groupRules:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/groupRules'
 *       '401':
 *         description: Unauthorized
 *       '404':
 *         description: Not Found
 *       '400':
 *         description: Bad Request
 *       '5XX':
 *         description: Unexpected error
 */
module.exports.patch = async (req, res, next) => {
  const group = req.body;
  let isLocked = 0;
  try {
    isLocked = await lockGroup(req, req.group);

    group.accountId = req.user.accountId;
    const roles = group.roles;
    delete group.roles;
    const groupRules = group.groupRules;
    delete group.groupRules;
    const originalGroup = await db.groups.findByPk(req.group.id);
    let updatedGroup = await req.group.update(req.body);
    if (originalGroup.name !== updatedGroup.name) {
      const logData = {
        feature: 'groups',
        object: 'groups',
        action: `edit ${req.group.groupType} group`,
        objectId: updatedGroup.id,
        originalData: { name: originalGroup.name },
        updatedData: { name: updatedGroup.name },
      };
      await createAuditLog(logData, req);
    }
    if (roles) {
      await updateGroupRoles(updatedGroup, roles);
    }
    if (updatedGroup.groupType === 'dynamic' && !_.isNil(groupRules)) {
      // find custom account fields and pass them along to the sql parser so they
      // can be evaluated along with the default user fields
      const accountFields = await getAccountFields(req.user.accountId);
      await updateGroupRules(req, updatedGroup, groupRules, accountFields);
    }
    updatedGroup = await loadGroup(updatedGroup.id);
    const flattenedGroup = updatedGroup.get({ plain: true });
    const data = await includeUserCount(req, flattenedGroup);
    if (updatedGroup.groupType === 'dynamic') {
      data.groupRules = groupRules;
    }
    resetDynamicGroupMembers(updatedGroup);
    res.json(data);
  } catch (err) {
    next(err);
  } finally {
    try {
      if (isLocked) {
        await unlockGroup(req.group.id);
      }
    } catch (err) {
      logger.error(`Exception unlocking group in groups.patch(), err= ${err}`);
    }
  }
};

/**
 * @openapi
 * /groups/{groupId}:
 *   delete:
 *     summary: Delete Group
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: groupId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       '200':
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/groups'
 *                 - type: object
 *                   properties:
 *                     roles:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/roles'
 *                           - type: object
 *                             properties:
 *                               groupRoles:
 *                                 $ref: '#/components/schemas/groupRoles'
 *                     userGroups:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/userGroups'
 *                           - type: object
 *                             properties:
 *                               user:
 *                                 $ref: '#/components/schemas/users'
 *                     groupRules:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/groupRules'
 *       '401':
 *         description: Unauthorized
 *       '404':
 *         description: Not Found
 *       '400':
 *         description: Bad Request
 *       '5XX':
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  const group = req.group;
  let isLocked;

  try {
    isLocked = await lockGroup(req, group);
    const data = await destroyGroup(group);
    if (data && data.id) {
      const logData = {
        feature: 'groups',
        object: 'groups',
        action: `delete ${group.groupType} group`,
        objectId: group.id,
        updatedData: { name: group.name },
      };
      await createAuditLog(logData, req);
    }
    res.json(data);
  } catch (err) {
    // unlock group only if we locked it and an error occurs
    if (isLocked) {
      await unlockGroup(group.id);
    }
    next(err);
  }
};

// Middleware to retrieve the group when an id is passed in the route
module.exports.groupById = async function (req, res, next, id) {
  try {
    const allowedPermissions = await acl.getAllowedPermissions(req, req.user ? req.user.id : null, 'groups', req.tokenPayload);
    let group;
    if (req.user) {
      group = await loadGroup(id);
    }
    if (!group || (group.accountId !== req.user.accountId && !allowedPermissions.groups.includes('read'))) {
      const err = new Error(req.i18n.t('groups.group_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.group = group;
    next();
  } catch (err) {
    next(err);
  }
};

module.exports.listCampaigns = async (req, res, next) => {
  try {
    let campaignStatus = ['scheduled', 'inProgress'];
    let lifecycle = {
      [Op.eq]: 'active',
    };
    if (req.query.includeAllCampaignsStatus && req.query.includeAllCampaignsStatus === 'true') {
      campaignStatus = ['scheduled', 'inProgress', 'closed', 'withdrawn'];
      lifecycle = {
        [Op.in]: ['active', 'close'],
      };
    }
    const group = await Groups.findByPk(req.params.id, { attributes: ['id'], raw: true });
    if (!group) {
      const err = new Error(req.i18n.t('groups.group_load_Error', { id: req.params.id }));
      err.status = 404;
      throw err;
    }
    const accountId = req.user.accountId;
    const campaignsQuery = {
      attributes: ['id', 'name'],
      where: {
        accountId,
        lifecycle,
        status: {
          [Op.in]: campaignStatus,
        },
      },
      include: {
        model: GroupAssignments,
        attributes: [],
        required: true,
        where: {
          groupId: req.params.id,
        },
      },
    };

    const countQuery = {
      where: _.pick(campaignsQuery, ['where']).where,
      include: _.pick(campaignsQuery, ['include']).include,
      distinct: true,
    };

    const data = await Campaigns.findAll(campaignsQuery);
    const count = await Campaigns.count(countQuery);

    res.json({ total: count, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /groups/{groupId}/archive-unarchive:
 *   patch:
 *     summary: Archive or unarchive a group
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: groupId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       description: Archive/unarchive flag
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               isArchived:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       '200':
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/groups'
 *                 - type: object
 *                   properties:
 *                     roles:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/roles'
 *                           - type: object
 *                             properties:
 *                               groupRoles:
 *                                 $ref: '#/components/schemas/groupRoles'
 *                     userGroups:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/userGroups'
 *                           - type: object
 *                             properties:
 *                               user:
 *                                 $ref: '#/components/schemas/users'
 *                     groupRules:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/groupRules'
 *       '401':
 *         description: Unauthorized
 *       '404':
 *         description: Not Found
 *       '400':
 *         description: Bad Request
 *       '5XX':
 *         description: Unexpected error
 */
module.exports.archiveUnarchiveGroup = async (req, res, next) => {
  const group = req.group;
  let isLocked;
  const groupId = group.id;
  let action = '';
  try {
    if (req.body.isArchived) {
      isLocked = await lockGroup(req, group);
      action = 'archived';
    } else {
      await unlockGroup(groupId);
      isLocked = false;
      action = 'unarchived';
    }
    group.isArchived = req.body.isArchived;
    group.archivedAt = req.body.isArchived ? Date.now() : null;
    const data = group.save();
    if (data) {
      const logData = {
        feature: 'groups',
        object: 'groups',
        action: `${action} ${group.groupType} group`,
        objectId: groupId,
        updatedData: { name: group.name },
      };
      await createAuditLog(logData, req);
    }
    res.json(group);
  } catch (err) {
    // unlock group only if we locked it and an error occurs
    if (isLocked) {
      await unlockGroup(groupId);
    }
    next(err);
  }
};

/**
 * @openapi
 * components:
 *   schemas:
 *     groups:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         accountId:
 *           type: integer
 *         name:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         groupType:
 *           type: string
 *           enum: [static, dynamic]
 *         deletedAt:
 *           type: string
 *           format: date-time
 *         status:
 *           type: string
 *         roles:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/roles'
 *         userGroups:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/userGroups'
 *         groupRules:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/groupRules'
 *
 *     roles:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         groupRoles:
 *           $ref: '#/components/schemas/groupRoles'
 *
 *     userGroups:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         userId:
 *           type: integer
 *         user:
 *           $ref: '#/components/schemas/users'
 *
 *     groupRules:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         field:
 *           type: string
 *         operator:
 *           type: string
 *         values:
 *           type: array
 *           items:
 *             type: string
 *
 *     users:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         firstName:
 *           type: string
 *         lastName:
 *           type: string
 *         email:
 *           type: string
 *
 *     groupRoles:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         roleId:
 *           type: integer
 *         groupId:
 *           type: integer
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *
 *     roleGroups:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         roleId:
 *           type: integer
 *         groupId:
 *           type: integer
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */
