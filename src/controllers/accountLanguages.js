const db = require('../db');
const { getWorkdayCCLAccount } = require('../services/utils/workdayCclUtils.js');

const AccountLanguages = db.accountLanguages;
const Account = db.accounts;
const WorkdayContentData = db.workdayContentData;

/**
 * @swagger
 * /accounts/{accountId}/languages:
 *  post:
 *    summary: Add Learner Languages To Account
 *    tags: [Account]
 *    security:
 *      - JWT: []
 *    parameters:
 *      - name: accountId
 *        in: path
 *        required: true
 *        schema:
 *          type: integer
 *      - in: body
 *        name: body
 *        description: Suggested fields parameters
 *        required: true
 *        schema:
 *          type: object
 *          properties:
 *            selectedLanguages:
 *              type: string
 *              example: "en,hi,en-gb"
 *    responses:
 *      200:
 *        description: Added learner languages to account successfully.
 *        content:
 *          application/json:
 *            schema:
 *              $ref: '#/components/schemas/accountLanguages'
 *      404:
 *        description: Not Found
 *      401:
 *        description: Unauthorized
 *      400:
 *        description: Bad Request
 *      5XX:
 *        description: Unexpected error
 */

module.exports.create = async (req, res, next) => {
  try {
    req.body.accountId = req.account.id;
    // Need to fix a security bug that exists across a lot of our account API's
    // We should possibly get the account id from the user account id and compare.
    // if (!req.body.accountId) {
    //  req.body.accountId = req.user.accountId;
    // }
    const existingAccountLanguages = await
    AccountLanguages.findOne({ where: { accountId: req.body.accountId } });

    let accountLanguages;
    if (existingAccountLanguages) {
      req.body.id = existingAccountLanguages.id;
      accountLanguages = await existingAccountLanguages.update(req.body);
    } else {
      accountLanguages = await AccountLanguages.create(req.body);
    }

    const workdayCCLAccount = await getWorkdayCCLAccount(req.body.accountId);
    
    // If workdayCCLAccount exists, bulk update all matching records
    if (workdayCCLAccount) {
      await WorkdayContentData.update(
        { languagesChanged: true },
        { 
          where: { 
            accountId: req.body.accountId,
            isDeleted: false,
          }
        }
      );
    }

    res.json(accountLanguages);
  } catch (err) {
    if (err.name === 'SequelizeValidationError') {
      // statements to handle TypeError exceptions
      err.status = 422;
    }
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  const existingAccountLanguages = req.accountLanguages;
  const modifiedAccountLanguages = req.body;

  try {
    const updatedAccountLanguages = await existingAccountLanguages.update(modifiedAccountLanguages);
    const finalAccountLanguages = await AccountLanguages.findByPk(updatedAccountLanguages.id);
    const workdayCCLAccount = await getWorkdayCCLAccount(req.body.accountId);

    // If workdayCCLAccount exists, bulk update all matching records
    if (workdayCCLAccount) {
      await WorkdayContentData.update(
        { languagesChanged: true },
        { 
          where: { 
            accountId: req.body.accountId,
            isDeleted: false,
          }
        }
      );
    }

    res.json(finalAccountLanguages);
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  try {
    res.json(req.accountLanguages);
  } catch (err) {
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  const accountLanguages = req.accountLanguages;

  try {
    const data = await accountLanguages.destroy();
    res.json(data);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the accountLanguages when an id is passed in the route
module.exports.accountLanguagesById = async function (req, res, next, accountLanguagesId) {
  try {
    const accountLanguages = await AccountLanguages.findByPk(accountLanguagesId, {
      include: { model: Account },
    });
    if (!accountLanguages) {
      const err = new Error(req.i18n.t('accountLanguages.retrieval_Error', { accountLanguagesId }));
      err.status = 404;
      throw err;
    }
    req.accountLanguages = accountLanguages;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * components:
 *   schemas:
 *     accountLanguages:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         accountId:
 *           type: integer
 *         selectedLanguages:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */
