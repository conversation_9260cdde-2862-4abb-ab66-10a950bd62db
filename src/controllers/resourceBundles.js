const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { restQueryToSequelize } = require('../services/utils/userUtils');
const { PUBLIC_BUNDLE_ID, getAccountBundle } = require('../services/utils/resourceUtils');
const { lookupLessons } = require('../services/utils/programUtils');
const { convertBoolean } = require('../services/utils/jsUtils');
const { authenticate, getContent, putContent } = require('../services/utils/workdayCCLHelpers.js');

const Op = db.Sequelize.Op;
const Accounts = db.accounts;
const Bundles = db.bundles;
const AccountBundles = db.accountBundles;
const ResourceBundles = db.resourceBundles;
const Integrations = db.integrations;
const LessonPrograms = db.lessonPrograms;
const Resources = db.resources;
const Programs = db.programs;
const WorkdayContentData = db.workdayContentData;

const getIncludeParams = () => {
  return [
    {
      model: Bundles,
      include: [
        {
          model: AccountBundles,
          include: [
            {
              model: Accounts,
              include: [{
                model: Integrations,
              }],
            },
          ],
        },
      ],
      where: {
        id: {
          [Op.ne]: PUBLIC_BUNDLE_ID,
        },
      },
    },
  ];
};

const getAccounts = (bindings) => {
  let accounts = [];
  for (const binding of bindings) {
    let data = [];
    if (binding.bundle && binding.bundle.accountBundles) {
      data = binding.bundle.accountBundles.map((accountBundle) => {
        return accountBundle.account;
      });
    }
    accounts = [...accounts, ...data];
  }
  return accounts;
};

const fetchResourceBindings = async (resId) => {
  const queryParams = {
    where: {
      resourceId: resId,
    },
    include: getIncludeParams(),
  };
  const bindings = await ResourceBundles.findAll(queryParams);
  return getAccounts(bindings);
};

const fetchBinding = async (id) => {
  return ResourceBundles.findByPk(id, {
    include: getIncludeParams(),
  });
};

const fetchAccount = async (id) => {
  return Accounts.findByPk(id, {
    include:[{
      model: Integrations,
    }],
  });
};

const fetchWorkdayContentData = async (activityId, contentType, accountId) => {
  return WorkdayContentData.findOne({
    where: { activityId, contentType, accountId },
  });
};

const addAccess = async (i18n, resourceId, accountId) => {
  let retVal = 0;
  const accountBundle = await getAccountBundle(accountId);
  if (accountBundle) {
    retVal = await ResourceBundles.findOrCreate({
      where: {
        bundleId: accountBundle.bundleId,
        resourceId,
      },
      defaults: {
        bundleId: accountBundle.bundleId,
        resourceId,
      },
    });
  } else {
    const id = accountId;
    const msg = i18n ? i18n.t('bundles.account_bundle_load_Error', { id }) : `Failed to load account bundle: ${id}.`;
    const err = new Error(msg);
    err.status = 404;
    throw err;
  }
  return retVal;
};

const removeAccess = async (req, resourceId, accountId) => {
  let retVal = 0;
  const accountBundle = await getAccountBundle(accountId);
  if (accountBundle) {
    retVal = await ResourceBundles.destroy({
      where: {
        bundleId: accountBundle.bundleId,
        resourceId,
      },
    });
  } else {
    const id = accountId;
    const err = new Error(req.i18n.t('bundles.account_bundle_load_Error', { id }));
    err.status = 404;
    throw err;
  }
  return retVal;
};

const addProgramChildAccess = async (req, programId, accountId) => {
  let promises = [];
  const lessonBindings = await lookupLessons(programId);
  promises = lessonBindings.map((lessonBinding) => {
    return addAccess(req.i18n, lessonBinding.lesson.resourceId, accountId);
  });
  return promises;
};

const lookupOtherPrograms = async (lessonId, currentProgramId, bundleId) => {
  const lessonPrograms = await LessonPrograms.findAll({
    where: {
      lessonId,
      programId: {
        [Op.not]: currentProgramId,
      },
    },
    include: [{
      model: Programs,
      required: true,
      include: [
        {
          model: Resources,
          as: 'resource',
          required: true,
          include: [
            {
              model: ResourceBundles,
              required: true,
              where: {
                bundleId,
                status: 'active',
              },
            },
          ],
        },
      ],
    }],
  });
  return lessonPrograms;
};

const removeProgramChildAccess = async (req, programId, accountId) => {
  const promises = [];
  const lessonBindings = await lookupLessons(programId);
  const accountBundle = await getAccountBundle(accountId);
  if (accountBundle && lessonBindings) {
    for await(const lessonBinding of lessonBindings) {
      // only remove access to this lesson if its not part of another program in the same bundle
      const programBindings = await lookupOtherPrograms(lessonBinding.lesson.id, programId, accountBundle.bundleId);
      if (!programBindings || programBindings.length === 0) {
        const removeAcc = await removeAccess(req, lessonBinding.lesson.resourceId, accountId)
        promises.push(removeAcc);
      }
    }
  }
  return promises;
};

// replaces resource's account access with incoming accounts in accountIds
async function resetAccess(req, resourceId, accountIds, programId, includeChildren) {
  const user = req.user;
  // figure out what groups should be removed and what groups should be added
  const preexistingAccounts = await fetchResourceBindings(resourceId);

  const preexistingAccountIds = preexistingAccounts.map(account => account.id);

  const removedAccountIds = preexistingAccountIds.filter((accountId) => {
    return !accountIds.includes(accountId) && accountId !== user.accountId;
  });

  const newAccountIds = accountIds.filter((id) => {
    return !preexistingAccountIds.includes(id);
  });

  const remainingAccountIds = preexistingAccountIds.filter((id) => {
    return !removedAccountIds.includes(id);
  });

  // remove accounts
  const promises = [];
  for await(const accountId of removedAccountIds) {
    if (includeChildren) {
      const removeProgChildAcc = await removeProgramChildAccess(req, programId, accountId)
      promises.push(removeProgChildAcc);
    }
    const removeResAcc = await removeAccess(req, resourceId, accountId)
    promises.push(removeResAcc);
  }
  await Promise.all(promises);

  // add new accounts
  const promises2 = [];
  for (const accountId of newAccountIds) {
    if (includeChildren) {
      promises2.push(addProgramChildAccess(req, programId, accountId));
    }
    promises2.push(addAccess(req.i18n, resourceId, accountId));
  };

  // make sure children of any preexisting accounts that remain have lesson access
  if (includeChildren) {
    const promises3 = [];
      for (const accountId of remainingAccountIds) {
      promises3.push(addProgramChildAccess(req, programId, accountId));
    }
    await Promise.all(promises3);
  }

  const data = await Promise.all(promises2);
  return data;
}

const list = async (req, res, next) => {
  let customSort = 0;
  if (req.query && req.query.customSort) {
    delete (req.query.customSort);
    customSort = 1;
  }
  const resource = req.resource;
  const defaults = {
    where: {
      resourceId: resource.id,
    },
    include: getIncludeParams(),
    limit: config.paginate.default,
    offset: 0,
  };
  if (customSort) {
    defaults.order = db.Sequelize.literal('IF(`name` RLIKE \'^[A-Za-z]\', 1, 2), `name`');
  }
  const queryParams = restQueryToSequelize(req.query, defaults, false);

  const finalQuery = {
    ...queryParams,
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  const countQuery = {
    where: _.pick(finalQuery, ['where']).where,
  };

  try {
    const count = await ResourceBundles.count(countQuery);
    const bindings = await ResourceBundles.findAll(finalQuery);
    const data = getAccounts(bindings);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /resources/{resourceId}/accounts:
 *   post:
 *     summary: Add Account To Resource
 *     tags:
 *       - Resource
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: resourceId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               accountId:
 *                 type: integer
 *                 example: 26
 *     responses:
 *       200:
 *         description: Record created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/accounts'
 *               properties:
 *                 integrations:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/integrations'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal Server Error
 */
const create = async (req, res, next, programId = null, includeChildren = false) => {
  const accountId = req.body.accountId;
  const accountIds = req.body.accountIds;
  const resourceId = req.resource.id;
  try {
    if (accountIds) {
      // array mode resets account access to incoming array of accountIds
      await resetAccess(req, resourceId, accountIds, programId, includeChildren);
      const data = await fetchResourceBindings(resourceId);
      res.json(data);
    } else if (accountId) {
      // id mode adds account access
      const binding = await addAccess(req.i18n, resourceId, accountId);
      if (includeChildren) {
        await addProgramChildAccess(req, programId, accountId);
      }
      const newBinding = await fetchBinding(binding[0].id);
      const data = getAccounts([newBinding])[0];
      res.json(data);
    }
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /resources/{resourceId}/accounts/{accountId}:
 *   delete:
 *     summary: Remove Resource from Account
 *     tags:
 *       - Resource
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: resourceId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: accountId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/mediaAssets'
 *               properties:
 *                 resource:
 *                   $ref: '#/components/schemas/resources'
 *                 topics:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/topics'
 *                 tags:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/tags'
 *                 supportedLanguages:
 *                   $ref: '#/components/schemas/supportedLanguages'
 *                 lessonCards:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/lessonCards'
 *                 contentStrings:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/contentStrings'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
const remove = async (req, res, next, programId = null, includeChildren = false) => {
  const resourceId = req.resource.id;
  const accountId = req.params.accountId;
  try {
    if (accountId === req.user.accountId.toString()) {
      const id = accountId;
      const err = new Error(req.i18n.t('bundles.account_bundle_load_Error', { id }));
      err.status = 400;
      throw err;
    }
    if (accountId) {
      const delCount = await removeAccess(req, resourceId, accountId);
      if (delCount !== 1) {
        const err = new Error(req.i18n.t('bundles.access_not_found_Error', { resourceId, accountId }));
        err.status = 404;
        throw err;
      }
      if (includeChildren) {
        const promises = [];
        promises.push(removeProgramChildAccess(req, programId, accountId));
        await Promise.all(promises);
      }
    }
    res.json(accountId);
  } catch (err) {
    next(err);
  }
};

const update = async (req, res, next) => {
  const resourceBundle = req.resourceBundle;
  const id = req.params.resourceBundleId;
  if (!resourceBundle) {
    const err = new Error(req.i18n.t('bundles.account_bundle_load_Error', { id }));
    err.status = 400;
    throw err;
  }
  try {
    const updatedData = req.body;
    const allowedAttributes = ['status'];
    const newResourceBundle = Object.assign({}, resourceBundle);
    if (updatedData) {
      Object.keys(updatedData).forEach((key) => {
        if (allowedAttributes.includes(key)) {
          newResourceBundle[key] = updatedData[key];
        }
      });
    }
    const updatedResourceBundle = await resourceBundle.update(newResourceBundle);
    const { entityId, entityType, accountId, status } = req.body;
    const account = await fetchAccount(accountId);
    const workDayContentData = await fetchWorkdayContentData(entityId, entityType, accountId)
    if (workDayContentData && ['retired', 'active'].includes(status)) {
      const newStatus = status === 'retired' ? 'REMOVED' : 'PUBLISHED';
      const isRetired = newStatus === 'REMOVED';
      
      if (workDayContentData.status !== newStatus) {
        const workdayIntegration = account?.integrations?.find(integration => integration.integrationType === 'workdayCCL');
        const tenantAlias = workdayIntegration.tenantAlias;
        const auth = await authenticate({
          clientId: workdayIntegration.clientId,
          clientSecret: workdayIntegration.integrationKey,
          tenantAlias
        });
        if (!auth.ok) {
          res.json( { status: auth?.status, id: null, message: 'Workday Authentication Error' });
        }
        const responseData = await auth.json();
        const accessToken = responseData?.access_token;
        const response = await getContent({
          accessToken,
          contentId: updatedResourceBundle.resourceId,
          tenantAlias
        });
    
        response[0].lifecycle.status = newStatus;
    
        const result = await putContent({
          accessToken,
          contentItems: response,
          tenantAlias,
        });
    
        if (result === 'success') {
          await workDayContentData.update({ status: newStatus, isRetired });
        }
      }
    }
    res.json(updatedResourceBundle);
  } catch (err) {
    next(err);
  }
};

module.exports.listResourceAccess = async (req, res, next) => {
  return list(req, res, next);
};

module.exports.deleteResourceAccess = async (req, res, next) => {
  return remove(req, res, next);
};

module.exports.createResourceAccess = async (req, res, next) => {
  return create(req, res, next);
};

module.exports.patch = async (req, res, next) => {
  return update(req, res, next);
};

module.exports.listProgramAccess = async (req, res, next) => {
  const id = req.params.progId;
  const program = await Programs.findOne({ where: { id },
    include: [{ attributes: ['id'], model: Resources, as: 'resource' }] });

  if (!program) {
    const err = new Error(req.i18n.t('programs.program_load_Error', { id }));
    err.status = 404;
    throw err;
  }

  req.program = program;
  req.resource = req.program.resource;
  return list(req, res, next);
};

/**
 * @openapi
 * /programs/{programId}/accounts/{accountId}:
 *   delete:
 *     summary: Delete Account From Program
 *     tags:
 *       - Program
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: programId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: accountId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: includeLessons
 *         in: query
 *         required: false
 *         schema:
 *           type: boolean
 *           example: true
 *     responses:
 *       200:
 *         description: Delete Account From Program successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 message: Account removed from program successfully.
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

module.exports.deleteProgramAccess = async (req, res, next) => {
  const includeChildren = convertBoolean(req.query.includeLessons);
  return remove(req, res, next, req.program.id, includeChildren);
};

/**
 * @swagger
 * /programs/{programId}/accounts:
 *   post:
 *     summary: Add Accounts To Program
 *     tags: [Program]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: programId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: includeLessons
 *         in: query
 *         required: false
 *         schema:
 *           type: boolean
 *           default: true
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               accountId:
 *                 type: integer
 *                 example: 10
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 integrations:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/integrations'
 *                 account:
 *                   $ref: '#/components/schemas/accounts'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.createProgramAccess = async (req, res, next) => {
  const includeChildren = convertBoolean(req.query.includeLessons);
  return create(req, res, next, req.program.id, includeChildren);
};

module.exports.resourceBundleById = async function (req, res, next, id) {
  try {
    const resourceBundle = await ResourceBundles.findByPk(id);
    if (!resourceBundle) {
      const err = new Error(req.i18n.t('resourceBundles.resource_bundle_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.resourceBundle = resourceBundle;
    next();
  } catch (err) {
    next(err);
  }
};

// add lesson access to accounts that have existing access to the program
module.exports.addLessonAccessToPrograms = async (programResourceId, lessonResourceId, i18n) => {
  const accounts = await fetchResourceBindings(programResourceId);
  const promises = [];
  for (const account of accounts) {
    promises.push(addAccess(i18n, lessonResourceId, account.id));
  }
  return Promise.all(promises);
};

/**
 * @openapi
 * components:
 *   schemas:
 *     resourceBundles:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         bundleId:
 *           type: integer
 *         resourceId:
 *           type: integer
 *         status:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         deletedAt:
 *           type: string
 *           format: date-time
 */
