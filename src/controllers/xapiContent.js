const db = require('../db');
const logger = require('../logger');
const { tokenForUser } = require('../services/utils/authUtils');
const { isValidEmailAddress } = require('../services/utils/validationUtils');

const User = db.users;
const Accounts = db.accounts;
const Integrations = db.integrations;
const XapiPrograms = db.xapiPrograms; // Will need to create this model
const Programs = db.programs;
const Lessons = db.lessons;

/**
 * Verify xAPI authentication and return user/registration data
 * Similar to verifyScorm but for xAPI packages
 */
module.exports.verifyXapi = async (req, res, next) => {
  try {
    const {
      userId,
      registrationId,
      apiKey,
      integrationKey,
      programId,
      lessonId,
      firstName,
      lastName
    } = req.body;

    // Validate required parameters
    if (!userId || !registrationId || !apiKey || !integrationKey) {
      const err = new Error('Missing required xAPI parameters');
      err.status = 400;
      throw err;
    }

    // Get account from subdomain (similar to SCORM)
    const accountRecord = await getAccountFromRequest(req);
    if (!accountRecord || accountRecord.apiKey !== apiKey) {
      const err = new Error('Invalid API key or account');
      err.status = 401;
      throw err;
    }

    // Verify integration key
    const integrationRecord = accountRecord.integrations
      && accountRecord.integrations.find(i => 
        i.integrationKey === integrationKey && 
        ['xapi', 'tincan'].includes(i.integrationType)
      );

    if (!integrationRecord) {
      const err = new Error('Invalid xAPI integration key');
      err.status = 401;
      throw err;
    }

    // Find or create user and xAPI program record
    const user = await findOrCreateXapiUser({
      userId: userId.toLowerCase(),
      registrationId,
      accountId: accountRecord.id,
      programId: parseInt(programId),
      lessonId: parseInt(lessonId),
      firstName,
      lastName
    });

    // Return authentication token and xAPI program data
    res.json({
      accessToken: tokenForUser(user, { isXapi: true }),
      registrationId,
      xapiProgram: user.xapiPrograms[0]
    });

  } catch (err) {
    logger.error('xAPI verify controller error:', err);
    next(err);
  }
};

/**
 * Serve program content directly (not assignment list)
 * This is the main difference from SCORM - direct content delivery
 */
module.exports.serveProgramContent = async (req, res, next) => {
  try {
    const { programId } = req.params;
    const { registrationId } = req.query;

    // Authenticate and get user context
    const user = req.user; // Set by auth middleware
    
    // Find the xAPI program record
    const xapiProgram = await XapiPrograms.findOne({
      where: {
        userId: user.id,
        registrationId,
        resourceId: programId,
        model: 'program'
      }
    });

    if (!xapiProgram) {
      const err = new Error('xAPI program not found');
      err.status = 404;
      throw err;
    }

    // Get program data with lessons
    const program = await Programs.findByPk(programId, {
      include: [/* include lessons, lesson cards, etc. */]
    });

    // Render the content interface directly
    // This could be a React app, server-rendered HTML, or redirect to frontend
    res.render('xapi-program-content', {
      program,
      xapiProgram,
      user,
      registrationId
    });

  } catch (err) {
    next(err);
  }
};

/**
 * Serve lesson content directly
 */
module.exports.serveLessonContent = async (req, res, next) => {
  try {
    const { lessonId } = req.params;
    const { registrationId } = req.query;

    const user = req.user;
    
    const xapiProgram = await XapiPrograms.findOne({
      where: {
        userId: user.id,
        registrationId,
        resourceId: lessonId,
        model: 'lesson'
      }
    });

    if (!xapiProgram) {
      const err = new Error('xAPI lesson not found');
      err.status = 404;
      throw err;
    }

    const lesson = await Lessons.findByPk(lessonId, {
      include: [/* include lesson cards, etc. */]
    });

    res.render('xapi-lesson-content', {
      lesson,
      xapiProgram,
      user,
      registrationId
    });

  } catch (err) {
    next(err);
  }
};

// Helper functions
async function getAccountFromRequest(req) {
  // Extract subdomain and find account (similar to SCORM implementation)
  // This would be similar to your existing accountRecordFromHostname function
}

async function findOrCreateXapiUser(params) {
  // Similar to getScormProgramUser but for xAPI
  // Find or create user and xAPI program record
}
