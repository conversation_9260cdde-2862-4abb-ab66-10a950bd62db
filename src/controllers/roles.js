const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const userUtils = require('../services/utils/userUtils');
const campaignUtils = require('../services/utils/campaignUtils');
const acl = require('../services/acl/acl');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');

const Roles = db.roles;
const UserRole = db.userRoles;
const Accounts = db.accounts;
const Users = db.users;
const RoleFeaturePermissions = db.roleFeaturePermissions;
const Features = db.features;
const Permissions = db.permissions;
const AccountUsers = db.accountUsers;
const RoleGroups = db.roleGroups;
const Groups = db.groups;
const Op = db.Sequelize.Op;

const getIncludeParams = async (req, defaults, groupIds) => {
  const includes = [
    {
      model: Groups,
    },
    {
      model: Features,
    },
  ];
  if (groupIds) {
    includes[0].where = {
      id: { [Op.in]: groupIds },
    };
  }

  const includeUsers = !req.query.includeUsers || (req.query.includeUsers === 'true');
  if (includeUsers) {
    includes.push({
      model: AccountUsers,
      include: {
        model: Users,
      },
    });
  }
  return {
    ...defaults,
    include: includes,
  };
};

const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize(_.omit(query, ['$limit', '$skip', '$sort', 'includeUsers']));

  const newQuery = {
    ...defaults,
  };
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = userUtils.restSortToSequelize(query.$sort);
  }
  return newQuery;
};

/**
 * @openapi
 * /roles:
 *   get:
 *     summary: Fetch Admin User Roles
 *     tags: [Roles]
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records.
 *                 skip:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   description: Array of role data objects.
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/roles'
 *                       - type: object
 *                         properties:
 *                           groups:
 *                             type: array
 *                             items:
 *                               allOf:
 *                                 - $ref: '#/components/schemas/groups'
 *                                 - type: object
 *                                   properties:
 *                                     roleGroups:
 *                                       $ref: '#/components/schemas/roleGroups'
 *                           users:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/users'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['createdAt', 'ASC']],
    limit: 100,
    offset: 0,
  };
  const groupIds = req.query && req.query.groupIds;
  delete (req.query.groupIds);
  const queryParams = restQueryToSequelize(req.query, defaults);
  const withIncludes = await getIncludeParams(req, queryParams, groupIds);
  withIncludes.order = [[...withIncludes.order]];

  const finalQuery = {
    ...withIncludes,
  };
  const pagedResult = {
    limit: withIncludes.limit,
    skip: withIncludes.offset,
  };

  try {
    const allowedPermissions = await acl.getAllowedPermissions(req, req.user.id, 'roles', req.tokenPayload);

    // only generalized 'read' permission allows cross account roles to be included
    if (!allowedPermissions.roles.includes('read')) {
      if (finalQuery.where) {
        finalQuery.where.accountId = req.user.accountId;
      } else {
        const where = { accountId: req.user.accountId };
        Object.assign(finalQuery, { where });
      }
    } else if (!finalQuery.where) {
      const where = { accountId: req.user.accountId };
      Object.assign(finalQuery, { where });
    } else if (!finalQuery.where.accountId) {
      finalQuery.where.accountId = req.user.accountId;
    }

    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
    };
    const count = await Roles.count(countQuery);
    const data = await Roles.findAll(finalQuery);

    const flattenedData = data.map((role) => {
      const plainRole = role.get({ plain: true });

      const mappedRole = { ...plainRole };
      const hasDashboardPermissions =
        mappedRole.features ? !!mappedRole.features.find(feature => feature.name === 'dashboard') : false;
      const hasCampaignsPermissions =
        mappedRole.features ? !!mappedRole.features.find(feature => feature.name === 'campaigns') : false;
      const hasReportsPermissions =
        mappedRole.features ? !!mappedRole.features.find(feature => feature.name === 'reports') : false;
      const hasSiteConfigPermissions =
        mappedRole.features ? !!mappedRole.features.find(feature => feature.name === 'siteConfiguration') : false;
      const hasContentConfigPermissions =
        mappedRole.features ? !!mappedRole.features.find(feature => feature.name === 'contentConfiguration') : false;
      const hasUsersPermissions =
        mappedRole.features ? !!mappedRole.features.find(feature => feature.name === 'users') : false;
      const hasGroupsPermissions =
        mappedRole.features ? !!mappedRole.features.find(feature => feature.name === 'groups') : false;
      const hasAnalyticsPermission =
        mappedRole.features ? !!mappedRole.features.find(feature => feature.name === 'analytics') : false;
      const hasResourceAssetsPermission =
        mappedRole.features ? !!mappedRole.features.find(feature => feature.name === 'resourceAssets') : false;

      delete mappedRole.features;
      mappedRole.dashboardPermission = hasDashboardPermissions;
      mappedRole.campaignsPermission = hasCampaignsPermissions;
      mappedRole.reportsPermission = hasReportsPermissions;
      mappedRole.siteConfigPermission = hasSiteConfigPermissions;
      mappedRole.contentLibraryPermission = hasContentConfigPermissions;
      mappedRole.usersPermission = hasUsersPermissions;
      mappedRole.groupsPermission = hasGroupsPermissions;
      mappedRole.analyticsPermission = hasAnalyticsPermission;
      mappedRole.resourceAssetsPermission = hasResourceAssetsPermission;

      if (mappedRole.accountUsers) {
        mappedRole.users = mappedRole.accountUsers.filter((accountUser) => {
          return accountUser.user !== null;
        });
        delete mappedRole.accountUsers;
      }
      return mappedRole;
    });

    let hasActiveGroups = false;
    for (const nextRole of flattenedData) {
      let allUserIds = new Set();
      if (nextRole.groups && nextRole.groups.length > 0) {
        for (const nextGroup of nextRole.groups) {
          if (!nextGroup.deletedAt) {
            hasActiveGroups = true;
            const userIds = await campaignUtils.getGroupMembers(nextGroup.id);
            allUserIds = new Set([...allUserIds, ...userIds]);
          }
        }
        const allUserIdArray = Array.from(allUserIds);
        nextRole.totalUsers = allUserIdArray.length;
      } else {
        const requestingAccount = await Accounts.findOne({
          where: {
            id: req.user.accountId,
          },
        });

        const totalAccountUsers = await AccountUsers.count({
          where: {
            accountId: req.user.accountId,
          },
          include: [{
            model: Users,
            where: {
              id: { [Op.ne]: requestingAccount.accountOwnerId },
            },
            required: true,
          }],
        });
        nextRole.totalUsers = totalAccountUsers;
        if (nextRole?.users && nextRole.users.length) {
          for (const user of nextRole.users) {
            const checkAnalyticsAccess = await userUtils.checkAnalyticsAccess(user.userId);
            // eslint-disable-next-line no-param-reassign
            user.hasAnalyticsAccess = checkAnalyticsAccess;
          }
        }
      }
      // const analyticsRoleData = userUtils.getUserAnalyticsRolesData()
      nextRole.hasActiveGroups = hasActiveGroups;
    }
    res.json({ total: count, ...pagedResult, data: flattenedData });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /roles/{roleId}:
 *   get:
 *     summary: Get Role Details By roleId
 *     tags: [Roles]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: roleId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/roles'
 *                 - type: object
 *                   properties:
 *                     groups:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/groups'
 *                           - type: object
 *                             properties:
 *                               roleGroups:
 *                                 $ref: '#/components/schemas/roleGroups'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.read = async (req, res, next) => {
  const role = req.role;
  try {
    res.json(role);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /roles:
 *   post:
 *     summary: Add New Role
 *     tags: [Roles]
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Test Name"
 *               dashboardPermission:
 *                 type: boolean
 *                 example: true
 *               campaignsPermission:
 *                 type: boolean
 *                 example: false
 *               reportsPermission:
 *                 type: boolean
 *                 example: true
 *               siteConfigPermission:
 *                 type: boolean
 *                 example: true
 *               contentLibraryPermission:
 *                 type: boolean
 *                 example: false
 *               usersPermission:
 *                 type: boolean
 *                 example: true
 *               groupsPermission:
 *                 type: boolean
 *                 example: true
 *               analyticsPermission:
 *                 type: boolean
 *                 example: true
 *               resourceAssetsPermission:
 *                 type: boolean
 *                 example: true
 *               hasActiveGroups:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/roles'
 *                 - type: object
 *                   properties:
 *                     groups:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/groups'
 *                           - type: object
 *                             properties:
 *                               roleGroups:
 *                                 $ref: '#/components/schemas/roleGroups'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  const role = req.body;
  try {
    role.accountId = req.user.accountId;
    // Make sure there's a name
    if (!role.name || !role.name.length) {
      const err = new Error('There must be a name specified');
      err.status = 400;
      throw err;
    }
    // Make sure this name doesn't already exist as account role
    const roleWithSameName = await Roles.findOne({
      where: {
        name: role.name,
        accountId: role.accountId,
      },
    });
    if (roleWithSameName) {
      const err = new Error('The role already exists for this account');
      err.status = 400;
      throw err;
    }
    // Make sure this name isn't a system role
    const sytemRoleWithThisName = await Roles.findOne({
      where: {
        name: role.name,
        accountId: null,
      },
    });
    if (sytemRoleWithThisName) {
      const err = new Error('This role name cannot be used. It\'s a system role');
      err.status = 400;
      throw err;
    }
    if (role.accountId && req.body.dashboardPermission === false && req.body.campaignsPermission === false &&
      req.body.reportsPermission === false && req.body.siteConfigPermission === false &&
      req.body.contentLibraryPermission === false && req.body.usersPermission === false &&
      req.body.groupsPermission === false && req.body.analyticsPermission === false && req.body.resourceAssetsPermission === false) {
      const err = new Error('At least one permission must be set for role');
      err.status = 400;
      throw err;
    }

    const newRole = await Roles.create(role);
    const isRestricted = req.body.groups && req.body.groups.length > 0;

    if (isRestricted) {
      // Set the permissions not related to groups to be false
      req.body.dashboardPermission = false;
      req.body.campaignsPermission = false;
      req.body.siteConfigPermission = false;
      req.body.contentLibraryPermission = false;
      req.body.groupsPermission = false;
      req.body.analyticsPermission = false;
      req.body.resourceAssetsPermission = false;

      const roleGroups = req.body.groups.map((groupId) => {
        return {
          roleId: newRole.id,
          groupId,
        };
      });
      await RoleGroups.bulkCreate(roleGroups);
    }

    // Add all the accountAdminPermissions to this new role
    const accountAdminRole = await Roles.findOne({
      where: {
        name: 'accountAdmin',
      },
    });
    if (accountAdminRole) {
      const rfps = await RoleFeaturePermissions.findAll({
        where: {
          roleId: accountAdminRole.id,
        },
        include: [{
          model: Features,
        }, {
          model: Permissions,
        }],
      });
      const filteredRFPs = rfps.filter((rfp) => {
        if (rfp.feature.name === 'dashboard' && req.body.dashboardPermission === false) {
          return false;
        } else if (rfp.feature.name === 'campaigns' && req.body.campaignsPermission === false) {
          return false;
        } else if (rfp.feature.name === 'reports' && req.body.reportsPermission === false) {
          return false;
        } else if (rfp.feature.name === 'siteConfiguration' && req.body.siteConfigPermission === false) {
          return false;
        } else if (rfp.feature.name === 'contentConfiguration' && req.body.contentLibraryPermission === false) {
          return false;
        } else if (rfp.feature.name === 'users') {
          if (req.body.usersPermission === false) {
            return false;
          } else if (isRestricted && (rfp.permission.name === 'create' || rfp.permission.name === 'createAccount')) {
            return false;
          }
        } else if (rfp.feature.name === 'groups' && req.body.groupsPermission === false) {
          return false;
        } else if (rfp.feature.name === 'analytics' && req.body.analyticsPermission === false) {
          return false;
        } else if (rfp.feature.name === 'resourceAssets' && req.body.resourceAssetsPermission === false) {
          return false;
        }
        return true;
      });

      const newRfps = filteredRFPs.map((rfp) => {
        return {
          featureId: rfp.featureId,
          permissionId: rfp.permissionId,
          roleId: newRole.id,
        };
      });
      await RoleFeaturePermissions.bulkCreate(newRfps);
    }
    const updatedNewRole = await Roles.findByPk(newRole.id, { include: [{ model: Groups, required: false }] });
    res.json(updatedNewRole);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /roles/{roleId}:
 *   patch:
 *     summary: Update Role
 *     tags: [Roles]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: roleId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               dashboardPermission:
 *                 type: boolean
 *                 example: true
 *               campaignsPermission:
 *                 type: boolean
 *                 example: false
 *               reportsPermission:
 *                 type: boolean
 *                 example: true
 *               siteConfigPermission:
 *                 type: boolean
 *                 example: true
 *               contentLibraryPermission:
 *                 type: boolean
 *                 example: false
 *               usersPermission:
 *                 type: boolean
 *                 example: true
 *               groupsPermission:
 *                 type: boolean
 *                 example: true
 *               analyticsPermission:
 *                 type: boolean
 *                 example: true
 *               resourceAssetsPermission:
 *                 type: boolean
 *                 example: true
 *               hasActiveGroups:
 *                 type: boolean
 *                 example: true
 *               name:
 *                 type: string
 *                 example: "Test Name"
 *               groups:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [12, 15]
 *               users:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [12, 14, 15]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/roles'
 *                 - type: object
 *                   properties:
 *                     groups:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/groups'
 *                           - type: object
 *                             properties:
 *                               roleGroups:
 *                                 $ref: '#/components/schemas/roleGroups'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */

module.exports.patch = async (req, res, next) => {
  const role = req.role;
  try {
    // Ensure a user is not editing a role for another account
    if (role.accountId !== req.user.accountId) {
      const err = new Error('Cannot edit a role created by another account');
      err.status = 400;
      throw err;
    }
    // Make sure there's a name
    if (!req.body.name || !req.body.name.length) {
      const err = new Error('There must be a name specified');
      err.status = 400;
      throw err;
    }
    // Make sure this name doesn't already exist
    const roleWithSameName = await Roles.findOne({
      where: {
        name: req.body.name,
        accountId: role.accountId,
      },
    });
    if (role.accountId && req.body.dashboardPermission === false && req.body.campaignsPermission === false &&
      req.body.reportsPermission === false && req.body.siteConfigPermission === false &&
      req.body.contentLibraryPermission === false && req.body.usersPermission === false &&
      req.body.groupsPermission === false && req.body.analyticsPermission === false && req.body.resourceAssetsPermission === false) {
      const err = new Error('At least one permission must be set for role');
      err.status = 400;
      throw err;
    }
    if (roleWithSameName && roleWithSameName.id !== role.id) {
      const err = new Error('The role already exists for this account');
      err.status = 400;
      throw err;
    }
    // Make sure this name isn't a system role
    const sytemRoleWithThisName = await Roles.findOne({
      where: {
        name: req.body.name,
        accountId: null,
      },
    });
    if (sytemRoleWithThisName) {
      const err = new Error('This role name cannot be used. It\'s a system role');
      err.status = 400;
      throw err;
    }
    // validate if the users with this role has risk analytics access
    const validateUserRiskRole = await userUtils.checkRoleUserRiskData(role, req.body);
    if (validateUserRiskRole) {
      const err = new Error('Cannot remove analytics access');
      err.status = 400;
      throw err;
    }
    const updateGroups = req.body.groups;
    const wasRestricted = role.groups.length > 0;
    const isRestricted = updateGroups && updateGroups.length > 0;
    delete req.body.groups;

    const updatedRole = await role.update(req.body);

    // get all the users with this role and update the emtrainAdminRole
    const accountRoleUser = await AccountUsers.findAll({ where: { accountId: role.accountId, roleId: role.id }, attributes: ['userId'] });
    const userIds = accountRoleUser.map(({ userId }) => { return userId; });
    if (userIds.length) {
      await Users.update({ emtrainAdminRole: req.body.name }, { where: { id: { [Op.in]: userIds } } });
    }
    if (isRestricted) {
      // Set the permissions not related to groups to be false
      req.body.dashboardPermission = false;
      req.body.campaignsPermission = false;
      req.body.siteConfigPermission = false;
      req.body.contentLibraryPermission = false;
      req.body.groupsPermission = false;

      const existingRoleGroups = await RoleGroups.findAll({
        attributes: ['id', 'roleId', 'groupId'],
        where: {
          roleId: role.id,
        },
      });

      const newGroups = updateGroups.filter((groupId) => {
        return !existingRoleGroups.find(rg => rg.groupId === groupId);
      });
      const roleGroups = newGroups.map((groupId) => {
        return {
          roleId: role.id,
          groupId,
        };
      });
      // create the next groups just added
      await RoleGroups.bulkCreate(roleGroups);

      // destroy any groups now longer part of the role
      for (const nextRoleGroup of existingRoleGroups) {
        if (!updateGroups.find(groupId => groupId === nextRoleGroup.groupId)) {
          await nextRoleGroup.destroy();
        }
      }
    } else {
      await RoleGroups.destroy({
        where: {
          roleId: role.id,
        },
      });
    }

    if (updatedRole.accountId) {
      // Add all the accountAdminPermissions to this new role
      const accountAdminRole = await Roles.findOne({
        where: {
          name: 'accountAdmin',
        },
      });
      if (accountAdminRole) {
        const rfps = await RoleFeaturePermissions.findAll({
          where: {
            roleId: accountAdminRole.id,
          },
          include: [{
            model: Features,
          }, {
            model: Permissions,
          }],
        });
        const dashboardRFPs = rfps.filter((rfp) => {
          return rfp.feature.name === 'dashboard';
        });
        const campaignRFPs = rfps.filter((rfp) => {
          return rfp.feature.name === 'campaigns';
        });
        const reportsRFPs = rfps.filter((rfp) => {
          return rfp.feature.name === 'reports';
        });
        const siteConfigRFPs = rfps.filter((rfp) => {
          return rfp.feature.name === 'siteConfiguration';
        });
        const contentRFPs = rfps.filter((rfp) => {
          return rfp.feature.name === 'contentConfiguration';
        });
        const userRFPs = rfps.filter((rfp) => {
          return rfp.feature.name === 'users';
        });
        const groupRFPs = rfps.filter((rfp) => {
          return rfp.feature.name === 'groups';
        });
        const analyticsRFPs = rfps.filter((rfp) => {
          return rfp.feature.name === 'analytics';
        });
        const resourceAssetsRFPs = rfps.filter((rfp) => {
          return rfp.feature.name === 'resourceAssets';
        });

        const existingRFPS = await RoleFeaturePermissions.findAll({
          attributes: ['id', 'roleId', 'featureId', 'permissionId'],
          where: {
            roleId: role.id,
          },
          include: [{
            model: Features,
          }, {
            model: Permissions,
          }],
        });

        const hasDashboardPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'dashboard');
        const hasCampaignsPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'campaigns');
        const hasReportsPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'reports');
        const hasSiteConfigPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'siteConfiguration');
        const hasContentConfigPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'contentConfiguration');
        const hasUsersPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'users');
        const hasGroupsPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'groups');
        const hasAnalyticsPermission = !!existingRFPS.find(rfp => rfp.feature.name === 'analytics');
        const hasResourceAssetsPermission = !!existingRFPS.find(rfp => rfp.feature.name === 'resourceAssets');


        // handle dashboard permissions
        if (!hasDashboardPermissions && req.body.dashboardPermission !== false) {
          const newRfps = dashboardRFPs.map((rfp) => {
            return {
              featureId: rfp.featureId,
              permissionId: rfp.permissionId,
              roleId: role.id,
            };
          });
          await RoleFeaturePermissions.bulkCreate(newRfps);
        } else if (hasDashboardPermissions && req.body.dashboardPermission === false) {
          for (const nextRFP of existingRFPS) {
            if (nextRFP.feature.name === 'dashboard') {
              await RoleFeaturePermissions.destroy({ where: { id: nextRFP.id } });
            }
          }
        }

        // handle campaign permissions
        if (!hasCampaignsPermissions && req.body.campaignsPermission !== false) {
          const newRfps = campaignRFPs.map((rfp) => {
            return {
              featureId: rfp.featureId,
              permissionId: rfp.permissionId,
              roleId: role.id,
            };
          });
          await RoleFeaturePermissions.bulkCreate(newRfps);
        } else if (hasCampaignsPermissions && req.body.campaignsPermission === false) {
          for (const nextRFP of existingRFPS) {
            if (nextRFP.feature.name === 'campaigns') {
              await RoleFeaturePermissions.destroy({ where: { id: nextRFP.id } });
            }
          }
        }

        // handle reports permissions
        if (!hasReportsPermissions && req.body.reportsPermission !== false) {
          const newRfps = reportsRFPs.map((rfp) => {
            return {
              featureId: rfp.featureId,
              permissionId: rfp.permissionId,
              roleId: role.id,
            };
          });
          await RoleFeaturePermissions.bulkCreate(newRfps);
        } else if (hasReportsPermissions && req.body.reportsPermission === false) {
          for (const nextRFP of existingRFPS) {
            if (nextRFP.feature.name === 'reports') {
              await RoleFeaturePermissions.destroy({ where: { id: nextRFP.id } });
            }
          }
        }

        // handle site config permissions
        if (!hasSiteConfigPermissions && req.body.siteConfigPermission !== false) {
          const newRfps = siteConfigRFPs.map((rfp) => {
            return {
              featureId: rfp.featureId,
              permissionId: rfp.permissionId,
              roleId: role.id,
            };
          });
          await RoleFeaturePermissions.bulkCreate(newRfps);
        } else if (hasSiteConfigPermissions && req.body.siteConfigPermission === false) {
          for (const nextRFP of existingRFPS) {
            if (nextRFP.feature.name === 'siteConfiguration') {
              await RoleFeaturePermissions.destroy({ where: { id: nextRFP.id } });
            }
          }
        }

        // handle content config permissions
        if (!hasContentConfigPermissions && req.body.contentLibraryPermission !== false) {
          const newRfps = contentRFPs.map((rfp) => {
            return {
              featureId: rfp.featureId,
              permissionId: rfp.permissionId,
              roleId: role.id,
            };
          });
          await RoleFeaturePermissions.bulkCreate(newRfps);
        } else if (hasContentConfigPermissions && req.body.contentLibraryPermission === false) {
          for (const nextRFP of existingRFPS) {
            if (nextRFP.feature.name === 'contentConfiguration') {
              await RoleFeaturePermissions.destroy({ where: { id: nextRFP.id } });
            }
          }
        }

        // handle users permissions
        if (!hasUsersPermissions && req.body.usersPermission !== false) {
          // didn't have them but now has them
          // if restricted filter out create privileges
          const filteredUserRFPs = userRFPs.filter(rfp =>
            !isRestricted || (rfp.permission.name !== 'create' && rfp.permission.name !== 'createAccount'));
          const newRfps = filteredUserRFPs.map((rfp) => {
            return {
              featureId: rfp.featureId,
              permissionId: rfp.permissionId,
              roleId: role.id,
            };
          });
          await RoleFeaturePermissions.bulkCreate(newRfps);
        } else if (hasUsersPermissions && req.body.usersPermission === false) {
          // had them but now doesn't have them
          for (const nextRFP of existingRFPS) {
            if (nextRFP.feature.name === 'users') {
              await RoleFeaturePermissions.destroy({ where: { id: nextRFP.id } });
            }
          }
        } else if (wasRestricted && !isRestricted) {
          // was restricted and now isn't restricted
          // add create user privileges
          const filteredUserRFPs = userRFPs.filter(rfp =>
            rfp.permission.name === 'create' || rfp.permission.name === 'createAccount');

          const newRfps = filteredUserRFPs.map((rfp) => {
            return {
              featureId: rfp.featureId,
              permissionId: rfp.permissionId,
              roleId: role.id,
            };
          });
          await RoleFeaturePermissions.bulkCreate(newRfps);
        } else if (!wasRestricted && isRestricted) {
          // wasn't restricted and now is restricted
          // remove create user privileges
          for (const nextRFP of existingRFPS) {
            const fName = nextRFP.feature.name;
            const pName = nextRFP.permission.name;
            if (fName === 'users' && (pName === 'create' || pName === 'createAccount')) {
              await RoleFeaturePermissions.destroy({ where: { id: nextRFP.id } });
            }
          }
        }

        // handle groups permissions
        if (!hasGroupsPermissions && req.body.groupsPermission !== false) {
          const newRfps = groupRFPs.map((rfp) => {
            return {
              featureId: rfp.featureId,
              permissionId: rfp.permissionId,
              roleId: role.id,
            };
          });
          await RoleFeaturePermissions.bulkCreate(newRfps);
        } else if (hasGroupsPermissions && req.body.groupsPermission === false) {
          for (const nextRFP of existingRFPS) {
            if (nextRFP.feature.name === 'groups') {
              await RoleFeaturePermissions.destroy({ where: { id: nextRFP.id } });
            }
          }
        }

        // handle analytics permissions
        if (!hasAnalyticsPermission && req.body.analyticsPermission !== false) {
          const newRfps = analyticsRFPs.map((rfp) => {
            return {
              featureId: rfp.featureId,
              permissionId: rfp.permissionId,
              roleId: role.id,
            };
          });
          await RoleFeaturePermissions.bulkCreate(newRfps);
        } else if (hasAnalyticsPermission && req.body.analyticsPermission === false) {
          for (const nextRFP of existingRFPS) {
            if (nextRFP.feature.name === 'analytics') {
              await RoleFeaturePermissions.destroy({ where: { id: nextRFP.id } });
            }
          }
        }

        // handle resourceAssets permissions
        if (!hasResourceAssetsPermission && req.body.resourceAssetsPermission !== false) {
          const newRfps = resourceAssetsRFPs.map((rfp) => {
            return {
              featureId: rfp.featureId,
              permissionId: rfp.permissionId,
              roleId: role.id,
            };
          });
          await RoleFeaturePermissions.bulkCreate(newRfps);
        } else if (hasResourceAssetsPermission && req.body.resourceAssetsPermission === false) {
          for (const nextRFP of existingRFPS) {
            if (nextRFP.feature.name === 'resourceAssets') {
              await RoleFeaturePermissions.destroy({ where: { id: nextRFP.id } });
            }
          }
        }
      }
    }
    const updatedRoleWithGroups =
      await Roles.findByPk(updatedRole.id, { include: [{ model: Groups, required: false }] });
    res.json(updatedRoleWithGroups);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /roles/{roleId}:
 *   delete:
 *     summary: Delete Role
 *     tags: [Roles]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: roleId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/roles'
 *                 - type: object
 *                   properties:
 *                     groups:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/groups'
 *                           - type: object
 *                             properties:
 *                               roleGroups:
 *                                 $ref: '#/components/schemas/roleGroups'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */

module.exports.delete = async (req, res, next) => {
  const role = req.role;
  try {
    let data = {};
    await db.sequelize.transaction(async (transaction) => {
      const usersWithRoleCount = await AccountUsers.count({
        where: { roleId: role.id },
      });
      if (usersWithRoleCount > 0) {
        const err =
          new Error(`This role name cannot be deleted. ${usersWithRoleCount} users are assigned to this role`);
        err.status = 400;
        throw err;
      }

      await RoleFeaturePermissions.destroy({
        where: {
          roleId: role.id,
        },
        transaction,
      });

      await RoleGroups.destroy({
        where: {
          roleId: role.id,
        },
        transaction,
      });
      // Any users with this role should now get the regular account Admin Role
      const accountAdminRole = await Roles.findOne({
        where: {
          name: 'accountAdmin',
        },
        transaction,
      });
      if (accountAdminRole) {
        await AccountUsers.update(
          {
            roleId: accountAdminRole.id,
          },
          {
            where: {
              roleId: role.id,
            },
            fields: ['roleId'],
            validate: true,
            transaction,
          },
        );
      }
      data = await role.destroy({
        transaction,
      });
    });

    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.accountPermissionSettings = async (req, res, next) => {
  const role = req.role;
  try {
    let accountRolePermissionSettings = {};
    if (role.accountId && role.accountId === req.user.accountId) {
      const existingRFPS = await RoleFeaturePermissions.findAll({
        where: {
          roleId: role.id,
        },
        include: [
          {
            model: Features,
          },
        ],
      });

      const hasDashboardPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'dashboard');
      const hasCampaignsPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'campaigns');
      const hasReportsPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'reports');
      const hasSiteConfigPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'siteConfiguration');
      const hasContentConfigPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'contentConfiguration');
      const hasUsersPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'users');
      const hasGroupsPermissions = !!existingRFPS.find(rfp => rfp.feature.name === 'groups');
      const hasAnalyticsPermission = !!existingRFPS.find(rfp => rfp.feature.name === 'analytics');
      const hasResourceAssetsPermission = !!existingRFPS.find(rfp => rfp.feature.name === 'resourceAssets');


      accountRolePermissionSettings = {
        dashboardPermission: !!hasDashboardPermissions,
        campaignsPermission: !!hasCampaignsPermissions,
        reportsPermission: !!hasReportsPermissions,
        siteConfigPermission: !!hasSiteConfigPermissions,
        contentLibraryPermission: !!hasContentConfigPermissions,
        usersPermission: !!hasUsersPermissions,
        groupsPermission: !!hasGroupsPermissions,
        analyticsPermission: !!hasAnalyticsPermission,
        resourceAssetsPermission: !!hasResourceAssetsPermission,
      };
    }
    res.json(accountRolePermissionSettings);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the role when an id is passed in the route
module.exports.roleById = async function (req, res, next, id) {
  try {
    const allowedPermissions = await acl.getAllowedPermissions(
      req,
      req.user ? req.user.id : null, 'roles', req.tokenPayload,
    );
    let role;
    if (req.user) {
      role = await Roles.findByPk(id, { include: [{ model: Groups, required: false }] });
    }
    if (!role ||
      (role.accountId !== req.user.accountId && !allowedPermissions.roles.includes('read'))
    ) {
      const err = new Error(req.i18n.t('roles.role_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.role = role;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * components:
 *   schemas:
 *     roles:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 *         accountId:
 *           type: integer
 *         groups:
 *           type: array
 *           items:
 *             type: object
 *         siteConfigPermission:
 *           type: boolean
 *         contentLibraryPermission:
 *           type: boolean
 *         usersPermission:
 *           type: boolean
 *         groupsPermission:
 *           type: boolean
 *         campaignsPermission:
 *           type: boolean
 *         reportsPermission:
 *           type: boolean
 *         dashboardPermission:
 *           type: boolean
 *         analyticsPermission:
 *           type: boolean
 *         resourceAssetsPermission:
 *           type: boolean
 *         topicIds:
 *           type: array
 *           items:
 *             type: integer
 */
