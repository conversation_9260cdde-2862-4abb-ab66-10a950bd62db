// eslint-disable-next-line no-unused-vars
const config = require('../config/config');

const { thirtyDayLoginTrend, dashboardPopularContent } = require('../services/utils/dashboardUtils');

/**
 * @openapi
 * /dashboard/activeUserTrend:
 *   get:
 *     summary: Fetch active user trend records
 *     tags:
 *       - Dashboard
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records
 *                 limit:
 *                   type: integer
 *                   description: Limit records per page
 *                 skip:
 *                   type: integer
 *                   description: Skip number of records
 *                 data:
 *                   type: array
 *                   description: Array of report data object
 *                   items:
 *                     type: object
 *                     properties:
 *                       dateOfActivities:
 *                         type: integer
 *                       activityCount:
 *                         type: integer
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.activeUserTrend = async (req, res, next) => {
  try {
    const data = await thirtyDayLoginTrend(req);
    res.json(data);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /dashboard/popularContent?$limit={limit}&$skip={skip}:
 *   get:
 *     summary: Fetch popular content
 *     tags:
 *       - Dashboard
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         schema:
 *           type: integer
 *         required: true
 *         description: Number of records to return
 *       - name: $skip
 *         in: query
 *         schema:
 *           type: integer
 *         required: true
 *         description: Number of records to skip
 *     responses:
 *       200:
 *         description: Successfully fetched popular content
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     description: Popular content item
 *                     properties:
 *                       id:
 *                         type: integer
 *                       title:
 *                         type: string
 *                       views:
 *                         type: integer
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.popularContent = async (req, res, next) => {
  const limit = 6;
  try {
    const results = await dashboardPopularContent(req, limit, null);
    const data = {
      total: results.length,
      limit,
      skip: 0,
      data: results,
    };
    res.json(data);
  } catch (err) {
    next(err);
  }
};
