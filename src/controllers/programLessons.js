const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const programUtils = require('../services/utils/programUtils');
const { restQueryToSequelize } = require('../services/utils/userUtils');
const { addLessonToProgram, removeLessonFromProgram,
  reorderProgramLessonULs } = require('../services/utils/campaignUtils');
const { addLessonAccessToPrograms } = require('./resourceBundles');

const Lessons = db.lessons;
const Programs = db.programs;
const LessonPrograms = db.lessonPrograms;
const Op = db.Sequelize.Op;

async function refreshStack(programId, array) {
  await LessonPrograms.destroy({ where: { programId, dateRemoved: { [Op.eq]: null } } });
  const promises = [];
  array.forEach((lesson) => {
    const newLesson = {
      lessonId: lesson.lessonId,
      programId,
      position: lesson.position,
      dateAdded: lesson.dateAdded,
    };
    promises.push(LessonPrograms.create(newLesson));
  });
  await Promise.all(promises);
}

async function moveLessonsDown(programId, idx) {
  if (_.isNil(programId)) {
    return;
  }
  const lessonBindings = await programUtils.lookupLessons(programId);
  const bindings = lessonBindings.filter(binding => binding.position > idx);
  const ids = bindings.map((binding) => {
    return binding.id;
  });
  if (ids.length > 0) {
    await db.sequelize.query(
      'UPDATE `lessonPrograms` SET position = position + 1 WHERE id IN (?)',
      {
        replacements: [ids],
        type: db.sequelize.QueryTypes.UPDATE,
        raw: true,
      },
    );
  }
}

module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['position', 'ASC']],
    limit: 200,
    offset: 0,
    include: [
      {
        model: Lessons,
      },
      {
        model: Programs,
      },
    ],
  };
  const queryParams = restQueryToSequelize(req.query, defaults);
  queryParams.order = [[...queryParams.order]];
  queryParams.attributes = programUtils.queryAttributes;
  queryParams.where = { ...queryParams.where, dateRemoved: { [Op.eq]: null } };

  const finalQuery = {
    ...queryParams,
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  const countQuery = {
    where: _.pick(finalQuery, ['where']).where,
  };
  try {
    const count = await LessonPrograms.count(countQuery);
    const lessonBindings = await LessonPrograms.findAll(finalQuery);
    const data = lessonBindings.map(record => record.get({ plain: true }));
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res) => {
  res.json(req.programLesson);
};

/**
 * @swagger
 * /program-lessons:
 *   post:
 *     summary: Add lesson program
 *     tags: [Program]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - in: body
 *         name: body
 *         description: Suggested Fields parameters
 *         required: true
 *         schema:
 *           type: object
 *           properties:
 *             lessonId:
 *               type: integer
 *               example: 1
 *             position:
 *               type: integer
 *               example: 1
 *             programId:
 *               type: integer
 *               example: 1
 *     responses:
 *       200:
 *         description: Successful operation
 *         schema:
 *           type: object
 *           properties:
 *             lesson:
 *               $ref: '#/components/schemas/lessons'
 *             program:
 *               $ref: '#/components/schemas/programs'
 *             lessonProgram:
 *               $ref: '#/components/schemas/lessonPrograms'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  const programLesson = req.body;
  try {
    const oldBinding = await LessonPrograms.findOne({
      where:
        {
          programId: programLesson.programId,
          lessonId: programLesson.lessonId,
        },
    });

    if (oldBinding) {
      const id = programLesson.programId;
      let err = null;
      if (oldBinding.dateRemoved) {
        err = new Error(req.i18n.t('programs.lesson_removed_cannot_be_added_Error', { id }));
      } else {
        err = new Error(req.i18n.t('programs.lesson_already_exists_Error', { id }));
      }
      err.status = 400;
      throw err;
    }

    const oldLessons = await programUtils.lookupLessons(programLesson.programId);
    if (programLesson.position === undefined) {
      programLesson.position = oldLessons.length + 1; // add on end
    }
    let insertionPoint = programLesson.position - 1; // zero-base position
    if (oldLessons.length === 0) {
      insertionPoint = 0;
      programLesson.position = 1;
    } else if (insertionPoint > oldLessons.length) {
      insertionPoint = oldLessons.length;
    } else if (insertionPoint < 0) {
      insertionPoint = 0;
    }
    await moveLessonsDown(programLesson.programId, insertionPoint);

    // if this program has already been published, we should record the dateAdded for the lessonCard
    let dateAdded = null;
    const launchByUsersCount = await programUtils.launchedByUsersCount(programLesson.programId);
    if (launchByUsersCount > 0) {
      dateAdded = Date.now();
      programLesson.dateAdded = dateAdded;
    }
    let newUserLesson = await LessonPrograms.create(programLesson);
    await addLessonToProgram(newUserLesson);
    await reorderProgramLessonULs(programLesson.programId);
    newUserLesson = newUserLesson.get({ plain: true });
    const { programId, lessonId, position } = newUserLesson;
    const newProgramLesson = await LessonPrograms.findOne({
      attributes: programUtils.queryAttributes,
      where: {
        programId,
        lessonId,
        position,
      },
      include: [
        {
          model: Lessons,
        },
        {
          model: Programs,
        },
      ],
    });
    // grant access to any account that has access to program
    await addLessonAccessToPrograms(
      newProgramLesson.program.resourceId,
      newProgramLesson.lesson.resourceId,
      req.i18n,
    );
    res.json(newProgramLesson);
  } catch (err) {
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  try {
    const updatedProgramLesson = await req.programLesson.update(req.body);
    await refreshStack(updatedProgramLesson.programId, req.body);
    await reorderProgramLessonULs(updatedProgramLesson.programId);
    const newStack = await programUtils.lookupLessons(updatedProgramLesson.programId);
    res.json(newStack);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /program-lessons/{programLessonId}:
 *   delete:
 *     summary: Delete lesson from program
 *     tags: [Program]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: programLessonId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Deleted lesson successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Lesson deleted successfully
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  const programLesson = req.programLesson;
  const { programId } = programLesson;

  try {
    if (programLesson.dateAdded) {
      const id = programId;
      const err = new Error(req.i18n.t('programs.lesson_added_cannot_be_removed_Error', { id }));
      err.status = 400;
      throw err;
    }

    // if this lesson has already been published, we should record the dateAdded for the lessonCard
    let dateRemoved = null;
    const launchByUsersCount = await programUtils.launchedByUsersCount(programId);
    if (launchByUsersCount > 0) {
      dateRemoved = Date.now();
    }
    if (dateRemoved) {
      programLesson.dateRemoved = dateRemoved;
      await programLesson.save();
    }
    await removeLessonFromProgram(programLesson);
    if (!dateRemoved) {
      await programLesson.destroy();
    }
    await programUtils.refreshOrder(programId);
    await reorderProgramLessonULs(programId);
    const newStack = await programUtils.lookupLessons(programId);
    res.json(newStack);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve a specific program member when an id is passed in the route
module.exports.programLessonById = async function (req, res, next, id) {
  try {
    const programLesson = await LessonPrograms.findOne({
      attributes: programUtils.queryAttributes,
      where: {
        id,
      },
      include: [
        {
          model: Lessons,
        },
        {
          model: Programs,
        },
      ],
    });
    if (!programLesson) {
      const err = new Error(req.i18n.t('programs.lesson_binding_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.programLesson = programLesson;
    next();
  } catch (err) {
    next(err);
  }
};
