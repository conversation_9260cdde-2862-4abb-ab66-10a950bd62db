const db = require('../db');

const userAccountFields = db.userAccountFields;
const acl = require('../services/acl/acl');
const Op = db.Sequelize.Op;

const AccountFields = db.accountFields;
const Groups = db.groups;
const User = db.users;
const GroupRules = db.groupRules;
const CsvColumns = db.csvColumns;
const CsvSettings = db.csvSettings;

/**
 * @openapi
 * /account-fields:
 *   get:
 *     summary: Fetch Account Fields based on accountId
 *     tags:
 *       - Account Fields
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: accountId
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 1
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *     responses:
 *       200:
 *         description: Successfully fetched account fields list
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records
 *                 data:
 *                   type: array
 *                   description: Array of accountFields objects
 *                   items:
 *                     $ref: '#/components/schemas/accountFields'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     accountFields:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         value:
 *           type: string
 */

module.exports.list = async (req, res, next) => {
  try {
    const allowedPermissions = await acl.getAllowedPermissions(req, req.user.id, 'accounts', req.tokenPayload);

    let accountId = req.user.accountId;
    if (req.query && allowedPermissions.accounts.includes('read')) {
      if (req.query.suggestedFields) {
        accountId = null;
      } else if (req.query.accountId) {
        accountId = req.query.accountId;
      }
    }
    const accountFields = await AccountFields.findAll({
      where: {
        accountId,
      },
      order: [['updatedAt', 'DESC']],
    });
    res.json({ total: accountFields.length, data: accountFields });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /account-fields:
 *   post:
 *     summary: Add Suggested Fields To Account
 *     tags:
 *       - Account Fields
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       description: Suggested Fields parameters
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/accountFields'
 *     responses:
 *       200:
 *         description: Suggested fields added successfully.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/accountFields'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     accountFields:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         value:
 *           type: string
 */

module.exports.create = async (req, res, next) => {
  try {
    if (User.rawAttributes[req.body.fieldName] !== undefined) {
      const err = new Error('The field cannot have the same name as a reserved user field.');
      err.status = 409;
      throw err;
    }

    if (!req.body.accountId) {
      req.body.accountId = req.user.accountId;
    }

    let existingSortOrders = [];
    const existingAccountFields = await AccountFields.findAll({
      where: {
        accountId: req.body.accountId,
      },
      attributes: ['sortOrder']
    });

    if(existingAccountFields && existingAccountFields.length > 0) {
      existingSortOrders = existingAccountFields.map(accountField => accountField.sortOrder);
    }
    const newSortOrder = existingSortOrders && existingSortOrders.length > 0 ? Math.max(...existingSortOrders) + 1 : 0;
    req.body.sortOrder = newSortOrder;
    const accountField = await AccountFields.create(req.body);
    res.json(accountField);
  } catch (err) {
    if (err.name === 'SequelizeValidationError') {
      // statements to handle TypeError exceptions
      err.status = 422;
    }
    next(err);
  }
};

/**
 * @openapi
 * /account-fields/{accountFieldId}:
 *   patch:
 *     summary: Update Account Field
 *     tags:
 *       - Account Fields
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: accountFieldId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the account field to update
 *     requestBody:
 *       required: true
 *       description: Suggested Fields parameters
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/accountFields'
 *     responses:
 *       200:
 *         description: Update account field successfully.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/accountFields'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     accountFields:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         value:
 *           type: string
 */

module.exports.patch = async (req, res, next) => {
  const existingAccountField = req.accountField;
  const modifiedAccountField = req.body;

  const accountId = req.accountField.accountId;

  try {
    if (User.rawAttributes[modifiedAccountField.fieldName] !== undefined) {
      const err = new Error('The field cannot have the same name as a reserved user field.');
      err.status = 409;
      throw err;
    }

    // field is used in dynamic groups for this account
    const numDynamicGroups = await Groups.count({
      where: {
        groupType: 'dynamic',
        accountId,
      },
      include: [{
        model: GroupRules,
        where: { field: existingAccountField.fieldName },
      }],
    });
    if (numDynamicGroups > 0) {
      const err = new Error(req.i18n.t('accountFields.inUse_byGroup_edit_Error', { numDynamicGroups }));
      err.status = 404;
      throw err;
    }

    // field is used in csv configurations for this account
    const numCsvColumns = await CsvColumns.count({
      where: {
        userTableColumn: existingAccountField.fieldName,
      },
      include: [{
        model: CsvSettings,
        where: {
          accountId,
        },
      }],
    });
    if (numCsvColumns > 0) {
      const err = new Error(`The field cannot be edited because it is used by ${numCsvColumns} CSV import(s).`);
      err.status = 404;
      throw err;
    }

    const updatedAccountField = await existingAccountField.update(modifiedAccountField);
    const finalAccountField = await AccountFields.findByPk(updatedAccountField.id);
    res.json(finalAccountField);
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  try {
    const allowedPermissions = await acl.getAllowedPermissions(req, req.user.id, 'accounts', req.tokenPayload);

    const accountId = req.accountField.accountId;
    if (accountId !== req.user.accountId && !allowedPermissions.accounts.includes('read')) {
      const err = new Error(req.i18n.t('permissions.insufficientPermissions_Error'));
      err.status = 404;
      throw err;
    }

    res.json(req.accountField);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /account-fields/{accountFieldID}:
 *   delete:
 *     summary: Delete Suggested Field
 *     tags:
 *       - Account Fields
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: accountFieldID
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the account field to delete
 *     responses:
 *       200:
 *         description: Account field deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/accountFields'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     accountFields:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         value:
 *           type: string
 */

module.exports.delete = async (req, res, next) => {
  const accountField = req.accountField;

  const accountId = accountField.accountId;

  try {
    // field is used in dynamic groups
    const numDynamicGroups = await Groups.count({
      where: {
        groupType: 'dynamic',
        accountId,
      },
      include: [{
        model: GroupRules,
        where: { field: accountField.fieldName },
      }],
    });
    if (numDynamicGroups > 0) {
      const err = new Error(req.i18n.t('accountFields.customField_byGroup_delete_Error', { numDynamicGroups }));
      err.status = 404;
      throw err;
    }
    
    // field is used in Self Signup form
    if (accountField && accountField.includeInSelfSignup) {
      const err = new Error(req.i18n.t('accountFields.customField_selfSignup_delete_Error'));      
      err.status = 404;
      throw err;
    }

    // field is used in csv configurations for this account
    const numCsvColumns = await CsvColumns.count({
      where: {
        userTableColumn: accountField.fieldName,
      },
      include: [{
        model: CsvSettings,
        where: {
          accountId,
        },
      }],
    });
    if (numCsvColumns > 0) {
      const err = new Error(req.i18n.t('accountFields.customField_csv_roster_delete_Error'));   
      err.status = 404;
      throw err;
    }

    // field is used by users
    const numUserAccountFields = await userAccountFields.count({
      where: {
        accountFieldId: accountField.id,
        value: {
          [Op.and]: [
            { [Op.ne]: '' },      // Not an empty string
            { [Op.not]: null },   // Not NULL
            { [Op.ne]: 'null' },  // Not the string 'null'
          ],
        },
      },
      include: [{
        model: AccountFields,
        where: {
          accountId,
        },
      }],
    });

    if (numUserAccountFields > 0) {
      const err = new Error(req.i18n.t('accountFields.inUse_byUser_delete_Error', { numUserAccountFields }));
      err.status = 404;
      throw err;
    } else {
      // delete userAccountFields contains empty string and null value
      await userAccountFields.destroy({
        where: {
          accountFieldId: accountField.id,
          value: {
            [Op.or]: {
              [Op.eq]: '',     // Check for empty string
              [Op.eq]: 'null', // Check for empty string
              [Op.is]: null,   // Check for null
            },
          },
        },
        include: [{
          model: AccountFields,
          where: {
            accountId,
          },
        }],
      });
    }

    const allowedPermissions = await acl.getAllowedPermissions(req, req.user.id, 'accounts', req.tokenPayload);

    if (!allowedPermissions.accounts.includes('update')) {
      const err = new Error(req.i18n.t('permissions.insufficientPermissions_Error'));
      err.status = 404;
      throw err;
    }

    const data = await accountField.destroy();
    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.accountFieldById = async (req, res, next, id) => {
  try {
    const accountField = await AccountFields.findByPk(id);
    if (!accountField) {
      const err = new Error(req.i18n.t('accountFields.retrieval_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.accountField = accountField;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * components:
 *   schemas:
 *     accountFields:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         fieldName:
 *           type: string
 *         fieldType:
 *           type: string
 *           description: fieldType may be - number, string, email, SMS, date
 *         accountId:
 *           type: integer
 *         userEditable:
 *           type: boolean
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 */
