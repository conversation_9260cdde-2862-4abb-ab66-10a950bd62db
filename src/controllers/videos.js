const db = require('../db');
const logger = require('../logger');
const { secondsToVTTTime } = require('../services/utils/jsUtils');
const { getVideoData, getVideoSubtitleData } = require('../services/utils/centerstageUtils');
const { isBefore, differenceInSeconds } = require('date-fns');

const Captions = db.captions;
const Subtitles = db.subtitles;
const Videos = db.videos;
const VideoTranscripts = db.videoTranscripts;
const ContentStrings = db.contentStrings;

const convertToVTT = (subtitle) => {
  let vttBuffer = 'WEBVTT\r\n';
  for (const caption of subtitle.captions) {
    const begin = secondsToVTTTime(caption.t_begin);
    const end = secondsToVTTTime(caption.t_end);
    vttBuffer += '\r\n';
    vttBuffer += `${begin} --> ${end}\r\n`;
    vttBuffer += `${caption.content}\r\n`;
  }
  return vttBuffer;
};

const fetchSubtitleInfo = async (videoId, subtitleId) => {
  let subtitle;
  try {
    // separate fetch for subtitle caption bundles
    const csSubtitle = await getVideoSubtitleData(videoId, subtitleId);
    if (csSubtitle) {
      const captions = [];
      subtitle = {
        csSubtitleId: csSubtitle.id,
        csVideoId: csSubtitle.video_id,
        languageCode: csSubtitle.language,
        languageName: csSubtitle.languageName,
        csLastUpdate: csSubtitle.updated_at,
        captions,
      };
      for (const caption of csSubtitle.captions) {
        captions.push({
          content: caption.content,
          t_begin: caption.t_begin,
          t_end: caption.t_end,
        });
      }
    }
  } catch (err) {
    logger.error(`Exception caught in videos.fetchSubtitleInfo(), err = ${err}`);
  }
  return subtitle;
};

const fetchVideoInfo = async (videoId, includeCaptions = false) => {
  let vidData;
  try {
    // get video meta data structure from centerstage
    const csData = await getVideoData(videoId);
    if (csData) {
      // xform into db structure for insertion
      const subtitles = [];
      vidData = {
        csVideoId: csData.id,
        cdnUrl: csData.cdn_url_for_json,
        ssFilename: csData.screenshot_file_name,
        csLastUpdate: csData.updated_at,
        subtitles,
      };
      if (includeCaptions) {
        for (const subtitle of csData.subtitles) {
          // separate fetch for subtitle caption bundles
          const csSubtitle = await fetchSubtitleInfo(videoId, subtitle.id);
          // subtitle fetch does not include language proper name, so patch it here from video structure
          csSubtitle.languageProperName = subtitle.language.proper_name;
          subtitles.push(csSubtitle);
        }
      } else {
        for (const subtitle of csData.subtitles) {
          // just include subtitle thumbnails
          subtitles.push({
            csSubtitleId: subtitle.id,
            csVideoId: parseInt(videoId),
            languageCode: subtitle.language.iso_code,
            languageName: subtitle.language.name,
            languageProperName: subtitle.language.proper_name,
            csLastUpdate: null, // null update timestamp indicates subtitle is not fully fetched
          });
        }
      }
    }
  } catch (err) {
    logger.error(`Exception caught in videos.fetchVideoInfo(), err = ${err}`);
  }
  return vidData;
};

const updateSubtitle = async (subtitle, csSubtitleData) => {
  if (subtitle && csSubtitleData) {
    // update main record
    await subtitle.update(csSubtitleData);
    // clear old captions
    await Captions.destroy({
      where: {
        subtitleId: subtitle.id,
      },
    });
    // reinsert new captions
    const captions = [];
    for (const caption of csSubtitleData.captions) {
      captions.push({
        subtitleId: subtitle.id,
        content: caption.content,
        t_begin: caption.t_begin,
        t_end: caption.t_end,
      });
    }
    await Captions.bulkCreate(captions);
  }
  // reload with associations
  return Subtitles.findByPk(subtitle.id, {
    include: [{
      model: Captions,
    }],
  });
};

const removeSubtitle = async (subtitle) => {
  await Captions.destroy({
    where: {
      subtitleId: subtitle.id,
    },
  });
  return subtitle.destroy();
};

const getAndSaveSubtitleInfo = async (videoId, csVideoId, csSubtitleId, languageProperName = null) => {
  let newSubtitle;
  try {
    const csSubtitleData = await fetchSubtitleInfo(csVideoId, csSubtitleId);
    if (csSubtitleData) {
      csSubtitleData.videoId = videoId; // this is the db videoId
      if (languageProperName) {
        csSubtitleData.languageProperName = languageProperName; // subtitle api doesn't return this
      }
      // stick it in
      newSubtitle = await Subtitles.create(
        csSubtitleData,
        {
          include: [
            {
              model: Captions,
            },
          ],
        },
      );
    }
  } catch (err) {
    logger.error(`Exception caught in videos.getAndSaveSubtitleInfo(), err = ${err}`);
    throw err;
  }
  return newSubtitle;
};

const getAndUpdateSubtitleInfo = async (csVideoId, subtitle) => {
  let updatedSubtitle;
  try {
    const csSubtitleData = await fetchSubtitleInfo(csVideoId, subtitle.csSubtitleId);
    updatedSubtitle = await updateSubtitle(subtitle, csSubtitleData);
  } catch (err) {
    logger.error(`Exception caught in videos.getAndUpdateSubtitleInfo(), err = ${err}`);
    throw err;
  }
  return updatedSubtitle;
};

const removeVideo = async (video) => {
  if (video) {
    for (const subtitle of video.subtitles) {
      await removeSubtitle(subtitle);
    }
    return video.destroy();
  }
  return Promise.resolve();
};

const getAndSaveVideoInfo = async (videoId, includeCaptions = false) => {
  let newVideo;
  try {
    const csVideoData = await fetchVideoInfo(videoId, includeCaptions);
    if (csVideoData) {
      // stick it in
      newVideo = await Videos.create(
        csVideoData,
        {
          include: [
            {
              model: Subtitles,
              include: [
                {
                  model: Captions,
                },
              ],
            },
          ],
        },
      );
    }
  } catch (err) {
    logger.error(`Exception caught in videos.getAndSaveVideoInfo(), err = ${err}`);
    throw err;
  }
  return newVideo;
};

const updateVideo = async (video, csVideoData, includeCaptions = false, force = false, checkUpdate = false) => {
  if (video && csVideoData) {
    // check for updates to subtitles
    const existingSubtitleIds = video.subtitles.map(st => st.csSubtitleId);
    const newSubtitleIds = csVideoData.subtitles.map(st => st.csSubtitleId);
    const subtitlesToAdd = csVideoData.subtitles.filter(st => !existingSubtitleIds.includes(st.csSubtitleId));
    const subtitlesToRemove = video.subtitles.filter(st => !newSubtitleIds.includes(st.csSubtitleId));
    const subtitlesToCheck = video.subtitles.filter(st => newSubtitleIds.includes(st.csSubtitleId));

    // add any new subtitles
    for (const csSubtitle of subtitlesToAdd) {
      logger.info(`Video sweep, adding subtitle: ${csSubtitle.csSubtitleId} in video: ${video.csVideoId}`);
      await getAndSaveSubtitleInfo(video.id, video.csVideoId, csSubtitle.csSubtitleId, csSubtitle.languageProperName);
    }
    // delete any removed subtitles
    for (const subtitle of subtitlesToRemove) {
      logger.info(`Video sweep, removing subtitle: ${subtitle.csSubtitleId} in video: ${video.csVideoId}`);
      await removeSubtitle(subtitle);
    }
    // check if the rest need updates
    for (const subtitle of subtitlesToCheck) {
      const csSubtitleData = await fetchSubtitleInfo(video.csVideoId, subtitle.csSubtitleId);
      if (csSubtitleData) {
        if (force || isBefore(subtitle.csLastUpdate, csSubtitleData.csLastUpdate)) {
          logger.info(`Video sweep, updating subtitle: ${subtitle.csSubtitleId} in video: ${video.csVideoId}`);
          await updateSubtitle(subtitle, csSubtitleData);
        }
      }
    }
    // update video if indicated
    if (force || isBefore(video.csLastUpdate, csVideoData.csLastUpdate)) {
      logger.info(`Video sweep, updating video: ${video.csVideoId}`);
      await video.update(csVideoData);
    }
  }
  // reload with associations
  const includeClause = includeCaptions ? [{
    model: Subtitles,
    include: [{
      model: Captions,
    }],
  }] : [{
    model: Subtitles,
  }];
  if (checkUpdate) {
    includeClause.push({
      model: VideoTranscripts,
      attributes: ['transcript'],
    });
  }
  return Videos.findByPk(video.id, { include: includeClause });
};

/**
 * @swagger
 * /videos/{videoId}:
 *   get:
 *     summary: Fetch Video Details Based On VideoId
 *     tags: [Video]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: videoId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 video:
 *                   $ref: '#/components/schemas/Video'
 *                 subtitles:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Subtitle'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

const readVideo = (req, res) => {
  res.json(req.video);
};

/**
 * @swagger
 * /videos/{videoId}/subtitles/{subtitleId}.vtt:
 *   get:
 *     summary: Fetch video subtitle based on videoId and subtitleId
 *     tags: [Video]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: videoId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: subtitleId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successfully fetched video Subtitle based on videoId and subtitleId
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 subtitle:
 *                   $ref: '#/components/schemas/Subtitle'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
const readSubtitle = (req, res) => {
  const subtitle = req.subtitle;
  try {
    res.set({ 'Content-Type': 'text/vtt; charset=UTF-8' });
    res.write(convertToVTT(subtitle));
    res.end();
  } catch (err) {
    throw err;
  }
};

const videoById = async function (req, res, next, id) {
  try {
    const language = req.query.language || 'en';
    const cardId = req.query.cardId;
    const checkUpdate = !!(req.query.checkUpdate && req.query.checkUpdate === 'true');
    const includeCaptions = !!(req.query.includeCaptions && req.query.includeCaptions === 'true');
    const includeClause = includeCaptions ? [{
      model: Subtitles,
      include: [{
        model: Captions,
      }],
    }] : [{
      model: Subtitles,
    }];

    let video;
    video = await Videos.findOne({
      where: {
        csVideoId: id,
      },
      include: includeClause,
    });

    if (!video) {
      video = await getAndSaveVideoInfo(id, includeCaptions);
    } else if (checkUpdate) {
      // check for update from centerstage
      const csVideoData = await fetchVideoInfo(video.csVideoId, includeCaptions);
      video = await updateVideo(video, csVideoData, includeCaptions, false, checkUpdate);
      if (language && language !== 'en' && cardId) {
        // get video transcript
        const videoTranscript = await ContentStrings.findOne({
          attributes: ['value', 'contentId'],
          where: {
            model: 'lessonCard',
            field: 'list4',
            contentId: cardId,
            language
          }
        });
        if (videoTranscript && videoTranscript?.value) {
          video = video.toJSON();
          video.videoTranscript = { transcript: videoTranscript?.value };
        }
      }
    }

    if (!video) {
      const err = new Error(req.i18n.t('videos.video_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.video = video;
    next();
  } catch (err) {
    next(err);
  }
};

const subtitleById = async function (req, res, next, id) {
  try {
    let subtitle;
    const video = req.video;

    subtitle = await Subtitles.findOne({
      where: {
        csVideoId: video.csVideoId,
        csSubtitleId: id,
      },
      include: [{
        model: Captions,
      }],
    });

    if (!subtitle) {
      subtitle = await getAndSaveSubtitleInfo(video.id, video.csVideoId, id);
    } else if (!subtitle.csLastUpdate) {
      subtitle = await getAndUpdateSubtitleInfo(video.csVideoId, subtitle);
    }

    if (!subtitle) {
      const err = new Error(req.i18n.t('videos.subtitle_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.subtitle = subtitle;
    next();
  } catch (err) {
    next(err);
  }
};

const sweepVideos = async () => {
  const start = new Date();
  let count = 0;
  logger.info(`Video sweep, starting: ${start}`);
  try {
    const videos = await Videos.findAll({
      include: [{
        model: Subtitles,
      }],
    });
    count = videos.length;

    // for every video we track
    for (let video of videos) {
      // fetch centerstage video data
      logger.info(`Video sweep, checking video: ${video.csVideoId}`);

      const csVideoData = await fetchVideoInfo(video.csVideoId);
      if (csVideoData) {
        // update Videos record
        video = await updateVideo(video, csVideoData);
      } else {
        // We are removing the deletion of videos to fix a bug where videos
        // were being deleted if centerstage was down or unresponsive.
        logger.info(`Video sweep, unable to fetch video from centerstage. videoId: ${video.csVideoId}`);
        // logger.info(`Video sweep, removing video: ${video.csVideoId}`);
        // await removeVideo(video);
      }
    }
  } catch (err) {
    logger.error(`Exception caught in sweepVideos(), err = ${err}`);
    throw err;
  } finally {
    logger.info(`Video sweep, exiting, elapsed: ${differenceInSeconds(new Date(), start)}s}`);
  }
  return count;
};

module.exports = {
  sweepVideos,
  subtitleById,
  videoById,
  readSubtitle,
  readVideo,
  getAndSaveVideoInfo,
};

/**
 * @swagger
 * components:
 *   schemas:
 *     Video:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         csVideoId:
 *           type: integer
 *         cdnUrl:
 *           type: string
 *         ssFilename:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         csLastUpdate:
 *           type: string
 *           format: date-time
 *         subtitles:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Subtitle'
 *
 *     Subtitle:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         videoId:
 *           type: integer
 *         csSubtitleId:
 *           type: integer
 *         csVideoId:
 *           type: integer
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         languageCode:
 *           type: string
 *         languageProperName:
 *           type: string
 *         csLastUpdate:
 *           type: string
 *           format: date-time
 *         languageName:
 *           type: string
 */
