const moment = require('moment');
const { cond, stubTrue, get } = require('lodash');
const { createHmac, timingSafeEqual } = require('crypto');

const db = require('../db');
const { validateSlackCode, fetchSlackUsers, sendSlackNotification, fetchSlackUserMsgs } = require('../services/slack');
const config = require('../config/config');
const logger = require('../logger');
const { InternalServerError, BadRequestError } = require('../services/utils/errors');
const batchPromises = require('../services/utils/batchPromises');

const SLACK_SIGNING_SECRET = get(config, 'slack.signingSecret');
const SLACK_BOT_TOKEN = get(config, 'slack.botToken');
const MAX_PROMISE_POOL = 20;

const SlackTeams = db.slackTeams;
const Users = db.users;
const Accounts = db.accounts;

const buildApiUrl = () => config.localNgrokSlackUrl || `https://${config.apiHost}`;
const toBuffer = input => Buffer.from(input, 'utf8');

function updateSlackUser({ slackId, slackTeamId, email }) {
  return Users.update(
    { slackId, slackTeamId },
    { where: { email: email.toLowerCase() } },
  );
}

const messageTitleMap = {
  welcome: ({ user, subjectText, url }) => `:wave: *Hello ${user.firstName} ${user.lastName}*\n*<${url}|${subjectText || 'You have a new workplace skills assignment'}>*`,
  reminder: ({ subjectText, url }) => `:bell: *<${url}|${subjectText || 'Don’t forget your workplace skills assignment'}>*`,
  nag: ({ subjectText, url }) => `:alarm_clock: *<${url}|${subjectText || 'Your workplace skills assignment is past due'}>*`,
  completion: ({ user, subjectText }) => `:tada: *Congrats ${user.firstName} ${user.lastName}!*\n*${subjectText || "You’ve completed your workplace skills assignment"}*`,
  introductory: ({}) => `Welcome to the Emtrain app on Slack! :wave: \n \n I’ll notify you when it is time to train, when deadlines are approaching, and when assignments are past due. Access the Emtrain platform with one click from the message to sign in and get training!`,
};

async function handleSlackInstallRedirect(req, res, next) {
  try {
    // 1: Validate the installation code that slack gives us.
    const { code, accountId } = req.query;

    const account = await Accounts.findByPk(accountId); // We need to grab the account subdomain in order to rebuild the uri
    const redirectUri = `${buildApiUrl()}/slack/redirect?accountId=${accountId}`; // Must match what we initially pass to validate the code
    const slackResponse = await validateSlackCode({ code, redirectUri });

    // 2: If the code is valid, we store the slack team id and access token
    const { team: { id: slackTeamId, name }, access_token: accessToken } = slackResponse;
    await SlackTeams.upsert({ slackTeamId, name, accessToken, accountId });

    // 3: Fetch the users in that slack team and sync them with our db if they exist
    const slackUsers = await fetchSlackUsers({ accessToken });
    await batchPromises(MAX_PROMISE_POOL, slackUsers, updateSlackUser);

    // 4: Finally, redirect them back to the integrations page
    res.redirect(`${config.frontEndManageHost.replace('{subdomain}', account.subdomain)}/manage/config`);
  } catch (e) {
    logger.error(`Could not handle slack install and redirect: ${e}`);
    next(e);
  }
}

const handleUserChangeEvent = async ({ event }, res) => {
  const userDeleted = get(event, 'user.deleted');
  const email = get(event, 'user.profile.email');
  const slackId = get(event, 'user.id');
  const slackTeamId = get(event, 'user.team_id');

  if (email) {
    const slackColumnsUpdate = userDeleted ? { slackId: null, slackTeamId: null } : { slackId, slackTeamId };
    try {
      await updateSlackUser({ email, ...slackColumnsUpdate });
    } catch (e) {
      throw new InternalServerError(e.message);
    }
  }

  res.sendStatus(200);
};

const handleAppUninstallEvent = async ({ team_id: teamId }, res) => {
  // remove the team record and update the users of that slack team
  try {
    await SlackTeams.destroy({ where: { slackTeamId: teamId } });

    await Users.update(
      { slackTeamId: null, slackId: null },
      { where: { slackTeamId: teamId } },
    );
  } catch (e) {
    throw new InternalServerError(e.message);
  }

  res.sendStatus(200);
};

const handleHomeOpenedEvent = async ({ event }, res) => {
  
  if (event.tab === "messages") {
    // check the message history if there was a prior interaction for this App DM
    let channel = event.channel 
    let msgData = await fetchSlackUserMsgs({channel})
    
    if (!msgData.messages.length) {
      const getMessageTitle = messageTitleMap['introductory'];
      const message = [
      createSectionText(getMessageTitle({ }))
    ];
      sendSlackNotification({ channel: channel, accessToken: SLACK_BOT_TOKEN, message });
    }
  }

  res.sendStatus(200);
};

const handleUnrecognizedEvent = ({ challenge }, res) => res.send({ challenge });

const onAppUninstallEvent = ({ event }) => get(event, 'type') === 'app_uninstalled';
const onUserChangeEvent = ({ event }) => get(event, 'type') === 'user_change'; // Handles new users joining as well as users getting deactivated
const onAppHomeOpenedEvent = ({ event }) => get(event, 'type') === 'app_home_opened'; // Handles event when User clicked into your App Home
const onUnrecogniedEvent = stubTrue;

async function handleSlackEvent(req, res) {
  cond([
    [onAppUninstallEvent, handleAppUninstallEvent],
    [onUserChangeEvent, handleUserChangeEvent],
    [onAppHomeOpenedEvent, handleHomeOpenedEvent],
    [onUnrecogniedEvent, handleUnrecognizedEvent],
  ])(req.body, res);
}

function validateSlackRequest(req, res, next) {
  const timestamp = req.headers['x-slack-request-timestamp'];
  const slackSignature = req.headers['x-slack-signature'];
  const timeInSeconds = Math.floor(Date.now() / 1000);
  const [version] = slackSignature.split('=');

  // First check for a replay attack
  if (Math.abs((timeInSeconds) - +timestamp) > 60 * 5) {
    throw new BadRequestError('Slack request is too stale to process');
  }

  // Next generate our signature using the signing secret and compare
  const signatureBaseString = `${version}:${timestamp}:${req.rawBody}`;
  const generatedHash = `${version}=${createHmac('sha256', SLACK_SIGNING_SECRET).update(signatureBaseString).digest('hex')}`;

  if (timingSafeEqual(toBuffer(generatedHash), toBuffer(slackSignature))) {
    next();
  } else {
    throw new BadRequestError('Could not validate slack request');
  }
}

const formatHtmlString = (str) => {
  const htmlRegex = /(<([^>]+)>)/ig;
  const paragraphRegex = /<br\s*[\/]?>/ig;
  const formattedString = str.replace(paragraphRegex, '\n> ').replace(htmlRegex, '');

  if (formattedString.length > 246) {
    return `${formattedString.slice(0, 246)}...`;
  }

  return formattedString;
};

const createSectionText = text => ({
  type: 'section',
  text: { type: 'mrkdwn', text },
});

const createContextText = text => ({
  type: 'context',
  elements: [{ type: 'mrkdwn', text }],
});


function handleSlackAlertForUser({ user, accessToken, notification, linkUrl }) {
  const notificationType = get(notification, 'campaignNotification.notificationType');
  const messageText = get(notification, 'campaignNotification.notificationText');
  const subjectText = get(notification, 'campaignNotification.notificationSubject');
  const campaignDuration = get(notification, 'campaignNotification.campaign.duration');
  const campaignStartDate = get(notification, 'campaignNotification.campaign.startDate');
  const getMessageTitle = messageTitleMap[notificationType];
  const dueDate = moment(campaignStartDate).add(campaignDuration, 'days').format('MM/DD/YYYY');

  if (getMessageTitle) {
    const message = [
      createSectionText(getMessageTitle({ user, subjectText, url: linkUrl })),
      createSectionText(`> ${formatHtmlString(messageText)}`),
    ];

    if (notificationType !== 'completion') { // Never show due date for completed campaign notifications
      message.push(createContextText(`Assignment Due: *${dueDate}*`));
    }

    sendSlackNotification({ channel: user.slackId, accessToken: config.encryption.decrypt(accessToken), message });
  }
}

module.exports = {
  handleSlackInstallRedirect,
  handleSlackEvent,
  validateSlackRequest,
  handleSlackAlertForUser,
};
