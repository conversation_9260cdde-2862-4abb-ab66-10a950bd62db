const { omit, isEmpty, get } = require('lodash');

const db = require('../db');
const logger = require('../logger');
const { DynamoDBClient } = require("@aws-sdk/client-dynamodb");
const { DynamoDBDocumentClient, GetCommand, QueryCommand } = require("@aws-sdk/lib-dynamodb");
const config = require('../config/config');
const { sendVerification, validatePassword } = require('../services/utils/authManagementUtils');
const { createAcctSFTPFolder, uploadFile, uploadFileByFieldName, scanFile } = require('../services/utils/fileUtils');
const { restSortToSequelize, PUBLIC_BUNDLE_ID } = require('../services/utils/resourceUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { setupIntegration } = require('./accountIntegrations');
const { withdrawCampaign } = require('../services/utils/campaignUtils');
const { setupAccountRoster } = require('../services/utils/groupUtils');
const { findUserIdByEmailAndAccount, findUsersByAccountId } = require('../services/utils/userUtils');
const { getAccountLanguageStateCountry } = require('../services/utils/accountUtils');
const { ConflictError } = require('../services/utils/errors');
const {
  accountRecordFromHostname,
  isSSOIntegrationType,
  isSCORMIntegrationType,
} = require('../services/utils/accountUtils');
const { isScormAccount } = require('../services/utils/accountUtils');
const { genCSV } = require('../services/utils/reportingUtils');
const { endOfYear, startOfYear, startOfDay, endOfDay } = require('date-fns');
const userUtils = require('../services/utils/userUtils');
const moment = require('moment');

const { hash } = config.encryption;

const Users = db.users;
const Op = db.Sequelize.Op;

// Enums
const AUTH_FIELD_EMAIL = 'email';
const AUTH_FIELD_EMPLOYEED_ID = 'employeeId';

const validateAcctSubdomain = (req) => {
  const re = new RegExp(/^[a-z0-9-]+$/i); // alphanumeric and dash only to avoid TLS cert validation issue in iOS 14
  const validSubdomain = req.body.subdomain && re.test(req.body.subdomain);
  if (!validSubdomain) {
    throw new Error(req.i18n.t('accounts.invalid_subdomain_Error'));
  }
  return null;
};

const isLocalTestMode = () => {
  return (process.env.NODE_ENV === 'test' && (!config.s3.secretAccessKey || !config.s3.accessKeyId));
};

// Initialize DynamoDB Client
const ddbClient = new DynamoDBClient({
  region: "us-east-1",
  credentials: {
    accessKeyId: config.s3.accessKeyId,
    secretAccessKey: config.s3.secretAccessKey,
  },
});

const ddbDocumentClient = DynamoDBDocumentClient.from(ddbClient);

const licenseAggregatedInfo = async (accountId, period) => {
  const tableName = 'licenseAggregatedInfo';

  let result = {};
  try {
    const params = {
      Key: {
        accountId: parseInt(accountId),
        period: period || 3,
      },
      TableName: tableName,
    };
    result = await ddbDocumentClient.send(new GetCommand(params));
    result = result.Item;
  } catch (error) {
    logger.error(error);
  }
  return result;
};

const licenseAcitivityInfo = async (accountId, query) => {
  const tableName = 'licenseInfo';
  let result;
  let data = [];
  try {
    const params = {
      KeyConditionExpression: 'accountId = :accountId AND begins_with (id , :id)',
      ExpressionAttributeValues: {
        ':accountId': parseInt(accountId),
        ':id': `${query.period}-`,
      },
      ProjectionExpression: 'userId, scormId, firstName, lastName, email, firstLesson, dateFirstActivity',
      TableName: tableName,
    };

    result = await ddbDocumentClient.send(new QueryCommand(params));
    data = result.Items;
    while (result.LastEvaluatedKey) {
      params.ExclusiveStartKey = result.LastEvaluatedKey;
      result = await ddbDocumentClient.send(new QueryCommand(params));
      data = data.concat(result.Items);
    }
  } catch (error) {
    logger.error(error);
  }
  return data;
};

const restQueryToSequelize = (query, defaults, customSort = 0) => {
  const accountsPaginateMax = 700;

  const unpaginatedQuery = restOperatorsToSequelize(omit(query, ['$limit', '$skip', '$sort']), true);

  let newQuery = {};
  if (query) {
    newQuery = { ...defaults, ...unpaginatedQuery };
    // override defaults for limit, skip, sort if passed in by caller
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(accountsPaginateMax, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (customSort === 1) {
      // order a-z,0-9 for lesson accounts, program accounts pages
      const orderSql = 'IF(`accounts`.`name` RLIKE \'^[a-z]\', 1, 2), `accounts`.`name`';
      newQuery.order = db.Sequelize.literal(orderSql);
    } else if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
    newQuery.where = unpaginatedQuery;
  }
  return [newQuery, { where: unpaginatedQuery }];
};

const handleUserRoles = async (req, account, user) => {
  // find the user roles we'll apply later
  const userRoles = await db.roles.findAll({
    where: {
      [Op.or]: [{ name: 'user' }, { name: 'accountAdmin' }],
    },
  });

  if (userRoles.length !== 2) {
    throw new Error(req.i18n.t('users.unexpected_role_length_Error'));
  }

  // finish setting up the user roles and associations
  const promises = userRoles.map(async (userRole) => {
    if (userRole.name === 'user') {
      // add the user role
      return user.addRole(userRole);
    } else if (userRole.name === 'accountAdmin') {
      // add the account user role
      return account.addUser(user, { through: { roleId: userRole.id } });
    }
    throw new Error(req.i18n.t('users.unexpected_role_Error'));
  });
  return Promise.all(promises);
};

const setupAccountBundles = async (account) => {
  // create account's resource bundle
  const newBundle = await db.bundles.create({
    name: `${account.name} resources`,
  });

  // create account binding to account's resource bundle
  await db.accountBundles.create({
    bundleId: newBundle.id,
    accountId: account.id,
  });

  // create account binding to public bundle
  await db.accountBundles.create({
    bundleId: PUBLIC_BUNDLE_ID,
    accountId: account.id,
  });
};

const setupLicenseData = async (account) => {
  if (!account.licenseInfo && account.salesforceId) {
    return db.licenseInfo.create({
      accountId: account.id,
      salesforceId: account.salesforceId,
    });
  }
  return Promise.resolve(account.licenseInfo);
};

const deactivateAccount = async (account) => {
  const accountCampaigns = await db.campaigns.findAll({
    where: {
      accountId: account.id,
      status: {
        [Op.or]: ['scheduled', 'inProgress', 'closed'],
      },
    },
  });

  for (const campaign of accountCampaigns) {
    await withdrawCampaign(campaign);
    await campaign.update({ status: 'withdrawn', lifecycle: 'close', endDate: Date.now() });
  }
  return Promise.resolve();
};

const validateEmployeeIds = async ({ accountId }) => {
  const invalidEmployees = await findUsersByAccountId({
    accountId,
    where: {
      [Op.or]: [{ employeeId: null }, { employeeId: '' }],
    },
  });

  if (invalidEmployees.length) {
    // eslint-disable-next-line max-len
    throw new ConflictError('Cannot change login configuration to employee id because not all users have data to support it.');
  }
};

const validateEmployeeEmails = async ({ accountId }) => {
  const accountUsers = await findUsersByAccountId({ accountId });

  const emailHash = {};
  const invalidEmails = accountUsers.reduce((prev, { user: { email } }) => {
    /* eslint-disable no-param-reassign */
    if (emailHash[email]) { prev = prev.concat(email); }
    if (!email || email === '') { prev = prev.concat(email); }

    emailHash[email] = true;
    return prev;
  }, []);

  if (invalidEmails.length) {
    throw new
    ConflictError('Cannot change login configuration to email because not all users have data to support it.');
  }
};

const getUpdatedPasswordFields = (req) => {
  const intPasswordFields = ['maxPasswordLength', 'minPasswordLength'];
  const boolPasswordFields = ['upperCaseRequiredInPassword', 'numberRequiredInPassword',
    'specialCharRequiredInPassword'];
  const passwordFields = [...intPasswordFields, ...boolPasswordFields];

  return passwordFields.reduce((acc, field) => {
    if (req.body[field]) {
      const val = intPasswordFields.includes(field) ? parseInt(req.body[field]) : req.body[field] === 'true';
      return { ...acc, [field]: val };
    }
    return acc;
  }, {});
};

const updateCustomAccountFields = async (accountId, ids) => {
  if(!ids){
    return;
  }
  await db.accountFields.update({includeInSelfSignup: true},
    {
    where: {
      accountId,
      isStandard: false,
      id:  {
        [Op.in]: ids,
      },
    },
  });

  await db.accountFields.update({includeInSelfSignup: false},
    {
    where: {
      accountId,
      isStandard: false,
      id:  {
        [Op.notIn]: ids,
      },
    },
  });
}

const updateStandardAccountFields = async (accountId, names) => {
  let currentStandardFields = []
  let existingSortOrders = [];
  const allExistingAccountFields = await db.accountFields.findAll({
    where: {
      accountId,
    },
  });

  if(allExistingAccountFields && allExistingAccountFields.length > 0) {
    currentStandardFields = allExistingAccountFields.filter(accountField => accountField.isStandard === true);
    existingSortOrders = allExistingAccountFields.map(accountField => accountField.sortOrder);
  }
  let newSortOrderStartAt = existingSortOrders && existingSortOrders.length > 0 ? Math.max(...existingSortOrders) : 0;

  // get list of currently set standard fields and this list length
  const existingStandardFieldsSet = currentStandardFields?.filter(currentStandardField => 
    currentStandardField.includeInSelfSignup === true);
  const anyItemsToUpdate = existingStandardFieldsSet?.length > 0;

  // no-op if we are sent no names and no currentStandardFields exist or all existing standard fields are already not included in signup
  if(names?.length === 0 && !anyItemsToUpdate) return;

  // clear all selections for include in siginup here
  if(names?.length === 0 && anyItemsToUpdate) {
    const idsToClear = existingStandardFieldsSet?.map(field => field.id);

    await db.accountFields.update({includeInSelfSignup: false},
      {
      where: {
        accountId,
        isStandard: true,
        id:  {
          [Op.in]: idsToClear,
        },
      },
    });
  }

  // We received names from the input and these need to be set to be included in signup
  if(names?.length > 0){
    // If we receive any standard field names that don't already exist for this account, we need to create them
    const currentStandardFieldNames = currentStandardFields?.map(field => field.fieldName);
    const standardFieldsToAdd = names.filter(nameToAdd => !currentStandardFieldNames?.includes(nameToAdd))
    const standardFieldsToSet = names.filter(nameToSet => currentStandardFieldNames?.includes(nameToSet))
    const standardFieldsToUnset = currentStandardFieldNames?.filter(nameToUnset => !names.includes(nameToUnset))

    // Create the missing standard fields
    if(standardFieldsToAdd && standardFieldsToAdd?.length > 0) {
      const createdPromises = standardFieldsToAdd?.map(fieldToAdd => {
        newSortOrderStartAt += 1;
        return db.accountFields.create({
          fieldName: fieldToAdd,
          fieldType: 'string',
          accountId,
          userEditable: true,
          includeInSelfSignup: true,
          isStandard: true,
          sortOrder: newSortOrderStartAt
        })
      })
      await Promise.all(createdPromises);
    }

    // Update the existing standard fields now (set to include)
    if(standardFieldsToSet && standardFieldsToSet.length > 0) {
        await db.accountFields.update(
          {includeInSelfSignup: true},
          {
          where: {
            accountId,
            isStandard: true,
            fieldName:  {
              [Op.in]: standardFieldsToSet,
            },
          },
        })
    }

    // Update the existing standard fields now (unset to not include)
    if(standardFieldsToUnset && standardFieldsToUnset.length > 0) {
        await db.accountFields.update(
          {includeInSelfSignup: false},
          {
          where: {
            accountId,
            isStandard: true,
            fieldName:  {
              [Op.in]: standardFieldsToUnset,
            },
          },
        })
    }
  }
}

const updateSortOrderAndRequired = async (fields) => {
  if(!fields || fields?.length === 0){
    return;
  }
  try {
    let parsedFields = fields;
    if(typeof(fields) === "string")
    {
      parsedFields = JSON.parse(fields)
    }
    const updatedPromises = parsedFields?.map( (field, index) => {
      return db.accountFields.update(
        { 
          sortOrder: index,
          required: field.required
        },
        {
        where: {
          id: field?.id
        },
      })
    })
    await Promise.all(updatedPromises);
  }
  catch(e) {
    throw new Error(`Error: ${e}`);
  }
}
/**
 * @swagger
 * /accounts?$limit={limit}&$skip={skip}:
 *   get:
 *     summary: Fetch Accounts List
 *     tags: [Account]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - in: query
 *         name: $limit
 *         schema:
 *           type: integer
 *           example: 10
 *         required: true
 *         description: limit (integer) for number of records
 *       - in: query
 *         name: $skip
 *         schema:
 *           type: integer
 *           example: 0
 *         required: true
 *         description: offset or skip position before beginning to return result
 *     responses:
 *       200:
 *         description: Successfully fetched accounts list
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/accounts'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['createdAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };

  let customSort = 0;
  if (req.query && req.query.customSort) {
    delete (req.query.customSort);
    customSort = 1;
  }
  try {
    const [queryParams, unpaginatedQuery] = restQueryToSequelize(req.query, defaults, customSort);
    queryParams.limit = req.query.$limit ? parseInt(req.query.$limit) : queryParams.limit;
    const pagedResult = {
      limit: queryParams.limit,
      skip: queryParams.offset,
    };

    const promises = [];
    // count total in the set
    promises.push(db.accounts.count(unpaginatedQuery));

    queryParams.attributes = [
      'id',
      'name',
      'numberOfLicenses',
      'createdAt',
      'clientAccountOwnerId',
      'minPasswordLength',
      'maxPasswordLength',
      'upperCaseRequiredInPassword',
      'numberRequiredInPassword',
      'specialCharRequiredInPassword',
      'csvImportSyncField',
      'hideOrg',
      'autoCreateUser',
      'selfSignupEntry',
      'status',
      'subdomain',
      'reassignScorm',
      'responseThreshold',
      'videoClosedCaption',
      'analyticsId',
      'welcomeMessageEnabled',
      'insightsReportEnabled'
    ];

    queryParams.include = [{
      model: db.users,
      as: 'clientAccountOwner',
      attributes: [
        'isVerified',
        'email',
      ],
    }, {
      model: db.integrations,
    }, {
      model: db.accountDemographicFields,
      required: false,
    }, {
      model: db.licenseInfo,
      required: false,
    }];

    promises.push(db.accounts.findAll(queryParams));

    const [accountsCount, accountData] = await Promise.all(promises);

    res.json({ total: accountsCount, ...pagedResult, data: accountData });
  } catch (error) {
    next(error);
  }
};

/**
 * @swagger
 * /accounts/{accountId}:
 *   get:
 *     summary: Fetch account information
 *     tags: [Account]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: accountId
 *         in: path
 *         schema:
 *           type: integer
 *         required: true
 *     responses:
 *       200:
 *         description: Successfully fetched account information
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/accounts'
 *               type: object 
 *               properties:
 *                integrations:
 *                  $ref: '#/components/schemas/integrations'
 *                bundles:
 *                  $ref: '#/components/schemas/bundles'
 *                clientAccountOwners:
 *                  $ref: '#/components/schemas/clientAccountOwners'  
 *       404:
 *         description: Not Found
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.read = async (req, res) => {
  const account = req.account.get({ plain: true });
  if (account.defaultPassword) {
    const { decrypt } = config.encryption;
    account.defaultPassword = decrypt(account.defaultPassword);
  }
  let licenseInfo = await licenseAggregatedInfo(account.id);
  if (licenseInfo) {
    const currentDate = moment().tz('America/Los_Angeles').format('YYYY-MM-DD');
    const contractEndDate = moment(licenseInfo.contractEndDate).tz('America/Los_Angeles').format('YYYY-MM-DD');
    if (currentDate > contractEndDate) {
      licenseInfo = await licenseAggregatedInfo(account.id, 4);
    }
  }
  account.licenseInfo = licenseInfo;
  account.analyticsUsers = await userUtils.activeClientAdminUsers(account.id);
  res.json(account);
};

/**
 * @swagger
 * /accounts:
 *  post:
 *    summary: Add New Account
 *    tags: [Account]
 *    security:
 *      - JWT: []
 *    consumes:
 *      - multipart/form-data
 *    parameters:
 *      - name: name
 *        in: formData
 *        type: string
 *        description: Account Name
 *        required: true
 *      - name: subdomain
 *        in: formData
 *        type: string
 *        description: Subdomain
 *      - name: selfSignup
 *        in: formData
 *        type: boolean
 *        description: Allow Self-Signup
 *      - name: pgpEncryptedImports
 *        in: formData
 *        type: boolean
 *        description: PGP Encrypted CSV Imports
 *      - name: enableAskExpert
 *        in: formData
 *        type: boolean
 *        description: Enable ask expert
 *      - name: enableMachineLanguages
 *        in: formData
 *        type: boolean
 *        description: Enable machine languages
 *      - name: hideOrg
 *        in: formData
 *        type: boolean
 *        description: Exclude Org Data
 *      - name: enableAccountDemographicsFeature
 *        in: formData
 *        type: boolean
 *        description: Enable Demographic Data Collection
 *      - name: authField
 *        in: formData
 *        type: string
 *        enum: [email, employeeId, emailOrEmployeeId]
 *        description: Login field
 *      - name: signupCode
 *        in: formData
 *        type: string
 *        description: Optional Self-Signup Code
 *      - name: selfSignupEntry
 *        in: formData
 *        type: string
 *        enum: [signup, signin]
 *        description: Self Signup Entry Point
 *      - name: numberOfLicenses
 *        in: formData
 *        type: integer
 *        description: Number of Licenses
 *      - name: quickbooksId
 *        in: formData
 *        type: string
 *        description: Quickbooks ID
 *      - name: salesforceId
 *        in: formData
 *        type: string
 *        description: Salesforce ID
 *      - name: responseThreshold
 *        in: formData
 *        type: integer
 *        description: Response threshold (default 5)
 *
 *      # Client Account Owner (User 1)
 *      - name: users[1][firstName]
 *        in: formData
 *        type: string
 *        description: Client Account Owner First Name
 *      - name: users[1][lastName]
 *        in: formData
 *        type: string
 *        description: Client Account Owner Last Name
 *      - name: users[1][email]
 *        in: formData
 *        type: string
 *        description: Client Account Owner Email
 *      - name: users[1][employeeId]
 *        in: formData
 *        type: string
 *        description: Client Account Owner Employee ID
 *      - name: users[1][password]
 *        in: formData
 *        type: string
 *        description: Client Account Password
 *        required: true
 *
 *      - name: accountType
 *        in: formData
 *        type: string
 *        enum: [customer, internal]
 *        description: Account Type
 *      - name: apiKey
 *        in: formData
 *        type: string
 *        description: API Key
 *      - name: reassignScorm
 *        in: formData
 *        type: boolean
 *        description: Reassign Scorm
 *      - name: skipVerification
 *        in: formData
 *        type: boolean
 *        description: Skip email verification for Emtrain Admin
 *      - name: skipVerificationClientAccountOwner
 *        in: formData
 *        type: boolean
 *        description: Skip email verification for Client Account Owner
 *      - name: minPasswordLength
 *        in: formData
 *        type: integer
 *        description: Minimum Password Length (Min 5)
 *      - name: maxPasswordLength
 *        in: formData
 *        type: integer
 *        description: Maximum Password Length (Max 250)
 *      - name: upperCaseRequiredInPassword
 *        in: formData
 *        type: boolean
 *        description: Require uppercase letter in password
 *      - name: numberRequiredInPassword
 *        in: formData
 *        type: boolean
 *        description: Require number in password
 *      - name: specialCharRequiredInPassword
 *        in: formData
 *        type: boolean
 *        description: Require special character in password
 *      - name: file
 *        in: formData
 *
 *    responses:
 *       200:
 *         description: Successfully fetched account information
 *         content:
 *           application/json:
 *             schema:
 *              $ref: '#/components/schemas/accounts'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

module.exports.create = async (req, res, next) => {
  delete req.body.skipVerification;
  const skipVerificationClientAccountOwner = req.body.skipVerificationClientAccountOwner === 'true';
  delete req.body.skipVerificationClientAccountOwner;
  delete req.body.apiKey; // we generate this

  const clientAccountOwnerToCreate = get(req.body, 'users[0]');

  if (req.body.integrations) {
    // eslint-disable-next-line no-param-reassign
    req.body.integrations.forEach((integration) => {
      if (isSCORMIntegrationType(integration.integrationType)) {
        req.body.csvImportSyncField = 'scormId';
      }
      delete integration.integrationKey;
    });
    if (req.body.csvImportSyncField === 'scormId') {
      req.body.reassignScorm = true;
    }
  }

  if (skipVerificationClientAccountOwner && clientAccountOwnerToCreate) {
    clientAccountOwnerToCreate.isVerified = true;
  }

  let newAccount;
  try {
    validateAcctSubdomain(req);
    // av scan before starting transaction
    if (req.file) {
      await scanFile(req.app, req.file);
    }

    if (req.body.defaultPassword) {
      const pwFields = getUpdatedPasswordFields(req);
      await validatePassword(req, null, { ...req.body, ...pwFields }, req.body.defaultPassword);
    }

    const clientAccountOwnerEmail = get(clientAccountOwnerToCreate, 'email');
    if (req.body.accountType && req.body.accountType === 'customer') {
      if (!clientAccountOwnerToCreate) {
        throw new Error(req.i18n.t('accounts.client_account_owner_required'));
      } else if (!clientAccountOwnerEmail) {
        throw new Error(req.i18n.t('accounts.client_account_owner_email_required'));
      }
    }

    const clientOwnerEmployeeId = get(clientAccountOwnerToCreate, 'employeeId');

    let clientAccountOwner = null;
    const accountFieldsError = await userUtils.validateAccountFields(req, clientOwnerEmployeeId);
    if (accountFieldsError && accountFieldsError.length) {
      res.status(400).send({
        message: 'Validation error',
        errors: accountFieldsError,
      });
      throw new Error('Validation error');
    }

    await db.sequelize.transaction(async (transact) => {
      if (clientAccountOwnerToCreate) {
        clientAccountOwner = await db.users.create(clientAccountOwnerToCreate, { transaction: transact });
      }

      newAccount = await db.accounts.create(req.body, {
        include: [
          {
            association: db.accounts.associations.integrations,
          },
        ],
        transaction: transact,
      });
      if (clientAccountOwner) {
        await newAccount.setClientAccountOwner(clientAccountOwner, { transaction: transact });
      }
    });

    const file = await uploadFile(req, `accounts/${newAccount.id}/assets`);
    if (file) {
      req.body.fileId = file.id;
    }

    // do any setup for account integrations
    if (newAccount.integrations) {
      newAccount.integrations.forEach(integration => setupIntegration(req, integration));
    }

    if (newAccount.id && !isLocalTestMode()) {
      await createAcctSFTPFolder(newAccount.id);
    }

    await setupAccountBundles(newAccount);
    const newlicenseData = await setupLicenseData(newAccount);

    if (!skipVerificationClientAccountOwner && clientAccountOwner) {
      sendVerification(clientAccountOwner, true, newAccount.subdomain);
    }
    if (clientAccountOwner) {
      await handleUserRoles(req, newAccount, clientAccountOwner);
    }
    // setup roster after account owners and account integrations are created
    await setupAccountRoster(newAccount);

    delete newAccount.users;

    newAccount = newAccount.get({ plain: true });
    if (newlicenseData) {
      newAccount.licenseInfo = newlicenseData.get({ plain: true });
    }
    if (newAccount.defaultPassword) {
      const { decrypt } = config.encryption;
      newAccount.defaultPassword = decrypt(newAccount.defaultPassword);
    }

    const promise = [];
    if (clientAccountOwner && clientAccountOwner.id) {
      // update emtrainroles for clientAccountOwner
      promise.push(userUtils.updateEmtrainRoles(clientAccountOwner.id));
    }
    await Promise.all(promise);
    res.json(newAccount);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /accounts/{accountId}:
 *  patch:
 *    summary: Update Account
 *    tags: [Account]
 *    security:
 *      - JWT: []
 *    consumes:
 *      - multipart/form-data
 *    parameters:
 *      - in: path
 *        name: accountId
 *        type: integer
 *        required: true
 *        description: Unique identifier for the account
 *      - in: formData
 *        name: name
 *        type: string
 *        required: true
 *        description: Account name
 *      - in: formData
 *        name: subdomain
 *        type: string
 *        description: Subdomain associated with the account
 *      - in: formData
 *        name: selfSignup
 *        type: boolean
 *        description: Allow users to self-signup
 *      - in: formData
 *        name: pgpEncryptedImports
 *        type: boolean
 *        description: Enable PGP encrypted CSV imports
 *      - in: formData
 *        name: enableAskExpert
 *        type: boolean
 *        description: Enable "Ask an Expert" feature
 *      - in: formData
 *        name: enableMachineLanguages
 *        type: boolean
 *        description: Enable machine translation for languages
 *      - in: formData
 *        name: hideOrg
 *        type: boolean
 *        description: Hide organizational data
 *      - in: formData
 *        name: enableAccountDemographicsFeature
 *        type: boolean
 *        description: Enable demographic data collection
 *      - in: formData
 *        name: authField
 *        type: string
 *        enum: [email, employeeId, emailOrEmployeeId]
 *        description: Primary authentication field
 *      - in: formData
 *        name: signupCode
 *        type: string
 *        description: Optional code used during self-signup
 *      - in: formData
 *        name: selfSignupEntry
 *        type: string
 *        enum: [signup, signin]
 *        description: Default entry point for self-signup users
 *      - in: formData
 *        name: numberOfLicenses
 *        type: integer
 *        description: Number of active licenses assigned to account
 *      - in: formData
 *        name: quickbooksId
 *        type: string
 *        description: External QuickBooks integration ID
 *      - in: formData
 *        name: salesforceId
 *        type: string
 *        description: External Salesforce integration ID
 *      - in: formData
 *        name: responseThreshold
 *        type: integer
 *        description: Minimum response threshold (default is 5)
 *      - in: formData
 *        name: accountType
 *        type: string
 *        enum: [customer, internal]
 *        description: Type of account
 *      - in: formData
 *        name: apiKey
 *        type: string
 *        description: API key for external access
 *      - in: formData
 *        name: reassignScorm
 *        type: boolean
 *        description: Reassign SCORM modules
 *      - in: formData
 *        name: skipVerification
 *        type: boolean
 *        description: Skip email verification for admin user
 *      - in: formData
 *        name: skipVerificationClientAccountOwner
 *        type: boolean
 *        description: Skip email verification for client account owner
 *      - in: formData
 *        name: minPasswordLength
 *        type: integer
 *        description: Minimum password length (minimum 5)
 *      - in: formData
 *        name: maxPasswordLength
 *        type: integer
 *        description: Maximum password length (maximum 250)
 *      - in: formData
 *        name: upperCaseRequiredInPassword
 *        type: boolean
 *        description: Require at least one uppercase letter in password
 *      - in: formData
 *        name: numberRequiredInPassword
 *        type: boolean
 *        description: Require at least one number in password
 *      - in: formData
 *        name: specialCharRequiredInPassword
 *        type: boolean
 *        description: Require at least one special character in password
 *
 *    responses:
 *       200:
 *         description: Successfully fetched account information
 *         content:
 *           application/json:
 *             schema:
 *              $ref: '#/components/schemas/accounts'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

module.exports.update = async (req, res, next) => {
  delete req.body.apiKey; // can't touch this
  try {
    const subdomain = req.body && req.body.subdomain;
    if (subdomain !== undefined) {
      validateAcctSubdomain(req);
    }

    let deleteDemographicData = false;
    let addDemographicData = false;
    let accountDemographicField = null;
    if (!isEmpty(req.body) &&
      ('enableAccountDemographicsFeature' in req.body) &&
      (req.body.enableAccountDemographicsFeature === false || req.body.enableAccountDemographicsFeature === 'false')) {
      deleteDemographicData = true;
    }
    if (!isEmpty(req.body) &&
      ('enableDemographicData' in req.body) &&
      (req.body.enableDemographicData === false || req.body.enableDemographicData === 'false')) {
      deleteDemographicData = true;
    } else if (!isEmpty(req.body) && req.body.accountDemographicField) {
      accountDemographicField = {
        accountId: req.account.id,
        dateOfBirth: req.body.accountDemographicField.dateOfBirth,
        gender: req.body.accountDemographicField.gender,
        sexuality: req.body.accountDemographicField.sexuality,
        raceEthnicity: req.body.accountDemographicField.raceEthnicity,
        disabilityStatus: req.body.accountDemographicField.disabilityStatus,
        veteranStatus: req.body.accountDemographicField.veteranStatus,
        customMessage: req.body.accountDemographicField.customMessage,
      };
    } else if (!isEmpty(req.body) &&
      !req.body.accountDemographicField &&
      ('enableDemographicData' in req.body) &&
      (req.body.enableDemographicData === true || req.body.enableDemographicData === 'true')) {
      addDemographicData = true;
    }
    delete req.body.accountDemographicField;

    // If the auth field gets updated we need to make sure all the users can support the change
    const authField = get(req.body, 'authField');
    const authFieldUpdated = authField !== req.account.authField;

    if (authField === AUTH_FIELD_EMPLOYEED_ID && authFieldUpdated) {
      await validateEmployeeIds({ accountId: req.account.id });
    }

    if (authField === AUTH_FIELD_EMAIL && authFieldUpdated) {
      await validateEmployeeEmails({ accountId: req.account.id });
    }

    if (req.body.accountType &&
      req.body.accountType === 'customer' &&
      !req.body.clientAccountOwnerId &&
      !req.body.clientAccountOwnerEmail) {
      const err = new Error(req.i18n.t('accounts.client_account_owner_email_required'));
      err.status = 404;
      throw err;
    }
    const deactivate = req.body.status && req.body.status === 'deactive' && req.account.status === 'active';
    if (!isEmpty(req.body) && req.body.file === 'remove') {
      req.body.fileId = null;
    } else if (req.files && req.files.file) {
      const file = await uploadFileByFieldName(req, 'file', `accounts/${req.account.id}/assets`);
      if (file) {
        req.body.fileId = file.id;
      }
    }
    if (!isEmpty(req.body) && req.body.darkLogoFile === 'remove') {
      req.body.darkLogoFileId = null;
    } else if (req.files && req.files.darkLogoFile) {
      const file = await uploadFileByFieldName(req, 'darkLogoFile', `accounts/${req.account.id}/assets`);
      if (file) {
        req.body.darkLogoFileId = file.id;
      }
    }

    if (req.body.defaultPassword) {
      const pwFields = getUpdatedPasswordFields(req);
      await validatePassword(req, null, { ...req.account.get({ plain: true }), ...pwFields }, req.body.defaultPassword);
    }

    let newClientAccountOwnerId;
    if (req.body.clientAccountOwnerEmail) {
      newClientAccountOwnerId =
        await findUserIdByEmailAndAccount(req, req.body.clientAccountOwnerEmail, req.account.id);
      if (!newClientAccountOwnerId) {
        // new owner doesn't exist as a user in the current account.
        const err =
          new Error(req.i18n.t('users.noUserAccountForEmail_Error', { email: req.body.clientAccountOwnerEmail }));
        err.status = 404;
        throw err;
      }
    }
    if (typeof req.body.selfSignup !== 'undefined') {
      // eslint-disable-next-line max-len
      if (req.body.selfSignup === 'false' || (req.body.selfSignup === 'true' && req.body.signinAccountFieldId === '0')) {
        req.body.signinAccountFieldId = null;
      } else {
        const customAccountFieldIds = req.body.customAccountFieldIds ? req.body.customAccountFieldIds?.split(",") : [];
        const standardAccountFieldNames = req.body.standardAccountFieldNames ? req.body.standardAccountFieldNames?.split(",") : [];
        const fields = req?.body?.signinAccountFields ? req.body.signinAccountFields : [];

        await updateCustomAccountFields(req.account.id, customAccountFieldIds);
        await updateStandardAccountFields(req.account.id, standardAccountFieldNames);
        await updateSortOrderAndRequired(fields);
      }
    }
    if (!req.body.gdprPolicyUrl || req.body.gdprPolicyUrl === '') { req.body.gdprPolicyUrl = null; }

    if (req.body.salesforceId && req.account.licenseInfo) {
      req.account.licenseInfo.salesforceId = req.body.salesforceId;
    }

    req.body.analyticsId = req.body.analyticsId === '' ? null : req.body.analyticsId;
    req.body.responseThreshold = req.body.responseThreshold === '' ? null : req.body.responseThreshold;

    // prevent analyticsId from being erased if not included in the request
    if (req.body.analyticsId === undefined) {
      req.body.analyticsId = req.account.analyticsId;
    }
    req.body.analyticsIdHash = req.body.analyticsId ? hash(req.body.analyticsId) : null;

    // prevent responseThreshold from being erased if not included in the request
    if (req.body.responseThreshold === undefined) {
      req.body.responseThreshold = req.account.responseThreshold;
    }

    const accountFieldsError = await userUtils.validateAccountFields(req);
    if (accountFieldsError && accountFieldsError.length) {
      res.status(400).send({
        message: 'Validation error',
        errors: accountFieldsError,
      });
    }

    let updatedAccount = await req.account.update(req.body);

    if (newClientAccountOwnerId) {
      const oldClientAccountOwnerId = updatedAccount.clientAccountOwnerId;
      if (newClientAccountOwnerId !== oldClientAccountOwnerId) {
        // Set the new client account owner and change their role to accountAdmin
        const newClientAccountOwner = await db.users.findByPk(newClientAccountOwnerId);
        await updatedAccount.setClientAccountOwner(newClientAccountOwner);
        await handleUserRoles(req, updatedAccount, newClientAccountOwner);
        // Reload the account to include the new client account owner
        await updatedAccount.reload();

        // update emptrainroles
        const promise = [];
        promise.push(userUtils.updateEmtrainRoles(newClientAccountOwnerId));
        promise.push(Users.update({ emtrainAdminRole: 'Unrestricted' }, { where: { id: oldClientAccountOwnerId } }));
        await Promise.all(promise);
      } else {
        await db.users.update(
          {
            employeeId: req.body.clientAccountOwnerEmployeeId,
            firstName: req.body.clientAccountOwnerFirstName,
            lastName: req.body.clientAccountOwnerLastName,
          },
          { where: { id: newClientAccountOwnerId } },
        );
      }
    }

    updatedAccount = updatedAccount.get({ plain: true });
    if (updatedAccount.defaultPassword) {
      const { decrypt } = config.encryption;
      updatedAccount.defaultPassword = decrypt(updatedAccount.defaultPassword);
    }

    if (deleteDemographicData) {
      await db.accountDemographicFields.destroy({ where: { accountId: updatedAccount.id } });
    } else if (accountDemographicField || addDemographicData) {
      const existingAccountDemographicField =
        await db.accountDemographicFields.findOne({ where: { accountId: updatedAccount.id } });
      if (existingAccountDemographicField && accountDemographicField) {
        await db.accountDemographicFields.update(
          accountDemographicField,
          { where: { id: existingAccountDemographicField.id } },
        );
      } else if (accountDemographicField) {
        await db.accountDemographicFields.create(accountDemographicField);
      } else if (!existingAccountDemographicField && !accountDemographicField && addDemographicData) {
        await db.accountDemographicFields.create({ accountId: updatedAccount.id });
      }
    }
    // deactivate account
    if (deactivate) {
      await deactivateAccount(req.account);
    }

    res.json(updatedAccount);
  } catch (err) {
    next(err);
  }
};

module.exports.accountById = async (req, res, next, id) => {
  try {
    const account = await db.accounts.findByPk(id, {
      attributes: [
        'id',
        'name',
        'domain',
        'subdomain',
        'signupCode',
        'selfSignup',
        'pgpEncryptedImports',
        'enableAskExpert',
        'enableMachineLanguages',
        'apiKey',
        'authField',
        'defaultPassword',
        'loginInstructions',
        'numberOfLicenses',
        'createdAt',
        'clientAccountOwnerId',
        'minPasswordLength',
        'maxPasswordLength',
        'upperCaseRequiredInPassword',
        'numberRequiredInPassword',
        'specialCharRequiredInPassword',
        'csvImportSyncField',
        'hideOrg',
        'autoCreateUser',
        'selfSignupEntry',
        'status',
        'reassignScorm',
        'quickbooksId',
        'salesforceId',
        'responseThreshold',
        'accountType',
        'enableAccountDemographicsFeature',
        'videoClosedCaption',
        'gdprPolicyUrl',
        'signinAccountFieldId',
        'hasAnalytics',
        'hasSegmentation',
        'licenseLevel',
        'analyticsId',
        'analyticsIdHash',
        'sessionTimeout',
        'isJettClient',
        'welcomeVideoEnabled',
        'welcomeMessageEnabled',
        'insightsReportEnabled',
        'hasCultureSkills',
        'hasHrPeopleRisk',
        'hasBusinessComplianceRisk',
        'reportingAccess',
        'trainingRecommender'
      ],
      include: [
        {
          model: db.files,
          as: 'file',
          attributes: ['path'],
        },
        {
          model: db.files,
          as: 'darkLogoFile',
          attributes: ['path'],
        },
        {
          model: db.users,
          as: 'clientAccountOwner',
          attributes: [
            'email',
            'firstName',
            'lastName',
            'isVerified',
            'employeeId',
          ],
        },
        {
          model: db.integrations,
        },
        {
          model: db.socialLoginConfig,
          attributes: [
            'id',
            'provider',
            'configData',
          ],
        },
        {
          model: db.accountLanguages,
        },
        {
          model: db.accountDemographicFields,
          required: false,
        },
        {
          model: db.bundles,
          attributes: [
            'name',
          ],
        },
        {
          model: db.licenseInfo,
          required: false,
        }],
    });

    if (!account) {
      const err = new Error(req.i18n.t('accounts.load_Error', { id }));
      err.status = 404;
      throw err;
    }

    req.account = account;
    next();
  } catch (err) {
    next(err);
  }
};

/*
 * Returns the current account based only on the subdomain.
 * No security on this, so don't send anything private back.
 */
/**
 * @swagger
 * /accounts/current:
 *  get:
 *    summary: Get Current Account (User Profile)
 *    tags: [Account]
 *    security:
 *      - JWT: []
 *    produces:
 *      - application/json
 *    responses:
 *      200:
 *        description: Successfully fetched account details
 *        schema:
 *          $ref: '#/components/schemas/accounts'
 *      400:
 *        description: Bad Request
 *      401:
 *        description: Unauthorized
 *      500:
 *        description: Internal Server Error
 */
module.exports.current = async (req, res, next) => {
  try {
    const accountRecord = await accountRecordFromHostname(req);
    const accountFields = accountRecord?.accountFields;

    let returnObject = {};
    let googleClientId = null;
    let linkedinClientId = null;
    let microsoftClientId = null;
    let microsoftTenantId = null;
    const workdayCCLEnabled = false;
    if (accountRecord) {
      if (accountRecord.socialLoginConfigs && accountRecord.socialLoginConfigs.length) {
        const googleOauthDetails = accountRecord.socialLoginConfigs.find((obj) => {
          return obj.provider === 'google';
        });
        const linkedinOauthDetails = accountRecord.socialLoginConfigs.find((obj) => {
          return obj.provider === 'linkedin';
        });

        if (googleOauthDetails) {
          googleClientId = googleOauthDetails.configData?.clientId || null;
        }

        if (linkedinOauthDetails) {
          linkedinClientId = linkedinOauthDetails.configData?.clientId || null;
        }

        const microsoftOauthDetails = accountRecord.socialLoginConfigs.find((obj) => {
          return obj.provider === 'microsoft';
        });

        if (microsoftOauthDetails) {
          microsoftClientId = microsoftOauthDetails.configData?.clientId || null;
          microsoftTenantId = microsoftOauthDetails.configData?.tenantId || null;
        }
      }
      returnObject = {
        name: accountRecord.name,
        subdomain: accountRecord.subdomain,
        accountType: accountRecord.accountType,
        selfSignup: accountRecord.selfSignup,
        pgpEncryptedImports: accountRecord.pgpEncryptedImports,
        enableAskExpert: accountRecord.enableAskExpert,
        enableMachineLanguages: accountRecord.enableMachineLanguages,
        minPasswordLength: accountRecord.minPasswordLength,
        maxPasswordLength: accountRecord.maxPasswordLength,
        upperCaseRequiredInPassword: accountRecord.upperCaseRequiredInPassword,
        numberRequiredInPassword: accountRecord.numberRequiredInPassword,
        specialCharRequiredInPassword: accountRecord.specialCharRequiredInPassword,
        csvImportSyncField: accountRecord.csvImportSyncField,
        useCode: accountRecord.signupCode ? true : false, // eslint-disable-line no-unneeded-ternary
        sso: false,
        authField: accountRecord.authField,
        numberOfLicenses: accountRecord.numberOfLicenses,
        hideOrg: accountRecord.hideOrg,
        selfSignupEntry: accountRecord.selfSignupEntry,
        status: accountRecord.status,
        reassignScorm: accountRecord.reassignScorm,
        hasSlackTeam: !!accountRecord.slackTeam,
        loginInstructions: accountRecord.loginInstructions,
        enableAccountDemographicsFeature: accountRecord.enableAccountDemographicsFeature,
        accountDemographicField: accountRecord.accountDemographicField,
        gdprPolicyUrl: accountRecord.gdprPolicyUrl,
        signinAccountFieldId: accountRecord.signinAccountFieldId,
        signinAccountFields: accountFields?.filter(field => field.includeInSelfSignup),
        hasAnalytics: accountRecord.hasAnalytics,
        hasSegmentation: accountRecord.hasSegmentation,
        licenseLevel: accountRecord.licenseLevel,
        analyticsId: accountRecord.analyticsId,
        analyticsIdHash: accountRecord.analyticsIdHash,
        isJettClient: accountRecord.isJettClient,
        welcomeVideoEnabled: accountRecord.welcomeVideoEnabled,
        enforceFullVideoView: accountRecord.enforceFullVideoView,
        launchDarklyKey: config.launchDarkly.key,
        googleClientId,
        linkedinClientId,
        microsoftClientId,
        microsoftTenantId,
        workdayCCLEnabled,
        welcomeMessageEnabled: accountRecord.welcomeMessageEnabled,
        insightsReportEnabled: accountRecord.insightsReportEnabled,
      };
      if (accountRecord.fileId) {
        const logo = await accountRecord.getFile({ attributes: ['path'], raw: true });
        returnObject.customLogoSrc = logo.path;
      }
      if (accountRecord.darkLogoFileId) {
        const darkLogo = await accountRecord.getDarkLogoFile({ attributes: ['path'], raw: true });
        returnObject.customDarkLogoSrc = darkLogo.path;
      }
      if (accountRecord.integrations) {
        const ssoIntegrations = accountRecord.integrations.filter(intg => isSSOIntegrationType(intg.integrationType));
        const integration = ssoIntegrations[0]; // only one for now. this could bite us
        if (integration) {
          returnObject.sso = true;
          returnObject.ssoEntryPoint = integration.entryPoint;
          returnObject.ssoLogoutUrl = integration.ssoLogoutUrl;
        }
        const scormIntegrations =
          accountRecord.integrations.filter(intg => isSCORMIntegrationType(intg.integrationType));
        const scormIntegration = scormIntegrations[0]; // only one scorm integration per account for now
        if (scormIntegration) {
          returnObject.scorm = scormIntegration.integrationType;
        }
        returnObject.sftpDetails = accountRecord.integrations.find((obj) => {
          return obj.integrationType === 'sftp';
        });
        returnObject.aragornDetails = accountRecord.integrations.find((obj) => {
          return obj.integrationType === 'aragorn';
        });

        if (accountRecord.accountLanguage) {
          returnObject.selectedLanguages = accountRecord.accountLanguage.selectedLanguages;
        }

        returnObject.workdayCCLEnabled = accountRecord.integrations.some(obj => obj.integrationType === 'workdayCCL');
      }

      if (req.query && req.query.countryStateLanguage && req.query.countryStateLanguage === 'true') {
        const accountLanguageStateCountry = await getAccountLanguageStateCountry(req.query, accountRecord.id);
        returnObject.countryStateLanguage = accountLanguageStateCountry;
      }
    }
    if (!accountRecord) {
      returnObject.notFoundError = true;
    }
    res.json(returnObject);
  } catch (err) {
    next(err);
  }
};

/*
 * List all of the UserAccount records for a user.
 * Note: This feature should probably be in a userAccounts controller, but checked with Bob.
 * End of sprint, so don't want to create a new controller just for this feature.
 */
/**
 * @swagger
 * /accounts/{userId}/accountRoles:
 *  get:
 *    summary: List User Account Roles
 *    tags: [Account]
 *    security:
 *      - JWT: []
 *    produces:
 *      - application/json
 *    parameters:
 *      - name: userId
 *        in: path
 *        type: integer
 *        required: true
 *        description: ID of the user
 *    responses:
 *      200:
 *        description: Successfully fetched user account roles
 *        schema:
 *          type: array
 *          items:
 *            type: object
 *            properties:
 *              userId:
 *                type: integer
 *              accountId:
 *                type: integer
 *              roleId:
 *                type: integer
 *              createdAt:
 *                type: string
 *                format: date-time
 *              updatedAt:
 *                type: string
 *                format: date-time
 *              account:
 *                type: object
 *                properties:
 *                  id:
 *                    type: integer
 *                  name:
 *                    type: string
 *      400:
 *        description: Bad Request
 *      401:
 *        description: Unauthorized
 *      500:
 *        description: Internal Server Error
 */
module.exports.listUserAccountRoles = async (req, res, next) => {
  try {
    const userId = req.params.userId;

    const accountUsers = await db.accountUsers.findAll({
      where: {
        userId,
      },
      include: [{
        model: db.accounts,
        attributes: ['id', 'name'],
      },
      {
        model: db.roles,
        as: 'role',
        attributes: ['name'],
        required: false,
      }],
    });

    res.json(accountUsers);
  } catch (error) {
    next(error);
  }
};

/*
 * Update the roleId for a user in the userAccounts table.
 * Note: This feature should probably be in a userAccounts controller, but checked with Bob.
 * End of sprint, so don't want to create a new controller just for this feature.
 */
/**
 * @swagger
 * /accounts/{userId}/accountRoles:
 *  post:
 *    summary: Assign User Account Role
 *    tags: [Account]
 *    security:
 *      - JWT: []
 *    produces:
 *      - application/json
 *    parameters:
 *      - name: userId
 *        in: path
 *        type: integer
 *        required: true
 *        description: ID of the user
 *      - in: body
 *        name: body
 *        description: User account roles parameters
 *        required: true
 *        schema:
 *          type: object
 *          required:
 *            - accountId
 *            - roleId
 *          properties:
 *            accountId:
 *              type: integer
 *              example: 1
 *            roleId:
 *              type: integer
 *              example: 2
 *    responses:
 *      200:
 *        description: Successfully fetched user account roles
 *        schema:
 *          type: object
 *          properties:
 *            userId:
 *              type: integer
 *            accountId:
 *              type: integer
 *            roleId:
 *              type: integer
 *            createdAt:
 *              type: string
 *              format: date-time
 *            updatedAt:
 *              type: string
 *              format: date-time
 *            account:
 *              type: object
 *              properties:
 *                id:
 *                  type: integer
 *                name:
 *                  type: string
 *      400:
 *        description: Bad Request
 *      401:
 *        description: Unauthorized
 *      500:
 *        description: Internal Server Error
 */
module.exports.setUserRole = async (req, res, next) => {
  try {
    const accountId = req.body.accountId;
    const userId = req.params.userId;
    const roleId = req.body.roleId;

    await db.accountUsers.update(
      { roleId },
      {
        where: {
          [Op.and]: [
            { userId },
            { accountId },
          ],
        },
      },
    );

    // Get the updated account user rows
    const accountUsers = await db.accountUsers.findAll({
      where: {
        userId,
      },
      include: [{
        model: db.accounts,
        attributes: ['id', 'name'],
      }],
    });
    // update emtrainroles
    await userUtils.updateEmtrainRoles(userId);
    res.json(accountUsers);
  } catch (error) {
    next(error);
  }
};

const generateLicenseReport = async (queryParams, accountId) => {
  try {
    let period = 3;
    const isScorm = await isScormAccount(accountId);
    if (queryParams.period) {
      period = queryParams.period;
    }
    const tableName = 'licenseInfo';
    let result;
    let data = [];

    try {
      const params = {
        KeyConditionExpression: 'accountId = :accountId AND begins_with (id , :id)',
        ExpressionAttributeValues: {
          ':accountId': parseInt(accountId),
          ':id': `${period}-`,
        },
        ProjectionExpression: (isScorm) ? 'userId, scormId, firstName, lastName, firstLesson, dateFirstActivity' :
          'userId, email, firstName, lastName, firstLesson, dateFirstActivity',
        TableName: tableName,
      };
      result = await ddbDocumentClient.send(new QueryCommand(params));
      data = result.Items;
      while (result.LastEvaluatedKey) {
        params.ExclusiveStartKey = result.LastEvaluatedKey;
        result = await ddbDocumentClient.send(new QueryCommand(params));
        data = data.concat(result.Items);
      }
    } catch (error) {
      logger.error(error);
    }

    return {
      data,
      total: data.Count,
      limit: queryParams.limit,
      skip: queryParams.offset,
      isScormAccount: isScorm,
    };
  } catch (err) {
    throw err;
  }
};

/**
 * @swagger
 * /accounts/{accountId}/license-report?$limit={limit}&$skip={skip}&startDate={startDate}&endDate={endDate}:
 *  get:
 *    summary: Fetch licenses consumed report
 *    tags: [Account]
 *    security:
 *      - JWT: []
 *    parameters:
 *      - name: accountId
 *        in: path
 *        required: true
 *        schema:
 *          type: integer
 *          example: 1
 *        description: Account ID
 *      - name: $limit
 *        in: query
 *        required: true
 *        schema:
 *          type: integer
 *          example: 10
 *        description: Limit (number of records)
 *      - name: $skip
 *        in: query
 *        required: true
 *        schema:
 *          type: integer
 *          example: 0
 *        description: Number of records to skip (offset)
 *      - name: startDate
 *        in: query
 *        required: true
 *        schema:
 *          type: string
 *          format: date
 *          example: '2022-01-01'
 *        description: Start date for license report
 *      - name: endDate
 *        in: query
 *        required: true
 *        schema:
 *          type: string
 *          format: date
 *          example: '2022-12-31'
 *        description: End date for license report
 *    produces:
 *      - application/json
 *    responses:
 *      200:
 *        description: Successfully fetched license report
 *        schema:
 *          type: object
 *          properties:
 *            total:
 *              type: integer
 *              description: Total number of records
 *            limit:
 *              type: integer
 *              description: Number of records per page
 *            skip:
 *              type: integer
 *              description: Number of records skipped
 *            data:
 *              type: array
 *              description: Array of licenses consumed report entries
 *              items:
 *                type: object
 *                properties:
 *                  id:
 *                    type: integer
 *                  firstName:
 *                    type: string
 *                  lastName:
 *                    type: string
 *                  email:
 *                    type: string
 *      400:
 *        description: Bad Request
 *      401:
 *        description: Unauthorized
 *      404:
 *        description: Not Found
 *      5XX:
 *        description: Unexpected error
 */
module.exports.getLicenseReport = async (req, res, next) => {
  const account = req.account;
  const defaults = {
    limit: config.paginate.default,
    offset: 0,
  };

  try {
    const isScorm = await isScormAccount(account.id);
    const [queryParams] = restQueryToSequelize(req.query, defaults);
    const reportData = await licenseAcitivityInfo(account.id, req.query);
    res.json({
      limit: queryParams.limit,
      skip: queryParams.offset,
      isScormAccount: isScorm,
      total: reportData.length,
      data: reportData,
    });
  } catch (err) {
    logger.error(`Exception caught in getLicenseReport() for account: ${account.id}, err= ${err}`);
    next(err);
  }
};

module.exports.generateCSV = async (req, res, next) => {
  const account = req.account;
  try {
    const isScorm = await isScormAccount(account.id);
    const reportColumns = isScorm ?
      ['scormId', 'userId', 'firstName', 'lastName', 'firstLesson', 'dateFirstActivity'] :
      ['userId', 'firstName', 'lastName', 'email', 'firstLesson', 'dateFirstActivity'];
    const filename = 'licensesConsumedReport';
    const timeZone = req.query.timeZone;

    // user can't specify limit or skip for the csv.
    delete req.query.$limit;
    delete req.query.$skip;
    delete req.query.attributes;

    const queryParams = req.query;
    genCSV(reportColumns, queryParams, filename, res, generateLicenseReport, timeZone)(account.id, queryParams);
  } catch (err) {
    logger.error(`Exception caught in generateCSV() for account: ${account.id}, err= ${err}`);
    next(err);
  }
};

/**
 * @swagger
 * /accounts/{accountId}/automationSwitchJettClient:
 *  patch:
 *    summary: Update isJettClient
 *    tags: [Account]
 *    security:
 *      - JWT: []
 *    parameters:
 *      - in: path
 *        name: accountId
 *        required: true
 *        type: integer
 *        description: Account ID
 *      - in: body
 *        name: body
 *        description: isJettClient field to update
 *        required: true
 *        schema:
 *          type: object
 *          properties:
 *            isJettClient:
 *              type: boolean
 *              description: isJettClient field
 *    responses:
 *      200:
 *        description: Successfully updated account information
 *        schema:
 *          $ref: '#/components/schemas/accounts'
 *      404:
 *        description: Not Found
 *      401:
 *        description: Unauthorized
 *      400:
 *        description: Bad Request
 *      5XX:
 *        description: Unexpected error
 */
module.exports.automationSwitchJettClient = async (req, res, next) => {
  try {
    if (req.body.isJettClient === undefined || typeof req.body.isJettClient !== 'boolean') {
      const error = new Error('Provide valid isJettClient');
      error.status = 400;
      throw error;
    }
    await req.account.update({ isJettClient: req.body.isJettClient });
    res.json('OK');
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /accounts/{accountId}/updateAnalyticsPermissions:
 *  post:
 *    summary: Update Analytics Permissions
 *    tags: [Account]
 *    security:
 *      - JWT: []
 *    parameters:
 *      - name: accountId
 *        in: path
 *        required: true
 *        type: integer
 *        description: Account ID
 *      - in: body
 *        name: body
 *        description: User analytics roles parameters
 *        required: true
 *        schema:
 *          type: array
 *          items:
 *            type: object
 *            properties:
 *              userId:
 *                type: integer
 *                example: 1
 *              hrPersonnelRisk:
 *                type: boolean
 *                example: false
 *              businessComplianceRisk:
 *                type: boolean
 *                example: true
 *    produces:
 *      - application/json
 *    responses:
 *      200:
 *        description: Successfully updated analytics permissions
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              properties:
 *                success:
 *                  type: boolean
 *      401:
 *        description: Unauthorized
 *      400:
 *        description: Bad Request
 *      500:
 *        description: Internal Server Error
 */
module.exports.updateUserRiskPermissions = async (req, res, next) => {
  try {
    const reqData = req.body;
    const accountId = req.params.accountId;
    const validateUsers = await userUtils.validateAccountUsers(reqData);
    if (reqData.length === 0 || !validateUsers) {
      const err = new Error(req.i18n.t('system.invalid_parameter_Error'));
      err.status = 400;
      throw err;
    }

    const { hrPersonnelRiskId, businessComplianceRiskId } = await userUtils.analyticsUserRoleData();
    const originalUserRoleData = await userUtils.getOriginalRoleData(reqData);
    await userUtils.updateUserAnalyticsRoles(reqData, hrPersonnelRiskId, businessComplianceRiskId);
    await userUtils.createRiskAnalyticsAuditLogs(reqData, hrPersonnelRiskId, businessComplianceRiskId, req.user, accountId, originalUserRoleData);

    res.json({ success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * components:
 *  schemas:
 *    accounts:
 *      type: object
 *      properties:
 *        id:
 *          type: integer
 *        name:
 *          type: string
 *        createdAt:
 *          type: string
 *        updatedAt:
 *          type: string
 *        subdomain:
 *          type: string
 *        domain:
 *          type: string
 *        deletedAt:
 *          type: string
 *        accountType:
 *          type: string
 *        selfSignup:
 *          type: string
 *        fileId:
 *          type: integer
 *        signupCode:
 *          type: string
 *        apiKey:
 *          type: string
 *        authField:
 *          type: string
 *        minPasswordLength:
 *          type: integer
 *        maxPasswordLength:
 *          type: integer
 *        upperCaseRequiredInPassword:
 *          type: boolean
 *        numberRequiredInPassword:
 *          type: boolean
 *        specialCharRequiredInPassword:
 *          type: boolean
 *        numberOfLicenses:
 *          type: integer
 *        pgpEncryptedImports:
 *          type: boolean
 *        enableAskExpert:
 *          type: boolean
 *        csvImportSyncField:
 *          type: string
 *        hideOrg:
 *          type: boolean
 *        autoCreateUser:
 *          type: boolean
 *        selfSignupEntry:
 *          type: string
 *        reassignScorm:
 *          type: boolean
 *        status:
 *          type: string
 *        enableMachineLanguages:
 *          type: boolean
 *        quickbooksId:
 *          type: boolean
 *        salesforceId:
 *          type: string
 *        responseThreshold:
 *          type: integer
 *        clientAccountOwnerId:
 *          type: integer
 *        defaultPassword:
 *          type: string
 *        loginInstructions:
 *          type: string
 *        enableAccountDemographicsFeature:
 *          type: boolean
 *        bundles:
 *          type: array
 *        integrations:
 *          type: array
 *
 *    bundles:
 *      type: object
 *      properties:
 *        id:
 *          type: integer
 *        name:
 *          type: string
 *        createdAt:
 *          type: string
 *        updatedAt:
 *          type: string
 *
 *    accountBundles:
 *      type: object
 *      properties:
 *        id:
 *          type: integer
 *        bundleId:
 *          type: integer
 *        accountId:
 *          type: integer
 *        createdAt:
 *          type: string
 *        updatedAt:
 *          type: string
 *
 *    accountUsers:
 *      type: object
 *      properties:
 *        id:
 *          type: integer
 *        userId:
 *          type: integer
 *        accountId:
 *          type: integer
 *        roleId:
 *          type: integer
 *        isVerified:
 *          type: boolean
 *        email:
 *          type: string
 *        createdAt:
 *          type: string
 *        updatedAt:
 *          type: string
 *
 *    clientAccountOwners:
 *      type: object
 *      properties:
 *        isVerified:
 *          type: boolean
 *        email:
 *          type: string
 *
 *    integrations:
 *      type: object
 *      properties:
 *        id:
 *          type: integer
 *        accountId:
 *          type: integer
 *        cert:
 *          type: string
 *        entryPoint:
 *          type: string
 *        integrationType:
 *          type: string
 *        integrationKey:
 *          type: string
 *        createdAt:
 *          type: string
 *        updatedAt:
 *          type: string
 *        deletedAt:
 *          type: string
 * 
 *    accountLanguages:
 *      type: object
 *      properties:
 *        code:
 *          type: string
 *        name:
 *          type: string
 */