/* eslint-disable no-param-reassign */
// eslint-disable-next-line no-unused-vars
const logger = require('../logger');
const db = require('../db');
const { Sequelize } = require('sequelize');

// create connection to analytics db
const analyticsConn = db.sequelizeAnalyticsDb;

const getRiskCatalogItems = async(riskAreaIds) => {
  const catalogTitleList = await analyticsConn.query(
    `SELECT * FROM riskAreaContentMap WHERE riskAreaId IN (:riskAreaIds)`,
    {
      replacements: { riskAreaIds },
      type: Sequelize.QueryTypes.SELECT,
    },
  );
  const catalogTitleMap = new Map();
  for (const item of catalogTitleList) {
    if (!catalogTitleMap.has(item.riskAreaId)) {
      catalogTitleMap.set(item.riskAreaId, []);
    }
    if (item.catalogList) {
      catalogTitleMap.get(item.riskAreaId).push(item.catalogList);
    }
  }
  return catalogTitleMap;
};

const getPreviousPeriodString = (PeriodId, periodCategory, periodSubCategory) => {
  console.log(periodSubCategory)
  let pDateString = '';
  if (PeriodId && periodCategory === 'Calendar Year') {
    const options = { month: 'short', day: '2-digit', year: 'numeric' };
    const format = (date) => date.toLocaleDateString('en-US', options);
    pDateString = `${format(new Date(`${new Date().getFullYear() - parseInt(PeriodId)}-01-01`))}–${format(
        new Date(`${new Date().getFullYear() - parseInt(PeriodId)}-12-31`),
      )}`;
  } else if (PeriodId && periodSubCategory && periodCategory === '6 Months') {
    const currentYear = parseInt(periodSubCategory);
    const prevYear = currentYear - 1; 
    const startDate = new Date(prevYear, 0, 1);
    const endDate = new Date(prevYear, 11, 31); 
    const options = { month: 'short', day: '2-digit', year: 'numeric' };

    const startStr = startDate.toLocaleDateString('en-US', options);
    const endStr = endDate.toLocaleDateString('en-US', options);
    pDateString = `${startStr}–${endStr}`;
  }
   return pDateString;
};

/**
 * @openapi
 * /analytics/riskAreas:
 *   get:
 *     summary: Fetch risk areas
 *     tags:
 *       - Analytics
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: total number of records.
 *                 data:
 *                   type: array
 *                   description: Array of report data object.
 *                   items:
 *                     type: object
 *                     properties:
 *                       dateOfActivities:
 *                         type: integer
 *                       activityCount:
 *                         type: integer
 */
module.exports.riskAreas = async (req, res) => {
  let result = {};
  const analyticsId = req.query.analyticsId;
  try {
    const riskResult = await analyticsConn.query(
      'SELECT * FROM masterRiskData;',
      {
        type: Sequelize.QueryTypes.SELECT,
      },
    );

    const flagHasTenureData = await analyticsConn.query(
      'SELECT id FROM riskScoresClient WHERE analyticsId = :analyticsId AND flagHasTenureData=1 limit 1;',
      {
        replacements: { analyticsId },
        type: Sequelize.QueryTypes.SELECT,
      },
    );
    result = { ...result, riskAreas: riskResult, flagHasTenureData: flagHasTenureData.length > 0 };
  } catch (error) {
    logger.error(error);
  }
  res.json({ total: result.length, data: result });
};

// Helper to determine sorting based on sort criteria
const getSortConditions = (context, benchmarkAgainst) => {
  switch (context) {
    case 'Risk Level':
      return [
        ['riskScoresClient.sortRiskLevel', 'ASC'],
        ['riskScoresClient.sortPercentageComparison', 'ASC'],
      ];
    case 'Healthy Responses':
      if (benchmarkAgainst === 'orgsSimilarSize') {
        return [
          ['riskScoresClientBySize.sortPercentageHealthy', 'ASC'],
          ['riskScoresClientBySize.sortPercentageComparisonClientSize', 'ASC'],
        ];
      }
      if (benchmarkAgainst === 'oneYearPrior') {
        return [
          ['riskScoresClientByLongitudinal.sortPercentageHealthy', 'ASC'],
          ['riskScoresClientByLongitudinal.sortPercentageComparisonLongitudinal', 'ASC'],
        ];
      }
      return [
        ['riskScoresClient.sortPercentageHealthy', 'ASC'],
        ['riskScoresClient.totalRespondents', 'ASC'],
      ];
    case 'Industry Comparison':
      if (benchmarkAgainst === 'orgsSimilarSize') {
        return [
          ['riskScoresClientBySize.sortPercentageComparisonClientSize', 'ASC'],
          ['riskScoresClientBySize.totalRespondents', 'ASC'],
        ];
      }
      if (benchmarkAgainst === 'oneYearPrior') {
        return [
          ['riskScoresClientByLongitudinal.sortPercentageComparisonLongitudinal', 'ASC'],
          ['riskScoresClientByLongitudinal.totalRespondents', 'ASC'],
        ];
      }
      return [
        ['riskScoresClient.sortPercentageComparison', 'ASC'],
        ['riskScoresClient.totalRespondents', 'ASC'],
      ];

    case 'Skill Area':
      if (benchmarkAgainst === 'orgsSimilarSize') {
        return [
          ['riskScoresClientBySize.sortPercentageComparisonClientSize', 'ASC'],
          ['riskScoresClientBySize.totalRespondents', 'ASC'],
        ];
      }
      if (benchmarkAgainst === 'oneYearPrior') {
        return [
          ['riskScoresClientByLongitudinal.sortPercentageComparisonLongitudinal', 'ASC'],
          ['riskScoresClientByLongitudinal.totalRespondents', 'ASC'],
        ];
      }
      return [
        ['riskScoresClient.sortRiskLevel', 'ASC'],
        ['riskScoresClient.sortPercentageComparison', 'ASC'],
      ];
    default:
      throw new Error('Invalid context for sorting');
  }
};
const safeMapGet = (map, key, fallback = [])  => {
  return map.get(key) ??
         map.get(String(key)) ??
         map.get(Number(key)) ??
         fallback;
}

/**
 * @openapi
 * /analytics/riskDetails:
 *   post:
 *     summary: Fetch risk details
 *     tags:
 *       - Analytics
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: total number of records.
 *                 data:
 *                   type: array
 *                   description: Array of report data object.
 *                   items:
 *                     type: object
 *                     properties:
 *                       dateOfActivities:
 *                         type: integer
 *                       activityCount:
 *                         type: integer
 */
// eslint-disable-next-line consistent-return
module.exports.riskDetails = async (req, res, next) => {
  let riskData = [];
  let catalogResult = [];
  let catalogList = '';
  const analyticsId = req.body.analyticsId;
  const riskId = req.body.riskId || '1';
  const minResponseRate = req.body.minResponseRate;
  const includeNewHires = req.body?.includeNewHires ? 'All' : 'Existing';
  const periodCategory = req.body?.periodCategory || '6 Months';
  const periodId = req.body?.periodId;
  const benchmarkAgainst = req.body.benchmarkAgainst || 'industry';
  const sortConditions = getSortConditions(req.body.sortCriteria, benchmarkAgainst);
  const orderClause = riskId === '1' ? 'ORDER BY questionInfo.skillAreaId ASC' : '';
  const isDefaultPeriod = req.body.isDefaultPeriod || false;
  let isDeployed = true;
  let periodString = null;

  try {
    // fetch catalogTitleList based on riskArea
    const catalogTitleList = await analyticsConn.query(
      'SELECT catalogList FROM riskAreaContentMap where riskAreaId= :riskId',
      {
        replacements: { riskId },
        type: Sequelize.QueryTypes.SELECT,
      },
    );
    catalogList = catalogTitleList[0]?.catalogList || '';
    const questionData = await analyticsConn.query(
      // eslint-disable-next-line max-len
      `SELECT questionGroupId,questionGroup,skillArea,skillAreaId,skillAreaDescription FROM questionInfo where analyticsId = :analyticsId and riskAreaId= :riskId ${orderClause};`,
      {
        replacements: { analyticsId, riskId },
        type: Sequelize.QueryTypes.SELECT,
      },
    );

    catalogResult = await analyticsConn.query(
      'SELECT distinct(catalogTitle) FROM clientQuestionContentMap where analyticsId= :analyticsId and periodId= :periodId and riskAreaId= :riskId order by catalogTitle;',
      {
        replacements: { analyticsId, periodId, riskId },
        type: Sequelize.QueryTypes.SELECT,
      },
    );

    if (questionData.length === 0) {
      isDeployed = false;
      return res.json({ total: questionData.length, data: questionData, deployed: isDeployed, catalogList });
    }
    const questionGroupMap = questionData.reduce((map, qItem) => {
      map[qItem.questionGroupId] = qItem.questionGroup;
      return map;
    }, {});
    const { skillAreaMap, skillAreaIdMap, skillAreaDescMap } = questionData.reduce(
      (acc, sItem) => {
        acc.skillAreaMap[sItem.questionGroupId] = sItem.skillArea;
        acc.skillAreaIdMap[sItem.questionGroupId] = sItem.skillAreaId;
        acc.skillAreaDescMap[sItem.questionGroupId] = sItem.skillAreaDescription;
        return acc;
      },
      { skillAreaMap: {}, skillAreaIdMap: {}, skillAreaDescMap: {} },
    );
    const uniqueQuestionGroupIds = [...new Set(questionData.map(item => item.questionGroupId))];
    const riskScoresTable = benchmarkAgainst === 'oneYearPrior'
      ? 'riskScoresClientByLongitudinal'
      : benchmarkAgainst === 'orgsSimilarSize'
        ? 'riskScoresClientBySize'
        : 'riskScoresClient';

    const periodIdQuery = benchmarkAgainst === 'oneYearPrior'
      ? 'AND previousPeriodId LIKE :previousPeriodId'
      : 'AND periodId LIKE :periodId';

    const queryReplacements = {
      analyticsId,
      uniqueQuestionGroupIds,
      minResponseRate,
      includeNewHires,
      periodCategory,
    };

    if (benchmarkAgainst === 'oneYearPrior') {
      queryReplacements.previousPeriodId = `%${periodId}`;
    } else {
      queryReplacements.periodId = `%${periodId}`;
    }

    if (uniqueQuestionGroupIds.length > 0) {
      if (isDefaultPeriod) {
        // fetching with flagIsDefaultPeriodRiskPersona = true
        riskData = await analyticsConn.query(
          `
        SELECT * 
        FROM ${riskScoresTable}
        WHERE
          analyticsId = :analyticsId
          AND questionGroupId IN (:uniqueQuestionGroupIds)
          AND responseRate >= :minResponseRate
          AND employeeTenure = :includeNewHires
          AND flagIsDefaultPeriodRiskPersona = true
          ORDER BY ${sortConditions.map(([field, order]) => `${field} ${order}`).join(', ')};
        `,
          {
            replacements: {
              analyticsId,
              uniqueQuestionGroupIds,
              minResponseRate,
              includeNewHires,
            },
            type: Sequelize.QueryTypes.SELECT,
          },
        );
      }
      if (!isDefaultPeriod || riskData.length === 0) {
        riskData = await analyticsConn.query(
        `
        SELECT * FROM (
          SELECT CAST(id AS CHAR) AS id_str, ${riskScoresTable}.*
          FROM ${riskScoresTable}
          WHERE
            analyticsId = :analyticsId
            AND questionGroupId IN (:uniqueQuestionGroupIds)
            AND responseRate >= :minResponseRate
            AND employeeTenure = :includeNewHires
            AND periodCategory = :periodCategory
            ${periodIdQuery}
        ) AS sub
        GROUP BY id_str
        ORDER BY ${sortConditions.map(([field, order]) => `${field} ${order}`).join(', ')};
        `,
        {
          replacements: queryReplacements,
          type: Sequelize.QueryTypes.SELECT,
        },
      );
      }
      // Combine the map creation and riskData modification into one map operation
      riskData = riskData.map((item) => {
        // Create the questionGroupMap dynamically as part of the map operation
        // Add questionGroup based on questionGroupId
        item.questionGroup = questionGroupMap[item.questionGroupId] || null;
        item.skillArea = skillAreaMap[item.questionGroupId] || null;
        item.skillAreaId = skillAreaIdMap[item.questionGroupId] || null;
        item.skillAreaDescription = skillAreaDescMap[item.questionGroupId] || null;
 
        if (benchmarkAgainst === 'industry') {
          item.flagIsTrueIndustry = item.flagIsTrueIndustry;
        } else if (benchmarkAgainst === 'orgsSimilarSize') {
          item.flagIsTrueClientSize = item.flagIsTrueClientSize;
        }
        periodString = [item.period, getPreviousPeriodString(item.periodId, item.periodCategory, item.periodSubCategory)];
        return item;
      });
    }
  } catch (error) {
    logger.error(error);
  }
  res.json({ total: riskData.length, benchmarkAgainst, periodString, data: riskData, deployed: isDeployed, catalogList, responses: catalogResult, });
};

/**
 * @swagger
 * /analytics/overview/{analyticsId}:
 *  get:
 *    summary: Fetch overview data based on risk persona
 *    tags:
 *      - Analytics
 *    parameters:
 *      - name: analyticsId
 *        in: path
 *        schema:
 *          type: integer
 *        required: true
 *        description: The analyticsId of an account
 *    security:
 *      - JWT: []
 *    produces:
 *      - application/json
 *    responses:
 *      200:
 *        description: successful operation
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              properties:
 *                data:
 *                  type: array
 *                  description: Array of report data object.
 *                  items:
 *                    type: object
 *                    properties:
 *                      countRiskArea:
 *                        type: integer
 *                      maxResponseRate:
 *                        type: integer
 *                      riskPersona:
 *                        type: string
 */
module.exports.analyticsOverview = async (req, res, next) => {
  const analyticsId = req.params.analyticsId;
  try {
    if (!analyticsId) {
      const err = new Error('Please provide valid analyticsId');
      err.status = 400;
      throw err;
    }
    const promise = [];
    promise.push(analyticsConn.query(
      'SELECT countRiskArea, maxResponseRate, riskPersona FROM overallDataPopulationStats WHERE analyticsId= :analyticsId;',
      {
        replacements: { analyticsId },
        type: Sequelize.QueryTypes.SELECT,
      },
    ));
    promise.push(analyticsConn.query(
      'SELECT * FROM riskDeploymentStats WHERE analyticsId= :analyticsId;',
      {
        replacements: { analyticsId },
        type: Sequelize.QueryTypes.SELECT,
      },
    ));
    const [overview, overviewStats] = await Promise.all(promise);
    res.json({ overview, overviewStats });
  } catch (error) {
    logger.error(error);
    next(error);
  }
};

/**
 * @swagger
 * /analytics/riskSummary:
 *  post:
 *    summary: Fetch risk summary
 *    tags:
 *      - Analytics
 *    security:
 *      - JWT: []
 *    produces:
 *      - application/json
 *    responses:
 *      200:
 *        description: successful operation
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              properties:
 *                total:
 *                  type: integer
 *                  description: total number of records.
 *                data:
 *                  type: array
 *                  description: Array of report data objects.
 *                  items:
 *                    type: object
 *                    properties:
 *                      dateOfActivities:
 *                        type: integer
 *                      activityCount:
 *                        type: integer
 */
module.exports.riskSummary = async (req, res, next) => {
  const analyticsId = req.body.analyticsId;
  const riskPersona = req.body.riskPersona || 'HR';
  const includeNewHires = req.body?.includeNewHires ? 'All' : 'Existing';
  const periodId = req.body?.periodId;
  const isDefaultPeriod = req.body.isDefaultPeriod || false;

  try {
    // fetch riskAreas & riskAreaIds based on riskPersona
    const riskResult = await analyticsConn.query(
      'SELECT * FROM masterRiskData WHERE riskPersona=:riskPersona;',
      {
        type: Sequelize.QueryTypes.SELECT,
        replacements: { riskPersona },
      },
    );
    const riskAreaIds = [];
    const riskAreaMap = new Map();
    for (const item of riskResult) {
      const id = item?.riskAreaId;
      if (id != null && !riskAreaMap.has(id)) {
        riskAreaIds.push(id);
        riskAreaMap.set(id, item.riskArea);
      }
    }

    // fetch catalogTitleList based on riskArea
    const catalogTitleList = await analyticsConn.query(
      `SELECT riskAreaId,catalogTitleBase FROM riskSummaryClientContentMap 
      WHERE analyticsId=:analyticsId and periodId=:periodId and riskAreaId IN (:riskAreaIds)
      GROUP BY riskAreaId, catalogTitleBase;`,
      {
        replacements: { analyticsId, periodId, riskAreaIds },
        type: Sequelize.QueryTypes.SELECT,
      },
    );
    const catalogTitleMap = new Map();
    for (const item of catalogTitleList) {
      if (!catalogTitleMap.has(item.riskAreaId)) {
        catalogTitleMap.set(item.riskAreaId, []);
      }
      if (item.catalogTitleBase) {
        catalogTitleMap.get(item.riskAreaId).push(item.catalogTitleBase);
      }
    }

    const promise = [];
    // fetch summary risk data
    const allAreasSql = riskAreaIds
      .map((id, idx) => `SELECT ${id} AS riskAreaId${idx === 0 ? '' : ''}`)
      .join(' UNION ALL ');

    promise.push(analyticsConn.query(`
        WITH allAreas AS (
          ${allAreasSql}
        ),
        filteredStats AS (
          SELECT *
          FROM riskSummaryStats
          WHERE analyticsId = :analyticsId
            AND riskPersona = :riskPersona
            AND employeeTenure = :includeNewHires
        )
        SELECT
          a.riskAreaId,
          CASE
            WHEN COUNT(f.riskAreaId) = 0 THEN TRUE
            WHEN SUM(CASE
                       WHEN f.maxRiskLevel IS NOT NULL OR f.riskLevel IS NOT NULL THEN 1
                       ELSE 0
                     END) = 0 THEN TRUE
            ELSE FALSE
          END AS isDeploy,
          MAX(f.maxRiskLevel) AS maxRiskLevel
        FROM allAreas a
        LEFT JOIN filteredStats f ON a.riskAreaId = f.riskAreaId
        GROUP BY a.riskAreaId
        ORDER BY a.riskAreaId
      `, {
      replacements: {
        analyticsId,
        riskPersona,
        includeNewHires
      },
      type: Sequelize.QueryTypes.SELECT
    }));

    let filteredSummaryData;
    if (isDefaultPeriod) {
      // fetching with flagIsDefaultPeriodRiskPersona = true
      filteredSummaryData = await analyticsConn.query(`
        SELECT *, CAST(flagIsDefaultPeriodRiskPersona AS UNSIGNED) AS flagIsDefaultPeriodRiskPersona
        FROM riskSummaryStats
        WHERE analyticsId = :analyticsId
          AND riskPersona = :riskPersona
          AND employeeTenure = :includeNewHires
          AND flagIsDefaultPeriodRiskPersona = true
        ORDER BY riskAreaId ASC
      `, {
        replacements: {
          analyticsId,
          riskPersona,
          includeNewHires,
        },
        type: Sequelize.QueryTypes.SELECT
      });
    }

    // If no data found, or isDefaultPeriod is false fallback to periodId-based query
    if (!isDefaultPeriod || filteredSummaryData.length === 0) {
      filteredSummaryData = await analyticsConn.query(`
          SELECT *, CAST(flagIsDefaultPeriodRiskPersona AS UNSIGNED) AS flagIsDefaultPeriodRiskPersona
          FROM riskSummaryStats
          WHERE analyticsId = :analyticsId
            AND riskPersona = :riskPersona
            AND employeeTenure = :includeNewHires
            AND periodId = :periodId
          ORDER BY riskAreaId ASC
        `, {
        replacements: {
          analyticsId,
          riskPersona,
          includeNewHires,
          periodId,
        },
        type: Sequelize.QueryTypes.SELECT
      });
    }


    // check flagHasTenureData for an account
    promise.push(analyticsConn.query(
      'SELECT riskAreaId FROM riskSummaryStats WHERE analyticsId = :analyticsId AND flagHasTenureData=1 limit 1;',
      {
        replacements: { analyticsId },
        type: Sequelize.QueryTypes.SELECT,
      },
    ));

    // fetch master catalogTitleList based on riskArea
    promise.push(getRiskCatalogItems(riskAreaIds));

    const [deployFlagData, flagHasTenureData, riskCatalogList] = await Promise.all(promise);
    // merge all the data
    const riskSummaryData = deployFlagData.map(item => {
      const content = filteredSummaryData.find(stat => Number(stat.riskAreaId) === Number(item.riskAreaId));
      const isDeploy = !!item.isDeploy;
      let catalogTitle = safeMapGet(catalogTitleMap, item.riskAreaId);
      if (isDeploy && catalogTitle.length === 0) {
        catalogTitle = safeMapGet(riskCatalogList, item.riskAreaId);
      }
      const riskArea = safeMapGet(riskAreaMap, item.riskAreaId)
      return {
        ...(content || { riskAreaId: item.riskAreaId }),
        isDeploy,
        riskArea,
        catalogTitle
      };
    });
    res.json({ riskSummaryData, flagHasTenureData: flagHasTenureData.length > 0 });
  } catch (err) {
    logger.error(err);
    next(err);
  }
};
