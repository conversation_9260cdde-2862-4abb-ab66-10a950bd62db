const _ = require('lodash');
const db = require('../db');

const Account = db.accounts;
const SocialLoginConfig = db.socialLoginConfig;

module.exports.read = (req, res) => {
  res.json(req.accountSocialLoginConfig);
};

/**
 * @openapi
 * /accounts/{accountId}/socialLoginConfig:
 *   post:
 *     summary: Create Social login config
 *     tags:
 *       - SocialLoginConfig
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: accountId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the account
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/createSocialLoginConfig'
 *           examples:
 *             example:
 *               summary: Example social login config
 *               value:
 *                 type: google
 *                 clientId: "your-client-id"
 *                 clientSecret: "your-client-secret"
 *     responses:
 *       200:
 *         description: SocialLoginConfig added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "SocialLoginConfig added successfully"
 *
 * components:
 *   schemas:
 *     createSocialLoginConfig:
 *       type: object
 *       required:
 *         - type
 *         - clientId
 *       properties:
 *         type:
 *           type: string
 *           description: The type of the social login config. ('google', 'linkedin', 'microsoft')
 *         clientId:
 *           type: string
 *           description: The clientId of the social login config
 *         clientSecret:
 *           type: string
 *           description: The clientSecret of the social login config
 */
module.exports.create = async (req, res, next) => {
  try {
    req.body.accountId = req.account.id;
    const newSocialLoginConfig = await SocialLoginConfig.create(req.body);
    res.json(newSocialLoginConfig);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /accounts/{accountId}/socialLoginConfig/{accountSocialLoginConfigId}:
 *   put:
 *     summary: Update the Social login config based on Id
 *     tags:
 *       - SocialLoginConfig
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: accountId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: accountId of the existing SocialLogin config
 *       - name: accountSocialLoginConfigId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: accountSocialLoginConfigId of the existing SocialLogin config
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/editSocialLoginConfig'
 *           examples:
 *             example:
 *               summary: Example edit config
 *               value:
 *                 type: google
 *                 clientId: "updated-client-id"
 *                 clientSecret: "updated-client-secret"
 *     responses:
 *       200:
 *         description: SocialLoginConfig updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "SocialLoginConfig updated successfully"
 *
 * components:
 *   schemas:
 *     editSocialLoginConfig:
 *       type: object
 *       required:
 *         - clientId
 *       properties:
 *         type:
 *           type: string
 *           description: Type of the SocialLoginConfig ('google', 'linkedin', 'microsoft')
 *         clientId:
 *           type: string
 *           description: The clientId of the social login config
 *         clientSecret:
 *           type: string
 *           description: The clientSecret of the social login config
 */
module.exports.patch = async (req, res, next) => {
  try {
    const updatedSocialLoginConfig = await req.accountSocialLoginConfig.update(req.body);
    res.json(updatedSocialLoginConfig);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /accounts/{accountId}/socialLoginConfig/{accountSocialLoginConfigId}:
 *   delete:
 *     summary: Delete the existing SocialLoginConfig
 *     tags:
 *       - SocialLoginConfig
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: accountId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: accountId of the existing SocialLogin config
 *       - name: accountSocialLoginConfigId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: accountSocialLoginConfigId of the existing SocialLogin config
 *     responses:
 *       200:
 *         description: Successfully deleted Social LoginConfig record
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Successfully deleted Social LoginConfig record"
 */
module.exports.delete = async (req, res, next) => {
  const accountSocialLoginConfig = req.accountSocialLoginConfig;
  try {
    const data = await accountSocialLoginConfig.destroy();
    res.json(data);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the accountSocialLoginConfig when an id is passed in the route
module.exports.accountSocialLoginConfigById = async function (req, res, next, id) {
  try {
    const accountSocialLoginConfig = await SocialLoginConfig.findByPk(id, {
      include: [
        {
          model: Account,
        },
      ],
    });
    if (!accountSocialLoginConfig) {
      const err = new Error(req.i18n.t('system.socialLoginConfig_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.accountSocialLoginConfig = accountSocialLoginConfig;
    next();
  } catch (err) {
    next(err);
  }
};
