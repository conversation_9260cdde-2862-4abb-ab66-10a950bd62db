const db = require('../db');
const _ = require('lodash');
const config = require('../config/config');
const { restSortToSequelize } = require('../services/utils/resourceUtils');
const { getAllowedPermissions } = require('../services/acl/acl');
const {
  getLessonCardSourceLifecycle,
  updateResultsCache,
} = require('../services/utils/lessonUtils');
const { includeAnswerReport } = require('../services/utils/answerCardUtils');
const logger = require('../logger');
const { differenceInMilliseconds } = require('date-fns');

const AnswerCard = db.answerCards;
const Lessons = db.lessons;
const LessonCards = db.lessonCards;
const LessonLessonCards = db.lessonLessonCards;
const Users = db.users;
const Op = db.Sequelize.Op;

// post processes AwnserCard to add other data to record
const flattenAnswerCard = (answerCard, lessonCardId = null) => {
  const flattenedRecord = answerCard.get({ plain: true });
  // move lesson card id to legacy location
  if (lessonCardId) {
    flattenedRecord.lessonCardId = lessonCardId;
  } else if (flattenedRecord.lessonLessonCard) {
    flattenedRecord.lessonCardId = flattenedRecord.lessonLessonCard.lessonCardId;
  }
  return flattenedRecord;
};

const flattenAnswerCards = (answerCards) => {
  let flattenedRecords;
  if (answerCards) {
    flattenedRecords = answerCards.map(answerCard => flattenAnswerCard(answerCard));
  }
  return flattenedRecords;
};

const restQueryToSequelize = (query, defaults) => {
  const whereClause = { ..._.omit(query, ['$limit', '$skip', '$sort', 'topics', 'tags', 'deletedAt']) };
  const newQuery = {
    ...defaults,
    where: { ...defaults.where, ...whereClause },
  };

  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }
  return newQuery;
};

async function getLessonFilterParams(req) {
  // ??? add check for account
  // base permission checks on lessonCard as they are more restrictive than answer card
  const allowedPermissions = await getAllowedPermissions(
    req,
    req.user ? req.user.id : null, 'lessonCards', req.tokenPayload,
  );
  const lifecycleClauses = [{ lifecycle: 'publish' }, { lifecycle: 'retired' }];
  if (allowedPermissions.lessonCards.includes('update')) {
    lifecycleClauses.push({ lifecycle: 'review' });
    lifecycleClauses.push({ lifecycle: 'draft' });
    lifecycleClauses.push({ lifecycle: 'close' });
  } else if (allowedPermissions.lessonCards.includes('review')) {
    lifecycleClauses.push({ lifecycle: 'review' });
  }

  return { [Op.or]: lifecycleClauses };
}

const getIncludeParams = async (req, defaults) => {
  const includes = [
    {
      model: LessonLessonCards,
      include: {
        model: Lessons,
        where: await getLessonFilterParams(req),
        required: true,
      },
      required: true,
    },
  ];
  if (req.user) {
    includes.push({ model: Users, required: false });
  }
  return {
    ...defaults,
    include: includes,
  };
};

module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };

  const queryParams = restQueryToSequelize(req.query, defaults);
  const withUsers = await getIncludeParams(req, queryParams);
  withUsers.order = [[...withUsers.order]];

  const finalQuery = {
    ...withUsers,
  };
  const pagedResult = {
    limit: withUsers.limit,
    skip: withUsers.offset,
  };

  try {
    // Do a count query without skip and limit to get total
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
    };
    const count = await AnswerCard.count(countQuery);
    const results = await AnswerCard.findAll(finalQuery);
    const data = flattenAnswerCards(results);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /answer-cards:
 *   post:
 *     summary: Create Answer Card
 *     tags:
 *       - Answer Cards
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Answer card parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lessonId:
 *                 type: integer
 *               lessonCardId:
 *                 type: integer
 *               enrollmentId:
 *                 type: integer
 *               programId:
 *                 type: integer
 *               answer1:
 *                 type: integer
 *               sourceLifecycle:
 *                 type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/answerCards'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 */

module.exports.create = async (req, res, next) => {
  let answerCard;
  const userId = req.user ? req.user.id : null;
  const accountId = req.user ? req.user.accountId : null;
  const lessonCardId = req.body.lessonCardId;
  const lessonId = req.body.lessonId;
  const enrollmentId = req.body.enrollmentId;
  const programId = req.body.programId;
  const sourceLifecycle = req.body.sourceLifecycle || await getLessonCardSourceLifecycle(lessonId, lessonCardId);

  const start = new Date();

  // to be replaced when UI can specify
  req.publishOnly = true;
  try {
    if (!lessonId || !lessonCardId) {
      const err = new Error(req.i18n.t('answerCards.missing_lesson_info_Error'));
      err.status = 400;
      throw err;
    }
    const binding = await LessonLessonCards.findOne({
      where: {
        lessonId,
        lessonCardId,
      },
      include: {
        model: LessonCards,
      },
    });

    if (!binding) {
      const err = new Error(req.i18n.t('answerCards.invalid_lessonCard_Error', { lessonCardId }));
      err.status = 400;
      throw err;
    }
    // upsert - find or create answer card
    if (userId) {
      answerCard = await AnswerCard.findOne({
        include: [{
          model: LessonLessonCards,
        }],
        where: {
          userId,
          accountId,
          enrollmentId,
          lessonLessonCardId: binding.id,
          sourceLifecycle,
        },
      });
    }

    if (!answerCard) {
      answerCard = await AnswerCard.create({
        ...req.body,
        userId,
        accountId,
        enrollmentId,
        programId,
        lessonLessonCardId: binding.id,
        sourceLifecycle,
      });
    } else {
      // if there is already an answer card for this enrollment, we should preserve
      // the programId of the first answer and NOT owerwrite it.
      delete req.body.programId;
      answerCard = await answerCard.update({
        ...req.body,
      });
    }
    if (answerCard.sourceLifecycle === 'publish') {
      await updateResultsCache(answerCard, binding.id, binding.lessonCard);
    }
    const flattenedRecord = flattenAnswerCard(answerCard, lessonCardId);
    const data = await includeAnswerReport(req, flattenedRecord, lessonId, lessonCardId);
    res.json(data);
  } catch (err) {
    next(err);
  } finally {
    logger.info(`POST answerCard completed,
      user=${userId}, lesson=${lessonId}, lessonCard=${lessonCardId},
      lifecycle=${sourceLifecycle}, enrollmentId==${enrollmentId},
      elapsed=${differenceInMilliseconds(new Date(), start)}ms.`);
  }
};

module.exports.patch = async (req, res, next) => {
  const answerCard = req.answerCard;
  // to be replaced when UI can specify
  req.publishOnly = true;
  try {
    // req.body.sourceLifecycle = await getLessonCardSourceLifecycle(req.body.lessonCardId);
    const newCard = await answerCard.update(req.body);
    const flattenedRecord = flattenAnswerCard(newCard);
    const data = await includeAnswerReport(
      req, flattenedRecord,
      flattenedRecord.lessonLessonCard.lessonId,
      flattenedRecord.lessonCardId,
    );
    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  // to be replaced when UI can specify
  req.publishOnly = true;
  try {
    const flattenedRecord = flattenAnswerCard(req.answerCard);
    const data = await includeAnswerReport(
      req, flattenedRecord,
      flattenedRecord.lessonLessonCard.lessonId,
      flattenedRecord.lessonCardId,
    );
    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.answerCardById = async (req, res, next, id) => {
  const defaults = {
    where: { id },
  };

  try {
    const finalQuery = await getIncludeParams(req, defaults);
    const answerCard = await AnswerCard.findOne(finalQuery);
    if (!answerCard) {
      const err = new Error(req.i18n.t('answerCards.answerCard_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.answerCard = answerCard;
    next();
  } catch (err) {
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  const answerCard = req.answerCard;
  try {
    const card = await answerCard.destroy();
    res.json(card);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * definitions:
 *  answerCards:
 *    type: object
 *    properties:
 *      id:
 *        type: integer
 *      userId:
 *        type: integer
 *      answer1:
 *        type: string
 *      answer2:
 *        type: string
 *      answer3:
 *        type: string
 *      answer4:
 *        type: string
 *      answer5:
 *        type: string
 *      answer6:
 *        type: string
 *      answer7:
 *        type: string
 *      answer8:
 *        type: string
 *      answer9:
 *        type: string
 *      answer10:
 *        type: string
 *      createdAt:
 *        type: string
 *      updatedAt:
 *        type: string
 *      textAnswer:
 *        type: string
 *      sourceLifecycle:
 *        type: string
 *      lessonLessonCardId:
 *        type: integer
 *      enrollmentId:
 *        type: integer
 *      programId:
 *        type: integer
 *      results:
 *        type: array
 *        items:
 *          properties:
 *            level:
 *              type: string
 *            numResponses:
 *              type: integer
 *            data:
 *              type: array
 *              items:
 *                properties:
 *                  perc:
 *                    type: number
 *                  response:
 *                    type: integer
 */

