const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { removeExpertFields } = require('../services/utils/userUtils');
const { restSortToSequelize } = require('../services/utils/resourceUtils');

const User = db.users;
const FileModel = db.files;
const Topic = db.topics;
const File = db.files;
const UserRole = db.userRoles;
const Role = db.roles;

const includeParams = [
  {
    // find only users with a user Role of expert. We can ignore accountUser role of expert for now.
    attributes: ['id', 'name'],
    model: Role,
    through: 'userRoles',
    where: {
      name: 'expert',
    },
  },
  {
    model: FileModel,
    as: 'avatar',
  },
  {
    model: FileModel,
    as: 'banner',
  },
  {
    model: Topic,
    through: 'userTopics',
  },
];

const restQueryToSequelize = (query, defaults) => {
  const newQuery = {
    ...defaults,
    include: [...defaults.include],
  };
  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }
  return newQuery;
};

module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
    include: includeParams,
  };
  const finalQuery = restQueryToSequelize(req.query, defaults);
  // TODO: Since we find experts via JS code, we need to handle limit and skip ourselves
  // but we never use this, so put this off until later
  const workingLimit = finalQuery.limit;
  const workingSkip = finalQuery.offset;
  delete finalQuery.limit;
  delete finalQuery.offset;

  try {
    const allExperts = await User.findAll(finalQuery);
    const count = allExperts.length;

    const modifiedExperts = allExperts.map((expert) => {
      return removeExpertFields(expert);
    });
    res.json({ total: count, limit: workingLimit, skip: workingSkip, data: modifiedExperts });
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  let expert = req.expert;
  try {
    expert = removeExpertFields(expert);
    res.json(expert);
  } catch (err) {
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  let expert = req.expert;
  try {
    let banner;
    let avatar;
    const newExpert = {
    };
    if (req.body.title) {
      newExpert.title = req.body.title;
    }
    if (req.body.description) {
      newExpert.description = req.body.description;
    }
    if (req.body.avatar) {
      avatar = await File.create({
        path: req.body.avatar,
        kb: 0,
        type: 'link',
      });
      newExpert.avatarId = avatar.id;
    }
    if (req.body.banner) {
      banner = await File.create({
        path: req.body.banner,
        kb: 0,
        type: 'link',
      });
      newExpert.bannerId = banner.id;
    }
    if (!_.isEmpty(newExpert)) {
      expert = await expert.update(newExpert);
    }
    const expertRole = await db.roles.findOne({
      where: {
        name: 'expert',
      },
    });
    await UserRole.update(
      {
        roleId: expertRole.id,
      },
      {
        where: {
          userId: expert.id,
        },
        fields: ['roleId'],
      },
    );
    let finalExpert = await User.findByPk(expert.id, {
      include: includeParams,
    });
    finalExpert = removeExpertFields(finalExpert);
    res.json(finalExpert);
  } catch (err) {
    next(err);
  }
};

module.exports.expertById = async (req, res, next, id) => {
  try {
    const user = await User.findOne({
      where: {
        id,
      },
      include: includeParams,
    });
    if (!user) {
      const err = new Error(req.i18n.t('users.user_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.expert = user;
    next();
  } catch (err) {
    next(err);
  }
};
