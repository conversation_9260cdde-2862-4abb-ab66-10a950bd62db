const db = require('../db');
const _ = require('lodash');
const jwt = require('jwt-simple');

const config = require('../config/config');
const logger = require('../logger');
const { createAuditLog } = require('./auditLogs');
const { EMTRAIN_ACCOUNT_IDS } = require('../constants');

// Load keys for JWT decoding
const { getPublicKey } = require('../services/utils/loadAuthKeys');
const publicKey = getPublicKey();
const { restQueryToSequelize, findActiveItemsForUser } = require('../services/utils/userUtils');
const { aggregateStatus, gleanStatus, propagateAssignmentChange, initializeStatus,
  updateProgramAssignments, updateCampaignAssignment, forceProgramComplete } = require('../services/utils/calcUtils');
const { calcUserLessonPctComplete } = require('../services/utils/eventUtils');
const { localizeModelObject, localizeProgram,
  getContentStringQuery, getQueryLanguages } = require('../services/utils/localizationUtils');
const { getAllowedPermissions } = require('../services/acl/acl');
const { getGroupMembers } = require('../services/utils/campaignUtils');
const { configureProgramForAccount } = require('../services/utils/programUtils');
const { configureLessonForAccount } = require('../services/utils/lessonUtils');
const { newRestQueryToSequelize, getStatusString, userAssignmentDetails } = require('../services/utils/reportingUtils');

const UserLessons = db.userLessons;
const Lessons = db.lessons;
const LessonCards = db.lessonCards;
const LessonLessonCards = db.lessonLessonCards;
const Programs = db.programs;
const MediaAssets = db.mediaAssets;
const QuestionAnswers = db.questionAnswers;
const GroupAssignments = db.groupAssignments;
const Campaigns = db.campaigns;
const Resources = db.resources;
const ResourceBundles = db.resourceBundles;
const LessonPrograms = db.lessonPrograms;
const AccountBundles = db.accountBundles;
const AccountPrograms = db.accountPrograms;
const AccountLessons = db.accountLessons;
const AccountUsers = db.accountUsers;
const Accounts = db.accounts;
const ContentStrings = db.contentStrings;
const ScormPrograms = db.scormPrograms;
const Events = db.events;
const Users = db.users;
const Groups = db.groups;
const ResourceAssets = db.resourceAssets;

const Op = db.Sequelize.Op;

function flattenItem(binding) {
  const resource = binding.resource;
  const flattenedBinding = binding.get({ plain: true });
  return Object.assign(flattenedBinding, { resource });
}

// attaches refered resource to resourceBinding
async function lookupResource(req, resourceBinding) {
  let resource = null;
  if (resourceBinding.type === 'program') {
    resource = await Programs.findByPk(resourceBinding.resourceId, {
      include: [getContentStringQuery(req, getQueryLanguages(req, req.i18n.language || 'en'), 'program')],
    });
    if (resource && req.user && req.user.accountId) {
      const account = await Accounts.findByPk(req.user.accountId);
      if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
        const accountProgram = await AccountPrograms.findOne({
          where: {
            accountId: req.user.accountId,
            programId: resourceBinding.resourceId,
          },
        });
        if (accountProgram) {
          resource = configureProgramForAccount(resource, accountProgram);
        }
      }
    }
  } else if (resourceBinding.type === 'lesson') {
    resource = await Lessons.findByPk(resourceBinding.resourceId, {
      include: [{
        model: db.files,
      }, {
        model: db.lessonCards,
        attributes: ['id'],
      },
      getContentStringQuery(req, getQueryLanguages(req, req.i18n.language || 'en'), 'lesson')],
    });
    if (resource && req.user && req.user.accountId) {
      const account = await Accounts.findByPk(req.user.accountId);
      if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
        const accountLesson = await AccountLessons.findOne({
          where: {
            accountId: req.user.accountId,
            lessonId: resourceBinding.resourceId,
          },
        });
        if (accountLesson) {
          resource = configureLessonForAccount(resource, accountLesson);
        }
      }
    }
  } else if (resourceBinding.type === 'resourceAsset') {
    resource = await ResourceAssets.findByPk(resourceBinding.resourceId, {
      include: [{
        model: db.files,
        as: 'file',
      },
      getContentStringQuery(req, getQueryLanguages(req, req.i18n.language || 'en'), 'resourceAsset')],
    });
  } else if (resourceBinding.type === 'questionAnswer') {
    resource = await QuestionAnswers.findByPk(resourceBinding.resourceId, {
      include: [getContentStringQuery(req, getQueryLanguages(req, req.i18n.language || 'en'), 'questionAnswer')],
    });
  } else if (resourceBinding.type === 'campaign') {
    resource = await Campaigns.findByPk(resourceBinding.resourceId);
  }

  const downloadLanguage = req.query.downloadLanguage ? req.query.downloadLanguage : req.i18n.language;
  resource = localizeModelObject(req, resource, downloadLanguage, true);
  Object.assign(resourceBinding, { resource });
  return resourceBinding;
}

// looks up resources referenced from resourceBindings
async function lookupResources(req, resourceBindings) {
  const filteredItems = [];
  // remove duplicates
  for (const item of resourceBindings) {
    const index = filteredItems.findIndex(i =>
      ((i.userId === item.userId)
      && (i.assignmentId === item.assignmentId)
      && (i.resourceId === item.resourceId)
      && (i.type === item.type)));
    // not there, add it
    if (index === -1) {
      filteredItems.push(item);
    }
    // there, make sure to take lowest id
    if (index > -1) {
      if (item.id < filteredItems[index].id) {
        filteredItems[index].id = item.id;
      }
    }
  }

  const promises = [];
  filteredItems.forEach((binding) => {
    promises.push(lookupResource(req, binding));
  });
  const items = await Promise.all(promises);

  return items.map((binding) => {
    return flattenItem(binding);
  });
}

async function updateRecord(userLesson, updates, req) {
  let parents;
  // don't allow completed status to change
  if (userLesson.status === 'completed') {
    // eslint-disable-next-line no-param-reassign
    updates.status = 'completed';
  }

  // detect change in status.
  // Status update is propagated to all matching records even if the update matches this record.
  if (updates.status) {
    const candidate = Object.assign(userLesson, updates);
    parents = await propagateAssignmentChange(candidate);
  }
  const updatedRecord = await userLesson.update(updates);

  // roll up parent status if necessary
  if (parents && parents.size > 0) {
    await updateProgramAssignments(req.user.accountId, parents);
    await updateCampaignAssignment(parents, req.sessionId);
  }
  return updatedRecord;
}

const formatUserStatus = (userLesson) => {
  const { status, removedAt, removalReason } = userLesson;
  if (status === 'completed') {
    return { status: 'Completed' };
  } else if (status === 'closed') {
    return { status: getStatusString(removalReason), removeDate: removedAt };
  } else if (status === 'open') {
    return { status: 'Not Started' };
  }
  return { status: 'In Progress' };
};
const formatContentTitle = (userLesson) => {
  let displayString;
  switch (userLesson.type) {
    case 'campaign':
      displayString = `${userLesson.groupAssignment.campaign.name}`;
      break;
    case 'program':
      displayString = `${userLesson.program.name}`;
      break;
    case 'lesson':
      displayString = `${userLesson.lesson.title}`;
      break;
    default:
      break;
  }
  return displayString;
};

module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };
  const queryParams = restQueryToSequelize(req.query, defaults, false);
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  const countQuery = {
    where: _.pick(queryParams, ['where']).where,
  };

  queryParams.include = [{
    model: GroupAssignments,
    attributes: ['id'],
    include: [{ model: Campaigns, attributes: ['name'] }],
  }];

  try {
    const count = await UserLessons.count(countQuery);
    const results = await UserLessons.findAll(queryParams);
    const data = results.map(record => record.get({ plain: true }));
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  try {
    res.json(req.userLesson);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /user-lessons:
 *   post:
 *     summary: Add New User Lesson
 *     tags: [User Lessons]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - in: body
 *         name: body
 *         description: Suggested Fields parameters
 *         required: true
 *         schema:
 *           type: object
 *           properties:
 *             sourceLifecycle:
 *               type: string
 *               example: "preview"
 *             lessonId:
 *               type: integer
 *               example: 1
 *             status:
 *               type: string
 *               example: "inProgress"
 *             enrollmentId:
 *               type: integer
 *               example: 1
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/userLessons'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  // default type is 'lesson' for historic reasons
  const type = req.body.type ? req.body.type : 'lesson';
  const status = req.body.status ? req.body.status : 'open';
  const enrollmentId = req.body.enrollmentId ? req.body.enrollmentId : 1;

  const lessonParams = {
    resourceId: req.body.lessonId,
    userId: req.user.id,
    enrollmentId,
    status,
    type,
  };
  try {
    const userLesson = await lookupResource(req, lessonParams);
    const lessonLifecycle = userLesson.resource.lifecycle;
    const sourceLifecycle = req.body.sourceLifecycle ? req.body.sourceLifecycle : lessonLifecycle;
    const userLessons = await UserLessons.findAll({
      where: {
        userId: lessonParams.userId,
        resourceId: lessonParams.resourceId,
        type: lessonParams.type,
        sourceLifecycle: sourceLifecycle === 'retired' ? 'publish' : sourceLifecycle,
        enrollmentId: lessonParams.enrollmentId,
        status: {
          [Op.and]: [
            { [Op.ne]: 'closed' },
            { [Op.ne]: 'incomplete' },
          ],
        },
      },
    });
    // if the enrollmentId is not null or !== 1 the call is not valid
    // because the userLesson would have been created by the campaign sweeper
    if (userLessons.length === 0 && enrollmentId && enrollmentId !== 1) {
      // eslint-disable-next-line max-len
      const err = new Error(`Invalid enrollmentId for resource. resourceId: ${lessonParams.resourceId}, enrollmentId: ${lessonParams.enrollmentId}`);
      err.status = 400;
      throw err;
    }
    if (userLessons.length === 0) {
      // create
      userLesson.sourceLifecycle = sourceLifecycle;
      userLesson.startedAt = Date.now();
      const newUserLesson = await UserLessons.create(userLesson);
      res.json(newUserLesson);
    } else {
      // update including duplicates
      const updatedLesson = await updateRecord(userLessons[0], req.body, req);
      res.json(updatedLesson);
    }
  } catch (err) {
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  try {
    const updatedLesson = await updateRecord(req.userLesson, req.body, req);
    res.json(updatedLesson);
  } catch (err) {
    next(err);
  }
};

module.exports.forceComplete = async (req, res, next) => {
  const userLesson = req.userLesson;
  let updatedUserLesson = userLesson;
  try {
    if (userLesson.status !== 'completed') {
      if (userLesson.type === 'program') {
        // get account of userLesson's userId
        const user = await Users.findByPk(userLesson.userId, {
          include: [{
            model: AccountUsers,
            required: true,
          }],
        });
        if (user && user.accountUser) {
        // program
          const minTime = await forceProgramComplete(userLesson, user.accountUser.accountId);
          // adjust elapsed time to statisfy min time requirement
          const elapsed = userLesson.elapsedTimeInSeconds >= minTime ? userLesson.elapsedTimeInSeconds : minTime;
          updatedUserLesson = await updateRecord(userLesson, {
            status: 'completed', elapsedTimeInSeconds: elapsed,
          }, req);
        }
      } else {
        // lesson
        updatedUserLesson = await updateRecord(userLesson, { status: 'completed' }, req);
      }

      // emit event
      await Events.create({
        action: true,
        sourceLifecycle: userLesson.sourceLifecycle,
        type: 'userLessonCompletionForced',
        userId: userLesson.userId,
        trackableId: userLesson.resourceId,
        trackableType: userLesson.type === 'program' ? 'programs' : 'resource',
        trackableData: {
          userLesson,
          requestingUser: req.user.id,
        },
        accountId: req.user.accountId,
        sessionId: req.sessionId,
      });
    }
    res.json(updatedUserLesson);
  } catch (err) {
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  try {
    const userLesson = req.userLesson;
    const data = await userLesson.destroy();
    res.json(data);
  } catch (err) {
    next(err);
  }
};

/**
 * check if the userLesson by lessonId/enrollmentId is complete. Return the userLesson.
 *
 */

/**
 * @openapi
 * /user-lessons/{lessonId}/checkLessonEnrollmentCompletion/{enrollmentId}:
 *   get:
 *     summary: Check Lesson Enrollment Completion
 *     tags:
 *       - User Lessons
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: lessonId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: enrollmentId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: sourceLifecycle
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           example: publish
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/userLessons'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 */
module.exports.checkComplete = async (req, res, next) => {
  try {
    const lessonId = parseInt(req.params.lessonId);
    const enrollmentId = parseInt(req.params.enrollmentId);
    const sourceLifecycle = req.query.sourceLifecycle || 'publish';
    const userId = req.user ? req.user.id : null;
    let userLesson = await UserLessons.findOne({
      where: {
        userId,
        resourceId: lessonId,
        enrollmentId,
        type: 'lesson',
        sourceLifecycle,
      },
    });
    if (!userLesson) {
      const err =
        new Error(req.i18n.t('userLessons.userLesson_by_lessonId_enrollmentId_load_Error', { lessonId, enrollmentId }));
      err.status = 404;
      throw err;
    }

    if (userId && userLesson && userLesson.percentComplete < 100) {
      const percentComplete =
        await calcUserLessonPctComplete(userId, lessonId, sourceLifecycle, enrollmentId);
      // update % complete if changed
      if (userLesson.percentComplete !== percentComplete) {
        userLesson = await userLesson.update({ percentComplete });
        const parents = await propagateAssignmentChange(userLesson);
        await updateProgramAssignments(req.user.accountId, parents);
        await updateCampaignAssignment(parents, req.sessionId);

        // eslint-disable-next-line max-len
        logger.info(`userLesson calculated as complete, updated userLesson: ${userLesson.id} for user: ${userId} and lesson: ${lessonId} to ${percentComplete}`);
      }
    }
    res.json(userLesson);
  } catch (err) {
    logger.error('checkComplete error: %j', err);
    next(err);
  }
};

// produce a hierarchical view of a user's userLesson records
// takes a flat list returned by a db query and iterates over it to
// create a struture:
// finalList : [
//  item: {
//    ...,
//    items: [],
//  },
//   ...,
// ]
// items can be resources, campaigns or programs.
// campaigns and programs can have children

/**
 * @swagger
 * /users/{userId}/activeItems:
 *   get:
 *     summary: User Active Items
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         schema:
 *           type: integer
 *         required: true
 *         description: User ID
 *       - name: $limit
 *         in: query
 *         schema:
 *           type: integer
 *           example: 10
 *         required: true
 *         description: limit (integer) for number of records
 *       - name: $skip
 *         in: query
 *         schema:
 *           type: integer
 *           example: 0
 *         required: true
 *         description: offset or skip position before beginning to return result
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 accountFields:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/accountFields'
 *                 topicIds:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/topics'
 *                 accounts:
 *                   type: array
 *                   items:
 *                     $ref: '#/definitions/accounts'
 *                 groups:
 *                   type: array
 *                   items:
 *                     type: object
 *                     allOf:
 *                       - $ref: '#/components/schemas/groups'
 *                       - properties:
 *                           roleGroups:
 *                             $ref: '#/definitions/roleGroups'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.findActiveItems = async (req, res, next) => {
  const userId = req.requestedUser.id;
  const finalList = [];
  const campaigns = new Map();
  const getCampaign = async (item) => {
    if (campaigns.has(item.groupAssignment.campaignId)) {
      return campaigns.get(item.groupAssignment.campaignId);
    }
    const newCampaign = {
      programs: new Map(),
      campaign: {
        userId,
        resourceId: item.groupAssignment.campaignId,
        type: 'campaign',
        status: item.status,
        elapsedTimeInSeconds: 0,
        percentComplete: 0,
        resource: item.groupAssignment.campaign,
        items: [],
        enabled: true,
      },
      campaignStatus: item,
    };
    campaigns.set(item.groupAssignment.campaignId, newCampaign);
    return newCampaign;
  };

  try {
    const activeItems = await findActiveItemsForUser({ userId });
    let filteredActiveItems = [];
    if (req.query.includeClosedCampaigns) {
      filteredActiveItems = activeItems;
    } else {
      const userGroupCache = {};
      // completed assignments should not show up for campaigns which have been closed or past due
      for (const ai of activeItems) {
        if (!ai.groupAssignment) {
          filteredActiveItems.push(ai);
        } else {
          const groupId = ai.groupAssignment.groupId;
          logger.debug('**** groupId', groupId);
          let hasUserId;
          if (userGroupCache[groupId] !== undefined) {
            // use cache
            hasUserId = userGroupCache[groupId];
            logger.debug('*** Using cache... value:', hasUserId);
          } else {
            // Get all direct and indirect members of the group
            const allGroupUserIds = await getGroupMembers(groupId);
            hasUserId = allGroupUserIds.has(userId);
            userGroupCache[groupId] = hasUserId; // cache it
            logger.debug('*** Not using cache... value:', hasUserId);
          }
          filteredActiveItems.push(ai);
        }
      }
    }

    const populatedItems = await lookupResources(req, filteredActiveItems);
    const activeIterations = new Map();

    // figure out what reoccurence iteration is active for campaigns
    // based on the group assignments, not on the campaign because
    // the campaign could be ahead one iteration if the user has not yet been
    // assigned the new iteration (so we want to continue to look at the previous one)
    for (const item of populatedItems) {
      if (item.groupAssignment && item.groupAssignment.campaign) {
        const campaign = item.groupAssignment.campaign;
        if (!activeIterations.has(campaign.id)) {
          activeIterations.set(campaign.id, item.groupAssignment.reoccurenceIteration);
        }
        const iteration = activeIterations.get(campaign.id);
        if (item.groupAssignment.reoccurenceIteration > iteration) {
          activeIterations.set(campaign.id, item.groupAssignment.reoccurenceIteration);
        }
      }
    }

    for await (const item of populatedItems) {
      item.enabled = true;
      if (item.groupAssignment) {
        // ignore items from any iterations except the active one
        if (item.groupAssignment.reoccurenceIteration === activeIterations.get(item.groupAssignment.campaign.id)) {
          // find item's campaign
          const currentCampaign = await getCampaign(item);

          // add to current campaign list

          // check to see if item should be added to a program
          if (item.groupAssignment.programId && currentCampaign.programs.has(item.groupAssignment.programId) && item.resource) {
            const programLessons = await LessonPrograms.findOne({
              where: { lessonId: item.resource.id, programId: item.groupAssignment.programId },
            });

            // if the lesson has been removed from programLessons, we shouldn't include it.
            if (programLessons && !programLessons.dateRemoved) {
              // add item to existing program
              const program = currentCampaign.programs.get(item.groupAssignment.programId);
              // if not already in program.items, add it
              const lessonIdx = program.items.findIndex(i =>
                ((i.userId === item.userId)
                  && (i.resourceId === item.resourceId)
                  && (i.type === item.type)));
              if (lessonIdx === -1) {
                program.items.push(item);
              }
            }
          } else if (item.type !== 'program' && item.type !== 'campaign') {
            // add non-program item to campaign
            currentCampaign.campaign.items.push(item);
          } else if (item.type === 'program' && !currentCampaign.programs.has(item.resourceId)) {
            // add program item to campaign
            const items = [];
            Object.assign(item, { items });
            currentCampaign.programs.set(item.resourceId, item);
            currentCampaign.campaign.items.push(item);
          }
        }
      } else {
        // item is not part of a campaign
        finalList.push(item);
      }
    }
    campaigns.forEach((value) => {
      const campaignStruct = value;
      // campaign status
      const aggregatedStatus = campaignStruct.campaignStatus;
      campaignStruct.campaign.status = aggregatedStatus.status;
      campaignStruct.campaign.elapsedTimeInSeconds = aggregatedStatus.elapsedTimeInSeconds;
      campaignStruct.campaign.percentComplete = aggregatedStatus.percentComplete;
      finalList.push(campaignStruct.campaign);
    });

    for (const nextItem of finalList) {
      if (nextItem.type === 'campaign' && nextItem.items && nextItem.items.length > 0) {
        for (const campaignItem of nextItem.items) {
          if (campaignItem.type === 'program') {
            for (const programItem of campaignItem.items) {
              if (programItem.type === 'lesson') {
                const lessonProgram =
                  await LessonPrograms.findOne({
                    where: { lessonId: programItem.resourceId, programId: campaignItem.resourceId },
                  });
                programItem.position = lessonProgram.position;
              } else {
                programItem.position = 0;
              }
            }
            campaignItem.items.sort((item1, item2) => ((item1.position > item2.position) ? 1 : -1));
          }
        }
      }
    }

    // When building the assignments list, the logic for associating lessons with programs is dependent on the order
    // For this reason, it's best to just reverse the order when the full list is constructed.
    finalList.reverse();
    res.json(finalList);
  } catch (err) {
    next(err);
  }
};

// resets userLessons for a programId, userId, sourceLifecycle = 'preview' to open, no progress state
module.exports.prepProgramPreview = async (req, res, next) => {
  try {
    const programId = parseInt(req.params.programId1);
    const userId = parseInt(req.params.userId1);
    // find program's lesson Ids
    const lessonBindings = await LessonPrograms.findAll({
      where: {
        programId,
        dateRemoved: {
          [Op.eq]: null,
        },
      },
    });
    const lessonIds = lessonBindings.map(binding => binding.lessonId);
    // reset userLessons for lessonIds, userId with 'preview' sourceLifecycle
    const updated = await UserLessons.update({
      status: 'open',
      percentComplete: 0,
      elapsedTimeInSeconds: 0,
      startedAt: null,
      completionDate: null,
    }, {
      where: {
        userId,
        resourceId: {
          [Op.in]: lessonIds,
        },
        type: 'lesson',
        sourceLifecycle: 'preview',
      },
    });
    res.json(updated); // return as an array
  } catch (err) {
    logger.error('Exception caught in prepProgramPreview(), err: %j', err);
    next(err);
  }
};

/**
 * getProgramProgress:
 *
 * Returns a program with it's lessons, and a list of lessonCardIds for each lesson
 * The data is formatted in a similar enough manner to the findUserAssignments call
 * such that the client doesn't need to treat the data differently when tracking
 * a user through their assignments or through a specific program.
 *
 * This won't include any campaign information or groupAssignment info. It's assumed
 * the client doesn't depend on that info, since that gets processed out when defining
 * actual assignments.
 */
/**
 * @swagger
 * /users/{userId}/programProgress/{programId}:
 *  get:
 *    summary: User Program Progress
 *    tags: [User]
 *    security:
 *      - JWT: []
 *    parameters:
 *      - name: userId
 *        in: path
 *        schema:
 *          type: integer
 *        required: true
 *        description: User ID
 *      - name: programId
 *        in: path
 *        schema:
 *          type: integer
 *        required: true
 *        description: Program ID
 *      - name: isPreview
 *        in: query
 *        schema:
 *          type: boolean
 *          default: false
 *        required: false
 *        description: Preview flag
 *    responses:
 *      200:
 *        description: Successful operation
 *        content:
 *          application/json:
 *            schema:
 *              allOf:
 *                - $ref: '#/definitions/userLessons'
 *                - type: object
 *                  properties:
 *                    resource:
 *                      allOf:
 *                        - $ref: '#/definitions/resources'
 *                        - type: object
 *                          properties:
 *                            resourceBundles:
 *                              type: array
 *                              items:
 *                                $ref: '#/definitions/resourceBundles'
 *                    contentStrings:
 *                      type: array
 *                      items:
 *                        $ref: '#/definitions/contentStrings'
 *                    items:
 *                      type: array
 *                      items:
 *                        $ref: '#/definitions/programs'
 *      400:
 *        description: Bad Request
 *      401:
 *        description: Unauthorized
 *      404:
 *        description: Not Found
 *      5XX:
 *        description: Unexpected error
 */

module.exports.getProgramProgress = async (req, res, next) => {
  const entered = Date.now();
  try {
    logger.debug(`Entering getProgramProgress, progId=${req.program.id}`);
    const programSequelize = req.program;
    const userId = req.requestedUser.id;

    // You should only get your own progress
    if (userId !== req.user.id) {
      const err = new Error('Can only get progress for logged in user');
      err.status = 401;
      throw err;
    }

    let accountProgram = null;
    let accountLessons = null;
    if (req.user && req.user.accountId) {
      const account = await Accounts.findByPk(req.user.accountId);
      if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
        accountProgram =
          await AccountPrograms.findOne({ where: { accountId: account.id, programId: req.program.id } });
        accountLessons = await AccountLessons.findAll({ where: { accountId: account.id } });
      }
    }
    const programLessons = programSequelize.lessons;
    let programResource = programSequelize.get({ plain: true });
    delete programResource.lessons;
    if (accountProgram) {
      programResource = configureProgramForAccount(programResource, accountProgram);
    }
    const requiredSeconds = programResource.minTimeInMinutes ? programResource.minTimeInMinutes * 60 : 0;
    const elapsedSeconds = programResource.userLesson ? programResource.userLesson.elapsedTimeInSeconds : 0;
    const program = {
      type: 'program',
      resource: programResource,
      resourceId: programResource.id,
      sourceLifecycle: programResource.sourceLifecycle,
      status: programResource.userLesson ? programResource.userLesson.status : 'open',
      percentComplete: programResource.userLesson ? programResource.userLesson.percentComplete : 0,
      elapsedTimeInSeconds: elapsedSeconds,
      enabled: true,
      requiredSeconds,
      requiredSecondsTaken: elapsedSeconds > requiredSeconds ? requiredSeconds : elapsedSeconds,
    };

    // this structure holds the status of the programs as an aggregate of its lessons
    let programStatus = await initializeStatus('program', programResource.id, req.user.accountId);

    const downloadLanguage = req.query.downloadLanguage ? req.query.downloadLanguage : req.i18n.language;
    const filteredProgramLessons = programLessons.filter(pl => pl.lessonPrograms.dateRemoved === null);
    const lessonsWithStatus = filteredProgramLessons.map((l) => {
      const lesson = l.get({ plain: true });
      let { title } = localizeModelObject(req, lesson, downloadLanguage, true);
      const userLesson = lesson.userLesson || { status: 'open', percentComplete: 0, elapsedTimeInSeconds: 0 };
      programStatus = aggregateStatus(programStatus, userLesson);

      if (accountLessons) {
        const matchingAccountLesson = accountLessons.find(accountLesson => accountLesson.lessonId === lesson.id);
        if (matchingAccountLesson) {
          lesson.title = matchingAccountLesson.title ? matchingAccountLesson.title : lesson.title;
          title = matchingAccountLesson.title ? matchingAccountLesson.title : lesson.title;
        }
      }

      return {
        ...lesson,
        title,
        status: userLesson.status,
        percentComplete: userLesson.percentComplete,
        elapsedTimeInSeconds: userLesson.elapsedTimeInSeconds,
        enrollmentId: 1,
      };
    });
    // program status
    const aggregatedProgramStatus = gleanStatus(programStatus);
    program.status = aggregatedProgramStatus.status;
    program.percentComplete = aggregatedProgramStatus.percentComplete;
    program.elapsedTimeInSeconds = aggregatedProgramStatus.elapsedTimeInSeconds;
    // eslint-disable-next-line max-len
    program.requiredSecondsTaken = program.elapsedTimeInSeconds > requiredSeconds ? requiredSeconds : program.elapsedTimeInSeconds;

    const scormProgram = await
    ScormPrograms.findOne({ where: { userId, model: 'program', resourceId: req.program.id } });
    // if we find a scormProgram that has the completion date set, we know this program was previously completed
    if (scormProgram && scormProgram.completionDate) {
      program.status = 'completed';
      program.percentComplete = 100;
      // if the elapsedTimeInSeconds is not at least the required amount, we need to set it to the min required time + 10 seconds padding.
      program.elapsedTimeInSeconds =
        program.elapsedTimeInSeconds < (program.minTimeInMinutes * 60) ?
          (program.minTimeInMinutes * 60) + 10 : program.elapsedTimeInSeconds;
      program.scormProgramCompletionDate = scormProgram.completionDate;
    }

    const finalLessons = lessonsWithStatus.map((lesson) => {
      return {
        resource: lesson,
        type: 'lesson',
        status: lesson.status,
        percentComplete: lesson.percentComplete,
        elapsedTimeInSeconds: lesson.elapsedTimeInSeconds,
        enrollmentId: lesson.enrollmentId,
        resourceId: lesson.id,
        enabled: true,
      };
    });

    program.items = finalLessons;
    res.json([program]); // return as an array
  } catch (err) {
    logger.error('findProgramProgress error: %j', err);
    next(err);
  } finally {
    const elapsed = Date.now() - entered;
    logger.debug(`Exiting getProgramProgress, elapsed=${elapsed}ms.`);
  }
};

module.exports.findByPk = async (req, res, next, id) => {
  try {
    const userLesson = await UserLessons.findOne({
      where: {
        id,
      },
    });
    if (!userLesson) {
      const err = new Error(req.i18n.t('mediaAssets.asset_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.userLesson = userLesson;
    next();
  } catch (err) {
    next(err);
  }
};

const getProgramIncludeParams = async (req, user) => {
  const bundleAccountId = user ? user.accountId : null;
  const isPreview = !!(req.query.isPreview && req.query.isPreview === 'true');
  const sourceLifecycle = isPreview ? 'preview' : 'publish';
  const includeParams = [{
    model: Lessons,
    through: 'lessonPrograms',
    required: false,
    include: [
      {
        model: Resources,
        as: 'resource',
      },
      {
        model: LessonCards,
        attributes: ['id'],
      },
      {
        model: UserLessons,
        required: false,
        where: {
          userId: user.id,
          type: 'lesson',
          status: {
            [Op.notIn]: ['closed', 'incomplete'],
          },
          enrollmentId: 1, // ignore any campaign assignments
          sourceLifecycle,
        },
      },
      getContentStringQuery(req, ['en'], 'lesson'), // empty template, needed by sequelize to be able to merge in later
    ],
  },
  {
    model: UserLessons,
    required: false,
    where: {
      userId: user.id,
      type: 'program',
      status: {
        [Op.notIn]: ['closed', 'incomplete'],
      },
      enrollmentId: 1, // ignore any campaign assignments
      sourceLifecycle,
    },
  },
  {
    model: Resources,
    as: 'resource',
    include: [
      {
        model: ResourceBundles,
        required: true,
        include: {
          model: AccountBundles,
          where: {
            accountId: bundleAccountId,
          },
        },
      },
    ],
    required: true,
  },
  getContentStringQuery(req, ['en'], 'program'), // empty, but adds boilerplate to allow change later
  ];
  return includeParams;
};

async function getProgramFilterParams(req) {
  const allowedPermissions = await getAllowedPermissions(
    req,
    req.user ? req.user.id : null, 'programs', req.tokenPayload,
  );
  const lifecycleClauses = [{ lifecycle: 'publish' }];
  if (allowedPermissions.programs.includes('update')) {
    lifecycleClauses.push({ lifecycle: 'review' });
    lifecycleClauses.push({ lifecycle: 'draft' });
    lifecycleClauses.push({ lifecycle: 'close' });
  } else if (allowedPermissions.programs.includes('review')) {
    lifecycleClauses.push({ lifecycle: 'review' });
  }

  return { [Op.or]: lifecycleClauses };
}

const lessonCardOrder = [
  { model: Lessons, through: 'lessonPrograms' },
  { model: LessonCards, through: 'lessonLessonCards' },
  { model: LessonLessonCards },
  'position',
  'asc',
];

const lessonOrder = [
  { model: Lessons, through: 'lessonPrograms' },
  { model: LessonPrograms },
  'position',
  'asc',
];

// Middleware to retrieve the program when an id is passed in the route
// Specifically designed to set up query results for getProgramProgress
module.exports.programById = async function (req, res, next, id) {
  const entered = Date.now();
  logger.debug(`Entering programById, progId=${id}`);
  const queryParams = {
    where: { id },
    order: [lessonOrder, lessonCardOrder],
    include: await getProgramIncludeParams(req, req.user),
  };
  try {
    const filterParams = await getProgramFilterParams(req);
    const finalQuery = {
      ...queryParams,
      where: { ...queryParams.where, ...filterParams },
    };
    const program = await Programs.findOne(finalQuery);

    if (!program) {
      const err = new Error(req.i18n.t('programs.program_load_Error', { id }));
      err.status = 404;
      throw err;
    }

    // Get content strings separately here instead of in the original find, then merge them in
    const programContentStrings = await ContentStrings.findAll({
      where: {
        contentId: program.id,
        model: 'program',
        language: getQueryLanguages(req, req.i18n.language || 'en'),
      },
    });
    program.set('contentStrings', programContentStrings);
    const lessonIds = program.lessons.map(l => l.id);
    const lContentStrings = await ContentStrings.findAll({
      where: {
        contentId: lessonIds,
        model: 'lesson',
        language: getQueryLanguages(req, req.i18n.language || 'en'),
      },
    });
    program.lessons.forEach((lesson) => {
      // Note: we lose createdAt and updatedAt when we use filter(), but that should be ok, and is better than
      // doing a separate query for each lesson's contentStrings. If only Sequelize had a way to filter data??
      const contentStrings = lContentStrings.filter(cs => cs.contentId === lesson.id);
      lesson.set('contentStrings', contentStrings);
    });

    req.program = localizeProgram(req, program, req.i18n.language, true);
    req.resource = req.program.resource; // needed for resource access checks
    next();
  } catch (err) {
    next(err);
  } finally {
    const elapsed = Date.now() - entered;
    logger.debug(`Exiting programById,${elapsed}ms.`);
  }
};

/**
 * @swagger
 * /users/{userId}/newActiveItems:
 *  get:
 *    summary: User New Active Items
 *    tags: [User]
 *    security:
 *      - JWT: []
 *    parameters:
 *      - name: userId
 *        in: path
 *        required: true
 *        schema:
 *          type: integer
 *        description: User ID
 *      - name: $limit
 *        in: query
 *        required: true
 *        schema:
 *          type: integer
 *          example: 10
 *        description: Number of records to return
 *      - name: $skip
 *        in: query
 *        required: true
 *        schema:
 *          type: integer
 *          example: 0
 *        description: Number of records to skip (pagination)
 *      - name: $includePreview
 *        in: query
 *        required: false
 *        schema:
 *          type: boolean
 *          default: true
 *        description: whether to include userLessons with sourceLifecycle preview
 *    responses:
 *      200:
 *        description: Successful operation
 *        content:
 *          application/json:
 *            schema:
 *              allOf:
 *                - $ref: '#/definitions/users'
 *                - type: object
 *                  properties:
 *                    accountFields:
 *                      type: array
 *                      items:
 *                        $ref: '#/components/schemas/accountFields'
 *                    topicIds:
 *                      type: array
 *                      items:
 *                        $ref: '#/components/schemas/topics'
 *                    accounts:
 *                      type: array
 *                      items:
 *                        $ref: '#/definitions/accounts'
 *                    groups:
 *                      type: array
 *                      items:
 *                        allOf:
 *                          - $ref: '#/components/schemas/groups'
 *                          - type: object
 *                            properties:
 *                              roleGroups:
 *                                $ref: '#/definitions/roleGroups'
 *      400:
 *        description: Bad Request
 *      401:
 *        description: Unauthorized
 *      404:
 *        description: Not Found
 *      5XX:
 *        description: Unexpected error
 */
module.exports.findActiveItemsNew = async (req, res, next) => {
  const defaults = {
    order: [['id', 'DESC']],
    limit: 500,
    offset: 0,
  };
  try {
    const model = '';
    const userId = req.requestedUser.id;
    const includePreview = req?.query?.includePreview
    const rqts = await newRestQueryToSequelize(model, userId, req.query, defaults);
    const queryParams = rqts.newQuery;
    const limit = queryParams.limit;
    const offset = queryParams.offset;
    const userAssignments = await userAssignmentDetails(userId, req.user.accountId, offset, limit, includePreview);
    const reportUsers = [];
    const userProgramLessons = {};
    for (const reportRow of userAssignments) {
      if (reportRow.type !== 'campaign') {
        const requiredMinutes = Math.floor(reportRow.requiredSeconds / 60);
        const requiredMinutesTaken = Math.floor(reportRow.requiredSecondsTaken / 60);
        const takenMinutes = Math.floor(reportRow.elapsedTimeInSeconds / 60);
        const rowStatus = formatUserStatus(reportRow);
        const reportUser = {
          id: reportRow.id,
          assignmentId: reportRow.assignmentId,
          createdAt: reportRow.createdAt,
          assigned: reportRow.assignedAt,
          completed: reportRow.completionDate,
          dueDate: reportRow.dueDate,
          campaignId: reportRow.groupAssignment ? reportRow.groupAssignment.campaignId : '',
          campaign: reportRow.groupAssignment ? reportRow.groupAssignment.campaign.name : '',
          percentComplete: reportRow.percentComplete,
          timeComplete: requiredMinutes > 0 ? `${requiredMinutesTaken} of ${requiredMinutes}` : null,
          minutesSpent: takenMinutes.toString(),
          removed: reportRow.removedAt,
          started: reportRow.startedAt,
          contentTitle: formatContentTitle(reportRow),
          resourceId: reportRow.resourceId,
          type: reportRow.type,
          assignmentStatus: rowStatus.status,
          hasCertificate: reportRow.program ? reportRow.program.hasCertificate : false,
          groupAssignment: reportRow.groupAssignment,
          lessons: reportRow.lesson,
          lessonPosition: reportRow.lessonPosition,
        };
        if (reportUser.groupAssignment && reportUser.groupAssignment.programId) {
          reportUser.lessons.lessonPosition = reportUser.lessonPosition ? reportUser.lessonPosition : 0;
          if (reportUser.groupAssignment.programId in userProgramLessons) {
            userProgramLessons[reportUser.groupAssignment.programId].push(reportUser);
          } else {
            userProgramLessons[reportUser.groupAssignment.programId] = [reportUser];
          }
        }
        const indexes = [];
        if (reportUsers.length) {
          const arrUserReport = reportUsers;
          for (let index = 0; index < arrUserReport.length; index++) {
            const element = arrUserReport[index];
            if (
              element.groupAssignment &&
              reportUser.resourceId === element.groupAssignment.programId &&
              reportUser.campaignId === element.groupAssignment.campaignId
            ) {
              indexes.push(index);
            }
          }
        }
        if (indexes.length) {
          indexes.sort((a, b) => {
            return b - a;
          });
          for (const index of indexes) {
            reportUsers.splice(index, 1);
          }
        }
        reportUsers.push(reportUser);
      }
    }
    if (reportUsers.length) {
      const userProgramsReports = reportUsers;
      for (let index = 0; index < userProgramsReports.length; index++) {
        if (userProgramLessons && userProgramsReports[index] && userProgramsReports[index].resourceId) {
          userProgramsReports[index].lessons = userProgramLessons[userProgramsReports[index].resourceId];
        }
      }
    }
    res.json({
      total: reportUsers.length,
      limit,
      skip: offset,
      data: reportUsers,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /user-lessons/assignment-learner-count/{contentId}:
 *  get:
 *    summary: Get count of learners with open assignments for a specific content item
 *    tags:
 *      - User Lessons
 *    parameters:
 *      - name: contentId
 *        in: path
 *        schema:
 *          type: integer
 *        required: true
 *        description: The ID of the program or lesson
 *      - name: type
 *        in: query
 *        schema:
 *          type: string
 *          enum: [program, lesson]
 *        required: true
 *        description: The type of content (program or lesson)
 *    security:
 *      - JWT: []
 *    produces:
 *      - application/json
 *    responses:
 *      200:
 *        description: successful operation
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              properties:
 *                count:
 *                  type: integer
 *                  description: Number of learners with open assignments for this content item
 *      400:
 *        description: Bad Request - Invalid type parameter
 *      401:
 *        description: Unauthorized
 *      5XX:
 *        description: Unexpected error
 */
module.exports.getAssignmentLearnerCount = async (req, res, next) => {
  try {
    const contentId = parseInt(req.params.contentId);
    const contentType = req.query.type;
    const accountId = req.user.accountId;

    // Validate required parameters
    if (!contentId || isNaN(contentId)) {
      const err = new Error('Invalid contentId parameter');
      err.status = 400;
      throw err;
    }

    if (!contentType || !['program', 'lesson'].includes(contentType)) {
      const err = new Error('Invalid type parameter. Must be "program" or "lesson"');
      err.status = 400;
      throw err;
    }

    // Count distinct baseAssignments that have:
    // 1. assignmentType = 'external'
    // 2. accountId = current account
    // 3. status = 'open'
    // 4. Have associated userLessons with the specified contentId and open/inProgress status
    const count = await db.baseAssignments.count({
      distinct: true,
      col: 'id',
      where: {
        accountId: accountId,
        assignmentType: 'external',
        status: 'open'
      },
      include: [
        {
          model: UserLessons,
          where: {
            resourceId: contentId,
            type: contentType,
            status: {
              [Op.in]: ['open', 'inProgress'] // Include both open and in-progress as "open assignments"
            },
            // For lessons, only include standalone assignments (not part of program assignments)
            ...(contentType === 'lesson' && { baseAssignmentParentProgramId: null })
          },
          required: true
        }
      ]
    });

    res.json({ count });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /user-lessons/close-assignments/{contentId}:
 *  post:
 *    summary: Close all assignments for a specific content item
 *    tags:
 *      - User Lessons
 *    parameters:
 *      - name: contentId
 *        in: path
 *        schema:
 *          type: integer
 *        required: true
 *        description: The ID of the program or lesson
 *      - name: type
 *        in: query
 *        schema:
 *          type: string
 *          enum: [program, lesson]
 *        required: true
 *        description: The type of content (program or lesson)
 *    security:
 *      - JWT: []
 *    produces:
 *      - application/json
 *    responses:
 *      200:
 *        description: successful operation
 *        content:
 *          application/json:
 *            schema:
 *              type: object
 *              properties:
 *                closedBaseAssignments:
 *                  type: integer
 *                  description: Number of baseAssignments closed
 *                closedUserLessons:
 *                  type: integer
 *                  description: Number of userLessons closed
 *      400:
 *        description: Bad Request - Invalid type parameter
 *      401:
 *        description: Unauthorized
 *      5XX:
 *        description: Unexpected error
 */
module.exports.closeAssignments = async (req, res, next) => {
  try {
    const contentId = parseInt(req.params.contentId);
    const contentType = req.query.type;
    const accountId = req.user.accountId;

    // Validate required parameters
    if (!contentId || isNaN(contentId)) {
      const err = new Error('Invalid contentId parameter');
      err.status = 400;
      throw err;
    }

    if (!contentType || !['program', 'lesson'].includes(contentType)) {
      const err = new Error('Invalid type parameter. Must be "program" or "lesson"');
      err.status = 400;
      throw err;
    }

    // First, find all baseAssignments that match our criteria
    const baseAssignmentsToClose = await db.baseAssignments.findAll({
      where: {
        accountId: accountId,
        assignmentType: 'external',
        status: 'open'
      },
      include: [
        {
          model: UserLessons,
          where: {
            resourceId: contentId,
            type: contentType,
            status: {
              [Op.in]: ['open', 'inProgress']
            },
            // For lessons, only include standalone assignments (not part of program assignments)
            ...(contentType === 'lesson' && { baseAssignmentParentProgramId: null })
          },
          required: true
        }
      ]
    });

    const baseAssignmentIds = baseAssignmentsToClose.map(ba => ba.id);

    let closedBaseAssignments = 0;
    let closedUserLessons = 0;

    if (baseAssignmentIds.length > 0) {
      // Close all matching baseAssignments
      const [updatedBaseAssignments] = await db.baseAssignments.update(
        { status: 'closed' },
        {
          where: {
            id: {
              [Op.in]: baseAssignmentIds
            }
          }
        }
      );
      closedBaseAssignments = updatedBaseAssignments;
      const removedAt = Date.now();
      // For programs, we need to close both the parent program userLessons
      // AND all associated lesson userLessons with the same baseAssignmentId
      if (contentType === 'program') {
        // Close all userLessons (both program and lesson types) that have these baseAssignmentIds
        const [updatedUserLessons] = await UserLessons.update(
          { status: 'closed', removalReason: 'withdrawnByAdmin', removedAt },
          {
            where: {
              baseAssignmentId: {
                [Op.in]: baseAssignmentIds
              },
              status: {
                [Op.in]: ['open', 'inProgress']
              }
            }
          }
        );
        closedUserLessons = updatedUserLessons;
      } else {
        // For lessons, only close the specific lesson userLessons that are standalone (not part of program assignments)
        const [updatedUserLessons] = await UserLessons.update(
          { status: 'closed', removalReason: 'withdrawnByAdmin', removedAt },
          {
            where: {
              baseAssignmentId: {
                [Op.in]: baseAssignmentIds
              },
              resourceId: contentId,
              type: contentType,
              baseAssignmentParentProgramId: null, // Only standalone lesson assignments
              status: {
                [Op.in]: ['open', 'inProgress']
              }
            }
          }
        );
        closedUserLessons = updatedUserLessons;
      }

      // Log event for external assignments withdrawal
      if (closedBaseAssignments > 0) {
        try {
          // Determine trackableId and trackableType based on content type
          let trackableId;
          let trackableType;

          if (contentType === 'program') {
            // For programs: trackableId = programs.id, trackableType = 'programs'
            trackableId = contentId;
            trackableType = 'programs';
          } else {
            // For lessons: trackableId = lessons.resourceId, trackableType = 'resource'
            const lesson = await Lessons.findByPk(contentId);
            trackableId = lesson ? lesson.resourceId : contentId;
            trackableType = 'resource';
          }

          // Create the withdrawal event
          await db.events.create({
            type: 'externalAssignmentsWithdrawn',
            action: 1,
            userId: req.user.id,
            trackableId,
            recipientId: null,
            trackableType,
            trackableData: {
              contentId,
              contentType,
              closedBaseAssignments,
              closedUserLessons,
              withdrawnAt: new Date(),
              withdrawnBy: req.user.id
            },
            sessionId: req.sessionId || null,
            accountId,
            sourceLifecycle: 'publish'
          });
        } catch (eventError) {
          // Log the error but don't fail the main operation
          logger.error('Failed to create withdrawal event:', eventError);
        }

        // Create audit log for systemAdmin/superAdmin using "Log Me In"
        try {
          // Check if this is a systemAdmin/superAdmin using "Log Me In" functionality
          if (req.headers.authorization) {
            const tokenPayload = jwt.decode(req.headers.authorization, publicKey, true, 'RS256');
            const { adminAccountId } = tokenPayload;

            // Only create audit log if adminAccountId exists and is an Emtrain admin account
            if (adminAccountId && EMTRAIN_ACCOUNT_IDS.includes(adminAccountId)) {
              // Determine object and objectId based on content type
              let object;
              let objectId;

              if (contentType === 'program') {
                object = 'programs';
                objectId = contentId; // programs.id
              } else {
                object = 'lessons';
                objectId = contentId; // lessons.id (not resourceId as specified, but contentId is lessons.id)
              }

              const auditLogData = {
                feature: 'external-assignments',
                action: 'withdraw',
                object,
                objectId,
                childObject: null,
                childObjectId: null,
                field: null,
                originalData: {
                  assignmentCount: closedBaseAssignments
                },
                updatedData: {
                  assignmentCount: 0 // All assignments are withdrawn/closed
                },
                userId: null, // NULL as specified - no single target user
                userAccountId: accountId // The customer account being withdrawn from
              };

              await createAuditLog(auditLogData, req);
            }
          }
        } catch (auditError) {
          // Log the error but don't fail the main operation
          logger.error('Failed to create audit log:', auditError);
        }
      }
    }

    res.json({
      closedBaseAssignments,
      closedUserLessons
    });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * components:
 *   schemas:
 *     userLessons:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         userId:
 *           type: integer
 *         resourceId:
 *           type: integer
 *         status:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         type:
 *           type: string
 *         assignmentId:
 *           type: integer
 *         startDate:
 *           type: string
 *           format: date
 *         dueDate:
 *           type: string
 *           format: date
 *         sequence:
 *           type: string
 *         sourceLifecycle:
 *           type: boolean
 *         startedAt:
 *           type: string
 *           format: date-time
 *         elapsedTimeInSeconds:
 *           type: integer
 *         percentComplete:
 *           type: number
 *           format: float
 *         completionDate:
 *           type: string
 *           format: date
 *         enrollmentId:
 *           type: integer
 *         items:
 *           type: array
 *           items: {}
 *         contentStrings:
 *           type: array
 *           items: {}
 */
