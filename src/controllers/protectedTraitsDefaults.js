const db = require('../db');

const { accountAllowsMT } = require('../services/utils/accountUtils');

const ProtectedTraitsDefaults = db.protectedTraitsDefaults;
const ProtectedTraits = db.protectedTraits;
const Op = db.Sequelize.Op;

/**
 * @openapi
 * /protected-traits-defaults:
 *   get:
 *     summary: View question by questionId
 *     tags:
 *       - Protected Traits Defaults
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: lang
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *         example: en
 *       - name: allowMT
 *         in: query
 *         required: false
 *         schema:
 *           type: boolean
 *         example: true
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records.
 *                 data:
 *                   type: array
 *                   description: Array of protectedTraitsDefaults object.
 *                   items:
 *                     $ref: '#/components/schemas/protectedTraitsDefaults'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       default:
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  try {
    const language = req.query.lang || 'en';
    const mapType = req.query.mapType || '';
    const allowMT = !(req.query.allowMT && req.query.allowMT === 'false'); // default to true, must be set to "false" to disallow
    const mtOk = allowMT && accountAllowsMT(req);

    const params = {
      order: [['language', 'ASC'], ['abbr', 'ASC']],
      where: {
        language,
      },
      raw: true,
    };
    if (mtOk && language !== 'en') {
      params.where.language = {
        [Op.in]: ['en', language, `${language}-mt`, `${language}-act`],
      };
    }
    if (mapType) {
      params.where.mapType = mapType;
    }

    let protectedTraitsDefaults = await ProtectedTraitsDefaults.findAll(params);
    if (mtOk && language !== 'en') {
      // use either machine language or human language (all or nothing here and only if they don't exist
      const human = protectedTraitsDefaults.some(ptd => ptd.language === language);
      if (!human) {
        const englishRecords = protectedTraitsDefaults.filter(ptd => ptd.language === 'en');
        const machineRecords = protectedTraitsDefaults.filter(ptd => ptd.language !== 'en');
        const updatedMachineRecords = machineRecords.map((mr) => {
          return {
            ...mr,
            language,
          };
        });
        protectedTraitsDefaults = [...englishRecords, ...updatedMachineRecords];
      } else {
        protectedTraitsDefaults = protectedTraitsDefaults.filter(ptd => ptd.language !== `${language}-mt` && ptd.language !== `${language}-act`);
      }
    }
    res.json({ total: protectedTraitsDefaults.length, data: protectedTraitsDefaults });
  } catch (err) {
    next(err);
  }
};

module.exports.update = async (req, res, next) => {
  const modifiedProtectedTraitsDefault = req.body;
  try {
    const country = req.query.country.toUpperCase();
    const mapType = req.query.mapType;
    if (!mapType || (mapType && !['protected_traits', 'wage_and_hour'].includes(mapType))) {
      const err = new Error('Map type is required and must be protected_traits or wage_and_hour');
      err.status = 401;
      throw err;
    }
    if (req.query.newLanguage) {
      const newLanguageString = req.query.newLanguage.toLowerCase();
      const englishTraitsDefaults = await ProtectedTraitsDefaults.findAll({
        attributes: [
          'abbr',
        ],
        where: {
          language: 'en',
          country,
          mapType,
        },
        order: [['abbr', 'ASC']],
      });
      const newLanguageTraitsDefaults = englishTraitsDefaults.map((nextTraitsDefault) => {
        const nextNewLanguageTraitsDefault = {
          abbr: nextTraitsDefault.abbr,
          language: newLanguageString,
          traits: '',
          country,
          mapType,
        };
        return nextNewLanguageTraitsDefault;
      });
      await ProtectedTraitsDefaults.bulkCreate(newLanguageTraitsDefaults, { validate: true });

      // Now we also need to check if there are any lesson cards in the database for this new language.
      const traitsInserts = [];
      const traitsQuery =
        'SELECT lessonCards.id, supportedLanguages.language ' +
        'FROM lessonCards, lessonLessonCards, supportedLanguages ' +
        'WHERE lessonCards.id = lessonLessonCards.lessonCardId ' +
        'AND lessonLessonCards.lessonId = supportedLanguages.langSupportableId ' +
        'AND supportedLanguages.langSupportable = "lesson" AND supportedLanguages.language = ? ' +
        'AND lessonCards.cardType = "statesMap"' +
        `AND lessonCards.list1 = "${country}"` +
        `AND lessonCards.list2 = "${mapType}"`;
      await db.sequelize.query(traitsQuery, {
        replacements: [newLanguageString],
        type: db.sequelize.QueryTypes.SELECT,
        raw: true,
      }).then((lessonCards) => {
        lessonCards.forEach((card) => {
          newLanguageTraitsDefaults.forEach((nextTraitsDefault) => {
            const insert = {
              lessonCardId: card.id,
              language: card.language,
              abbr: nextTraitsDefault.abbr,
              traits: '',
              mapType,
            };
            traitsInserts.push(insert);
          });
        });
        if (traitsInserts.length > 0) {
          return ProtectedTraits.bulkCreate(traitsInserts, { validate: true });
        }
        return null;
      });
    } else {
      let promises = [];
      for (const protectedTraitsDefault of modifiedProtectedTraitsDefault) {
        promises.push(ProtectedTraitsDefaults.update(
          { traits: protectedTraitsDefault.traits },
          {
            where: {
              id: protectedTraitsDefault.id,
            },
          },
        ));
        if (promises.length > 20) {
          // clear promises every 20
          await Promise.all(promises);
          promises = [];
        }
      }
      await Promise.all(promises);
      promises = [];

      if (req.query && req.query.updateLessonCards && req.query.updateLessonCards === 'true') {
        for (const protectedTraitsDefault of modifiedProtectedTraitsDefault) {
          promises.push(ProtectedTraits.update(
            { traits: protectedTraitsDefault.traits },
            {
              where: {
                abbr: protectedTraitsDefault.abbr,
                language: protectedTraitsDefault.language,
                isModified: false,
                mapType,
              },
            },
          ));
          if (promises.length > 20) {
            // clear promises every 20
            await Promise.all(promises);
            promises = [];
          }
        }
        await Promise.all(promises);
      }
    }
    const updatedProtectedTraitsDefaults = await ProtectedTraitsDefaults.findAll({
      where: { country, mapType },
      order: [['language', 'ASC'], ['abbr', 'ASC']],
    });
    res.json({ total: updatedProtectedTraitsDefaults.length, data: updatedProtectedTraitsDefaults });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * components:
 *   schemas:
 *     protectedTraitsDefaults:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         language:
 *           type: string
 *         abbr:
 *           type: string
 *         traits:
 *           type: integer
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         country:
 *           type: string
 *         mapType:
 *           type: string
 */