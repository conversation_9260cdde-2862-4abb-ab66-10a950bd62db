const db = require('../db');
const logger = require('../logger');

const LastViewedLessonCards = db.lastViewedLessonCards;
const ViewLessonCardEvents = db.viewLessonCardEvents;
const LessonLessonCards = db.lessonLessonCards;
const Op = db.Sequelize.Op;

/**
 * getLastViewedLessonCard:
 *
 */
/**
 * @swagger
 * /users/{userId1}/lastViewedLessonCard/{lessonId}:
 *   get:
 *     summary: Check Lesson Enrollment Completion
 *     tags: [User]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: userId1
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: lessonId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: enrollmentId
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *       - name: sourceLifecycle
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           enum: [publish, draft]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/lessonLessonCards'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

module.exports.getLastViewedLessonCard = async (req, res, next) => {
  try {
    const userId = parseInt(req.params.userId1);
    const lessonId = parseInt(req.params.lessonId);
    const sourceLifecycle = req.query.sourceLifecycle || 'publish';
    let enrollmentId = -1;

    if (req.query.enrollmentId) {
      const parsedNum = parseInt(req.query.enrollmentId);
      if (!Number.isNaN(parsedNum)) {
        enrollmentId = parsedNum;
      }
    }
    // You should only get your own data
    if (userId !== req.user.id) {
      const err = new Error('Can only get last viewed lessonCard for logged in user');
      err.status = 401;
      throw err;
    }

    let lastViewedLessonCard = await LastViewedLessonCards.findOne({
      where: {
        userId,
        lessonId,
        enrollmentId,
        sourceLifecycle,
      },
    });

    // If we didn't find it, we need to go old school and see if it's in the events table
    // and then update the lastViewedLessonCards table for the next time if the event is there.
    // Eventually we may not want to query events, but in that case we'd need a migration to set up the lastViewed table
    // which is probably a long running task

    // first check to see if any cards have been added to the lesson after it was published
    const addedLessonCardsCount = await LessonLessonCards.count({ where: { lessonId, dateAdded: { [Op.ne]: null } } });

    let lessonCardId = null;
    if (addedLessonCardsCount === 0 && !lastViewedLessonCard) {
      const event = await ViewLessonCardEvents.findOne({
        where: {
          userId,
          type: 'view',
          lessonId,
          enrollmentId,
          sourceLifecycle,
        },
        order: [['updatedAt', 'DESC']],
      });
      if (event) {
        logger.info(`Get getLastViewedLessonCard, found event=${event.id}`);
        lessonCardId = event.lessonCardId;
        lastViewedLessonCard = await LastViewedLessonCards.create({
          userId,
          lessonId,
          lessonCardId,
          enrollmentId,
          sourceLifecycle,
        });
      }
    }

    res.json(lastViewedLessonCard || { userId, lessonId, lessonCardId });
  } catch (err) {
    logger.error('getLastViewedLessonCard error: %j', err);
    next(err);
  }
};
