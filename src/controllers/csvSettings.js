const logger = require('../logger');
const db = require('../db');
const acl = require('../services/acl/acl');

const CsvSetting = db.csvSettings;
const CsvImport = db.csvImports;
const CsvColumn = db.csvColumns;
const Account = db.accounts;

const resetColumns = async (columns, csvSettingId) => {
  // Replace all the column info (assume this is the complete set)
  if (columns) {
    await CsvColumn.destroy({
      where: {
        csvSettingId,
      },
    });
    const columnData = columns.map((c) => {
      return {
        csvSettingId,
        columnNumber: c.columnNumber,
        userTableColumn: c.userTableColumn,
        shouldImport: c.userTableColumn && c.shouldImport,
        isKeyColumn: c.isKeyColumn,
      };
    });
    await CsvColumn.bulkCreate(columnData, { validate: true });
  }
};

module.exports.create = async (req, res, next) => {
  try {
    const accountId = req.user.accountId;
    const columns = req.body.columns;
    delete req.body.columns;
    // See if we already got one. We're only allowing one right now
    let csvSettings = await CsvSetting.findOne({
      where: {
        accountId,
      },
    });
    if (!csvSettings) {
      // if there isn't a settings record, create it.
      const settings = {
        ...req.body,
        accountId,
      };
      csvSettings = await CsvSetting.create(settings);
    }
    await resetColumns(columns, csvSettings.id);
    const csvSettingsWithIncludes = await CsvSetting.findByPk(csvSettings.id, {
      include: [
        {
          model: CsvImport,
        },
        {
          model: CsvColumn,
        },
        {
          model: Account,
        },
      ],
    });
    res.json(csvSettingsWithIncludes);
  } catch (err) {
    logger.error('CSV create settings controller error handler: %j', err);
    next(err);
  }
};

module.exports.list = async (req, res, next) => {
  try {
    const allowedPermissions = await acl.getAllowedPermissions(req, req.user.id, 'accounts', req.tokenPayload);

    // limit to only one account, and if the caller is asking for a different account, make sure it's ok.
    // only generalized 'read' permission allows cross account data to be included
    let accountId = req.user.accountId;
    if (req.query && req.query.accountId) {
      if (allowedPermissions.accounts.includes('read')) {
        accountId = req.query.accountId;
      }
    }
    // only support one setting object for now, so no paging
    const csvSettings = await CsvSetting.findOne({
      where: {
        accountId,
      },
      include: [
        {
          model: CsvImport,
        },
        {
          model: CsvColumn,
        },
        {
          model: Account,
        },
      ],
      order: [
        [CsvColumn, 'columnNumber', 'ASC'],
      ],
    });
    res.json([csvSettings]);
  } catch (err) {
    logger.error('CSV list settings controller error handler: %j', err);
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  try {
    res.json(req.csvSetting);
  } catch (err) {
    logger.error('CSV read settings controller error handler: %j', err);
    next(err);
  }
};

module.exports.update = async (req, res, next) => {
  try {
    const columns = req.body.columns;
    delete req.body.columns;
    delete req.body.accountId;
    const updated = await req.csvSetting.update(req.body);
    await resetColumns(columns, updated.id);
    const updatedWithIncludes = await CsvSetting.findByPk(updated.id, {
      include: [
        {
          model: CsvImport,
        },
        {
          model: CsvColumn,
        },
        {
          model: Account,
        },
      ],
      order: [
        [CsvColumn, 'columnNumber', 'ASC'],
      ],
    });
    res.json(updatedWithIncludes);
  } catch (err) {
    logger.error('CSV update settings controller error handler: %j', err);
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  try {
    const csvSettingId = req.csvSetting.id;
    const deleted = await req.csvSetting.destroy();
    await CsvColumn.destroy({
      where: {
        csvSettingId,
      },
    });
    // Don't delete the import record or error records?
    res.json(deleted);
  } catch (err) {
    logger.error('CSV delete settings controller error handler: %j', err);
    next(err);
  }
};

module.exports.settingById = async (req, res, next, id) => {
  try {
    const allowedPermissions = await acl.getAllowedPermissions(
      req,
      req.user ? req.user.id : null, 'accounts', req.tokenPayload,
    );
    let accountId = req.user.accountId;
    if (req.query && req.query.accountId) {
      if (allowedPermissions.accounts.includes('read')) {
        accountId = req.query.accountId;
      }
    }
    const csvSetting = await CsvSetting.findOne({
      where: {
        accountId,
        id,
      },
      include: [
        {
          model: CsvImport,
        },
        {
          model: CsvColumn,
        },
        {
          model: Account,
        },
      ],
      order: [
        [CsvColumn, 'columnNumber', 'ASC'],
      ],
    });
    if (!csvSetting) {
      const err = new Error(req.i18n.t('csvImport.csvSetting_load_Error', { id }));
      err.status = 404;
      throw err;
    }

    req.csvSetting = csvSetting;
    next();
  } catch (err) {
    next(err);
  }
};
