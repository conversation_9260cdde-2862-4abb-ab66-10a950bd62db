/* eslint-disable no-param-reassign */
const _ = require('lodash');
const db = require('../db');

const config = require('../config/config');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { restSortToSequelize } = require('../services/utils/resourceUtils');
const { Op } = require('sequelize');

const Categories = db.categories;
const Concepts = db.concepts;

/**
 * converts a rest query from the client to sequelize params
 */
const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize(_.omit(
    query,
    ['$limit', '$skip', '$sort'],
  ));
  const newQuery = {
    ...defaults,
  };
  // eslint-disable-next-line no-undef
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    const paginateMax = 700;
    const limit = parseInt(query.$limit);
    newQuery.limit = Math.min(paginateMax, limit);
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  return newQuery;
};

const getNextPriorityCategory = async () => {
  const lastCategory = await Categories.count();
  return lastCategory + 1;
};

const checkUniqueName = async (data, id = null) => {
  const query = {
    name: data.name,
  };
  if (id) {
    query.id = {
      [Op.ne]: id,
    };
  }
  const categoryCount = await Categories.count({
    where: query,
  });
  if (categoryCount) {
    const err = new Error('Category Name must be unique');
    err.status = 400;
    throw err;
  }
};

/**
 * @openapi
 * /categories:
 *   post:
 *     summary: Create Category
 *     tags:
 *       - Categories
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCategory'
 *           examples:
 *             example:
 *               summary: Example category
 *               value:
 *                 name: "New Category"
 *     responses:
 *       200:
 *         description: Category added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Category added successfully"
 *
 * components:
 *   schemas:
 *     CreateCategory:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           description: The category name
 */
module.exports.create = async (req, res, next) => {
  const data = req.body;
  try {
    await checkUniqueName(data);
    // get the next priority
    data.priority = await getNextPriorityCategory();
    const newCategory = await Categories.create(data);
    res.json(newCategory);
  } catch (err) {
    if (err.message === 'Validation error') {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /categories/{categoryId}:
 *   patch:
 *     summary: Update Category
 *     tags:
 *       - Categories
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: categoryId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the existing category to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateCategory'
 *           example:
 *             name: "Updated Category Name"
 *     responses:
 *       200:
 *         description: Category updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Category updated successfully
 *
 * components:
 *   schemas:
 *     UpdateCategory:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           description: The category name
 */
module.exports.patch = async (req, res, next) => {
  try {
    const data = req.body;
    const { categoryId } = req.params;
    await checkUniqueName(data, categoryId);
    await Categories.update(data, {
      where: {
        id: categoryId,
      },
    });
    const updatedCategory = await Categories.findByPk(categoryId);
    res.json(updatedCategory);
  } catch (err) {
    if (err.message === 'Validation error') {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /categories:
 *   get:
 *     summary: Fetch all the Category values
 *     tags:
 *       - Categories
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successfully fetched all Categories
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Category'
 *
 * components:
 *   schemas:
 *     Category:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: Category ID
 *         name:
 *           type: string
 *           description: Category name
 *       example:
 *         id: "abc123"
 *         name: "Science"
 */
module.exports.list = async (req, res, next) => {
  try {
    const defaults = {
      order: [['updatedAt', 'DESC']],
      limit: config.paginate.default,
      offset: 0,
    };
    const queryParams = restQueryToSequelize(req.query, defaults);
    queryParams.order = [[...queryParams.order]];
    const finalQuery = {
      ...queryParams,
      where: queryParams.where,
      include: [{
        model: Concepts,
        as: 'concepts',
        attributes: ['id'],
      }],
      group: ['categories.id'],
    };
    const pagedResult = {
      limit: queryParams.limit,
      skip: queryParams.offset,
    };
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
      include: _.pick(finalQuery, ['include']).include,
      distinct: true,
    };
    const count = await Categories.count(countQuery);
    const data = await Categories.findAll(finalQuery);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /categories/{categoryId}:
 *   get:
 *     summary: Fetch category by categoryId
 *     tags:
 *       - Categories
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: categoryId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: categoryId of the existing Category
 *     responses:
 *       200:
 *         description: Successfully fetched the particular category based on its ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 name:
 *                   type: string
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *       404:
 *         description: Category not found
 */
module.exports.read = async (req, res, next) => {
  try {
    const categoryData = req.category.get({ plain: true });
    res.json(categoryData);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /categories/{categoryId}:
 *   delete:
 *     summary: Delete an existing category by ID
 *     tags:
 *       - Categories
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: categoryId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the category to delete
 *     responses:
 *       200:
 *         description: Successfully deleted category
 *       404:
 *         description: Category not found
 */
module.exports.delete = async (req, res, next) => {
  try {
    const { priority } = req.category;
    const { categoryId } = req.params;
    const data = await Categories.destroy({
      where: {
        id: categoryId,
      },
    });
    // reorder of rank
    Categories.decrement('priority', { by: 1, where: { priority: { [Op.gt]: priority } } });
    res.json(data);
  } catch (err) {
    if (err.message.includes('Cannot delete or update a parent row')) {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /categories/rankCategories:
 *   post:
 *     summary: Update categories rank
 *     tags:
 *       - Categories
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       description: The model for the parameters is described below
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateCategoryRank'
 *           example:
 *             categories:
 *               - id: 16
 *                 priority: 1
 *     responses:
 *       200:
 *         description: Category updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   nullable: true
 *
 * components:
 *   schemas:
 *     UpdateCategoryRank:
 *       type: object
 *       required:
 *         - categories
 *       properties:
 *         categories:
 *           type: array
 *           items:
 *             type: object
 *             required:
 *               - id
 *               - priority
 *             properties:
 *               id:
 *                 type: integer
 *               priority:
 *                 type: integer
 */
module.exports.rankCategories = async (req, res, next) => {
  try {
    const data = req.body.categories;
    if (data.length) {
      const promise = [];
      for (const category of data) {
        promise.push(Categories.update({ priority: category.priority }, {
          where: {
            id: category.id,
          },
        }));
      }
      await Promise.all(promise);
    }
    res.json(data);
  } catch (err) {
    if (err.message === 'Validation error') {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

// Middleware to retrieve the Category when an id is passed in the route
module.exports.categoryById = async function (req, res, next, id) {
  const queryParams = {
    where: { id },
  };
  try {
    const finalQuery = {
      ...queryParams,
      where: { ...queryParams.where },
    };
    const category = await Categories.findOne(finalQuery);
    if (!category) {
      const err = new Error(req.i18n.t('category.category_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.category = category;
    next();
  } catch (err) {
    next(err);
  }
};
