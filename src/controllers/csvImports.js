const fs = require('fs');
const stream = require('stream');
const path = require('path');
const _ = require('lodash');
const { S3Client, DeleteObjectCommand } = require("@aws-sdk/client-s3");
const { Upload } = require("@aws-sdk/lib-storage");
const logger = require('../logger');
const config = require('../config/config');
const db = require('../db');
const acl = require('../services/acl/acl');
const { restSortToSequelize } = require('../services/utils/resourceUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { getCsvS3Bucket } = require('../services/utils/fileUtils');
const { importCsv, readCsv } = require('../services/csv/csvimport');

const Accounts = db.accounts;
const CsvSetting = db.csvSettings;
const CsvImport = db.csvImports;
const CsvError = db.csvErrors;
const Events = db.events;
const LINE_FEED = '\n'.charCodeAt(0);

const s3Client = new S3Client({
  region: "us-east-1",
  credentials: {
    accessKeyId: config.s3.accessKeyId,
    secretAccessKey: config.s3.secretAccessKey,
  },
});

const isLocalTestMode = () => {
  return (process.env.NODE_ENV === 'test' && (!config.s3.secretAccessKey || !config.s3.accessKeyId));
};

const getLocalTestFilename = (filename) => {
  const uploadFolder = '../../testuploads/';
  const uploadPath = path.join(__dirname, uploadFolder);
  return (path.join(uploadPath, filename));
};

const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize({
    ..._.omit(query, [
      '$limit', '$skip', '$sort', 'accountId', 'allAccounts', 'account.id', 'account.name',
    ]),
  });

  const accountWhere = restOperatorsToSequelize({ ..._.pick(query, ['account.id', 'account.name']) });
  const newQuery = {
    ...defaults,
  };
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  return { queryParams: newQuery, accountWhere };
};

const loadImport = async (id) => {
  return CsvImport.findByPk(id, {
    include: [
      {
        model: CsvError,
      },
      {
        model: CsvSetting,
      },
    ],
  });
};

const checkPermissions = async (req) => {
  const allowedPermissions = await acl.getAllowedPermissions(req, req.user.id, 'accounts', req.tokenPayload);
  const perms = allowedPermissions.accounts;
  const allAccounts = !!(req.query.allAccounts && req.query.allAccounts === 'true');

  let allowed = true;
  if (allAccounts) {
    allowed = perms.includes('read');
  } else {
    const requestorAccountId = req.user.accountId;
    const targetAccountId = req.query.accountId || req.user.accountId;

    allowed = perms.includes('read') ||
      ((targetAccountId === requestorAccountId) && perms.includes('readAccount'));
  }

  if (!allowed) {
    const err = new Error(req.i18n.t('csvImport.csvImport_no_permissions_Error'));
    err.status = 401;
    throw err;
  }
};

module.exports.create = async (req, res, next) => {
  try {
    const accountId = req.user.accountId;
    // Find the settings record based on the accountId.
    let csvSettings = await CsvSetting.findOne({
      where: {
        accountId,
      },
    });
    if (!csvSettings) {
      // if there isn't a settings record, create a default one to start.
      const settings = {
        accountId,
      };
      csvSettings = await CsvSetting.create(settings);
    }

    let totalRecords = 0;
    let fileSize = 0;
    let avResult = null;

    req.busboy.on('file', async (fieldname, file, origFileInfo) => {
      // generate a random file name - from the node-temp library
      const now = new Date();
      const filename = [
        now.getFullYear(), now.getMonth(), now.getDate(),
        '-',
        process.pid,
        '-',
        ((Math.random() * 0x100000000) + 1).toString(36),
        '.csv',
      ].join('');

      const getWriteStream = () => {
        if (isLocalTestMode()) {
          return {
            fstream: fs.createWriteStream(getLocalTestFilename(filename)),
            uploadPromise: null,
          };
        }
        // Create a write stream of the new file
        const s3UploadStream = ({ Bucket, Key }) => {
          const pass = new stream.PassThrough();

          const upload = new Upload({
            client: s3Client,
            params: {
              Bucket,
              Key,
              Body: pass,
              ServerSideEncryption: "AES256",
            },
          });
          return {
            fstream: pass,
            uploadPromise: upload.done(),
          };
        };
        const { bucket, keyPrefix } = getCsvS3Bucket(accountId, 'csv-uploads');
        return s3UploadStream({
          Bucket: bucket,
          Key: `${keyPrefix}${filename}`,
        });
      };

      const { fstream, uploadPromise } = getWriteStream();
      file
        .on('data', (buffer) => {
          for (let i = 0; i < buffer.length; ++i) {
            if (buffer[i] === LINE_FEED) {
              totalRecords++; // eslint-disable-line no-plusplus
            }
          }
          fileSize += buffer.length;
        });
      // AV scanning
      const clamScanner = req.app.get('clamScan');
      if (clamScanner) {
        const av = clamScanner.passthrough();
        file.pipe(av).pipe(fstream);
        av.on('scan-complete', (result) => {
          avResult = result;
        });
      } else {
        file.pipe(fstream);
      }

      const onStreamClose = async (filePath) => {
        try {
          logger.info(`Upload of '${filePath}' finished`);

          // check for viral infestations
          if (avResult && avResult.is_infected) {
            const { bucket, keyPrefix } = getCsvS3Bucket(accountId, 'csv-uploads');
            const command = new DeleteObjectCommand({
                Bucket: bucket,
                Key: `${keyPrefix}${filename}`,
            });
            await s3Client.send(command);
            logger.verbose('removed infected file', filename);
            // log & throw an error
            logger.error(`Virus detected: ${avResult.viruses}`);
            const err = new Error(`File is infected with the following viruses: ${avResult.viruses}`);
            err.status = 400;
            throw err;
          }
          // Create the import record with the filename
          const importData = {
            csvSettingId: csvSettings.id,
            fileLocation: 'S3',
            filePath,
            fileSize,
            origFilename: origFileInfo.filename,
            totalRecords,
          };
          const importRecord = await CsvImport.create(importData);
          // TODO: For now, return when the file is done uploading, but really we want to return before it's done and then check status
          res.json(await loadImport(importRecord.id));
        } catch (err) {
          logger.error('CSV finished uploading, other error: %j', err);
          next(err);
        }
      };

      // On finish of the upload... The S3 stuff is handled slightly differently
      if (isLocalTestMode()) {
        fstream.on('close', async () => {
          onStreamClose(getLocalTestFilename(filename));
        });
      } else {
        const result = await uploadPromise;
        onStreamClose(result.Location);
      }
    });
    req.busboy.on('field', (fieldname, val, fieldnameTruncated, valTruncated, encoding, mimetype) => { // eslint-disable-line no-unused-vars
      // This is where we get the other fields of the form type.
      // console.log('field:', fieldname, ':', val);
    });
    req.busboy.on('finish', () => {
      // console.log('Busboy finish, Done parsing upload form!');
    });
    req.busboy.on('error', (err) => {
      logger.error('Busboy Error: %j', err);
      next(err);
    });
    req.pipe(req.busboy); // Pipe it through busboy
  } catch (err) {
    logger.error('CSV create controller error handler: %j', err);
    next(err);
  }
};

module.exports.startImport = async (req, res, next) => {
  try {
    // Big long job will return to the client before complete if no errors on job startup,
    // so let the function return from the API call (res.json).
    // We still want to 'await' here so that the catch block will handle all errors.
    await importCsv(res, req.i18n, req.user.accountId, req.csvImport);
  } catch (err) {
    logger.error('CSV Start Import error: %j', err);
    next(err);
  }
};

module.exports.scheduleImport = async (req, res, next) => {
  try {
    // Schedule an import to be handled by the scheduled jobs later
    // Only schedule jobs that have notstarted status
    if (req.csvImport.status === 'notstarted') {
      const status = 'scheduled';
      await CsvImport.update(
        {
          status,
        },
        {
          where: {
            id: req.csvImport.id,
          },
          fields: ['status'],
        },
      );
    }
    res.json(await loadImport(req.csvImport.id));
  } catch (err) {
    logger.error('Schedule CSV Import error: %j', err);
    next(err);
  }
};

module.exports.status = async (req, res, next) => {
  try {
    res.json(req.csvImport);
  } catch (err) {
    logger.error('CSV Import get status error: %j', err);
    next(err);
  }
};

module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [
      // this trickery sorts nulls above non-null importStartedAt values
      [db.Sequelize.fn('ISNULL', db.Sequelize.col('importStartedAt')), 'DESC'],
      ['importStartedAt', 'DESC'],
      ['priority', 'ASC'],
      ['fileSize', 'ASC'],
    ],
    limit: config.paginate.default,
    offset: 0,
  };
  const { queryParams, accountWhere } = restQueryToSequelize(req.query, defaults);

  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };

  try {
    await checkPermissions(req);
    const allAccounts = !!(req.query.allAccounts && req.query.allAccounts === 'true');
    const accountId = req.query.accountId || req.user.accountId;

    const finalQuery = {
      ...queryParams,
      include: [
        {
          model: CsvSetting,
          include: [{
            model: Accounts,
            attributes: ['id', 'name'],
            required: true,
          }],
          required: true,
        },
        {
          model: CsvError,
        },
      ],
    };

    if (!finalQuery.include[0].include[0].where) {
      const where = {};
      Object.assign(finalQuery.include[0].include[0], { where });
    }

    const accountClause = finalQuery.include[0].include[0].where;
    const accountNameTerm = _.get(accountWhere, 'account.name');
    const accountIdTerm = _.get(accountWhere, 'account.id');

    if (allAccounts) {
      if (accountNameTerm) {
        accountClause.name = accountNameTerm;
      }
      if (accountIdTerm) {
        accountClause.id = accountIdTerm;
      }
    } else if (finalQuery.include[0].where) {
      finalQuery.include[0].where.accountId = accountId;
    } else {
      const where = { accountId };
      Object.assign(finalQuery.include[0], { where });
    }

    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
      include: _.pick(finalQuery, ['include']).include,
      distinct: true,
    };
    const count = await CsvImport.count(countQuery);
    // We want the error records to be sorted by row, so tweak the query.
    finalQuery.order = [
      ...finalQuery.order,
      [CsvError, 'row', 'ASC'],
    ];
    const data = await CsvImport.findAll(finalQuery);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    logger.error('CSV Import list error: %j', err);
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  try {
    const { csvImport } = req;
    const maxRecords = (req.query && req.query.maxRecords && parseInt(req.query.maxRecords)) || 10;

    const records = await readCsv(csvImport, csvImport.csvSetting.accountId, maxRecords);
    const flattenedRecord = csvImport.get({ plain: true });
    flattenedRecord.records = records;
    res.json(flattenedRecord);
  } catch (err) {
    logger.error('CSV Import read error: %j', err);
    next(err);
  }
};

module.exports.update = async (req, res, next) => {
  try {
    const updateData = {};
    if (req.body.fileLocation) {
      updateData.fileLocation = req.body.fileLocation;
    }
    if (req.body.filePath) {
      updateData.filePath = req.body.filePath;
    }
    const updatedImport = await req.csvImport.update(updateData);
    res.json(await loadImport(updatedImport.id));
  } catch (err) {
    logger.error('CSV Import update error: %j', err);
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  try {
    const deleted = await req.csvImport.destroy();
    res.json(deleted);
  } catch (err) {
    logger.error('CSV Import delete error: %j', err);
    next(err);
  }
};

module.exports.incrementPrority = async (req, res, next) => {
  try {
    const job = req.csvImport;
    if (job.status === 'notstarted' || job.status === 'scheduled') {
      const oldPriority = job.priority;
      const newPriority = oldPriority + 1;
      const updatedImport = await job.update({ priority: newPriority });
      // emit event
      await Events.create({
        action: true,
        type: 'cvsImportPriorityIncremented',
        userId: req.user.id,
        trackableId: job.id,
        trackableType: 'csvImport',
        trackableData: {
          oldPriority,
          newPriority,
        },
        accountId: req.user.accountId,
        sessionId: req.sessionId,
      });
      res.json(await loadImport(updatedImport.id));
    } else {
      res.json(job);
    }
  } catch (err) {
    logger.error('CSV Import incrementPrority error: %j', err);
    next(err);
  }
};

module.exports.decrementPrority = async (req, res, next) => {
  try {
    const job = req.csvImport;
    if (job.status === 'notstarted' || job.status === 'scheduled') {
      const oldPriority = job.priority;
      const newPriority = Math.max(oldPriority - 1, 0);
      const updatedImport = await job.update({ priority: newPriority });
      // emit event
      await Events.create({
        action: false,
        type: 'cvsImportPriorityDecremented',
        userId: req.user.id,
        trackableId: job.id,
        trackableType: 'csvImport',
        trackableData: {
          oldPriority,
          newPriority,
        },
        accountId: req.user.accountId,
        sessionId: req.sessionId,
      });
      res.json(await loadImport(updatedImport.id));
    } else {
      res.json(job);
    }
  } catch (err) {
    logger.error('CSV Import decrementPrority error: %j', err);
    next(err);
  }
};

module.exports.cancelUpload = async (req, res, next) => {
  try {
    const job = req.csvImport;
    if (job.status === 'importing') {
      const updatedImport = await job.update({ status: 'failed' });
      res.json(await loadImport(updatedImport.id));
    } else {
      res.json(job);
    }
  } catch (err) {
    logger.error('CSV Cance Upload error: %j', err);
    next(err);
  }
};

module.exports.importById = async (req, res, next, id) => {
  try {
    const allowedPermissions = await acl.getAllowedPermissions(
      req,
      req.user ? req.user.id : null, 'accounts', req.tokenPayload,
    );
    const csvImport = await loadImport(id);

    if (!csvImport || !csvImport.csvSetting ||
      (csvImport.csvSetting.accountId !== req.user.accountId && !allowedPermissions.accounts.includes('read'))
    ) {
      const err = new Error(req.i18n.t('csvImport.csvImport_load_Error', { id }));
      err.status = 404;
      throw err;
    }

    req.csvImport = csvImport;
    next();
  } catch (err) {
    next(err);
  }
};
