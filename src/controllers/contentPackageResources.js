const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { restSortToSequelize, addProgramChildAccess,
  addResourceAccessToBundle } = require('../services/utils/resourceUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { getAccountBundle } = require('../services/utils/resourceUtils');

const ContentPackageResources = db.contentPackageResources;
const AccountContentPackages = db.accountContentPackages;
const Resources = db.resources;
const Programs = db.programs;
const Lessons = db.lessons;
const Accounts = db.accounts;
const Op = db.Sequelize.Op;

const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize({
    ..._.omit(query, [
      '$limit', '$skip', '$sort',
    ]),
  });
  const includeParams = [{
    model: Resources,
  }];
  const newQuery = {
    ...defaults,
    where: { ...defaults.where, ...whereClause },
    include: includeParams,
  };

  // override defaults for limit, skip, and sort if passed in by caller
  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }
  return newQuery;
};

const getResourceObject = (resource, programs, lessons) => {
  if (resource.digestable === 'lessons') {
    return lessons.find(l => l.resourceId === resource.id);
  }
  if (resource.digestable === 'programs') {
    return programs.find(p => p.resourceId === resource.id);
  }
  return null;
};

/**
 * @swagger
 * /content-packages/{contentPackageId}/resources?$limit={limit}:
 *   get:
 *     summary: List Lessons And Programs Added To Package
 *     tags: [Package]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: contentPackageId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: $limit
 *         in: query
 *         required: true
 *         description: Limit (integer) for number of records
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records.
 *                 limit:
 *                   type: integer
 *                   description: Limit records per page.
 *                 data:
 *                   type: array
 *                   description: Array of report data objects.
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/contentPackageResources'
 *                       - type: object
 *                         properties:
 *                           contentPackage:
 *                             $ref: '#/components/schemas/contentPackages'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     contentPackageResources:
 *       type: object
 *       description: Resource item in content package
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *     contentPackages:
 *       type: object
 *       description: Package details
 *       properties:
 *         id:
 *           type: integer
 *         title:
 *           type: string
 */
module.exports.list = async (req, res, next) => {
  try {
    const contentPackageId = parseInt(req.params.contentPackageId);
    const defaults = {
      order: [['updatedAt', 'DESC']],
      limit: config.paginate.default,
      offset: 0,
      where: {
        contentPackageId,
      },
    };
    const finalQuery = restQueryToSequelize(req.query, defaults);

    const contentPackageResources = await ContentPackageResources.findAll(finalQuery);
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
    };
    const count = await ContentPackageResources.count(countQuery);

    // We have resource ids, but we need the digestable for each of the resources
    const programResourceIds = contentPackageResources
      .filter(cpr => cpr.resource.digestable === 'programs')
      .map(cpr => cpr.resourceId);
    const lessonResourceIds = contentPackageResources
      .filter(cpr => cpr.resource.digestable === 'lessons')
      .map(cpr => cpr.resourceId);

    let programs = [];
    if (programResourceIds && programResourceIds.length > 0) {
      programs = await Programs.findAll({
        where: {
          resourceId: {
            [Op.in]: programResourceIds,
          },
        },
      });
    }

    let lessons = [];
    if (lessonResourceIds && lessonResourceIds.length > 0) {
      lessons = await Lessons.findAll({
        where: {
          resourceId: {
            [Op.in]: lessonResourceIds,
          },
        },
      });
    }
    const data = contentPackageResources
      .map(cpr => cpr.get({ plain: true }))
      .map((cpr) => {
        return {
          ...cpr,
          resource: {
            ...cpr.resource,
            resourceObject: getResourceObject(cpr.resource, programs, lessons),
          },
        };
      });

    res.json({ total: count, limit: finalQuery.limit, skip: finalQuery.skip, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /content-packages/{contentPackageId}/resources:
 *   post:
 *     summary: Lesson Add To Package
 *     tags: [Package]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: contentPackageId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       description: Suggested Fields parameters
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - resourceId
 *             properties:
 *               resourceId:
 *                 type: integer
 *                 example: 37
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 contentPackageId:
 *                   type: integer
 *                 resourceId:
 *                   type: integer
 *                 updatedAt:
 *                   type: string
 *                 createdAt:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  try {
    const { resourceId } = req.body;
    const contentPackageId = parseInt(req.params.contentPackageId);

    if (!resourceId) {
      const err = new Error(req.i18n.t('contentPackages.badParameter_Error'));
      err.status = 400;
      throw err;
    }
    let contentPackageResource = await ContentPackageResources.findOne({
      where: {
        contentPackageId,
        resourceId,
      },
    });
    if (!contentPackageResource) {
      contentPackageResource = await ContentPackageResources.create({
        contentPackageId,
        resourceId,
      });
      // If the resource is new to the content package, then find all of the accounts that have this
      // content package linked and add the content to the account.
      const accountContentPackages = await AccountContentPackages.findAll({
        where: {
          contentPackageId,
        },
        include: {
          attributes: [],
          model: Accounts,
          where: {
            accountType: {
              [Op.in]: ['customer', 'internal'],
            },
          },
          required: true,
        },
      });
      // Add the resource to the accounts.
      const resource = await Resources.findOne({
        attributes: ['digestable'],
        where: {
          id: resourceId,
        },
      });
      const accountIds = accountContentPackages.map(acp => acp.accountId);
      for (const accountId of accountIds) {
        const accountBundle = await getAccountBundle(accountId);
        if (accountBundle) {
          await addResourceAccessToBundle(resourceId, accountBundle.bundleId);
          if (resource.digestable === 'programs') {
            const program = await Programs.findOne({
              where: {
                resourceId,
              },
            });
            if (program) {
              await addProgramChildAccess(program.id, accountBundle.bundleId);
            }
          }
        }
      }
    }

    res.json(contentPackageResource);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /content-packages/{contentPackageId}/resources/{resourceId}:
 *   delete:
 *     summary: Delete lessons or programs from package
 *     tags: [Package]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: contentPackageId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: resourceId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successfully deleted lessons or programs from package
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       404:
 *         description: Resource not found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  try {
    const resourceId = req.resource.id;
    const contentPackageId = req.contentPackage.id;

    let contentPackageResource = await ContentPackageResources.findOne({
      where: {
        resourceId,
        contentPackageId,
      },
    });
    if (contentPackageResource) {
      contentPackageResource = await contentPackageResource.destroy();
    }

    res.json(contentPackageResource);
  } catch (err) {
    next(err);
  }
};

module.exports.resourceById = async (req, res, next, id) => {
  try {
    const resource = await Resources.findByPk(id);
    if (!resource) {
      const err = new Error(req.i18n.t('contentPackages.resource_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.resource = resource;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * definitions:
 *  contentPackageResources:
 *    type: object
 *    properties:
 *      id:
 *        type: integer
 *      contentPackageId:
 *        type: integer
 *      resourceId:
 *        type: integer
 *      createdAt:
 *        type: string
 *      updatedAt:
 *        type: string
 *      deletedAt:
 *        type: string
 */
