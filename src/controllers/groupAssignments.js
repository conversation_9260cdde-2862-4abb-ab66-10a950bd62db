const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { createAuditLog } = require('./auditLogs');
const { restQueryToSequelize } = require('../services/utils/userUtils');
const { closeAssignments, addGroupToCampaign } = require('../services/utils/campaignUtils');

const CampaignItems = db.campaignItem;
const GroupAssignments = db.groupAssignments;
const Groups = db.groups;
const Op = db.Sequelize.Op;

function flattenAssignment(groupAssignment) {
  const group = groupAssignment.group;
  const flattenedAssignment = groupAssignment.get({ plain: true });
  return Object.assign(flattenedAssignment, { group });
}

// looks up Group from groupAssignment
async function lookupGroup(groupAssignment, paranoid = true, hooks = true) {
  const group = await Groups.findByPk(groupAssignment.groupId, { paranoid, hooks });
  Object.assign(groupAssignment, { group });
  return groupAssignment;
}

// return array of program ids that are in campaign
async function getCampaignPrograms(campaignId) {
  const items = await CampaignItems.findAll({ where: { campaignId } });
  return items.filter(item => item.itemType === 'program').map(item => item.itemId);
}

// create necessary groupAssignments records for tracking a group in a campaign
async function createGroupAssignments(campaignId, reoccurenceIteration, groupId, progIds) {
  // main record
  let newAssignment = await GroupAssignments.create({ campaignId, reoccurenceIteration, groupId });
  const promises = [];
  // records for any programs in campaign
  progIds.forEach((programId) => {
    promises.push(GroupAssignments.create({ campaignId, reoccurenceIteration, groupId, programId }));
  });
  await Promise.all(promises);
  await addGroupToCampaign(campaignId, groupId);
  newAssignment = await lookupGroup(newAssignment);
  return flattenAssignment(newAssignment);
}

// deletes groupAssignment closing off any associated user assignments
async function deleteGroupAssignment(campaign, groupAssignment) {
  const promises = [];
  if (campaign.status === 'inProgress') {
    // close active assignments for users in group
    promises.push(closeAssignments([groupAssignment.id]));
  }
  // delete any groupAssignments
  promises.push(GroupAssignments.destroy({
    where: {
      id: groupAssignment.id,
    },
  }));
  return promises;
}

// replaces campaigns group assigments with incoming groups in groupIds
async function resetGroupAssignments(campaign, groupIds, req = null) {
  const logData = {
    feature: 'campaigns',
    object: 'campaigns',
    objectId: campaign.id,
    childObject: 'gropuAssignments',
  };

  // figure out what groups should be removed and what groups should be added
  const preexistingAssignments = await GroupAssignments.findAll({
    where: {
      campaignId: campaign.id,
      reoccurenceIteration: campaign.reoccurenceIteration,
    },
  });
  const preexistingGroupIds = preexistingAssignments.map(assignment => assignment.groupId);

  const removedAssignments = preexistingAssignments.filter((assignment) => {
    return !groupIds.includes(assignment.groupId);
  });

  const newGroups = groupIds.filter((id) => {
    return !preexistingGroupIds.includes(id);
  });

  // remove groups
  const promises = removedAssignments.map(async (assignment) => {
    logData.action = 'draft-campaign-remove-group';
    logData.childObjectId = assignment.id;
    logData.updatedData = {
      // eslint-disable-next-line max-len
      groupAssignment: `id: ${assignment.id}, campaignId: ${assignment.campaignId}, groupId: ${assignment.groupId}, reoccurenceIteration: ${assignment.reoccurenceIteration}`,
    };
    await createAuditLog(logData, req);
    return deleteGroupAssignment(campaign, assignment);
  });
  await Promise.all(promises);

  // add new groups
  const progIds = await getCampaignPrograms(campaign.id);
  const promises2 = newGroups.map((groupId) => {
    return createGroupAssignments(campaign.id, campaign.reoccurenceIteration, groupId, progIds);
  });
  const data = await Promise.all(promises2);
  if (data && data.length > 0) {
    logData.action = 'draft-campaign-add-group';
    logData.childObjectId = data[0].id;
    logData.updatedData = {
      // eslint-disable-next-line max-len
      groupAssignment: `id: ${data[0].id}, campaignId: ${data[0].campaignId}, groupId: ${data[0].groupId}, reoccurenceIteration: ${data[0].reoccurenceIteration}`,
    };
    await createAuditLog(logData, req);
  }
  return data;
}

/**
 * @swagger
 * /campaigns/{campaignId}/groups:
 *   get:
 *     summary: Campaign Groups For Given CampaignId
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         schema:
 *           type: integer
 *         required: true
 *         description: Campaign ID
 *       - name: $limit
 *         in: query
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Limit (integer) for number of records
 *         required: true
 *       - name: $skip
 *         in: query
 *         schema:
 *           type: integer
 *           example: 0
 *         description: Number of records to skip
 *         required: true
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       groupId:
 *                         type: integer
 *                       campaignId:
 *                         type: integer
 *                       programId:
 *                         type: integer
 *                       reoccurenceIteration:
 *                         type: integer
 *                       createdAt:
 *                         type: string
 *                       updatedAt:
 *                         type: string
 *                       deletedAt:
 *                         type: string
 *                       group:
 *                         type: string
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  // option to return soft-deleted groups
  let groupLookupParanoid = true;
  if (req.query.includeDeletedGroups && req.query.includeDeletedGroups === 'true') {
    groupLookupParanoid = false;
  }
  delete (req.query.includeDeletedGroups);

  const campaign = req.campaign;
  let campaignIds = req.params.campaignId;
  if (campaignIds) {
    campaignIds = campaignIds.split(',');
  }
  const defaults = {
    order: [['createdAt', 'ASC']],
    limit: config.paginate.default,
    offset: 0,
    where: {
      campaignId: { [Op.in]: campaignIds },
      reoccurenceIteration: campaign.reoccurenceIteration,
      programId: null,
    },
  };
  const queryParams = restQueryToSequelize(req.query, defaults);
  queryParams.order = [[...queryParams.order]];

  const finalQuery = {
    ...queryParams,
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  const countQuery = {
    where: _.pick(finalQuery, ['where']).where,
  };
  try {
    const count = await GroupAssignments.count(countQuery);
    let groupAssignments = await GroupAssignments.findAll(finalQuery);
    const campaignGroups = {};
    groupAssignments.forEach((assignment) => {
      if (assignment.groupId && assignment.campaignId) {
        if (!_.has(campaignGroups, assignment.campaignId)) {
          campaignGroups[assignment.campaignId] = [assignment.groupId];
        } else {
          campaignGroups[assignment.campaignId].push(assignment.groupId);
        }
      }
    });
    groupAssignments = [...new Map(groupAssignments.map(item => [item.groupId, item])).values()];
    const promises = [];
    // look up items associated with campaign item bindings
    groupAssignments.forEach((assignment) => {
      promises.push(lookupGroup(assignment, groupLookupParanoid, false));
    });
    const results = await Promise.all(promises);
    // attach item to binding
    const data = results.map((assignment) => {
      for (const gCampaignId in campaignGroups) {
        if (_.has(campaignGroups, gCampaignId) && assignment.group) {
          // eslint-disable-next-line max-len
          campaignGroups[gCampaignId] = campaignGroups[gCampaignId].map((cGroup) => { return cGroup === assignment.group.id ? assignment.group.name : cGroup; });
        }
      }
      return flattenAssignment(assignment);
    });
    // sort by groupname
    const sortData = data.sort((a, b) => {
      if (!a.group) return 1;
      if (!b.group) return -1;
      if (a.group.name.toLowerCase() < b.group.name.toLowerCase()) return -1;
      if (a.group.name.toLowerCase() > b.group.name.toLowerCase()) return 1;
      return 0;
    });
    res.json({ total: count, ...pagedResult, data: sortData, campaignGroups });
  } catch (err) {
    next(err);
  }
};
module.exports.read = async (req, res) => {
  const groupAssignment = await lookupGroup(req.groupAssignment);
  const data = flattenAssignment(groupAssignment);
  res.json(data);
};

/**
 * @openapi
 * /campaigns/{campaignId}/groups:
 *   post:
 *     summary: Add Groups To Campaign
 *     tags:
 *       - Campaigns
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       description: Required parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               groupIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *             example:
 *               groupIds: [8, 10]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   campaignId:
 *                     type: integer
 *                   reoccurenceIteration:
 *                     type: integer
 *                   groupId:
 *                     type: integer
 *                   updatedAt:
 *                     type: string
 *                     format: date-time
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                   group:
 *                     $ref: '#/components/schemas/groups'
 *       404:
 *         description: Not Found
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  const groupId = req.body.groupId;
  const groupIds = req.body.groupIds;
  const campaign = req.campaign;
  try {
    let data;
    if (!_.isNil(groupIds)) {
      // mass reset of group assignments
      data = await resetGroupAssignments(req.campaign, groupIds, req);
    } else {
      // single group
      const oldAssignment = await GroupAssignments.findOne({
        where:
        {
          campaignId: campaign.id,
          reoccurenceIteration: campaign.reoccurenceIteration,
          groupId,
        },
      });

      if (oldAssignment) {
        const id = oldAssignment.id;
        const err = new Error(req.i18n.t('campaigns.group_already_exists_Error', { id }));
        err.status = 400;
        throw err;
      }
      const progIds = await getCampaignPrograms(campaign.id);
      data = await createGroupAssignments(campaign.id, campaign.reoccurenceIteration, groupId, progIds);
    }
    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  try {
    const groupAssignment = await lookupGroup(req.groupAssignment);
    const data = flattenAssignment(groupAssignment);
    await deleteGroupAssignment(req.campaign, groupAssignment);
    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.campaignGroupById = async function (req, res, next, id) {
  try {
    const groupAssignment = await GroupAssignments.findByPk(id);
    if (!groupAssignment) {
      const err = new Error(req.i18n.t('campaigns.groupAssignment_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.groupAssignment = groupAssignment;
    next();
  } catch (err) {
    next(err);
  }
};
