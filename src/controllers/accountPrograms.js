const db = require('../db');
const { updateElasticSearch } = require('../services/utils/resourceUtils');
const { deriveSearch, propagateProgramMinTimeChange } = require('../services/utils/programUtils');
const { isScormAccount } = require('../services/utils/accountUtils');
const { getContent, putContent, authenticate } = require('../services/utils/workdayCCLHelpers.js');
const { getWorkdayCCLAccount } = require('../services/utils/workdayCclUtils.js');

const AccountPrograms = db.accountPrograms;
const Programs = db.programs;
const WorkdayContentData = db.workdayContentData;

const updateInWorkdayCCL = async ( workdayCCLAccount, data ) => {
  try {
    const { activityId, contentType, name, minTimeInMinutes, resourceId } = data;
    const existingWorkdayContent = await WorkdayContentData.findOne({
      where: {
        accountId: workdayCCLAccount.id,
        activityId: activityId,
        contentType: contentType,
        isDeleted: false
      }
    });

    if (!existingWorkdayContent) {
      return;
    }

    const tenantAlias = workdayCCLAccount?.integrations?.[0]?.tenantAlias
    const clientId = workdayCCLAccount?.integrations?.[0]?.clientId
    const clientSecret = workdayCCLAccount?.integrations?.[0]?.integrationKey

    const auth = await authenticate({
      clientId,
      clientSecret,
      tenantAlias
    });

    if (!auth.ok) {
      console.error(`ERROR: Workday Authentication Error for account account id ${workdayCCLAccount?.id}`)
      return;
    }
    const responseData = await auth.json();
    const accessToken = responseData?.access_token;

    const response = await getContent({
      accessToken,
      contentId: resourceId,
      tenantAlias
    });

    response[0].title = name;
    response[0].contentDuration.value = minTimeInMinutes ? minTimeInMinutes : response[0].contentDuration.value;
    response[0].lifecycle.lastUpdatedDate = new Date().toISOString();

    const result = await putContent({
      accessToken,
      contentItems: response,
      tenantAlias,
    });

    if (result === 'success') {
      await existingWorkdayContent.update({ updatedAt: new Date().toISOString });
    }
    else {
      console.error(`ERROR: Failed to update workday content id: ${resourceId} for account id ${workdayCCLAccount?.id}`)
    }  
  } catch (error) {
    console.error('Error in updateInWorkdayCCL:', {
      error: error.message,
      accountId: workdayCCLAccount?.id,
      activityId: data?.activityId,
      resourceId: data?.resourceId
    });
  }
}

module.exports.create = async (req, res, next) => {
  try {
    if (!req.body.accountId) {
      req.body.accountId = req.user.accountId;
    }

    const accountId = req.body.accountId;
    const existingAccountProgram = await
    AccountPrograms.findOne({ where: { accountId: req.body.accountId, programId: req.body.programId } });

    let accountProgram;
    if (existingAccountProgram) {
      req.body.id = existingAccountProgram.id;
      accountProgram = await existingAccountProgram.update(req.body);
    } else {
      accountProgram = await AccountPrograms.create(req.body);
    }

    const program = await Programs.findOne({ where: { id: req.body.programId } });
    const workdayCCLAccount = await getWorkdayCCLAccount(accountId);

    if (workdayCCLAccount) {
      const data = { 
        activityId: req?.body?.programId, 
        contentType: 'program', 
        resourceId: program?.resourceId,
        name: req?.body?.name, 
        minTimeInMinutes: req?.body?.minTimeInMinutes 
      };      

      await updateInWorkdayCCL(workdayCCLAccount, data);
    }

    const searchInfo = await deriveSearch(program);
    await updateElasticSearch(req, program.resourceId, searchInfo);
    res.json(accountProgram);
  } catch (err) {
    if (err.name === 'SequelizeValidationError') {
      // statements to handle TypeError exceptions
      err.status = 422;
    }
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  const existingAccountProgram = req.accountProgram;
  const modifiedAccountProgram = req.body;
  const programId = existingAccountProgram.programId;
  const accountId = existingAccountProgram?.accountId;
  try {
    if (existingAccountProgram && existingAccountProgram.accountId) {
      const scormAccount = await isScormAccount(existingAccountProgram.accountId);
      if (scormAccount) {
        if (modifiedAccountProgram.hasOwnProperty('minTimeInMinutes')) {
          delete modifiedAccountProgram.minTimeInMinutes;
        }
        if (modifiedAccountProgram.hasOwnProperty('minCardTimeInSeconds')) {
          delete modifiedAccountProgram.minCardTimeInSeconds;
        }
      }

      const workdayCCLAccount = await getWorkdayCCLAccount(accountId);

      if (workdayCCLAccount) {
        const data = { 
          activityId: req?.body?.programId, 
          contentType: 'program', 
          resourceId: req?.body?.program?.resourceId,
          name: req?.body?.name, 
          minTimeInMinutes: req?.body?.minTimeInMinutes 
        };
        
        await updateInWorkdayCCL(workdayCCLAccount, data);
      }
    }
    const updatedAccountProgram = await existingAccountProgram.update(modifiedAccountProgram);

    const finalAccountProgram = await AccountPrograms.findByPk(updatedAccountProgram.id);
    const program = await Programs.findOne({ where: { id: programId } });
    const searchInfo = await deriveSearch(program);
    await updateElasticSearch(req, program.resourceId, searchInfo);
    res.json(finalAccountProgram);
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  try {
    res.json(req.accountProgram);
  } catch (err) {
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  const accountProgram = req.accountProgram;

  try {
    const data = await accountProgram.destroy();
    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.accountProgramByProgramId = async (req, res, next, id) => {
  try {
    const accountId = req.user.accountId;
    const accountProgram = await AccountPrograms.findOne({
      where: {
        accountId,
        programId: id,
      },
      include: [{
        model: Programs,
      }],
    });
    if (!accountProgram) {
      const err = new Error(req.i18n.t('accountPrograms.retrieval_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.accountProgram = accountProgram;
    next();
  } catch (err) {
    next(err);
  }
};
