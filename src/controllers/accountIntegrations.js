const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const logger = require('../logger');
const { restQueryToSequelize } = require('../services/utils/userUtils');
const { isSCORMIntegrationType } = require('../services/utils/accountUtils');
const { S3Client, PutObjectCommand } = require("@aws-sdk/client-s3");
const { getCsvS3Bucket } = require('../services/utils/fileUtils');
const { authenticate, syncContent, mapLanguageCodes } = require('../services/utils/workdayCCLHelpers.js');

const Op = db.Sequelize.Op;
const Account = db.accounts;
const Programs = db.programs;
const Lessons = db.lessons;
const AccountBundles = db.accountBundles;
const ResourceBundles = db.resourceBundles;
const Integration = db.integrations;
const CsvSetting = db.csvSettings;
const CsvColumn = db.csvColumns;
const CsvImport = db.csvImports;
const WorkdayContentData = db.workdayContentData;

// Fetch from Aragorn in batches - default page size is 100 records
const getAragornDataInBatches = async (projectId) => {
  try {
    // Start with first page - page 0 or 1 will each fetch the first page, so be sure to start with 1 to avoid duplicate data
    let pageNum = 1;
    const allEmployeeData = [];

    // Get the first batch and check if there are multiple pages, store the first batch
    const data = await fetchEmployeeData(projectId, pageNum);
    const totalPages = data?.totalPages;
    allEmployeeData.push(data?.employees);

    // If we have multiple pages, repeat calls until we get them all
    if (totalPages > 1) {
      pageNum++;
      while (pageNum <= totalPages) {
        const currentBatch = await fetchEmployeeData(projectId, pageNum);
        allEmployeeData.push(currentBatch?.employees);
        pageNum++;
      }
    }

    // Return the flattened array
    return allEmployeeData.flat();
  } catch (error) {
    const err = new Error(`Failed to fetch data from Aragorn: ${error?.message}`);
    throw new Error(err);
  }
};

// Call Aragorn employees API
const fetchEmployeeData = async (projectId, pageNum) => {
  try {
    // Get the aragorn environment variables for API call
    const aragornServer = _.get(config, 'aragorn.aragornServer');
    const aragornOrgId = _.get(config, 'aragorn.aragornOrgId');
    const aragornApiKey = _.get(config, 'aragorn.aragornApiKey');
    const apiUrl = `${aragornServer}/projects/${projectId}/employees?page=${pageNum}`;

    const aragornHeaders = {
      'Content-Type': 'application/json',
      'x-aragorn-org-id': aragornOrgId || '',
      'x-aragorn-api-key': aragornApiKey || '',
    };

    // Send the request and grab the response data
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: aragornHeaders,
    });

    const data = await response.json();

    if (response.status != 200) {
      throw new Error(JSON.stringify(data));
    }

    return data;
  } catch (error) {
    const err = new Error(`Failed to fetch data from Aragorn: ${error?.message}`);
    throw new Error(err);
  }
};

// Calculate the content duration
const calculateDuration = async (contentItem, accountId) => {
  if (!contentItem) {
    throw new Error('No content provided in duration calculation');
  }

  // default to 10 minutes
  const defaultTime = 10;

  // if we have a lesson, use the lessons duration field
  if (contentItem.itemType === 'lesson') {
    const lesson = await db.lessons.findByPk(contentItem.id, { attributes: ['requiredMinutes'] });
    const requiredMinutes = lesson?.requiredMinutes ? parseInt(lesson.requiredMinutes) : defaultTime;
    return requiredMinutes > 0 ? requiredMinutes : defaultTime;
  }

  // if we have a program, try the min time required, then the account program min
  // time required (if it exists) and finally the progrma duration
  if (contentItem.itemType === 'program') {
    const program = await db.programs.findByPk(contentItem.id, {
      attributes: ['duration', 'minTimeInMinutes', 'isTimed'],
      include: [{
        model: db.accountPrograms,
        where: { accountId },
        attributes: ['minTimeInMinutes'],
        required: false,
        limit: 1,
      }],
    });

    if (program?.isTimed && program?.minTimeInMinutes) {
      return program.minTimeInMinutes ? program.minTimeInMinutes : defaultTime;
    } else if (program?.accountPrograms && program?.accountPrograms?.length > 0) {
      return program.accountPrograms[0].minTimeInMinutes ? program.accountPrograms[0].minTimeInMinutes : defaultTime;
    }
    return program.duration ? program.duration : defaultTime;
  }
  return defaultTime;
};

// Get our content categories from catalog items/listings
const calculateDomainsFromConcepts = async (contentCatalogItem) => {
  const defaultDomain = [{ domain: 'Culture Skills' }];
  if (!contentCatalogItem) {
    // If we have no catelog Item at all, we send the default
    return defaultDomain;
  }

  // get concepts from catalog item id
  const catalogItemWithConcepts = await db.catalogItems.findByPk(contentCatalogItem?.id, {
    attributes: ['id'],
    include: [
      {
        attributes: ['id', 'concept'],
        model: db.concepts,
        through: db.catalogItemConcepts,
      },
      {
        model: db.listings,
        attributes: ['id'],
        include: {
          model: db.concepts,
          through: db.listingConcepts,
          attributes: ['id', 'concept'],
        },
      },
    ],
  });

  const catalogItemConcepts = catalogItemWithConcepts?.concepts?.map(concept => concept.concept);
  const listingConcepts = catalogItemWithConcepts?.listing?.concepts?.map(concept => concept.concept);

  if (catalogItemConcepts && catalogItemConcepts?.length) {
    return catalogItemConcepts.map(concept => ({ domain: concept }));
  } else if (listingConcepts && listingConcepts?.length) {
    return listingConcepts.map(concept => ({ domain: concept }));
  }

  return defaultDomain;
};


module.exports.checkAccountIntegrationUpdates = async (app, lastUpdate) => {
  const accountsToUpdate = await Integration.findAll({
    where: {
      updatedAt: {
        [Op.gte]: lastUpdate,
      },
    },
    include: [{
      model: Account,
      where: {
        status: 'active',
      },
      required: true,
    }],
  });

  for (const integration of accountsToUpdate) {
    const sftpServer = app.get('sftpServer');
    const sftpUser = integration.accountId.toString();
    try {
      logger.info(`updating integration for ${sftpUser}, cert: ${integration.cert}`);
      sftpServer.removePublicKey(sftpUser);
      if (integration.cert) {
        sftpServer.addPublicKey(integration.cert, sftpUser, 'accounts');
      }
    } catch (err) {
      // eslint-disable-next-line max-len
      logger.error(`Exception thrown in checkAccountIntegrationUpdates() while updating account ${sftpUser} public key on SFTP server: ${err}`);
    }
  }
  return accountsToUpdate.length;
};

module.exports.setupIntegration = (req, integration) => {
  if (integration.integrationType === 'sftp') {
    const sftpServer = req.app.get('sftpServer');
    const sftpUser = integration.accountId.toString();
    try {
      sftpServer.addPublicKey(integration.cert, sftpUser, 'accounts');
    } catch (err) {
      logger.error(`Exception thrown while adding account ${sftpUser} public key to SFTP server: ${err}`);
    }
  }
};

const updateIntegration = (req, oldIntegration, newIntegration) => {
  if (oldIntegration.integrationType === 'sftp') {
    const sftpServer = req.app.get('sftpServer');
    const sftpUser = oldIntegration.accountId.toString();
    const oldCert = oldIntegration.cert;
    const newCert = newIntegration.cert;
    if (oldCert && newCert && oldCert !== newCert) {
      try {
        sftpServer.removePublicKey(sftpUser);
        sftpServer.addPublicKey(newCert, sftpUser, 'accounts');
      } catch (err) {
        // eslint-disable-next-line max-len
        logger.error(`Exception thrown in updateIntegration() while updating account ${sftpUser} public key on SFTP server: ${err}`);
      }
    }
  }
};

const deleteIntegration = (req, integration) => {
  if (integration.integrationType === 'sftp') {
    const sftpServer = req.app.get('sftpServer');
    const sftpUser = integration.accountId.toString();
    try {
      sftpServer.removePublicKey(sftpUser);
    } catch (err) {
      logger.error(`Exception thrown while removing account ${sftpUser} public key from SFTP server: ${err}`);
    }
  }
};

module.exports.list = async (req, res, next) => {
  const account = req.account;
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
    where: {
      accountId: account.id,
    },
    include: [
      {
        model: Account,
      },
    ],
  };
  const queryParams = restQueryToSequelize(req.query, defaults);
  queryParams.order = [[...queryParams.order]];

  const finalQuery = {
    ...queryParams,
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  const countQuery = {
    where: _.pick(finalQuery, ['where']).where,
  };

  try {
    const count = await Integration.count(countQuery);
    const data = await Integration.findAll(finalQuery);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = (req, res) => {
  res.json(req.accountIntegration);
};

module.exports.create = async (req, res, next) => {
  try {
    req.body.accountId = req.account.id;
    let newIntegration;
    if (req.body?.integrationType === 'aragorn') {
      newIntegration = await Integration.create({
        accountId: req.body.accountId,
        integrationType: req.body.integrationType,
        integrationKey: req.body.projectId,
      });
    } else if (req.body?.integrationType === 'workdayCCL') {
      newIntegration = await Integration.create({
        accountId: req.body.accountId,
        integrationType: req.body.integrationType,
        integrationKey: req.body.clientSecret,
        clientId: req.body.clientId,
        tenantAlias: req.body.tenantAlias,
      });
    } else {
      delete req.body.integrationKey; // do this in a db hook
      newIntegration = await Integration.create(req.body);
      this.setupIntegration(req, newIntegration);
    }
    // Update Integration csvImportSync Field for scorm account on create
    if (isSCORMIntegrationType(newIntegration.integrationType)) {
      await Account.update({ csvImportSyncField: 'scormId', reassignScorm: true }, { where: { id: newIntegration.accountId } });
    }
    res.json(newIntegration);
  } catch (err) {
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  let updatedIntegration;
  try {
    if (req?.body?.integrationType === 'aragorn') {
      req.body.integrationKey = req.body.projectId;
      updatedIntegration = await req.accountIntegration.update(req.body);
    } else if (req?.body?.integrationType === 'workdayCCL') {
      req.body.integrationKey = req.body.clientSecret;
      updatedIntegration = await req.accountIntegration.update(req.body);
    } else {
      delete req.body.integrationKey; // can't touch this
      updateIntegration(req, req.accountIntegration, req.body);
      updatedIntegration = await req.accountIntegration.update(req.body);
    }
    res.json(updatedIntegration);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /aragorn/{accountId}:
 *   get:
 *     summary: Fetch Aragorn data based on accountId
 *     tags:
 *       - Aragorn
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: accountId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: accountId of the existing Account
 *     responses:
 *       200:
 *         description: Successfully fetched Aragorn data from provided accountId
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   additionalProperties: true
 *                 message:
 *                   type: string
 *                   example: Successfully fetched Aragorn data
 */
module.exports.syncAragorn = async (req, res, next) => {
  // Extract the needed parameters to fetch from Aragorm
  const accountId = req?.account?.id;
  const aragornIntegration = req?.account?.integrations?.find(integration => integration.integrationType === 'aragorn');
  const aragornProjectId = aragornIntegration?.integrationKey;

  try {
    // Fetch all the data
    const data = await getAragornDataInBatches(aragornProjectId);
    const totalRecords = data.length;

    // Same naming convention used for csv imports
    const now = new Date();
    const filename = [
      now.getFullYear(), now.getMonth(), now.getDate(),
      '-',
      process.pid,
      '-',
      ((Math.random() * 0x100000000) + 1).toString(36),
      '.csv',
    ].join('');
    const origFilename = `aragorn_${filename}`;

    // Find the settings record based on the accountId.
    let csvSettings = await CsvSetting.findOne({
      where: {
        accountId,
      },
    });

    // If there isn't a settings record, create a default one to start.
    if (!csvSettings) {
      const settings = {
        accountId,
      };
      csvSettings = await CsvSetting.create(settings);
    }

    // Convet the JSON response to CSV for upload and replace nulls with empty strings
    const replacer = (key, value) => (value === null ? '' : value); // specify how you want to handle null values here
    const header = Object.keys(data[0]);
    const csvData = [
      header.join(','), // header row first
      ...data.map(row => header.map(fieldName => JSON.stringify(row[fieldName], replacer)).join(',')),
    ].join('\r\n');
    const fileSize = Buffer.byteLength(csvData, 'utf8');

    // Upload the data to S3 as a CSV
    const s3 = new S3Client({
      region: 'us-east-1',
      credentials: {
        accessKeyId: config.s3.accessKeyId,
        secretAccessKey: config.s3.secretAccessKey,
      },
    });
    const { bucket, keyPrefix } = getCsvS3Bucket(accountId, 'csv-uploads');
    const params = {
      Bucket: bucket,
      Key: `${keyPrefix}${filename}`,
      Body: csvData,
      ContentType: 'text/csv',
      ServerSideEncryption: 'AES256',
    };
    await s3.send(new PutObjectCommand(params));

    // Create the import record in the database
    const importData = {
      csvSettingId: csvSettings.id,
      fileLocation: 'S3',
      filePath: `https://${bucket}.s3.amazonaws.com/${keyPrefix}${filename}`,
      fileSize,
      origFilename,
      totalRecords,
    };
    const importRecord = await CsvImport.create(importData);

    // Return the created import record ID so we can redirect there on the FE
    res.json(importRecord.id);
  } catch (err) {
    const error = new Error(`Failed to Process Sync: ${err?.message}`);
    next(error);
  }
};

module.exports.delete = async (req, res, next) => {
  const accountIntegration = req.accountIntegration;
  try {
    deleteIntegration(req, accountIntegration);
    // Update Integration csvImportSync Field for scorm account on delete
    if (isSCORMIntegrationType(accountIntegration.integrationType)) {
      await Account.update({ csvImportSyncField: 'email', reassignScorm: false }, { where: { id: accountIntegration.accountId } });
      // delete scromId(userTableColumn) from csvColumns for this accountId
      const csvSettingData = await CsvSetting.findOne({ where: { accountId: accountIntegration.accountId }, attributes: ['id'] });
      if (csvSettingData && csvSettingData.id) {
        await CsvColumn.destroy({
          where: {
            csvSettingId: csvSettingData.id,
            userTableColumn: 'scormId',
          },
        });
      }
    }
    const data = await accountIntegration.destroy();
    res.json(data);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the accountIntegration when an id is passed in the route
module.exports.accountIntegrationById = async function (req, res, next, id) {
  try {
    const accountIntegration = await Integration.findByPk(id, {
      include: [
        {
          model: Account,
        },
      ],
    });
    if (!accountIntegration) {
      const err = new Error(req.i18n.t('system.integration_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.accountIntegration = accountIntegration;
    next();
  } catch (err) {
    next(err);
  }
};

// Test our credentials against the workday authorization API
exports.validateWorkdayCredentials = async (req, res) => {
  try {
    const { clientId, clientSecret, tenantAlias } = req.body;

    // If any of these are missing, return an error
    if (!clientId || !clientSecret || !tenantAlias) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Call our helper
    const validationResponse = await authenticate({
      clientId,
      clientSecret,
      tenantAlias,
    });

    if (validationResponse.status === 500) {
      throw new Error(`HTTP error! Status: ${validationResponse.status}`);
    }

    // Parse the response - expecting JSON per the API spec
    const responseData = await validationResponse.json();
    if (responseData.access_token) {
      return res.status(200).json({ message: 'Credentials validated successfully. Account is not yet updated - Remember to click Save!' });
    }

    const parsedMessage = JSON.parse(responseData.message);
    return res.status(validationResponse.status).json({ error: parsedMessage?.error || 'Invalid credentials' });
  } catch (error) {
    return res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * @swagger
 * /workday/sync:
 *   post:
 *     summary: Sync Workday content for an account
 *     tags:
 *       - WorkdayCCL
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: Authorization
 *         in: header
 *         required: true
 *         schema:
 *           type: string
 *         description: The authentication token of a user
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - data
 *             properties:
 *               data:
 *                 type: array
 *                 description: List of content items to sync
 *                 items:
 *                   type: object
 *                   properties:
 *                     resourceId:
 *                       type: string
 *                       description: Unique identifier for the content item
 *                     itemType:
 *                       type: string
 *                       enum:
 *                         - program
 *                         - lesson
 *                       description: Type of content item
 *                     id:
 *                       type: string
 *                       description: Unique ID of the content item
 *                     catalogItem:
 *                       type: object
 *                       description: Catalog information of the content item
 *                     file:
 *                       type: object
 *                       properties:
 *                         path:
 *                           type: string
 *                           description: File path for content thumbnail (if applicable)
 *                         en:
 *                           type: string
 *                           description: English version of the file path (if applicable)
 *                     displayName:
 *                       type: string
 *                       description: Title of the content item
 *                     description:
 *                       type: string
 *                       description: Description of the content item
 *     responses:
 *       201:
 *         description: Content successfully created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 201
 *                 requestID:
 *                   type: string
 *                   example: "12345"
 *                 message:
 *                   type: string
 *                   example: "Content successfully created"
 *                 successCount:
 *                   type: integer
 *                   example: 5
 *                 failureCount:
 *                   type: integer
 *                   example: 0
 *       207:
 *         description: Partial success - some content items failed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 207
 *                 requestID:
 *                   type: string
 *                   example: "12345"
 *                 message:
 *                   type: string
 *                   example: "Partial success - some content items failed"
 *                 successCount:
 *                   type: integer
 *                   example: 3
 *                 failureCount:
 *                   type: integer
 *                   example: 2
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       status:
 *                         type: integer
 *                         example: 400
 *                       errorMessage:
 *                         type: string
 *                         example: "Validation errors have occurred"
 *                       details:
 *                         type: array
 *                         items:
 *                           type: string
 *                         example: ["Invalid format for field XYZ"]
 *       400:
 *         description: Bad request - Validation error or missing fields
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: "Bad Request"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: ["Missing required field XYZ"]
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
exports.syncWorkdayContent = async (req, res) => {
  try {
    const { data } = req.body;

    // If any of these are missing, return an error
    if (!data || !data?.length) {
      return res.status(400).json({ error: 'Missing Content items' });
    }

     // Get the account specific items we need for each content item
     const account = req.account;
     const languageCodes = await mapLanguageCodes(account?.accountLanguage?.selectedLanguages);
     const subdomain = account?.subdomain;
 
    const programIds = data?.filter(item => item.itemType === 'program').map(item => item.id);
    const lessonIds = data?.filter(item => item.itemType === 'lesson').map(item => item.id);
    const resourceIds = data?.map(item => item.resourceId);

    // Check for retired so we can filter it out
    const retiredPrograms = await Programs.findAll({
      where: {
        [Op.and]: [
          { id: { [Op.in]: programIds } },
          { lifecycle: "retired" }
        ]
      },
      attributes: ['id']
    });

    const retiredLessons = await Lessons.findAll({
      where: {
        [Op.and]: [
          { id: { [Op.in]: lessonIds } },
          { lifecycle: "retired" }
        ]
      },
      attributes: ['id']
    });

    // Find retired resourceBundles
    const retiredResourceBundles = await ResourceBundles.findAll({
      where: {
        resourceId: { [Op.in]: resourceIds },
        status: 'retired',
      }
      ,
      include: [{
        model: AccountBundles,
        where: {
          accountId: account?.id
        },
        required: true
      }],
      attributes: ['resourceId'],
    });

    // Collect retired resource IDs
    const retiredResourceIds = new Set([
      ...retiredPrograms.map(p => p.id),
      ...retiredLessons.map(l => l.id),
      ...retiredResourceBundles.map(r => r.resourceId),
    ]);

    // Filter out retired resources from the original list
    const filteredResources = data?.filter(r => !retiredResourceIds.has(r.id) && !retiredResourceIds.has(r.resourceId));

    // Check if we have any non-retired resources to process
    if (!filteredResources?.length) {
      return res.status(400).json({
        status: 400,
        message: "No eligible content items to sync - all selected items are retired",
        errors: data.map(item => ({
          contentID: item.resourceId,
          displayName: item.displayName,
          activityId: `${item.itemType}_${item.id}`,
          errorMessage: "Content item is retired"
        }))
      });
    }

    // Format each of our content items and perform the necessary calculations before sending to workday
    const formattedContent = await Promise.all(filteredResources?.map(async (contentItem) => {
      const domains = await calculateDomainsFromConcepts(contentItem.catalogItem);
      const uniqueDomains = [
        ...new Map(domains.map(d => [d.domain, d])).values(),
      ];
      const duration = await calculateDuration(contentItem, account?.id);
      const routeContentType = contentItem.itemType === 'program' ? 'program' : 'microlesson';
      const contentUrl = `${config.frontEndHost}/external/${routeContentType}/${contentItem.id}`.replace('{subdomain}', subdomain);
      const syncDate = new Date().toISOString();

      const programDefaultThumbnail = `https://s3.amazonaws.com/${config.s3.bucket}/static-assets/program_thumbnail.png`;
      const programThumbnail = contentItem.file || programDefaultThumbnail;
      const lessonDefaultThumbnail = `https://s3.amazonaws.com/${config.s3.bucket}/static-assets/lesson_thumbnail.png`;
      const lessonThumbnail = contentItem.file || lessonDefaultThumbnail;

      return {
        contentID: contentItem.resourceId,
        activityID: `${contentItem.itemType}_${contentItem.id}`,
        contentType: 'COURSE',
        domains: uniqueDomains,
        thumbnailURL: contentItem.itemType === 'program' ? programThumbnail : lessonThumbnail,
        contentDuration: { value: duration, timeUnit: 'MINUTES' },
        languageCodes,
        lifecycle: { dateAdded: syncDate,
          lastUpdatedDate: syncDate,
          status: 'PUBLISHED' },
        description: contentItem.description || contentItem.displayName,
        title: contentItem.displayName,
        url: contentUrl,
      };
    }));

    // Call our helper function to make the external API call to workday CCL
    const response = await syncContent({
      account,
      contentItems: formattedContent,
    });

    // Log successful content syncs to the database
    if (response.status === 201 || response.status === 207) {
      const successfulItems = response.successCount || 0;

      if (successfulItems > 0 && response.data) {
        const successfulContent = response.data
          .filter(item => item.status === 201)
          .map((item) => {
            const contentId = item.contentID;
            const activity = formattedContent?.find(contentItem => contentItem.contentID === parseInt(contentId))?.activityID;
            const [contentType, activityId] = activity.split('_');

            return {
              accountId: account.id,
              activityId: parseInt(activityId),
              contentType,
              isDeleted: false,
              syncedAt: new Date(),
              status: 'PUBLISHED',
            };
          });

        if (successfulContent.length > 0) {
          await WorkdayContentData.bulkCreate(successfulContent);
        }

        // Separate successful and failed items
        const successfulData = response.data.filter(item => item.status === 201);
        const failedData = response.data.filter(item => item.status === 400);

        // Enrich successful items in data array
        response.data = successfulData.map(item => {
          const matchingContent = filteredResources.find(
            resource => resource.resourceId === parseInt(item.contentID)
          );
          return {
            ...item,
            displayName: matchingContent?.displayName,
            activityId: matchingContent?.id
          };
        });

        // Enrich failed items in errors array
        response.errors = failedData.map(error => {
          const matchingContent = filteredResources.find(
            resource => resource.resourceId === parseInt(error.contentID)
          );
          return {
            ...error,
            displayName: matchingContent?.displayName,
            activityId: matchingContent?.id
          };
        });
      }
    }
    // Handle error response (400)
    else if (response.status === 400) {
      // Enrich the error response data with additional information
      response.errors = response.errors?.flat()?.map(error => {
        const matchingContent = filteredResources.find(
          resource => resource.resourceId === parseInt(error.contentID)
        );
        return {
          ...error,
          displayName: matchingContent?.displayName,
          activityId: matchingContent?.id
        };
      });
    }

    return res.status(response.status).json(response);
  } catch (error) {
    return res.status(500).json({ error: 'Internal server error' });
  }
};
