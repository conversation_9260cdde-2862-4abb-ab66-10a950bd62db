/* eslint-disable no-param-reassign */
const db = require('../db');
const _ = require('lodash');
const config = require('../config/config');
const { getAccountFields } = require('../services/utils/accountUtils');
const { restSortToSequelize } = require('../services/utils/resourceUtils');
const { findObjectByKeys, getAccountCustomFields, getUserCustomFieldValue } = require('../services/utils/userUtils');
// eslint-disable-next-line max-len
const { resetDynamicGroupMembers, getPreviewMembers, loadGroup, parseRule, getPreviewCountMembers, getDynamicGroupUsers, getCsvFileName } = require('../services/utils/groupUtils');
const { genDynamicUserCSV } = require('../services/utils/reportingUtils');
const logger = require('../logger');

const GroupRule = db.groupRules;
const GroupRuleValues = db.groupRuleValues;
const Group = db.groups;

const restQueryToSequelize = (query, defaults) => {
  const whereClause = { ..._.omit(query, ['$limit', '$skip', '$sort', 'topics', 'tags']) };

  const limitOverride = 500;

  const newQuery = {
    ...defaults,
    where: { ...defaults.where, ...whereClause },
  };

  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(limitOverride, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }
  return newQuery;
};

const restQueryForFilters = (query) => {
  const addFilters = [];
  const reportColumns = [
    'firstName',
    'lastName',
    'email',
    'title',
    'role',
    'location',
    'hireDate',
    'hireYear',
    'hireMonth',
    'employeeId',
    'managerEmail',
    'city',
    'stateCode',
    'countryCode',
    'exempt',
    'supervisor',
    'jobGroup',
    'department',
    'orgLevel',
    'remote',
  ];
  // eslint-disable-next-line curly
  if (!query) return addFilters;
  Object.entries(query).forEach(([key, value]) => {
    if (reportColumns.includes(key)) {
      let likeValue = value?.$like;
      if (likeValue && likeValue !== '%%') {
        likeValue = likeValue.replace(/'/g, "\\'");
        switch (key) {
          case 'hireYear':
            addFilters.push(`(YEAR(hireDate) = '${likeValue.replace('%', '')}')`);
            break;

          case 'hireMonth':
            addFilters.push(`(MONTH(hireDate) = '${likeValue.replace('%', '')}')`);
            break;

          // eslint-disable-next-line no-case-declarations
          case 'exempt':
            const exemptVal = ['%1%', '%TRUE%', '%true%'].includes(likeValue) ? 1 : 0;
            addFilters.push(`(exempt = '${exemptVal}')`);
            break;

          // eslint-disable-next-line no-case-declarations
          case 'remote':
            const remoteVal = /%y(e|es)?%/i.test(likeValue) ? 1 : 0;
            addFilters.push(`(u.${key} LIKE '%${remoteVal}%')`);
            break;

          default:
            addFilters.push(`(u.${key} LIKE '%${likeValue}%')`);
            break;
        }
      }
    }
  });

  return addFilters;
};

module.exports.list = async (req, res, next) => {
  const group = req.group;
  const defaults = {
    order: [['id', 'ASC']],
    limit: config.paginate.default,
    offset: 0,
    where: { groupId: group.id },
    include: [{
      model: GroupRuleValues,
      as: 'values',
      required: false,
    }],
  };

  const finalQuery = restQueryToSequelize(req.query, defaults);
  const pagedResult = {
    limit: finalQuery.limit,
    skip: finalQuery.offset,
  };

  try {
    // Do a count query without skip and limit to get total
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
    };
    const count = await GroupRule.count(countQuery);
    const results = await GroupRule.findAll(finalQuery);
    res.json({ total: count, ...pagedResult, data: results });
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  try {
    res.json(req.groupRule);
  } catch (err) {
    next(err);
  }
};

module.exports.create = async (req, res, next) => {
  let group = req.group;
  try {
    if (group.groupType === 'static') {
      const id = group.id;
      const err = new Error(req.i18n.t('groups.cannot_add_to_static_Error', { id }));
      err.status = 400;
      throw err;
    }
    // find custom account fields and pass them along to the sql parser so they
    // can be evaluated along with the default user fields
    const accountFields = await getAccountFields(req.user.accountId);
    const ruleData = { ...req.body, groupId: group.id };
    const customFieldAttributes = accountFields.find(af => af.fieldName === ruleData.field && !af.isStandard);
    ruleData.clause = parseRule(req, ruleData, customFieldAttributes);
    const groupRule = await GroupRule.create(ruleData, {
      include: [{
        model: GroupRuleValues,
        as: 'values',
        required: false,
      }],
    });

    // update group members with new rule
    group = await loadGroup(group.id);
    await resetDynamicGroupMembers(group);

    res.json(groupRule);
  } catch (err) {
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  let group = req.group;
  const groupRule = req.groupRule;
  try {
    if (group.groupType === 'static') {
      const id = group.id;
      const err = new Error(req.i18n.t('groups.cannot_modify_rules_of_static_Error', { id }));
      err.status = 400;
      throw err;
    }
    // find custom account fields and pass them along to the sql parser so they
    // can be evaluated along with the default user fields
    const accountFields = await getAccountFields(req.user.accountId);
    const ruleData = _.merge(groupRule, req.body);
    const customFieldAttributes = accountFields.find(af => af.fieldName === ruleData.field && !af.isStandard);
    const clause = parseRule(req, ruleData, customFieldAttributes);
    const newRule = await groupRule.update({
      ...req.body,
      clause,
    }, {
      include: [{
        model: GroupRuleValues,
        as: 'values',
        required: false,
      }],
    });

    // update group members with patched rule
    group = await loadGroup(group.id);
    await resetDynamicGroupMembers(group);

    res.json(newRule);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /groups/rules/preview?$limit={limit}&$skip={skip}:
 *  post:
 *   summary: Preview Rules
 *   tags: [Groups]
 *   security:
 *       - JWT: []
 *   parameters:
 *    - name: limit
 *      in: path
 *      schema:
 *        type: integer
 *      description: limit (integer) for number of records
 *      required: true
 *    - name: skip
 *      in: path
 *      schema:
 *        type: integer
 *        example: 0
 *      description: offset or skip position before beginning to return result
 *      required: true
 *    - in: body
 *      name: body
 *      description: Suggested Fields parameters
 *      required: true
 *      schema:
 *        type: object
 *        properties:
 *          rules:
 *            type: array
 *            items:
 *              type: object
 *              properties:
 *                field:
 *                  type: string
 *                  example: 'firstName'
 *                operator:
 *                  type: string
 *                  example: 'isNot'
 *                values:
 *                  type: array
 *                  items:
 *                    type: object
 *                    properties:
 *                      value:
 *                        type: string
 *                        example: 'TestFirstName'
 *   responses:
 *    200:
 *      description: Successful operation
 *      content:
 *        application/json
 *      schema:
 *        type: object
 *        properties:
 *          total:
 *            type: integer
 *            description: total number of records.
 *          limit:
 *            type: integer
 *            description: limit records per page.
 *          skip:
 *            type: integer
 *            description: skip records
 *          data:
 *            type: array
 *            description: Array of userGroups object.
 *            items:
 *              properties:
 *                memberId:
 *                  type: integer
 *                memberType:
 *                  type: string
 *                user:
 *                  $ref: '#/definitions/users'
 *    401:
 *     description: Unauthorized
 *    400:
 *     description: Bad Request
 *    5XX:
 *     description: Unexpected error
 */
module.exports.preview = async (req, res, next) => {
  const query = req.query;
  const rules = req.body.rules;
  let results = [];
  try {
    let limit = config.paginate.default;
    let skip = 0;
    if (query.$limit !== undefined) {
      limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      skip = parseInt(query.$skip) || 0;
    }
    let shortBy = [['firstName', 'ASC']];
    if (query.$sort !== undefined) {
      shortBy = Object.entries(query.$sort);
      shortBy[0][1] = shortBy[0][1] === '1' ? 'ASC' : 'DESC';
      delete query.$sort;
    }
    const filters = restQueryForFilters(query);

    // find custom account fields and pass them along to the sql parser so they
    // can be evaluated along with the default user fields
    const accountFields = await getAccountFields(req.user.accountId);
    if (rules.length) {
      for (const rule of rules) {
        const customFieldAttributes = accountFields.find(af => af.fieldName === rule.field && !af.isStandard);
        rule.clause = parseRule(req, rule, customFieldAttributes);
      }
      results = await getPreviewMembers(req.user.accountId, limit, skip, rules, shortBy, filters);
    }
    res.json(results);
  } catch (err) {
    next(err);
  }
};

module.exports.ruleById = async (req, res, next, id) => {
  try {
    const groupRule = await GroupRule.findOne({
      where: {
        id,
        groupId: req.group.id,
      },
      include: [{
        model: GroupRuleValues,
        as: 'values',
        required: false,
      }],
    });
    if (!groupRule) {
      const err = new Error(req.i18n.t('groups.group_rule_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.groupRule = groupRule;
    next();
  } catch (err) {
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  let group = req.group;
  const groupRule = req.groupRule;
  try {
    if (group.groupType === 'static') {
      const id = group.id;
      const err = new Error(req.i18n.t('groups.cannot_delete_rules_from_static_Error', { id }));
      err.status = 400;
      throw err;
    }
    const deletedRule = await groupRule.destroy();

    // update group members after rule deletion
    group = await loadGroup(group.id);
    await resetDynamicGroupMembers(group);

    res.json(deletedRule);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /groups/rules/previewCountOfMembers:
 *  post:
 *   summary: Preview Count of members
 *   tags: [Groups]
 *   security:
 *       - JWT: []
 *   parameters:
 *    - in: body
 *      name: body
 *      description: Suggested Fields parameters
 *      required: true
 *      schema:
 *        type: object
 *        properties:
 *          rules:
 *            type: array
 *            items:
 *              type: object
 *              properties:
 *                field:
 *                  type: string
 *                  example: 'firstName'
 *                operator:
 *                  type: string
 *                  example: 'isNot'
 *                values:
 *                  type: array
 *                  items:
 *                    type: object
 *                    properties:
 *                      value:
 *                        type: string
 *                        example: 'TestFirstName'
 *   responses:
 *    200:
 *      description: Successful operation
 *      content:
 *        application/json
 *      schema:
 *        type: object
 *        properties:
 *          total:
 *            type: integer
 *            description: total number of records.
 *    401:
 *     description: Unauthorized
 *    400:
 *     description: Bad Request
 *    5XX:
 *     description: Unexpected error
 */
module.exports.previewCountOfMembers = async (req, res, next) => {
  const rules = req.body.groupRules;
  try {
    // find custom account fields and pass them along to the sql parser so they
    // can be evaluated along with the default user fields
    const accountFields = await getAccountFields(req.user.accountId);

    for (const rule of rules) {
      const customFieldAttributes = accountFields.find(af => af.fieldName === rule.field && !af.isStandard);
      rule.clause = parseRule(req, rule, customFieldAttributes);
    }
    const results = await getPreviewCountMembers(req.user.accountId, rules);
    res.json(results);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * definitions:
 *  groupRoles:
 *    type: object
 *    properties:
 *      id:
 *        type: integer
 *      groupId:
 *        type: integer
 *      createdAt:
 *        type: string
 *      updatedAt:
 *        type: string
 *      roleId:
 *        type: integer
 *
 *  groupRules:
 *    type: object
 *    properties:
 *      id:
 *        type: integer
 *      groupId:
 *        type: integer
 *      createdAt:
 *        type: string
 *      updatedAt:
 *        type: string
 *      field:
 *        type: string
 *      operator:
 *        type: string
 *      clause:
 *        type: string
 *      refId:
 *        type: string
 */
module.exports.generateCSV = async (req, res, next) => {
  const accountId = req.user.accountId;
  const groupId = req.params.id;
  const queryParams = {};
  try {
    let reportColumns = ['firstName', 'lastName', 'email', 'employeeId'];
    const timeZone = req.query.timeZone;
    delete req.query.timeZone;
    queryParams.groupId = groupId;
    queryParams.accountId = accountId;
    queryParams.query = req.query;
    let shortBy = [['firstName', 'ASC']];
    if (queryParams.query.$sort !== undefined) {
      shortBy = Object.entries(queryParams.query.$sort);
      shortBy[0][1] = shortBy[0][1] === '1' ? 'ASC' : 'DESC';
      delete queryParams.query.$sort;
    }
    queryParams.query.filters = restQueryForFilters(queryParams.query);
    const groupRuleQuery = {
      where: { groupId },
      include: [
        {
          model: GroupRuleValues,
          as: 'values',
          required: false,
        },
      ],
    };
    const group = await Group.findOne({
      where: { id: groupId },
      attributes: ['name'],
    });
    const filename = getCsvFileName(group?.name);
    const rules = await GroupRule.findAll(groupRuleQuery);
    if (rules && rules.length) {
      const keepAttributes = [];
      for (const rule of rules) {
        keepAttributes.push(rule.field);
      }
      const addedColumns = keepAttributes.filter(item => !reportColumns.includes(item));
      reportColumns = [...reportColumns, ...addedColumns];
      reportColumns = _.uniq(reportColumns)
        .map((data) => {
          return ['lastAssigned', 'lastCompleted'].includes(data) ? null : data;
        })
        .filter(item => item !== null);
      queryParams.rules = rules;
      queryParams.shortBy = shortBy;
      // eslint-disable-next-line no-use-before-define, max-len
      await genDynamicUserCSV(reportColumns, queryParams, filename, res, generateDynamicGroupReport, timeZone)(accountId);
    } else {
      const err = new Error(`Failed to load group rule: ${groupId}`);
      err.status = 404;
      throw err;
    }
  } catch (err) {
    logger.error(`Exception caught in generateCSV() for account: ${accountId}, err= ${err}`);
    next(err);
  }
};

const generateDynamicGroupReport = async (queryParams) => {
  try {
    let result = [];
    result = await getDynamicGroupUsers(queryParams);
    const gUsers = result.data.map(u => u.id);
    const accountFields = await getAccountCustomFields(queryParams.accountId);
    let userAccountFields = null;
    if (accountFields && accountFields.length) {
      userAccountFields = await getUserCustomFieldValue(queryParams.accountId, gUsers);
    }
    await Promise.all(result.data.map(async (item) => {
      if (userAccountFields && userAccountFields.length) {
        for (const aField of accountFields) {
          const searchCriteria = { id: aField.id, fieldName: aField.fieldName, userId: item.id };
          const userAccountField = await findObjectByKeys(userAccountFields, searchCriteria);
          item[aField.fieldName] = userAccountField?.value || null;
        }
      }
      if (item.hireDate) {
        item.hireMonth = new Date(item.hireDate).toLocaleString('default', { month: 'long' });
        item.hireYear = new Date(item.hireDate).getFullYear();
      } else {
        item.hireMonth = null;
        item.hireYear = null;
      }
      // in CSV download mark true as Yes and false as No
      item.remote = item.remote === 1 ? 'Yes' : 'No';
    }));

    return {
      data: result.data,
      total: result.total,
      limit: queryParams.limit,
      skip: queryParams.offset,
    };
  } catch (err) {
    throw err;
  }
};
