const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const logger = require('../logger');
const { sendAnsweredEmail, sendExpertAssignedEmail } = require('../services/email');
const { makeSorter, getQuestionsIsPrivateClause, restSortToSequelize,
  includeUserResource, includeUserResources, decryptExpert, setResourceAccess,
  updateAttachedTopics, updateAttachedTags, getResourceBundlesIds, setPublicAccess,
  searchTextFromSearchInfo, updateElasticSearch, removeFromElasticSearch } = require('../services/utils/resourceUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { deriveSearch, similarQuestions } = require('../services/utils/questionAnswerUtils');
const { getAllowedPermissions } = require('../services/acl/acl');
const { removeQuestionAnswerFromCampaigns } = require('../services/utils/campaignUtils');
const { localizeModelObject } = require('../services/utils/localizationUtils');

const Op = db.Sequelize.Op;
const QuestionAnswer = db.questionAnswers;
const Resource = db.resources;
const Topic = db.topics;
const ResourceTopic = db.resourceTopics;
const Tag = db.tags;
const ResourceTag = db.resourceTags;
const User = db.users;
const Accounts = db.accounts;
const FileModel = db.files;
const ResourceBundles = db.resourceBundles;
const ContentStrings = db.contentStrings;

/**
 * This is the default set of include parameters
 * We want to include the resource for the question, and the tags and topics for that resource
 * Also, get the user's company infomation
 */

const isOwner = (user, qa) => {
  return (user ? user.id === qa.userId : false);
};

const isAssignee = (user, qa) => {
  return (user.id === qa.expertId);
};

const getIncludeParams = async (user) => {
  const bundleIds = await getResourceBundlesIds(user ? user.accountId : null);
  const includeParams = [
    {
      model: Resource,
      as: 'resource',
      include: [
        {
          model: Topic,
          through: 'resourceTopics',
        },
        {
          model: Tag,
          through: 'resourceTags',
        },
        {
          model: ResourceBundles,
          where: {
            bundleId: {
              [Op.in]: bundleIds,
            },
          },
        },
      ],
      required: true,
    },
    {
      model: User,
      as: 'user',
      attributes: ['firstName', 'lastName', 'email', 'isVerified'],
      include: {
        model: Accounts,
        attributes: ['id','name'],
        through: {
          attributes: [],
        },
      },
    },
    {
      model: User,
      as: 'expert',
      attributes: ['firstName', 'lastName', 'title', 'description', 'avatarId',
        'bannerId'],
      include: [{
        model: FileModel,
        as: 'avatar',
      }],
    },
    {
      model: ContentStrings,
      where: {
        model: 'questionAnswer',
      },
      required: false,
    },
  ];
  return includeParams;
};

/*
 * Sequelize sort order for topics within the resource
 */
const topicOrder = [
  { model: Resource, as: 'resource' },
  { model: Topic, through: 'resourceTopics' },
  { model: ResourceTopic },
  'order',
  'asc',
];

/*
 * Sequelize sort order for tags within the resource
 */
const tagOrder = [
  { model: Resource, as: 'resource' },
  { model: Tag, through: 'resourceTags' },
  { model: ResourceTag },
  'order',
  'asc',
];

/**
 * restQueryToSequelize
 *
 * This is for a find only.  It converts the query parameters we receive from the GET call into
 * a sequelize specific query.  Along the way, it adds in other relevant defaults to our initial query
 */
const restQueryToSequelize = async (req, defaults) => {
  const query = req.query;
  const whereClause = restOperatorsToSequelize({ ..._.omit(query, ['$limit', '$skip', '$sort', 'topics', 'tags', 'deletedAt']) });

  // includeParams has the default include params.  In this case, we want to add a where clause to the
  // topics and tags includes because the user is asking for specific tags and/or topics.
  const includeParams = await getIncludeParams(req.user);
  let topicsInclude = includeParams[0].include[0];
  if (query.topics) {
    const topicIds = query.topics.split(',');
    topicsInclude = {
      ...includeParams[0].include[0],
      where: {
        id: {
          [Op.in]: topicIds,
        },
      },
    };
  }

  let tagsInclude = includeParams[0].include[1];
  if (query.tags) {
    const tagIds = query.tags.split(',');
    tagsInclude = {
      ...includeParams[0].include[1],
      where: {
        id: {
          [Op.in]: tagIds,
        },
      },
    };
  }

  // set up the new query that's specific to sequelize.  Combine the defaults, then override the where clause
  // with a combo of the default where clause and the passed in where clause.
  // Then include all the things including the where clause for the tags and topics.
  const newQuery = {
    ...defaults,
    where: { ...defaults.where, ...whereClause },
    include: [
      {
        ...includeParams[0],
        include: [topicsInclude, tagsInclude],
      },
      includeParams[1], includeParams[2], includeParams[3],
    ],
  };

  // make sure to pick up ResourceBundle include
  if (includeParams[0].include[2]) {
    newQuery.include[0].include.push(includeParams[0].include[2]);
  }
  // override defaults for limit, skip, and sort if passed in by caller
  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }
  return newQuery;
};

async function checkVerifiedUser(user) {
  if (user && !user.isVerified) {
    const threeDaysInMS = 1000 * 60 * 60 * 24 * 3;

    if ((Date.now() - user.createdAt) > threeDaysInMS) {
      await user.update({ canAskExpert: false });
    } else {
      const qaCount = await QuestionAnswer.findAndCountAll({ where: { userid: user.id } });
      if (qaCount.count >= 10) {
        await user.update({ canAskExpert: false });
      }
    }
  }
}

/**
 * If the user passed in tags and topics, then they want the questions to be sorted by relevance of
 * those tags and topics.  We do this by determining how many tags and topics are associated with each
 * question and sort by the total count.
 */
async function orderByTagsAndTopics(req, data) {
  if (!req.query.tags && !req.query.topics) {
    return data;
  }
  let topicResourceIds = [];
  if (req.query.topics) {
    const topicIds = req.query.topics.split(',');
    const resourceTopics = await ResourceTopic.findAll({
      where: {
        topicId: {
          [Op.in]: topicIds,
        },
      },
    });
    topicResourceIds = resourceTopics.map(rt => rt.resourceId);
  }
  let tagResourceIds = [];
  if (req.query.tags) {
    const tagIds = req.query.tags.split(',');
    const resourceTags = await ResourceTag.findAll({
      where: {
        tagId: {
          [Op.in]: tagIds,
        },
      },
    });
    tagResourceIds = resourceTags.map(rt => rt.resourceId);
  }
  const resourceIds = topicResourceIds.concat(tagResourceIds);
  const topicTagWeight = resourceIds.reduce((total, resId) => {
    return { ...total, [resId]: (total[resId] || 0) + 1 };
  }, {});

  // Sort by topic tag weight
  const dataWithRelevance = data.map((d) => {
    const weighting = topicTagWeight[d.resource.id] || 0;
    return _.merge({}, d, { relevance: weighting });
  });

  const sortFn = makeSorter({
    relevance: -1,
  });
  const sortedData = sortFn(dataWithRelevance);

  return sortedData;
}

/**
 *   GET /question-answers (find)
 */
/**
 * @swagger
 * /question-answers?$limit={limit}&$skip={skip}:
 *   get:
 *     summary: Get all question list
 *     tags: [Questions]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *         description: Limit (integer) for number of records
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *         description: Skip (integer) for number of records
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                   description: Total number of records.
 *                 data:
 *                   type: array
 *                   description: Array of report data objects
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/questionAnswers'
 *                       - type: object
 *                         properties:
 *                           resource:
 *                             $ref: '#/components/schemas/resources'
 *                           expert:
 *                             $ref: '#/components/schemas/users'
 *                           contentStrings:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/contentStrings'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };
  const queryParams = await restQueryToSequelize(req, defaults);

  if (queryParams?.where?.parentId === 'null') {
    queryParams.where.parentId = null;
  }

  // Add in topics and tags to sort. This sorts tags and topics within each qA, but doesn't change the sort order
  // of qAs in general.
  queryParams.order = [[...queryParams.order], topicOrder, tagOrder];

  const allowedPermissions = await getAllowedPermissions(
    req,
    req.user ? req.user.id : null, 'questionAnswers', req.tokenPayload,
  );

  const isPrivateClause = getQuestionsIsPrivateClause(req.user, allowedPermissions);

  const finalQuery = {
    ...queryParams,
    where: {
      ...queryParams.where,
      ...isPrivateClause,
    },
  };
  logger.debug('questionAnswer query: %j', finalQuery);

  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };

  try {
    // Do a count query without skip and limit to get total
    const bundleIds = await getResourceBundlesIds(req.user ? req.user.accountId : null);
    const countQuery = {
      distinct: 'id',
      include: [{ // include resources because the join could affect the count, but not the extraneous stuff
        model: Resource,
        as: 'resource',
        include: {
          model: ResourceBundles,
          where: {
            bundleId: {
              [Op.in]: bundleIds,
            },
          },
        },
        required: true,
      }],
    };

    countQuery.where = _.pick(finalQuery, ['where']).where;
    const count = await QuestionAnswer.count(countQuery);
    const results = await QuestionAnswer.findAll(finalQuery);
    const flattenedData = results.map((record) => {
      return localizeModelObject(req, record.get({ plain: true }), req.i18n.language, false);
    });

    // Sort qAs by passed in tag and topic relevance
    const orderedData = await orderByTagsAndTopics(req, flattenedData);

    const decryptedData = decryptExpert(orderedData);

    // include the event information for each qA (helpful, saved, shared, viewed)
    const data = await includeUserResources(req, decryptedData);

    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 *   GET /question-answers/id (get)
 */

/**
 * @openapi
 * /question-answers/{questionId}:
 *   get:
 *     summary: View question by questionId
 *     tags:
 *       - Questions
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: questionId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 questionAnswers:
 *                   $ref: '#/components/schemas/questionAnswers'
 *                 resource:
 *                   $ref: '#/components/schemas/resources'
 *                 expert:
 *                   $ref: '#/components/schemas/users'
 *                 contentStrings:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/contentStrings'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       default:
 *         description: Unexpected error
 */
module.exports.read = async (req, res, next) => {
  try {
    const allowedPermissions = await getAllowedPermissions(
      req,
      req.user ? req.user.id : null, 'questionAnswers', req.tokenPayload,
    );

    if (req.questionAnswer.deletedAt !== null &&
      !allowedPermissions.questionAnswers.includes('readArchived')) {
      const err = new Error(req.i18n.t('questionAnswers.qa_no_permissions_Error'));
      err.status = 401;
      throw err;
    }
    if (req.questionAnswer.isPrivate &&
      !(allowedPermissions.questionAnswers.includes('readPrivate')
        || isOwner(req.user, req.questionAnswer))) {
      const err = new Error(req.i18n.t('questionAnswers.qa_no_permissions_Error'));
      err.status = 401;
      throw err;
    }
    const flattenedRecord = req.questionAnswer.get({ plain: true });
    const categories = await req.questionAnswer.getCategories({ attributes: ['id', 'name', 'priority'] });
    const decryptedRecord = decryptExpert(flattenedRecord);
    const data = await includeUserResource(req, decryptedRecord);
    const dataWithCategories = _.merge({}, data, { categories });
    res.json(dataWithCategories);
  } catch (err) {
    next(err);
  }
};

/**
 *   POST /question-answers
 */

/**
 * @openapi
 * /question-answers:
 *   post:
 *     summary: Add new question
 *     tags:
 *       - Questions
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Required parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               question:
 *                 type: string
 *                 example: "My text question example"
 *               isPrivate:
 *                 type: boolean
 *                 example: false
 *               notifyUser:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 questionAnswers:
 *                   $ref: '#/components/schemas/questionAnswers'
 *                 resource:
 *                   $ref: '#/components/schemas/resources'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       default:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  // Create the question and assign it to the current user
  // Also create the resource while we're at it.
  delete req.body.id;
  const qa = {
    ...req.body,
    status: 'pending', // always create in pending status
    userId: req.user.id, // set the user id to the caller
    questionEdited: req.body.question,
    resource: {
      digestable: 'questionAnswers',
    },
  };
  const searchInfo = await deriveSearch(qa);
  qa.resource.searchText = searchTextFromSearchInfo(searchInfo);

  try {
    if (!req.user.canAskExpert) {
      const err = new Error(req.i18n.t('questionAnswers.qa_user_verification_Error'));
      err.status = 400;
      throw err;
    }

    const newQa = await QuestionAnswer
      .create(qa, {
        include: [{
          association: QuestionAnswer.associations.resource,
        }],
      });
    await setResourceAccess(req.user, newQa.resource);
    await updateElasticSearch(req, newQa.resource.id, searchInfo);
    await checkVerifiedUser(req.user);
    res.json(newQa);
  } catch (err) {
    next(err);
  }
};


/**
 * @openapi
 * /question-answers/{questionId}:
 *   patch:
 *     summary: Assign question to Expert
 *     tags:
 *       - Questions
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: questionId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 example: "assigned"
 *               topicIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2, 5]
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ['help']
 *               expertId:
 *                 type: integer
 *                 example: 5
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 questionAnswers:
 *                   $ref: '#/components/schemas/questionAnswers'
 *                 resource:
 *                   $ref: '#/components/schemas/resources'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       default:
 *         description: Unexpected error
 */
module.exports.patch = async (req, res, next) => {
  const qa = req.questionAnswer;
  const initialStatus = qa.status;

  // Don't allow the userId, the resourceId, or the resource data to be changed by the user
  delete req.body.resourceId;
  delete req.body.userId;
  delete req.body.resource;

  try {
    // To patch a question, you need one of the following:
    // 'update' permissions
    // 'updateOwn' permissions AND you're the owner
    // 'updateAssigned' permissions AND you're the expert assigned
    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'questionAnswers', req.tokenPayload);
    if (!(allowedPermissions.questionAnswers.includes('update')
      || (allowedPermissions.questionAnswers.includes('updateOwn')
        && isOwner(req.user, qa))
      || (allowedPermissions.questionAnswers.includes('updateAssigned')
        && isAssignee(req.user, qa))
    )) {
      const err = new Error(req.i18n.t('questionAnswers.qa_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    const updatedQa = await qa.update(req.body);
    if (req?.body?.categoryIds) {
      await qa.setCategories(req.body.categoryIds);
    }

    // check if public access has changed
    if (req.body.isPublic !== undefined &&
        req.body.isPublic !== updatedQa.resource.isPublic) {
      const newResource = await updatedQa.resource.update({ isPublic: req.body.isPublic });
      await setPublicAccess(newResource);
    }

    const searchInfo = await deriveSearch(updatedQa);
    const searchText = searchTextFromSearchInfo(searchInfo);
    await updatedQa.resource.update({ searchText });
    await updateElasticSearch(req, updatedQa.resource.id, searchInfo);

    // Add or remove tags and topics as requested
    await updateAttachedTopics(req, qa);
    await updateAttachedTags(req, qa);
    // Get it again to get all updated tags and topics
    const sequelizeQa = await QuestionAnswer.findByPk(qa.id, {
      include: await getIncludeParams(req.user),
      order: [topicOrder, tagOrder],
    });
    const flattenedRecord = sequelizeQa.get({ plain: true });
    const decryptedRecord = decryptExpert(flattenedRecord);
    const finalQa = await includeUserResource(req, decryptedRecord);

    const recipient = await User.findOne({
      where: { id: finalQa.userId },
    });
    // hack alert: if the expertId is 0, then the expert is Emtrain Support
    // (we added an Emtrain Support "expert" to the system
    // but it this expert is not in the database anywhere)
    let expert;
    if (finalQa.expertId === 0) {
      expert = { firstName: 'Emtrain', lastName: 'Support' };
    } else {
      expert = await User.findOne({
        where: { id: finalQa.expertId },
      });
    }
    if (initialStatus !== 'answered' && finalQa.status === 'answered' && finalQa.notifyUser) {
      const recipientEmail = recipient.email ? recipient.email : finalQa.notifyEmail;
      if (recipientEmail) {
        const accounts = await recipient.getAccounts();
        const isScormUser = !!recipient.scormId;

        await sendAnsweredEmail(
          accounts[0].subdomain,
          recipientEmail,
          finalQa.id,
          finalQa.question,
          finalQa.answer,
          isScormUser,
          expert.firstName,
          expert.lastName,
        );
      }
    } else if (
      finalQa.status === 'assigned'
      && initialStatus !== 'assigned'
      && finalQa.expertId
      && !req.body.questionEdited
    ) {
      await sendExpertAssignedEmail(req.user.accounts[0].subdomain, expert.email, expert.firstName);
    }

    res.json(finalQa);
  } catch (err) {
    next(err);
  }
};

/**
 *   DELETE /question-answers/id
 */

/**
 * @openapi
 * /question-answers/{questionId}:
 *   delete:
 *     summary: Archive Question
 *     tags:
 *       - Questions
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: questionId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 questionAnswers:
 *                   $ref: '#/components/schemas/questionAnswers'
 *                 resource:
 *                   $ref: '#/components/schemas/resources'
 *                 expert:
 *                   $ref: '#/components/schemas/users'
 *                 contentStrings:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/contentStrings'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       default:
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  const qa = req.questionAnswer;

  try {
    // To delete (archive) a question, you need one of the following:
    // 'delete' permissions
    // 'deleteOwn' permissions AND you're the owner
    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'questionAnswers', req.tokenPayload);
    if (!(allowedPermissions.questionAnswers.includes('delete')
      || (allowedPermissions.questionAnswers.includes('deleteOwn')
        && isOwner(req.user, qa)))) {
      const err = new Error(req.i18n.t('questionAnswers.qa_no_permissions_Error'));
      err.status = 401;
      throw err;
    }
    const updatedQa = await qa.destroy();
    await removeFromElasticSearch(req, updatedQa.resourceId);

    // remove relationship records since our tables don't have cascade constraints
    await ResourceTag.destroy({
      where: {
        resourceId: updatedQa.resourceId,
      },
    });
    await ResourceTopic.destroy({
      where: {
        resourceId: updatedQa.resourceId,
      },
    });
    await removeQuestionAnswerFromCampaigns(qa.id);
    res.json(updatedQa);
  } catch (err) {
    next(err);
  }
};


/**
 * @openapi
 * /similar-questions:
 *   post:
 *     summary: Add new question
 *     tags:
 *       - Questions
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Required parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               text:
 *                 type: string
 *                 example: "My text question example"
 *     responses:
 *       200:
 *         description: Added new question successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 */
module.exports.findSimilarByText = async (req, res, next) => {
  try {
    let similarQuestionData = [];
    // the user's account or emtrain's account (id: 1). Emtrain has the default resourceBundle which should go to everybody
    const accountId = req.user && req.user.accountId;
    if (config.questionRecommendations.enabled) {
      if (config.questionRecommendations.searchMode === 'python_script') {
        const similarIds = await similarQuestions(5, req.body.text, accountId);
        similarQuestionData = await QuestionAnswer.findAll({
          attributes: ['id', 'question', 'questionEdited'],
          where: {
            id: {
              [Op.in]: Object.keys(similarIds).map(x => parseInt(x)),
            },
          },
        // include: {
        //   model: Resource,
        //   as: 'resource',
        // },
        });
      } else if (config.questionRecommendations.searchMode === 'sql_text_search') {
        similarQuestionData = await QuestionAnswer.findAll({
          attributes: ['id', 'question', 'questionEdited'],
          where: {
            [Op.and]: [
              db.Sequelize.literal('MATCH (questionEdited) AGAINST(:searchText IN NATURAL LANGUAGE MODE)'),
              { [Op.or]: [{ status: 'answered' }, { status: 'replied' }] },
              { isPrivate: 0 },
              { parentId: null },
            ],
          },
          replacements: { searchText: req.body.text },
          limit: 5,
        });
      }
    }
    res.json(similarQuestionData);
  } catch (err) {
    next(err);
  }
};

/**
 * Middleware to retrieve the question when an id is passed in the route
 */
module.exports.questionById = async (req, res, next, id) => {
  try {
    const qa = await QuestionAnswer.findOne({
      where: { id },
      include: await getIncludeParams(req.user),
      order: [topicOrder, tagOrder],
      paranoid: false, // includes soft deleted (archived) records
    });
    if (!qa) {
      const err = new Error(req.i18n.t('questionAnswers.qa_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.questionAnswer = localizeModelObject(req, qa, req.i18n.language, false);
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * Sandbox function attached to a route for testing sequelize queries.  This is similar to the final query
 * that we're trying to build in the list (find) function
 */
module.exports.testQuery = async (req, res, next) => {
  const topics = ['Bullying', 'Collaboration'];
  try {
    const query = {
      limit: 50,
      offset: 0,
      include: [
        {
          model: Resource,
          as: 'resource',
          include: [
            {
              model: Topic,
              through: 'resourceTopics',
              where: {
                title: {
                  [Op.in]: topics,
                },
              },
            },
            {
              model: Tag,
              through: 'resourceTags',
            },
          ],
        },
        {
          model: User,
          as: 'user',
          include: [{
            model: Accounts,
          }, {
            model: FileModel,
            as: 'avatar',
          }],
        },
      ],
      order: [
        ['id', 'ASC'], // Gotta sort on the main questionAnswers first
        topicOrder, tagOrder,
      ],
    };
    const qas = await QuestionAnswer.findAll(query);
    res.json(qas);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /question-answers/bulkQuestions:
 *   post:
 *     summary: Fetch questions
 *     tags:
 *       - Questions
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Required parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               questionIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2, 3]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: Array of report data objects.
 *                   items:
 *                     type: object
 *                     properties:
 *                       questionAnswers:
 *                         $ref: '#/components/schemas/questionAnswers'
 *                       resource:
 *                         $ref: '#/components/schemas/resources'
 *                       expert:
 *                         $ref: '#/components/schemas/users'
 *                       contentStrings:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/contentStrings'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       default:
 *         description: Unexpected error
 */
module.exports.bulkQuestions = async (req, res, next) => {

  try {
    const { questionIds } = req.body;

    if (!Array.isArray(questionIds) || questionIds.length === 0) {
      const err = new Error(req.i18n.t('questionAnswers.qa_no_question_ids'));
      err.status = 400;
      throw err;
    }
    const includeParams = await getIncludeParams(req.user);
    const questionAnswers = await QuestionAnswer.findAll({
      where: {
        id: {
          [Op.in]: questionIds,
        },
      },
      include: includeParams,
      order: [topicOrder, tagOrder],
      paranoid: false,
    });
    const localized = questionAnswers.map(qa =>
      localizeModelObject(req, qa, req.i18n.language, false)
    );

    res.json(localized);
  } catch (err) {
    next(err);
  }
};
/**
 * @openapi
 * /question-answers/bulkQuestions:
 *   patch:
 *     summary: Send Answer
 *     tags:
 *       - Questions
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               expertId:
 *                 type: integer
 *                 example: 5
 *               isPrivate:
 *                 type: boolean
 *                 example: false
 *               status:
 *                 type: string
 *                 example: "assigned"
 *               questionIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 example: [1, 2]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 questionAnswers:
 *                   $ref: '#/components/schemas/questionAnswers'
 *                 resource:
 *                   $ref: '#/components/schemas/resources'
 *                 expert:
 *                   $ref: '#/components/schemas/users'
 *                 contentStrings:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/contentStrings'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       default:
 *         description: Unexpected error
 */
module.exports.bulkQuestionsUpdate = async (req, res, next) => {
  const updates = req.body;
  const { questionIds, status, expertId, isPrivate, questionEdited } = updates;
  if (!Array.isArray(questionIds) || questionIds.length === 0) {
    return res.status(400).json({ message: req.i18n.t('questionAnswers.qa_no_question_ids') });
  }

  try {
    const results = [];

    for (const questionId of questionIds) {

      // Fetch the question by ID
      const qa = await QuestionAnswer.findByPk(questionId, {
        include: await getIncludeParams(req.user),
        paranoid: false,
      });

      if (!qa) {
        results.push({ questionId, error: `Question with ID ${questionId} not found.` });
        continue;
      }

      const initialStatus = qa.status;

      // Permission check
      const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'questionAnswers', req.tokenPayload);
      if (!(allowedPermissions.questionAnswers.includes('update')
        || (allowedPermissions.questionAnswers.includes('updateOwn') && isOwner(req.user, qa))
        || (allowedPermissions.questionAnswers.includes('updateAssigned') && isAssignee(req.user, qa))
      )) {
        results.push({ questionId, error: 'Unauthorized to update this question.' });
        continue;
      }
      let finalQa;
      // for assign
      if (status === 'assigned' || status === 'support') {
        // Patch the question answer
        const updatedQa = await qa.update({
          status,
          expertId,
          isPrivate
        });

        // Update the associated resource's isPublic field if needed
        if (isPrivate !== undefined && updatedQa.resource?.isPublic !== !isPrivate) {
          const newResource = await updatedQa.resource.update({ isPublic: !isPrivate });
          await setPublicAccess(newResource);
        }

        const searchInfo = await deriveSearch(updatedQa);
        const searchText = searchTextFromSearchInfo(searchInfo);
        await updatedQa.resource.update({ searchText });

        // Update topics/tags if needed (you can enhance this to accept topics/tags per record)
        await updateAttachedTopics(req, qa);
        await updateAttachedTags(req, qa);

        // Reload and finalize the updated QA
        const sequelizeQa = await QuestionAnswer.findByPk(qa.id, {
          include: await getIncludeParams(req.user),
          order: [topicOrder, tagOrder],
        });

        const decryptedRecord = decryptExpert(sequelizeQa.get({ plain: true }));
        finalQa = await includeUserResource(req, decryptedRecord);

        // Notification logic
        const recipient = await User.findByPk(finalQa.userId);
        let expert;
        if (finalQa.expertId === 0) {
          expert = { firstName: 'Emtrain', lastName: 'Support' };
        } else {
          expert = await User.findByPk(finalQa.expertId);
        }

        if (initialStatus !== 'answered' && finalQa.status === 'answered' && finalQa.notifyUser) {
          const recipientEmail = recipient?.email || finalQa.notifyEmail;
          if (recipientEmail) {
            const accounts = await recipient.getAccounts();
            const isScormUser = !!recipient.scormId;
            await sendAnsweredEmail(
              accounts[0].subdomain,
              recipientEmail,
              finalQa.id,
              finalQa.question,
              finalQa.answer,
              isScormUser,
              expert.firstName,
              expert.lastName
            );
          }
        } else if (
          finalQa.status === 'assigned' &&
          initialStatus !== 'assigned' &&
          finalQa.expertId &&
          !questionEdited
        ) {
          await sendExpertAssignedEmail(req.user.accounts[0].subdomain, expert.email, expert.firstName);
        }
      } else if (status === 'pending') {
        // update the status back to pending and unassign
        finalQa = await qa.update({
          status,
          expertId: null,
          expertReminderDate: null
        });
      }
      results.push(finalQa);
    }

    res.json(results);
  } catch (err) {
    next(err);
  }
}

/**
 * @swagger
 * components:
 *   schemas:
 *     questionAnswers:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         promiseDate:
 *           type: string
 *           format: date-time
 *         status:
 *           type: string
 *         question:
 *           type: string
 *         questionEdited:
 *           type: string
 *         answer:
 *           type: string
 *         expertId:
 *           type: integer
 *         createdAt:
 *           type: integer
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         notifyUser:
 *           type: boolean
 *         userId:
 *           type: integer
 *         isPrivate:
 *           type: boolean
 *         url:
 *           type: string
 *           format: uri
 *         expertReminderDate:
 *           type: string
 *           format: date-time
 *         resourceId:
 *           type: integer
 *         deletedAt:
 *           type: string
 *           format: date-time
 *         notifyEmail:
 *           type: string
 *           format: email
 *         linktext:
 *           type: string
 *         expert:
 *           $ref: '#/components/schemas/users'
 *         contentStrings:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/contentStrings'
 *         user:
 *           type: object
 *           properties:
 *             isVerified:
 *               type: boolean
 *             accounts:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 */
