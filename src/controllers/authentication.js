const { get } = require('lodash');
const jwt = require('jwt-simple');
const jwtWeb = require('jsonwebtoken');
const axios = require('axios');
const querystring = require('querystring');
const { v4: uuidv4 } = require('uuid');
const config = require('../config/config');
const logger = require('../logger');
const db = require('../db');

// Load keys
const { getPublicKey, getPrivateKey } = require('../services/utils/loadAuthKeys');
const publicKey = getPublicKey();
const privateKey  = getPrivateKey();

const { UnprocessableEntityError } = require('../services/utils/errors');
const { sendVerification, getLongToken, validatePassword, tokenForUser, initializeFailedLogin }
  = require('../services/utils/authManagementUtils');
const { accountRecordFromHostname } = require('../services/utils/accountUtils');
const { createSession } = require('../services/utils/sessions');
const { getAllowedPermissions } = require('../services/acl/acl');
const { setUserRole, validateUserCustomFields, checkEditableFields,
  findUserIdByEmailAndAccount, findUserByEmailAndAccount, separateUserFields, findUserByEmployeeIdOrEmailAndAccount,
  getRoleId, getLinkedInAccessToken, getLinkedInMemberProfile, getMicrosoftMemberDetails,
  updateEmtrainRoles, setUserAccountFieldValues } = require('../services/utils/userUtils'); // getLinkedInMemberEmailAddress
const { isValidEmailAddress } = require('../services/email');
const { getValidStateCode, getValidCountryCode } = require('../services/utils/iso3166Utils');
const { addUserToAccountRoster } = require('../services/utils/groupUtils');
const { getEmtrainSystemAdminUser } = require('../services/utils/authUtils');
const { createPresignedURL, S3FileExists } = require('../services/utils/fileUtils');

const Op = db.Sequelize.Op;

const User = db.users;
const Role = db.roles;
const Accounts = db.accounts;
const AccountUser = db.accountUsers;
// const UserRole = db.userRoles;
const Integrations = db.integrations;
const ScormPrograms = db.scormPrograms;

const gleanAccountFromRequest = async (req, user) => {
  let accountId;

  // get it from the request url
  const accountRecord = await accountRecordFromHostname(req);

  if (accountRecord) {
    // check to see if user has access to the account
    const accountUser = await AccountUser.findOne({
      where: {
        accountId: accountRecord.id,
        userId: user.id,
      },
      include: [{
        model: User,
        attributes: ['adminAccountId'],
        required: false,
      }],
    });

    if (accountUser) {
      accountId = accountRecord.id;
      // clear adminAccountId upon normal signin if it was set previously for an SSO admin using Log Me In
      if (accountUser?.user?.adminAccountId) {
        await User.update({ adminAccountId: null }, { where: { id: user.id } });
      }
    } else {
      // emtrain system admins have access to all accounts
      const emtrainSystemAdminUser = await getEmtrainSystemAdminUser(user);
      if (emtrainSystemAdminUser) {
        accountId = accountRecord.id;
      }
    }
  }
  if (accountId === undefined) {
    const id = user.id;
    const err = new Error(req.i18n.t('users.invalid_account_Error', { id }));
    err.status = 401;
    throw err;
  }
  return accountRecord;
};

const checkForUserOrAccountPermission = async (req, allowedPermissions) => {
  let selfSignupError = false;
  const userHasCreatePerm = allowedPermissions && allowedPermissions.users &&
    (allowedPermissions.users.includes('create') || allowedPermissions.users.includes('createAccount'));
  if (!userHasCreatePerm) {
    const accountRecord = await accountRecordFromHostname(req);

    if (accountRecord && !accountRecord.selfSignup) {
      selfSignupError = true;
    } else if (accountRecord && accountRecord.selfSignup && !req.body.oAuthProvider) {
      if (accountRecord.signupCode && accountRecord.signupCode !== req.body.signupCode) {
        selfSignupError = true;
      }
    }
  }

  if (selfSignupError) {
    const err = new Error(req.i18n.t('authentication.signup_Code_Error'));
    err.status = 401;
    throw err;
  }
};

const checkUserForOAuthProvider = async (req, userOAuth, seletedProvider) => {
  if (userOAuth && seletedProvider && userOAuth !== seletedProvider) {
    const err = new Error(JSON.stringify({ userOAuth, error: req.i18n.t('authentication.login_OAuth_Error') }));
    err.status = 401;
    throw err;
  }
};

const createScormProgram = async (params) => {
  const { userId, scormId, resourceId, model } = params;
  const scormProgram = await ScormPrograms.create({
    userId,
    scormId,
    resourceId,
    model,
  });

  return scormProgram;
};

const createScormProgamUser = async (params) => {
  const { firstName, lastName, scormId, accountId,
    resourceId, model, language } = params;

  const userData = {
    scormId,
    password: uuidv4(), // random password, shouldn't ever be used
    isVerified: true,
  };
  if (firstName) {
    userData.firstName = firstName;
  }
  if (lastName) {
    userData.lastName = lastName;
  }

  if (language) {
    userData.language = language;
  } else {
    userData.language = 'EN';
  }

  const user = await User.create(userData, { skipPasswordHash: true });
  if (user) {
    const scormRole = await setUserRole(user, 'scorm');
    await AccountUser.create({
      userId: user.id,
      accountId,
      roleId: scormRole.roleId,
    });
    await ScormPrograms.create({
      userId: user.id,
      scormId,
      resourceId,
      model,
    });
    // update emtrainroles
    await updateEmtrainRoles(user.id);
  }
  return user;
};

const getScormProgramUser = async (params) => {
  const { scormId, account, accountId, resourceId, model, suspendData, isEmail } = params;
  let user;
  let createNewUser = false;
  let createScormProgramOnly = false;
  const newParams = params;

  // get all users-scormProgams with the given resourceId
  const whereScormProg = {
    model,
    resourceId,
  };
  // if we have suspendData, lookup the user->scormProgram where scormProgram.id = suspendData
  if (suspendData) {
    whereScormProg.id = suspendData;
  }

  const users = await User.findAll({
    paranoid: false,
    where: {
      [Op.or]: [
        { scormId },
        { email: scormId },
      ],
    },
    include: [{
      model: Accounts,
      where: {
        id: accountId,
      },
      required: true,
    }, {
      model: ScormPrograms,
      where: whereScormProg,
      required: !!suspendData,
    }],
  });

  if (!users || (users && users.length === 0)) {
    if (suspendData) {
      // we have suspend data but did not find the scormProgram
      const err = new Error(`Scorm user not found suspendData: ${suspendData} scormId: ${scormId}`);
      err.status = 401;
      throw err;
    } else {
      newParams.existingEmailUser = null;
      createNewUser = true;
    }
  }

  if (users && users.length > 0) {
    // parse the user data
    const migratedUser = users.find((u => u.scormId && u.scormId === u.internalScormId));
    let scormProgramExists = false;
    if (!migratedUser) {
      users.forEach((u) => {
        if (u.scormPrograms &&
          u.scormPrograms.some(sp => sp.resourceId === resourceId && sp.model === model)
        ) {
          scormProgramExists = true;
        }
      });
    }

    if (suspendData) { // continue scormProgram
      user = users[0];
    } else {
      if (account.reassignScorm) {
        createNewUser = true;
      } else if (migratedUser) {
        createScormProgramOnly = true;
      } else if (scormProgramExists) {
        createNewUser = true;
      } else {
        createScormProgramOnly = true;
      }

      if (migratedUser) {
        await migratedUser.update({ internalScormId: null });
      }
      if (scormProgramExists) {
        const emailUser =
          isEmail && users.find((u => u.email && u.email === scormId));
        newParams.existingEmailUser = emailUser;
      }
    }
  }

  if (createNewUser) {
    if (users && users.length > 0) {
      // first reverse the list of users so we get the most recent one to get the language
      users.reverse();
      const scormIdUser =
        users && users.find((u => u.scormId && u.scormId === scormId));
      if (scormIdUser && scormIdUser.language) {
        newParams.language = scormIdUser.language.toUpperCase();
      } else {
        newParams.language = 'EN';
      }
    }
    user = await createScormProgamUser(newParams);
  } else if (createScormProgramOnly) {
    user = users[0];
    newParams.userId = user.id;

    await createScormProgram(newParams);
  }

  if (!user.scormId) {
    await user.update({ scormId });
  }
  if (user.deletedAt) {
    await user.restore();
  }

  return user;
};

const getAdditionalAccountData = async (req) => {
  const additionalData = {};
  // This code is added to the signin API because JETT does not call the current account API.
  const account = get(req, 'user.accounts[0]');
  if (account.fileId) {
    const logo = await account.getFile({ attributes: ['path'], raw: true });
    additionalData.customLogoSrc = logo.path;
  }
  if (account.darkLogoFileId) {
    const darkLogo = await account.getDarkLogoFile({ attributes: ['path'], raw: true });
    additionalData.customDarkLogoSrc = darkLogo.path;
  }
  if (account.enableAccountDemographicsFeature) {
    const demographicFields = await account.getAccountDemographicField({ raw: true });
    additionalData.demographicFields = demographicFields;
  }
  return additionalData;
};

const saveSigninEvent = async (req) => {
  if (req.user) {
    await createSession(req);
    const signinEvent = {
      type: 'signin',
      trackableId: req.user.id,
      trackableType: 'users',
      userId: req.user.id,
      accountId: req.user.accountId,
      sessionId: req.sessionId,
    };
    await db.events.create(signinEvent);
  }
};

const signInSignUpOauthUser = async (req, authProvider) => {
  const allowedPermissions = await getAllowedPermissions(req, req.user ? req.user.id : null, 'users', req.tokenPayload);
  req.body.oAuthProvider = authProvider;
  const accountRecord = await accountRecordFromHostname(req);
  const newUserAccountId = req.body.accountId || (accountRecord && accountRecord.id);
  delete req.body.accountId;

  if (accountRecord && accountRecord.status === 'deactive') {
    const error = new Error(req.i18n.t('authentication.deactive_account_Error'));
    error.status = 401;
    throw error;
  }

  // name need to be set when creating users this way.
  if (!req.body.firstName) {
    const error = new Error(req.i18n.t('authentication.signup_missingdata_Error'));
    error.status = 422;
    throw error;
  }

  // email must be set if account requires it. If email is set, it's validated in the model.
  if (
    !req.body.email &&
    accountRecord &&
    (accountRecord.authField === 'email' || (accountRecord.authField === 'emailOrEmployeeId' && !req.body.employeeId))
  ) {
    const error = new Error(req.i18n.t('authentication.signup_missingdata_Error'));
    error.status = 422;
    throw error;
  }
  // Employee id must be set if account requires it.
  if (
    !req.body.employeeId &&
    accountRecord &&
    (accountRecord.authField === 'employeeId' || (accountRecord.authField === 'emailOrEmployeeId' && !req.body.email))
  ) {
    const error = new Error(req.i18n.t('authentication.signup_missingdata_Error'));
    error.status = 422;
    throw error;
  }
  await validateUserCustomFields(req.body, newUserAccountId);

  // See if the user already exists in this account - email and employeeId need to be unique
  const existingUser = await findUserByEmployeeIdOrEmailAndAccount({
    email: req.body.email,
    employeeId: req.body.employeeId,
    accountId: newUserAccountId,
    paranoid: false,
  });
  let response = {};
  const accountOptions = newUserAccountId ? { accountId: newUserAccountId } : null;
  if (!existingUser) {
    await checkForUserOrAccountPermission(req, allowedPermissions);
    //  signup
    req.body.password = uuidv4(); // random password
    req.body.oAuthProvider = authProvider;
    const newUser = await User.create(req.body, accountOptions);
    const {
      // eslint-disable-next-line no-unused-vars
      coreUserFields,
      accountFieldPromises,
    } = await separateUserFields(req.body, newUser.id, accountOptions.accountId);
    await Promise.all(accountFieldPromises);
    await setUserRole(newUser, 'user');
    if (newUserAccountId) {
      newUser.accountId = newUserAccountId;
      await AccountUser.create({
        userId: newUser.id,
        accountId: newUser.accountId,
        roleId: 2,
      });
      await addUserToAccountRoster(newUser.accountId, newUser.id);
    }
    // retrieve the user with the role association
    const newUserWithRole = await User.findByPk(newUser.id, {
      include: [
        {
          model: Role,
          through: 'userRoles',
        },
        {
          model: Accounts,
          where: {
            status: 'active',
            id: newUserAccountId,
          },
        },
      ],
    });
    newUserWithRole.password = undefined;
    newUserWithRole.accountId = newUserAccountId;
    response = { accessToken: tokenForUser(newUserWithRole), user: newUserWithRole, isNewUser: true };
  } else {
    // login
    const user = await User.findOne({
      where: { email: req.body.email },
      include: [
        {
          model: Accounts,
          where: {
            status: 'active',
            id: newUserAccountId,
          },
          required: true,
        },
      ],
    });
    await checkUserForOAuthProvider(req, user.oAuthProvider, authProvider);
    user.password = undefined;
    req.user = user;
    req.user.accountId = newUserAccountId;
    response = {
      accessToken: tokenForUser(req.user),
      user: req.user,
      isNewUser: false,
    };
  }
  return response;
};

/**
 * @openapi
 * /authentication:
 *   post:
 *     summary: Sign in user
 *     tags:
 *       - Authentication
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 example: '123456'
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/userResponse'
 *       400:
 *         description: Bad Request
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     userResponse:
 *       type: object
 *       properties:
 *         accounts:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/accounts'
 *     Account:
 *       type: object
 *       properties:
 *         accountUsers:
 *           $ref: '#/components/schemas/accountUsers'
 *     AccountUsers:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 */
module.exports.signin = async (req, res, next) => {
  // user has already had their username/password authorized via middleware on the route.
  // We just need to give them a token and the user data
  // passport has set user object to req.user
  // Remove sensitive data before sending back user data (our passport hook removes password)
  try {
    await createSession(req);
    const account = await gleanAccountFromRequest(req, req.user);
    req.user.accountId = account.id;
    let token;
    let isScormToken = false;
    // If we're signing in with a jwt token, just keep using it. This is necessary for saml/sso
    // to keep using the same token created then.
    // We also want to inform the client if the token is a scorm token even if the user is not entering through scorm
    if (req.body.strategy === 'jwt') {
      token = req.body.accessToken;
      isScormToken = req.tokenPayload && req.tokenPayload.isScorm;
    }
    let returnVal = {
      accessToken: token || tokenForUser(req.user),
      user: req.user,
      isScormToken,
    };
    // adminSSO add target account to users.accounts
    if (req.user && req.user.adminAccountId) {
      const adminAccount = await Accounts.findOne({
        where: {
          id: req.user.adminAccountId,
        },
        include: [{
          required: false,
          model: Integrations,
          where: {
            integrationType: { [Op.in]: ['scorm', 'scorm20043rd', 'scorm20044th'] },
          },
        }],
      });

      const finalAccount = adminAccount;
      finalAccount.dataValues.adminSSOaccountIsScorm = adminAccount?.integrations?.length > 0;
      finalAccount.dataValues.integrations = []; // remove integrations from the response
      returnVal.user.accounts[1] = finalAccount;
    }
    if (req.user && req.user.resetPasswordRequired && req.changePasswordToken) {
      returnVal.changePasswordToken = req.changePasswordToken;
    }
    await initializeFailedLogin(req.user);

    // This code is added to the signin API because JETT does not call the current account API.
    const logoPaths = await getAdditionalAccountData(req);
    returnVal = { ...returnVal, ...logoPaths };
    res.send(returnVal);
  } catch (error) {
    next(error);
  }
};

module.exports.apiSignin = async (req, res, next) => {
  try {
    const { email, password, accountId, authKey } = req.body;
    const checkParams = () => {
      if (!email) {
        const err = new Error('Missing parameter: email');
        err.status = 401;
        throw err;
      }
      if (!password) {
        const err = new Error('Missing parameter: password');
        err.status = 401;
        throw err;
      }
      if (!accountId) {
        const err = new Error('Missing parameter: accountId');
        err.status = 401;
        throw err;
      }
      if (!authKey) {
        const err = new Error('Missing parameter: authKey');
        err.status = 401;
        throw err;
      }
    };
    checkParams(req);

    // make sure specified account has the proper integration type and key
    const account = await Accounts.findOne({
      where: {
        id: accountId,
      },
      include: [{
        model: Integrations,
        where: {
          integrationType: 'authentication',
          integrationKey: authKey,
        },
        required: true,
      }],
    });

    if (!account) {
      const err = new Error('Account is not setup to use this api.');
      err.status = 401;
      throw err;
    }

    if (account.status !== 'active') {
      const err = new Error('Account has be deactivated.');
      err.status = 401;
      throw err;
    }

    // look up the user by email in the indicated account
    const user = await findUserByEmailAndAccount(req, email, accountId, false); // false = don't consider deleted users
    if (!user) {
      const err = new Error('Auth user not found or created');
      err.status = 401;
      throw err;
    }

    // check password
    await config.encryption.comparePassword(password, user.password, (err) => {
      if (err) {
        return err;
      }
      const wrongPw = new Error('Auth user not found or created');
      wrongPw.status = 401;
      return wrongPw;
    });

    // success!
    // create auth token
    const authToken = await getLongToken(config.longTokenLen);
    const authExpires = Date.now() + config.authVerifyTimeout;
    // update user with token and expiry
    const newUser = await user.update({ authToken, authExpires });
    res.send({ authToken: newUser.authToken });
  } catch (error) {
    next(error);
  }
};

module.exports.apiSignup = async (req, res, next) => {
  try {
    const { email, password, accountId, authKey, firstName, lastName } = req.body;
    const checkParams = () => {
      if (!email) {
        const err = new Error('Missing parameter: email');
        err.status = 401;
        throw err;
      }
      if (!password) {
        const err = new Error('Missing parameter: password');
        err.status = 401;
        throw err;
      }
      if (!accountId) {
        const err = new Error('Missing parameter: accountId');
        err.status = 401;
        throw err;
      }
      if (!authKey) {
        const err = new Error('Missing parameter: authKey');
        err.status = 401;
        throw err;
      }
      if (!firstName) {
        const err = new Error('Missing parameter: firstName');
        err.status = 401;
        throw err;
      }
      if (!lastName) {
        const err = new Error('Missing parameter: lastName');
        err.status = 401;
        throw err;
      }
    };
    checkParams();

    // make sure specified account has the proper integration type and key
    const account = await Accounts.findOne({
      where: {
        id: accountId,
      },
      include: [{
        model: Integrations,
        where: {
          integrationType: 'authentication',
          integrationKey: authKey,
        },
        required: true,
      }],
    });

    if (!account) {
      const err = new Error('Account is not setup to use this api.');
      err.status = 401;
      throw err;
    }

    if (account.status !== 'active') {
      const err = new Error('Account has be deactivated.');
      err.status = 401;
      throw err;
    }

    // check if user already exists in account
    const existingUserId = await findUserIdByEmailAndAccount(req, email, accountId, false); // false = don't consider deleted users
    if (existingUserId) {
      const error = new Error(req.i18n.t('authentication.user_already_exists_in_account_Error', {
        email: req.body.email,
      }));
      error.status = 422;
      throw error;
    }

    const authToken = await getLongToken(config.longTokenLen);

    // validate password rules
    const tbdUser = {
      firstName,
      lastName,
      password,
      email,
      isVerified: true,
      authToken,
      authExpires: Date.now() + config.authVerifyTimeout,
    };
    await validatePassword(req, tbdUser, accountId, password);

    // create user with role and account bindings
    const roleId = await getRoleId('user');
    const user = await User.create(
      {
        ...tbdUser,
        accountUser: [{
          accountId,
        }],
        userRoles: [{
          roleId,
        }],
      },
      {
        include: [{
          association: User.associations.accountUser,
        }, {
          association: User.associations.userRoles,
        }],
      },
    );
    // update emtrainrole
    await updateEmtrainRoles(user.id);
    res.send({ authToken });
  } catch (error) {
    next(error);
  }
};

module.exports.signup = async (req, res, next) => {
  const allowedPermissions = await getAllowedPermissions(
    req,
    req.user ? req.user.id : null, 'users', req.tokenPayload,
  );
  try {
    await checkForUserOrAccountPermission(req, allowedPermissions);
    delete req.body.signupCode;

    const skipVerification = req.body.skipVerification;
    delete req.body.skipVerification;

    const { customSignupAccountFieldIds, standardSignupAccountFieldIds } = req.body;
    delete req.body.customSignupAccountFieldIds;
    delete req.body.standardSignupAccountFieldIds;

    const customSignupFieldValues = customSignupAccountFieldIds?.reduce((acc, key) => {
      if (req.body[key]) {
        acc.push({ id: key,
          value: req.body[key] });
        delete req.body[key];
        return acc;
      }
      return key, acc; // eslint-disable-line no-sequences
    }, []);

    // eslint-disable-next-line no-unused-vars
    const standardSignupFieldValues = standardSignupAccountFieldIds?.reduce((acc, key) => {
      if (req.body[key]) {
        acc.push({ id: key,
          value: req.body[key] });
        delete req.body[key];
        return acc;
      }
      return key, acc; // eslint-disable-line no-sequences
    }, []);

    await checkEditableFields(req, 'create', req.body, allowedPermissions);

    const accountRecord = await accountRecordFromHostname(req);
    const newUserAccountId = req.body.accountId || (accountRecord && accountRecord.id);
    delete req.body.accountId;

    if (accountRecord && accountRecord.status === 'deactive') {
      const error = new Error(req.i18n.t('authentication.deactive_account_Error'));
      error.status = 401;
      throw error;
    }

    if (skipVerification) {
      req.body.isVerified = true;
    }
    if (req.body.password) {
      let existingAccount = null;
      if (newUserAccountId) {
        existingAccount = await Accounts.findByPk(newUserAccountId);
      }
      const user = req.user ? req.user : req.body;
      await validatePassword(req, user, existingAccount, req.body.password);
    }

    // First name, last name need to be set when creating users this way.
    if (!req.body.firstName || !req.body.lastName) {
      const error = new Error(req.i18n.t('authentication.signup_missingdata_Error'));
      error.status = 422;
      throw error;
    }

    // email must be set if account requires it. If email is set, it's validated in the model.
    if (!req.body.email && accountRecord &&
      (accountRecord.authField === 'email' ||
        (accountRecord.authField === 'emailOrEmployeeId' && !req.body.employeeId))) {
      const error = new Error(req.i18n.t('authentication.signup_missingdata_Error'));
      error.status = 422;
      throw error;
    }

    // Employee id must be set if account requires it.
    if (!req.body.employeeId && accountRecord &&
      (accountRecord.authField === 'employeeId' ||
        (accountRecord.authField === 'emailOrEmployeeId' && !req.body.email))) {
      const error = new Error(req.i18n.t('authentication.signup_missingdata_Error'));
      error.status = 422;
      throw error;
    }

    await validateUserCustomFields(req.body, newUserAccountId);
    // See if the user already exists in this account - email and employeeId need to be unique
    const existingUser = await findUserByEmployeeIdOrEmailAndAccount({
      email: req.body.email,
      employeeId: req.body.employeeId,
      accountId: newUserAccountId,
      paranoid: false,
    });
    if (existingUser) {
      throw new UnprocessableEntityError(req.i18n.t('users.user_already_exists_Error'));
    }

    if (req.body.stateCode) {
      const validCode = getValidStateCode(req.body.stateCode);
      if (validCode) {
        req.body.stateCode = validCode;
      } else {
        req.body.stateCode = null;
      }
    }

    if (req.body.countryCode) {
      const validCode = getValidCountryCode(req.body.countryCode);
      if (validCode) {
        req.body.countryCode = validCode;
      } else {
        req.body.countryCode = null;
      }
    }

    const accountOptions = newUserAccountId ? { accountId: newUserAccountId } : null;
    const newUser = await User.create(req.body, accountOptions);
    await setUserAccountFieldValues(customSignupFieldValues, newUser.id, accountOptions.accountId);

    const {
      // eslint-disable-next-line no-unused-vars
      coreUserFields,
      accountFieldPromises,
    } = await separateUserFields(req.body, newUser.id, accountOptions.accountId);
    await Promise.all(accountFieldPromises);

    await setUserRole(newUser, 'user');

    if (newUserAccountId) {
      newUser.accountId = newUserAccountId;
      await AccountUser.create({
        userId: newUser.id,
        accountId: newUser.accountId,
        roleId: 2,
      });
      await addUserToAccountRoster(newUser.accountId, newUser.id);
    }
    // retrieve the user with the role association
    const newUserWithRole = await User.findByPk(newUser.id, {
      include: [{
        model: Role,
        through: 'userRoles',
      },
      {
        model: Accounts,
      }],
    });

    newUserWithRole.password = undefined;

    await createSession(req);
    if (!skipVerification) {
      const createdByAdmin = allowedPermissions.users.includes('create') ||
        allowedPermissions.users.includes('createAccount');
      await sendVerification(newUserWithRole, createdByAdmin);
    }
    // update emtrainroles
    await updateEmtrainRoles(newUser.id);
    res.send({ accessToken: tokenForUser(newUserWithRole), user: newUserWithRole });
  } catch (err) {
    // FIXME: there may be security implications associated with sending info about email uniqueness erros
    if (err.name === 'SequelizeValidationError' || err.name === 'SequelizeUniqueConstraintError') {
      err.status = 422;
    }
    next(err);
  }
};

module.exports.saml = async (req, res, next) => {
  try {
    logger.debug('SAML controller: req.body: %j', req.body);

    const longToken = await getLongToken(config.longTokenLen);

    await req.user.update({
      samlExpires: Date.now() + config.samlVerifyTimeout,
      samlToken: longToken,
    });

    res.redirect(`${req.body.RelayState}/${longToken}`);
  } catch (err) {
    logger.error('SAML controller: error handler: %j', err);
    next(err);
  }
};

module.exports.verifySaml = async (req, res, next) => {
  // SAML connection all good, send back a token to the client
  try {
    logger.info('SAML verify controller: req.user: %j', req.user);

    const account = await gleanAccountFromRequest(req, req.user);
    req.user.accountId = account.id;
    if (!req.user.accounts) {
      req.user.accounts = [account];
    }
    await saveSigninEvent(req);
    res.send({ accessToken: tokenForUser(req.user, { isSso: true }) });
  } catch (err) {
    logger.error('SAML verify controller: error handler: %j', err);
    next(err);
  }
};

module.exports.refreshToken = async (req, res, next) => {
  try {
    const { userId, accountId, isScorm, adminAccountId } = jwt.decode(req.headers.authorization, publicKey, true, 'RS256');
    const extraParams = {};
    if (userId) {
      const user = await User.findByPk(userId);
      user.accountId = accountId;
      extraParams.isScorm = isScorm;
      if (req.body?.isSso) {
        extraParams.isSso = req.body.isSso;
      }
      if (adminAccountId) {
        extraParams.adminAccountId = adminAccountId;
      }
      const token = tokenForUser(user, extraParams);
      res.send({ accessToken: token });
    }
  } catch (err) {
    next(err);
  }
};


module.exports.downloadToken = async (req, res, next) => {
  try {
    const downloadExpires = new Date(Date.now() + (60 * 1000)); // 60 seconds
    const account = await gleanAccountFromRequest(req, req.user);
    req.user.accountId = account.id;
    const token = jwt.encode({
      userId: req.user.id,
      expiresAt: downloadExpires,
      accountId: req.user.accountId,
    }, privateKey, 'RS256');
    res.send({ token });
  } catch (err) {
    next(err);
  }
};

module.exports.jettSSOToken = async (req, res, next) => {
  try {
    const { adminAccountId } = jwt.decode(req.headers.authorization, publicKey, true, 'RS256');
    // console.log('\n\n jettSSOToken - get adminAccountId from jwt header', adminAccountId);
    const tokenExpires = new Date(Date.now() + (60 * 1000)); // 60 seconds
    const account = await gleanAccountFromRequest(req, req.user);
    req.user.accountId = account.id;
    const token = jwt.encode({
      userId: req.user.id,
      expiresAt: tokenExpires,
      accountId: req.user.accountId,
      adminAccountId,
    }, privateKey, 'RS256');

    const user = await User.findOne({
      where: {
        id: req.user.id,
      },
    });
    await user.update({ jettSSOToken: token });
    res.send({ token });
  } catch (err) {
    next(err);
  }
};

/*
*  creates a token for an emtrain system admin user
*  with an account id which belongs to the target account
*  so that the user can be signed in to the target account
*  even though the user's account id is not the target account id
*/
module.exports.accountSSOToken = async (req, res, next) => {
  try {
    const accountId = parseInt(req.query.accountId);
    if (!accountId) {
      const err = new Error('Account required');
      err.status = 500;
      throw err;
    }

    const account = await Accounts.findByPk(accountId);
    if (!account) {
      const err = new Error('Account not found');
      err.status = 500;
      throw err;
    }

    req.user.accountId = account.id;
    const user = await getEmtrainSystemAdminUser(req.user);
    if (!user) {
      const err = new Error('User not found');
      err.status = 500;
      throw err;
    }

    const tokenExpires = new Date(Date.now() + (3600 * 1000)); // 1 hour
    const token = jwt.encode({
      userId: req.user.id,
      expiresAt: tokenExpires,
      accountId,
      adminAccountId: accountId,
    }, privateKey, 'RS256');

    await user.update({ accountSSOToken: token });
    res.send({ token });
  } catch (err) {
    next(err);
  }
};

module.exports.verifyScorm = async (req, res, next) => {
  // no middleware here, so req.user not set - gotta do it all here
  // body: {
  //    userId: user id used by LMS... possibly email
  //    firstName, (optional)
  //    lastName, (optional)
  //    apiKey,
  //    integrationKey,
  //    suspendData,
  //    programId,
  //    lessonId,
  // }
  try {
    const scormId = req.body && req.body.userId && req.body.userId.toLowerCase();
    const isEmail = scormId && isValidEmailAddress(scormId);
    const suspendData = req.body && req.body.suspendData ? req.body.suspendData : '';
    const firstName = req.body && req.body.firstName ? req.body.firstName : '';
    const lastName = req.body && req.body.lastName ? req.body.lastName : '';
    const programId = req.body && req.body.programId ? req.body.programId : null;
    const lessonId = req.body && req.body.lessonId ? req.body.lessonId : null;
    const resourceId = programId || lessonId;
    const model = programId ? 'program' : 'lesson';

    if (!scormId) {
      const err = new Error('No userId sent');
      err.status = 401;
      throw err;
    }

    if (!resourceId) {
      const err = new Error('No resourceId sent');
      err.status = 401;
      throw err;
    }

    if (!model) {
      const err = new Error('No model sent');
      err.status = 401;
      throw err;
    }

    const { apiKey, integrationKey } = req.body;
    if (!apiKey || !integrationKey) {
      const err = new Error('Keys not set');
      err.status = 401;
      throw err;
    }
    const accountRecord = await accountRecordFromHostname(req);
    if (!accountRecord) {
      const err = new Error('Invalid account subdomain');
      err.status = 401;
      throw err;
    }

    if (accountRecord.status === 'deactive') {
      const error = new Error(req.i18n.t('authentication.deactive_account_Error'));
      error.status = 401;
      throw error;
    }

    if (accountRecord.apiKey !== apiKey) {
      const err = new Error('Invalid API key');
      err.status = 401;
      throw err;
    }

    const integrationRecord = accountRecord.integrations
      && accountRecord.integrations.find(i => i.integrationKey === integrationKey &&
       ['scorm', 'scorm20043rd', 'scorm20044th'].includes(i.integrationType));

    if (!integrationRecord) {
      const err = new Error('Invalid Integration key');
      err.status = 401;
      throw err;
    }

    let user = null;
    const params = {
      scormId,
      suspendData: parseInt(suspendData),
      firstName,
      lastName,
      isEmail,
      account: accountRecord,
      accountId: accountRecord.id,
      resourceId: parseInt(resourceId),
      model,
    };

    user = await getScormProgramUser(params);

    if (!user) {
      const err = new Error('Scorm user not found or created');
      err.status = 401;
      throw err;
    }

    user = await User.findByPk(user.id, {
      include: [{
        model: Role,
        through: 'userRoles',
      },
      {
        model: Accounts,
      },
      {
        model: ScormPrograms,
        where: {
          model,
          resourceId,
        },
        required: true,
      }],
    });

    user.password = undefined;
    user.accountId = accountRecord.id;

    logger.info('SCORM verify controller - user: %j', user);

    // scormProgramId is suspendData or id of most recent suspendData row
    let scormProgramId;
    if (suspendData) {
      scormProgramId = suspendData;
    } else if (user.scormPrograms) {
      scormProgramId = user.scormPrograms[user.scormPrograms.length - 1].id;
    }
    const scormProgram = user.scormPrograms.find(sp => sp.id === scormProgramId);
    req.user = user;
    await saveSigninEvent(req);

    // send token back, they'll still need to sign in with this token
    res.send({ accessToken: tokenForUser(user, { isScorm: true }), scormProgramId, scormProgram });
  } catch (err) {
    logger.error('SCORM verify controller: error handler: %j', err);
    next(err);
  }
};

module.exports.verifyAuth = async (req, res, next) => {
  // verify auth token generated by apisignin or apisignup identifies a valid user in AUTH enabled account
  // returns jwt token if successful
  // clears authToken from user record
  try {
    const { authToken, authKey } = req.body;
    const user = await User.findOne({
      where: {
        authToken,
      },
      include: [{
        model: Accounts,
        include: [{
          model: Integrations,
          where: {
            integrationType: 'authentication',
            integrationKey: authKey,
          },
          required: true,
        }],
        where: {
          status: 'active',
        },
        required: true,
      }],
    });

    if (!user) {
      const err = new Error('Auth user cannot be found');
      err.status = 401;
      throw err;
    }
    if (user.authExpires < Date.now()) {
      const err = new Error('Auth token has expired');
      err.status = 401;
      throw err;
    }
    // clear auth token fields
    await user.update({ authToken: null, authExpires: null });
    user.accountId = user.accounts[0].id;
    req.user = user;
    await createSession(req);

    // return jwt token
    res.send({ accessToken: tokenForUser(req.user) });
  } catch (err) {
    logger.error('AUTH verify controller: error handler: %j', err);
    next(err);
  }
};

module.exports.verifyJettSSO = async (req, res, next) => {
  // The single signin token auth entry point is used so that we don't need an
  // additional login for JETT and AI while the two applications need to co-exist.
  // This will no longer be required once wh-frontend is no longer needed.
  try {
    const { email, employeeId, token } = req.body;
    const account = await gleanAccountFromRequest(req, req.user);
    if (!account) {
      const err = new Error('Account not found for JETT SSO user.');
      err.status = 500;
      throw err;
    }

    let user = await findUserByEmployeeIdOrEmailAndAccount({
      email,
      employeeId,
      accountId: account.id,
      paranoid: false,
    });

    let adminAccountId = null;
    if (req.user?.adminAccountId) {
      // console.log('\n\n verify jett sso - getEmtrainSystemAdminUser - set adminAccountId from user.accountUser (usually id 1) ');
      user = await getEmtrainSystemAdminUser(req.user);
      if (user) {
        adminAccountId = user.accountUser.accountId;
      }
    }

    if (!user) {
      const err = new Error('Auth user cannot be found');
      err.status = 401;
      throw err;
    }
    if (user.jettSSOToken !== token) {
      const err = new Error('Auth token not valid for user');
      err.status = 401;
      throw err;
    }
    // clear token from user entry
    await user.update({ jettSSOToken: null });
    user.accountId = account.id;
    user.accounts = [account];
    req.user = user;
    await createSession(req);

    // return jwt token
    res.send({ accessToken: tokenForUser(req.user, { adminAccountId }) });
  } catch (err) {
    logger.error('Single Signin token verify controller: error handler: %j', err);
    next(err);
  }
};

/*
* this verifies an emtrain system admin token
* assigns the the account corresponding to the subdomain to the user
* this IS NOT the emtain account with id 1
* clears the token from the user entry
* returns a jwt token
 */
module.exports.verifyAdminSSO = async (req, res, next) => {
  try {
    const { token } = req.body;
    const account = await accountRecordFromHostname(req);
    if (!account) {
      const err = new Error('Account not found for ADMIN SSO user.');
      err.status = 500;
      throw err;
    }
    const user = await getEmtrainSystemAdminUser(req.user);

    if (!user) {
      const err = new Error('Auth user cannot be found');
      err.status = 401;
      throw err;
    }

    if (user.accountSSOToken !== token) {
      const err = new Error('Auth token not valid for user');
      err.status = 401;
      throw err;
    }
    // clear token from user entry
    await user.update({ accountSSOToken: null });

    // assign the account which corresponds to the target account subdomain
    // this is NOT the Emtrain account, this is the account corresponding to the subdomain
    user.accountId = account.id;
    user.accounts = [account];

    // add the emtrain account to the user's accounts
    const emtrainAccount = await Accounts.findByPk(user.accountUser.accountId);
    user.accounts.push(emtrainAccount);
    // ^^^
    req.user = user;
    await createSession(req);

    // return jwt token with adminAccountId
    res.send({ accessToken: tokenForUser(req.user, { adminAccountId: emtrainAccount.id }), data: user });
  } catch (err) {
    logger.error('Admin ingle Signin token verify controller: error handler: %j', err);
    next(err);
  }
};

/**
 * @openapi
 * /authentication/tableauTrustedToken:
 *   post:
 *     summary: Get Tableau Trusted Token
 *     tags:
 *       - Authentication
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               url:
 *                 type: string
 *                 example: 'http://***********'
 *               userName:
 *                 type: string
 *                 example: 'tableau user'
 *               targetSite:
 *                 type: string
 *                 example: 'analyticsemtrain'
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 trustedToken:
 *                   type: string
 *                   example: "abc123xyz"
 *       400:
 *         description: Bad Request
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.tableauTrustedToken = async (req, res, next) => {
  try {
    const { url, userName, targetSite, tokenName } = req.body;
    if (!url) {
      const err = new Error('Missing parameter: url');
      err.status = 401;
      throw err;
    }
    if (!userName) {
      const err = new Error('Missing parameter: userName');
      err.status = 401;
      throw err;
    }

    const tableauUrl = `${url}/trusted`;
    const body = {};
    body.username = userName;
    body.target_site = targetSite;
    const postData = querystring.stringify(body);
    const headers = { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } };
    // send request to Tableau
    const response = await axios.post(tableauUrl, postData, headers);
    // return trusted token
    res.send({ trustedToken: response.data, tokenName });
  } catch (err) {
    logger.error('Tableau Trusted Auth controller: error handler: %j', err);
    next(err);
  }
};

/**
 * @openapi
 * /authentication/tableauJwtToken:
 *   get:
 *     summary: Get Tableau JWT Token
 *     tags:
 *       - Authentication
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 tableauJwtToken:
 *                   type: string
 *       400:
 *         description: Bad Request
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

module.exports.tableauJwtToken = async (req, res, next) => {
  try {
    const secret = config.tableau.secret;
    const secretId = config.tableau.secretId;
    const clientId = config.tableau.clientId;
    const scopes = ['tableau:views:embed', 'tableau:views:embed_authoring'];
    const userId = config.tableau.userId;
    const tokenExpiryInMinutes = 10; // Max of 10 minutes.

    const header = {
      alg: 'HS256',
      typ: 'JWT',
      kid: secretId,
      iss: clientId,
    };
    const data = {
      jti: uuidv4(),
      aud: 'tableau',
      sub: userId,
      scp: scopes,
      exp: Math.floor(Date.now() / 1000) + (tokenExpiryInMinutes * 60),
    };

    const token = jwtWeb.sign(data, secret, { header });
    res.send({ tableauJwtToken: token });
  } catch (err) {
    logger.error('Tableau JWT Auth controller: error handler: %j', err);
    next(err);
  }
};


module.exports.methodologyFile = async (req, res, next) => {
  try {
    let fileError;
    const { name, folder } = config.analyticsMethodologyFile;

    const account = await gleanAccountFromRequest(req, req.user);
    if (!account) {
      const err = new Error('Account not found');
      err.status = 500;
      throw err;
    }

    if (!name || !folder) {
      fileError = 'Config error';
    }

    const accountCanAccessAnalyticsFile = account.hasAnalytics && ['level2', 'level3'].includes(account.licenseLevel);
    if (!accountCanAccessAnalyticsFile) {
      fileError = 'Analytics must be enabled with level2 or level3';
    }

    const fileExists = !fileError && await S3FileExists(name, folder);
    if (!fileExists) {
      fileError = `File does not exist at ${config.s3.bucket}/${folder}/${name}`;
    }
    if (fileError) {
      res.send({ fileError });
    }
    const timeoutMinutes = (account.sessionTimeout && account.sessionTimeout / 60) || 60;
    const url = await createPresignedURL(name, folder, timeoutMinutes);
    res.send({ url });
  } catch (err) {
    logger.error('Presigned File URL: error handler: %j', err);
    next(err);
  }
};

module.exports.googleOauthEvent = async (req, res, next) => {
  try {
    let name = req.body.name;
    if (name) {
      name = name.split(' ');
      if (name[0]) {
        req.body.firstName = name[0];
      }
      if (name[1]) {
        req.body.lastName = name[1];
      }
      delete req.body.name;
    }
    let response = {};
    response = await signInSignUpOauthUser(req, 'google');
    await createSession(req);
    await initializeFailedLogin(req.user);
    res.json(response);
  } catch (err) {
    // FIXME: there may be security implications associated with sending info about email uniqueness erros
    if (err.name === 'SequelizeValidationError' || err.name === 'SequelizeUniqueConstraintError') {
      err.status = 422;
    }
    next(err);
  }
};

module.exports.linkedinOauth = async (req, res, next) => {
  try {
    const { code, redirectUrl } = req.body;
    req.hostname = redirectUrl;
    let linkedinConfig = null;
    if (!code || code.length === 0) {
      // eslint-disable-next-line max-len
      const error = new Error(req.i18n.t('authentication.authentication_failed_Error', { errorType: 'Missing or invalid authentication code' }));
      error.status = 401;
      throw error;
    }
    const accountRecord = await accountRecordFromHostname(req);
    if (accountRecord.socialLoginConfigs && accountRecord.socialLoginConfigs.length) {
      const linkedinOauthDetails = accountRecord.socialLoginConfigs.find((obj) => {
        return obj.provider === 'linkedin';
      });

      if (
        linkedinOauthDetails.configData &&
        linkedinOauthDetails.configData.hasOwnProperty('clientId') &&
        linkedinOauthDetails.configData.hasOwnProperty('clientSecret')
      ) {
        linkedinConfig = linkedinOauthDetails.configData;
      }
    }
    const accessTokenRes = await getLinkedInAccessToken(code, redirectUrl, linkedinConfig);
    if (!accessTokenRes) {
      // eslint-disable-next-line max-len
      const error = new Error(req.i18n.t('authentication.authentication_failed_Error', { errorType: 'Missing Access Token' }));
      error.status = 401;
      throw error;
    }

    if (accessTokenRes && accessTokenRes.data) {
      const accessToken = accessTokenRes.data.access_token;
      const memberProfile = await getLinkedInMemberProfile(accessToken);

      if (memberProfile && memberProfile.data) {
        req.body.firstName = memberProfile.data.given_name;
        req.body.lastName = memberProfile.data.family_name;
        req.body.email = memberProfile.data.email;

        const response = await signInSignUpOauthUser(req, 'linkedin');
        await createSession(req);
        await initializeFailedLogin(req.user);
        res.json(response);
      }
    }
  } catch (err) {
    // FIXME: there may be security implications associated with sending info about email uniqueness erros
    if (err.name === 'SequelizeValidationError' || err.name === 'SequelizeUniqueConstraintError') {
      err.status = 422;
    }
    next(err);
  }
};

module.exports.microsoftOauthEvent = async (req, res, next) => {
  try {
    const msToken = req.body.accessToken;
    delete req.body.accessToken;
    const resp = await getMicrosoftMemberDetails(msToken);
    if (resp.status !== 200) {
      // eslint-disable-next-line max-len
      const error = new Error(req.i18n.t('authentication.authentication_failed_Error', { errorType: 'Invalid Access Token' }));
      error.status = 401;
      throw error;
    }
    const name = resp.data.displayName.split(' ');
    if (resp.data.givenName) {
      req.body.firstName = resp.data.givenName;
    } else if (name[0]) {
      req.body.firstName = name[0];
    }
    if (resp.data.surname) {
      req.body.lastName = resp.data.surname;
    } else if (name[1]) {
      req.body.lastName = name[1];
    }
    if (resp.data.email) {
      req.body.email = resp.data.mail;
    } else {
      req.body.email = resp.data.userPrincipalName;
    }

    let response = {};
    response = await signInSignUpOauthUser(req, 'microsoft');
    await createSession(req);
    await initializeFailedLogin(req.user);
    res.json(response);
  } catch (err) {
    // FIXME: there may be security implications associated with sending info about email uniqueness erros
    if (err.name === 'SequelizeValidationError' || err.name === 'SequelizeUniqueConstraintError') {
      err.status = 422;
    }
    next(err);
  }
};
