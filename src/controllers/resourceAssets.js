/* eslint-disable no-undef */
const db = require('../db');
const _ = require('lodash');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { getResourceAssets, validateResourceAssets,
  saveAssociations, remove, restSortToSequelize, checkResourceAsset } = require('../services/utils/resourceAssetsUtils');
const { localizeModelObject } = require('../services/utils/localizationUtils');
const { removeResourceAssetFromCampaigns } = require('../services/utils/campaignUtils');
const { deriveSearch } = require('../services/utils/mediaAssetUtils');
const { uploadFile } = require('../services/utils/fileUtils');
const { searchTextFromSearchInfo, setResourceAccess } = require('../services/utils/resourceUtils');
const config = require('../config/config');

const assetsFolder = 'media-assets';
const ResourceAssets = db.resourceAssets;
const Files = db.files;
const Resources = db.resources;
const ResourceAssetPillars = db.resourceAssetPillars;
const SocialCapitalPillars = db.socialCapitalPillars;
const Users = db.users;
const includesModels = [
  {
    model: Files,
    as: 'file',
    required: false,
  },
  {
    model: SocialCapitalPillars,
    attributes: ['id', 'name'],
    through: { attributes: [] },
  },
  {
    model: Users,
    attributes: ['id', 'firstName', 'lastName'],
    required: false,
    as: 'resourceAssetCreatedByUser',
  },
  {
    model: Users,
    attributes: ['id', 'firstName', 'lastName'],
    required: false,
    as: 'resourceAssetUpdatedByUser',
  },
];

const restQueryToSequelize = async (req, defaults) => {
  const query = req.query;
  const whereClause = restOperatorsToSequelize({ ..._.omit(query, ['$limit', '$skip', '$sort']) });
  const newQuery = {
    ...defaults,
    where: { ...defaults.where, ...whereClause },
  };

  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }

  return newQuery;
};

module.exports.read = async (req, res, next) => {
  try {
    res.json(req.resourceAssets);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /resource-assets:
 *   get:
 *     summary: Resource Assets List
 *     tags: [Resource Assets]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *         description: Number of records to return
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *         description: Number of records to skip
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   description: Array of report data objects
 *                   items:
 *                     $ref: '#/components/schemas/resourceAssets'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };
  if (req.query.publishToManageLibrary) {
    req.query.publishToManageLibrary = (req.query.publishToManageLibrary === 'true');
  }
  const queryParams = await restQueryToSequelize(req, defaults);
  const finalQuery = {
    attributes: ['id', 'title', 'description', 'resourceType',
      'link', 'type', 'fileId', 'resourceId',
      'publishToManageLibrary', 'manageLibraryTitle', 'createdAt'],
    ...queryParams,
    where: {
      ...queryParams.where,
    },
  };

  finalQuery.order = [[...finalQuery.order]];
  const pagedResult = {
    limit: finalQuery.limit,
    skip: finalQuery.offset,
  };
  try {
    const countQuery = {
      distinct: 'id',
      paranoid: false,
    };
    countQuery.where = _.pick(finalQuery, ['where']).where;
    const count = await ResourceAssets.count(countQuery);
    const results = await ResourceAssets.findAll(finalQuery);
    const data = results.map((record) => {
      return localizeModelObject(req, record.get({ plain: true }), req.i18n.language, false);
    });
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /resource-assets:
 *   post:
 *     summary: Add Resource Assets
 *     tags: [Resource Assets]
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: 'Resource Test Title'
 *               description:
 *                 type: string
 *                 example: 'Sample description...'
 *               type:
 *                 type: string
 *                 example: 'link'
 *               link:
 *                 type: string
 *                 format: uri
 *                 example: 'https://meyerweb.com/eric/tools/dencoder/'
 *               resourceType:
 *                 type: string
 *                 example: 'Tip Sheets'
 *               manageLibraryTitle:
 *                 type: string
 *                 example: 'Resource Library Title'
 *               publishToManageLibrary:
 *                 type: boolean
 *                 example: false
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/resourceAssets'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */


module.exports.create = async (req, res, next) => {
  delete req.body.id;
  const searchInfo = await deriveSearch(req.body);
  const searchText = searchTextFromSearchInfo(searchInfo);
  let resourceAssetsId;
  try {
    validateResourceAssets(req);
    let finalAsset = null;
    await db.sequelize.transaction(async (transact) => {
      const file = await uploadFile(req, assetsFolder);
      const asset = {
        title: req.body.title,
        description: req.body.description,
        type: req.body.type,
        link: (req.body.link && req.body.type === 'link') ? req.body.link : null,
        fileId: file ? file.id : null,
        resourceType: req.body.resourceType,
        publishToManageLibrary: req.body.publishToManageLibrary,
        createdBy: req.user.id || null,
        updatedBy: req.user.id || null,
        manageLibraryTitle: req.body.manageLibraryTitle || null,
        resource: {
          digestable: 'resourceAssets',
          searchText,
        },
      };
      const resourceAsset = await checkResourceAsset(asset);
      if (resourceAsset) {
        const err = new Error('Resource already exists.');
        err.status = 404;
        throw err;
      }

      const newAsset = await ResourceAssets.create(asset, {
        include: ResourceAssets.associations.resource,
      });
      await setResourceAccess(req.user, newAsset.resource);
      resourceAssetsId = newAsset.id;
      if (newAsset.id) {
        if (req.body.resourceAssetPillars && req.body.publishToManageLibrary) {
          await saveAssociations(newAsset.id, req.body.resourceAssetPillars, 'pillarId', transact);
        }
      }

      if (file) {
        await file.update({
          fileable: 'resourceAsset',
          fileableId: newAsset.id,
        });
      }
    });
    if (resourceAssetsId) {
      finalAsset = await getResourceAssets(resourceAssetsId, includesModels);
    }
    res.json(finalAsset);
  } catch (err) {
    err.status = 422;
    next(err);
  }
};

/**
 * @swagger
 * /resource-assets/{resourceAssetsId}:
 *   patch:
 *     summary: Update Resource Asset
 *     tags: [Resource Assets]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: resourceAssetsId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: 'Resource Test Title'
 *               description:
 *                 type: string
 *                 example: 'Sample description...'
 *               type:
 *                 type: string
 *                 example: 'link'
 *               link:
 *                 type: string
 *                 format: uri
 *                 example: 'https://meyerweb.com/eric/tools/dencoder/'
 *               resourceType:
 *                 type: string
 *                 example: 'Tip Sheets'
 *               manageLibraryTitle:
 *                 type: string
 *                 example: 'Resource Library Title'
 *               publishToManageLibrary:
 *                 type: boolean
 *                 example: false
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/resourceAssets'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

module.exports.patch = async (req, res, next) => {
  const existingResourceAssets = req.resourceAssets;
  let file = req.file;
  const filePath = req.body.file_path;
  try {
    validateResourceAssets(req);
    await db.sequelize.transaction(async (transact) => {
      const asset = {
        title: req.body.title,
        description: req.body.description,
        type: req.body.type,
        link: (req.body.link && req.body.type === 'link') ? req.body.link : null,
        resourceType: req.body.resourceType,
        updatedBy: req.user.id || null,
        publishToManageLibrary: req.body.publishToManageLibrary,
        manageLibraryTitle: req.body.manageLibraryTitle || null,
      };

      const resourceAsset = await checkResourceAsset(asset);
      if (resourceAsset && resourceAsset.id !== existingResourceAssets.id) {
        const err = new Error('Resource already exists.');
        err.status = 404;
        throw err;
      }
      if (!filePath && file && req.body.type === 'file') {
        if (existingResourceAssets.fileId) {
          const fileAsset = await Files.findByPk(existingResourceAssets.fileId);
          if (fileAsset) {
            // eslint-disable-next-line no-use-before-define
            await remove(fileAsset, existingResourceAssets.fileId);
          }
          file = await uploadFile(req, assetsFolder);
        } else {
          file = await uploadFile(req, assetsFolder);
        }
        await file.update({
          fileable: 'resourceAsset',
          fileableId: existingResourceAssets.id,
        });
        asset.fileId = file ? file.id : null;
      }

      if (req.body.type !== existingResourceAssets.type) {
        if (req.body.type === 'link' && existingResourceAssets.fileId) {
          const fileAssets = await Files.findByPk(existingResourceAssets.fileId);
          if (fileAssets) {
            await remove(fileAssets, existingResourceAssets.fileId);
          }
          asset.fileId = null;
        }

        if (req.body.type === 'file' && existingResourceAssets.link) {
          asset.link = null;
        }
      }
      const updatedAsset = await existingResourceAssets.update(
        asset,
        {
          paranoid: false,
        },
      );
      const searchInfo = await deriveSearch(updatedAsset);
      const searchText = searchTextFromSearchInfo(searchInfo);
      await Resources.update({ searchText }, { where: { id: updatedAsset.resourceId } });
      if ((existingResourceAssets.socialCapitalPillars
        && existingResourceAssets.socialCapitalPillars.length && !req.body.publishToManageLibrary) ||
        (!req.body.resourceAssetPillars && req.body.publishToManageLibrary)) {
        await ResourceAssetPillars.destroy({ where: { resourceAssetId: existingResourceAssets.id },
          transaction: transact });
      }

      if (req.body.resourceAssetPillars && req.body.publishToManageLibrary) {
        let existingPillars = existingResourceAssets.socialCapitalPillars.map((n) => {
          return n.id;
        });
        if (existingPillars && existingPillars.length) {
          existingPillars = existingPillars.sort();
        }
        let updatedPillars = req.body.resourceAssetPillars.map((n) => {
          return parseInt(n);
        });
        if (updatedPillars && updatedPillars.length) {
          updatedPillars = updatedPillars.sort();
        }
        if (existingPillars.length && !_.isEqual(updatedPillars, existingPillars)) {
          await ResourceAssetPillars.destroy({ where: { resourceAssetId: existingResourceAssets.id },
            transaction: transact });
          await saveAssociations(existingResourceAssets.id, req.body.resourceAssetPillars, 'pillarId', transact);
        }
        if (!existingPillars.length && updatedPillars.length) {
          await saveAssociations(existingResourceAssets.id, req.body.resourceAssetPillars, 'pillarId', transact);
        }
      }
    });
    let finalAsset;
    if (existingResourceAssets) {
      finalAsset = await getResourceAssets(existingResourceAssets.id, includesModels);
    }
    res.json(finalAsset);
  } catch (err) {
    err.status = 422;
    next(err);
  }
};

/**
 * @swagger
 * /resource-assets/{resourceAssetsId}:
 *   delete:
 *     summary: Archive Resource
 *     tags: [Resource Assets]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: resourceAssetsId
 *         in: path
 *         required: true
 *         type: integer
 *     produces:
 *       - application/json
 *     responses:
 *       200:
 *         description: Successful operation
 *         schema:
 *           $ref: '#/components/schemas/resourceAssets'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

module.exports.delete = async (req, res, next) => {
  try {
    const asset = req.resourceAssets;
    const updatedAsset = await asset.destroy();
    if (updatedAsset.file) {
      await updatedAsset.file.destroy();
      await updatedAsset.update({
        fileId: null,
      });
    }
    await ResourceAssetPillars.destroy({
      where: {
        resourceAssetId: updatedAsset.id,
      },
    });
    const finalAsset = await ResourceAssets.findOne({
      where: {
        id: updatedAsset.id,
      },
      paranoid: false,
    });
    await removeResourceAssetFromCampaigns(asset.id);
    res.json(finalAsset);
  } catch (err) {
    next(err);
  }
};

module.exports.findByPk = async (req, res, next, id) => {
  try {
    const finalAsset = await getResourceAssets(id, includesModels);
    if (!finalAsset) {
      const err = new Error(`Failed to load ResourceAssets: ${id}.`);
      err.status = 404;
      throw err;
    }
    req.resourceAssets = finalAsset;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * components:
 *   schemas:
 *     resourceAssets:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           example: 101
 *         title:
 *           type: string
 *           example: "Sample Resource Title"
 *         description:
 *           type: string
 *           example: "This is a resource asset description."
 *         resourceType:
 *           type: string
 *           example: "video"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           example: "2025-05-19T12:00:00Z"
 *         publishToManageLibrary:
 *           type: boolean
 *           example: true
 */


