const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { restSortToSequelize } = require('../services/utils/resourceUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');

const ContentPackages = db.contentPackages;
const ContentPackageResources = db.contentPackageResources;
const AccountContentPackages = db.accountContentPackages;
const Resources = db.resources;

const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize({
    ..._.omit(query, [
      '$limit', '$skip', '$sort', 'includeContent',
    ]),
  });
  const includeParams = query.includeContent === 'true'
    ? [{
      model: Resources,
    }] // include the resources
    : [];
  const newQuery = {
    ...defaults,
    where: { ...defaults.where, ...whereClause },
    include: includeParams,
  };

  // override defaults for limit, skip, and sort if passed in by caller
  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }
  return newQuery;
};

/**
 * @swagger
 * /content-packages?$limit={limit}&$skip={skip}:
 *   get:
 *     summary: Fetch All Package
 *     tags: [Package]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Limit number of records per page
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *         description: Number of records to skip
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records
 *                 limit:
 *                   type: integer
 *                   description: Limit records per page
 *                 data:
 *                   type: array
 *                   description: Array of report data object
 *                   items:
 *                     $ref: '#/definitions/contentPackages'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  try {
    const defaults = {
      order: [['updatedAt', 'DESC']],
      limit: config.paginate.default,
      offset: 0,
    };
    const finalQuery = restQueryToSequelize(req.query, defaults);

    const contentPackages = await ContentPackages.findAll(finalQuery);
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
    };
    const count = await ContentPackages.count(countQuery);

    res.json({ total: count, limit: finalQuery.limit, skip: finalQuery.skip, data: contentPackages });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /content-packages:
 *   post:
 *     summary: Add New Package
 *     tags: [Package]
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       description: Suggested Fields parameters
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: 'Test Package'
 *               description:
 *                 type: string
 *                 example: 'Test description'
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/contentPackages'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  try {
    const data = { ...req.body };

    const contentPackage = await ContentPackages.create(data);
    res.json(contentPackage);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /content-packages/{contentPackageId}:
 *   get:
 *     summary: Get Package Details By Id
 *     tags: [Package]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: contentPackageId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/contentPackages'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.read = async (req, res, next) => {
  try {
    let contentPackage = req.contentPackage;
    if (req.query && req.query.includeContent === 'true') {
      // re-query the contentPackage and include the resources
      contentPackage = await ContentPackages.findByPk(req.contentPackage.id, {
        include: [{
          model: Resources,
        }],
      });
    }
    res.json(contentPackage);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /content-packages/{packageId}:
 *   patch:
 *     summary: Update Package
 *     tags: [Package]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: packageId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: 'Test Package'
 *               description:
 *                 type: string
 *                 example: 'Test description'
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/contentPackages'
 *       404:
 *         description: Not Found
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.patch = async (req, res, next) => {
  try {
    let contentPackage = req.contentPackage;
    const data = { ...req.body };
    contentPackage.update(data);

    if (req.query && req.query.includeContent === 'true') {
      // re-query the contentPackage and include the resources
      contentPackage = await ContentPackages.findByPk(contentPackage.id, {
        include: [{
          model: Resources,
        }],
      });
    }
    res.json(contentPackage);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /content-packages/{packageId}:
 *   delete:
 *     summary: Delete Package
 *     tags: [Package]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: packageId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/contentPackages'
 *       404:
 *         description: Not Found
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  try {
    await ContentPackageResources.destroy({
      where: {
        contentPackageId: req.contentPackage.id,
      },
    });
    // Need to remove content package pointer from all accounts
    await AccountContentPackages.destroy({
      where: {
        contentPackageId: req.contentPackage.id,
      },
    });
    await req.contentPackage.destroy();

    res.json(req.contentPackage);
  } catch (err) {
    next(err);
  }
};

module.exports.contentPackageById = async (req, res, next, id) => {
  try {
    const contentPackage = await ContentPackages.findByPk(id);
    if (!contentPackage) {
      const err = new Error(req.i18n.t('contentPackages.contentPackage_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.contentPackage = contentPackage;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * definitions:
 *  contentPackages:
 *    type: object
 *    properties:
 *      id:
 *        type: integer
 *      name:
 *        type: string
 *      description:
 *        type: string
 *      createdAt:
 *        type: string
 *      updatedAt:
 *        type: string
 *      deletedAt:
 *        type: string
 */
