const db = require('../db');
const merge = require('deepmerge');
const { groupBy } = require('lodash');
const config = require('../config/config');
const { restSortToSequelize } = require('../services/utils/resourceUtils');

const Events = db.events;
const Resource = db.resources;
const Op = db.Sequelize.Op;

function getEventAction(type, newEvent, currentUserResource) {
  // If newEvent hanppened use the action value
  if (newEvent !== null) {
    return newEvent.action;
  }
  // If there is a current userResource, use the odl value
  if (currentUserResource !== null) {
    return currentUserResource[type];
  }
  return false;
}

function getEventStatus(events, eventName) {
  const latestEvent = events.filter(e => e.type === eventName)[0];
  return latestEvent ? latestEvent.action : false;
}

function parseEvents(events) {
  return {
    viewed: getEventStatus(events, 'view'),
    shared: getEventStatus(events, 'share'),
    helpful: getEventStatus(events, 'helpful'),
    saved: getEventStatus(events, 'save'),
  };
}

const restQueryToSequelize = (query, defaults) => {
  const newQuery = {
    ...defaults,
  };

  // override defaults for limit, skip, and sort if passed in by caller
  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }
  return newQuery;
};

const VERB_TO_TYPE = {
  viewed: 'view',
  shared: 'share',
  helpful: 'helpful',
  saved: 'save',
};

const eventTypes = ['save', 'share', 'helpful', 'view'];

module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['createdAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
    where: {
      trackableType: 'resource',
      type: {
        [Op.in]: eventTypes,
      },
    },
  };
  const countQuery = {
    distinct: 'id',
    where: {
      trackableType: 'resource',
      type: {
        [Op.in]: eventTypes,
      },
    },
    include: [{
      model: Resource,
      as: 'resource',
    }],
  };
  const queryParams = restQueryToSequelize(req.query, defaults);
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  const finalQuery = {
    ...queryParams,
    where: {
      ...queryParams.where,
    },
  };
  if (req.body.resourceId) {
    finalQuery.where.trackableId = req.body.resourceId;
  }
  if (req.body.userId) {
    finalQuery.where.userId = req.body.userId;
  }
  try {
    const count = await Events.count(countQuery);
    const results = await Events.findAll(finalQuery);
    const grouped = groupBy(results, r => r.trackableId);
    const resourceIds = Object.keys(grouped);
    const data = [];
    resourceIds.forEach((resourceId) => {
      // Group by userId
      const userEvents = groupBy(grouped[resourceId], e => e.userId);
      const userIds = Object.keys(userEvents);
      // Create a data per user
      data.push(...userIds.map(userId => (
        merge({
          resourceId: +resourceId,
          userId: +userId,
        }, parseEvents(userEvents[userId]))
      )));
    });
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  try {
    res.json(req.userResource);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /user-resources:
 *   post:
 *     summary: New User Resource
 *     tags: [User Resources]
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       description: User resource parameters
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - resourceId
 *               - saved
 *             properties:
 *               resourceId:
 *                 type: integer
 *                 example: 123
 *               saved:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 resourceId:
 *                   type: integer
 *                   example: 123
 *                 userId:
 *                   type: integer
 *                   example: 456
 *                 saved:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  try {
    const userId = req.user ? req.user.id : null;
    const resourceId = req.body.resourceId;
    const promises = ['viewed', 'saved', 'shared', 'helpful'].map((type) => {
      if (req.body.hasOwnProperty(type)) {
        return Events.create({
          trackableId: resourceId,
          trackableType: 'resource',
          type: VERB_TO_TYPE[type],
          action: !!req.body[type],
          userId,
        });
      }
      return Promise.resolve(null);
    });
    promises.push(Events.findAll({
      where: {
        userId: req.user ? req.user.id : null,
        trackableId: req.body.resourceId,
        trackableType: 'resource',
      },
    }));
    const events = await Promise.all(promises).then(([viewed, saved, shared, helpful, current]) => {
      return {
        resourceId,
        userId,
        viewed: getEventAction('viewed', viewed, current),
        saved: getEventAction('saved', saved, current),
        shared: getEventAction('shared', shared, current),
        helpful: getEventAction('helpful', helpful, current),
      };
    });
    if (req.body.hasOwnProperty('helpful')) {
      const resource = await Resource.findByPk(resourceId);
      const helpfulCount = req.body.helpful ? resource.helpfulCount + 1 : resource.helpfulCount - 1;
      await resource.update({ helpfulCount });
    }
    res.json(events);
  } catch (err) {
    next(err);
  }
};


module.exports.findByPk = async (req, res, next, id) => {
  try {
    const userResource = await Events.findOne({
      where: {
        id,
      },
    });
    if (!userResource) {
      const err = new Error(req.i18n.t('userResources.userResource_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.userResource = userResource;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * definitions:
 *   userResources:
 *     type: object
 *     properties:
 *       resourceId:
 *         type: integer
 *         description: ID of the associated resource.
 *         example: 101
 *       helpful:
 *         type: boolean
 *         description: Indicates if the resource was marked helpful.
 *         example: true
 *       saved:
 *         type: boolean
 *         description: Indicates if the resource was saved by the user.
 *         example: false
 *       shared:
 *         type: boolean
 *         description: Indicates if the resource was shared by the user.
 *         example: true
 *       userId:
 *         type: integer
 *         description: ID of the user.
 *         example: 123
 *       viewed:
 *         type: boolean
 *         description: Indicates if the resource was viewed by the user.
 *         example: true
 */
