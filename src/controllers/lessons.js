const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const fs = require('fs');
const fse = require('fs-extra');
const stream = require('stream');
const path = require('path');
const { S3Client, DeleteObjectCommand } = require("@aws-sdk/client-s3");
const { Upload } = require("@aws-sdk/lib-storage");
const logger = require('../logger');
const { format } = require('date-fns');
const moment = require('moment');
const puppeteer = require('puppeteer');
const { restSortToSequelize,
  lifecycleByUser, canAccessLesson, includeUserResource, includeUserResources,
  searchTextFromSearchInfo, updateElasticSearch, setResourceAccess, setPublicAccess,
  removeFromElasticSearch, getResourceBundlesIds,
  PUBLIC_BUNDLE_ID, SEARCH_TEXT_COL_LIMIT } = require('../services/utils/resourceUtils');
const { restQueryToSequelize: rqtu, genCSV, cleanDownloadFilename, newRestQueryToSequelize,
  runLessonUserReport, runContentUserReport, genNewCSV, contentCompletionUserReport,
  contentCompletionGroups } = require('../services/utils/reportingUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { assignRequestValues } = require('../services/utils/controllerUtils');
const { checkAllowedAttributes } = require('../controllers/campaignReporting');
const { deriveSearch, flattenLesson, swapProtectedTraits,
  configureLessonForAccount, configurePolicyCardForAccount, includeNumUsersAccessed,
  generateLessonProgressReport, generateContentsProgressReport,
  duplicateNonSharedCard, createNewFile
} = require('../services/utils/lessonUtils');
const { includeAnswer } = require('../services/utils/answerCardUtils');
const { getPDFVideoTranscript } = require('../services/pdf/videoTranscript');

const {
  gatherLessoni18nStrings,
  updateLessoni18nStrings,
  updateLessonProtectedTraits,
  swapContentStrings,
  handleSupportedLanguages,
  localizeModelObject,
  getContentStringQuery,
  getQueryLanguages,
} = require('../services/utils/localizationUtils');
const { uploadFile, getExistingFile, getLaunchBrowser } = require('../services/utils/fileUtils');
const { validLanguageCodes } = require('../services/utils/localizationUtils.js');
const { removeLessonFromCampaigns, getContentItemsDeployedData } = require('../services/utils/campaignUtils');
const { getAllowedPermissions } = require('../services/acl/acl');
const { removeLessonFromPrograms } = require('../services/utils/programUtils');
const { getCsvS3Bucket } = require('../services/utils/fileUtils');
const { genAndDownloadScormArchive } = require('../services/scorm/scormPackage');
const { updateLastMappedDate } = require('../services/utils/commonUtils');
const { checkForWorkdayCCLUpdates } = require('../services/utils/workdayCclUtils.js');
const { useMachineTranslation } = require('../services/utils/lessonCardUtils.js');

const Op = db.Sequelize.Op;
const Lesson = db.lessons;
const Resource = db.resources;
const ResourceTopic = db.resourceTopics;
const ResourceTag = db.resourceTags;
const LessonCard = db.lessonCards;
const LessonLessonCard = db.lessonLessonCards;
const LessonProgram = db.lessonPrograms;
const AnswerCard = db.answerCards;
const UserLesson = db.userLessons;
const ResourceBundles = db.resourceBundles;
const ContentStrings = db.contentStrings;
const SupportedLanguages = db.supportedLanguages;
const Files = db.files;
const AccountLesson = db.accountLessons;
const AccountLessonCard = db.accountLessonCards;
const Account = db.accounts;
const Program = db.programs;
const Campaigns = db.campaigns;
const CampaignItem = db.campaignItem;
const ContentPackageResources = db.contentPackageResources;
const CatalogItem = db.catalogItems;
const GroupAssignments = db.groupAssignments;
const Groups = db.groups;
const Users = db.users;
const WorkdayContentData = db.workdayContentData;
const LINE_FEED = '\n'.charCodeAt(0);

const lessonRequestOptions = {
  title: { required: true },
  description: { required: true },
  edition: { min: 0, allowNull: true, defaultValue: null },
  version: { min: 0, allowNull: true, defaultValue: null },
  build: { min: 0, allowNull: true, defaultValue: null },
  lifecycle: { defaultValue: 'draft' },
};

const s3Client = new S3Client({
  region: "us-east-1",
  credentials: {
    accessKeyId: config.s3.accessKeyId,
    secretAccessKey: config.s3.secretAccessKey,
  },
});

const isLocalTestMode = () => {
  return (process.env.NODE_ENV === 'test' && (!config.s3.secretAccessKey || !config.s3.accessKeyId));
};

const getLocalTestFilename = (filename) => {
  const uploadFolder = '../../testuploads/';
  const uploadPath = path.join(__dirname, uploadFolder);
  return (path.join(uploadPath, filename));
};

function flattenAssignment(groupAssignment) {
  const group = groupAssignment.group;
  const flattenedAssignment = groupAssignment.get({ plain: true });
  return Object.assign(flattenedAssignment, { group });
}

// looks up Group from groupAssignment
async function lookupGroup(groupAssignment) {
  const group = await Groups.findByPk(groupAssignment.groupId);
  Object.assign(groupAssignment, { group });
  return groupAssignment;
}

const getIncludeParams = async (req, user, {
  accountId = null,
  includeLessonCards = true,
  resourceBundleStatus = null,
  includeContentStrings = true,
  manageContent = false,
  identifyingPolicyLesson = false,
} = {}) => {
  const includePrograms = req && req.query && req.query.includePrograms === 'true';
  const includeSourceLesson = req && req.query && req.query.includeSourceLesson === 'true';
  const notIncludeLessonPrograms = req && req.query && req.query.notIncludeLessonPrograms === 'true';
  const bundleAccountId = accountId || (user ? user.accountId : null);
  const bundleIds = await getResourceBundlesIds(bundleAccountId);
  const resourceBundlesWhere = {
    bundleId: {
      [Op.in]: bundleIds,
    },
  };
  if (resourceBundleStatus && ['active', 'retired'].includes(resourceBundleStatus)) {
    resourceBundlesWhere.status = resourceBundleStatus;
  }
  const includeResource = [{
    model: ResourceBundles,
    where: resourceBundlesWhere,
  }];

  const includeParams = [{
    model: Resource,
    as: 'resource',
    include: includeResource,
    required: true,
  }];

  if (!manageContent) {
    includeParams.push(
      { model: Files },
      { model: SupportedLanguages },
      { model: CatalogItem, attributes: ['id', 'title', 'listingId'] },
    );
  }

  if (includePrograms && !notIncludeLessonPrograms) {
    includeParams.push({
      model: Program,
      through: 'lessonPrograms',
      required: false,
    });
  }

  if (includeSourceLesson) {
    includeParams.push({
      model: Lesson,
      as: 'sourceLesson',
      required: false,
      attributes: ['id', 'title', 'internalName', 'deletedAt'],
      paranoid: false,
    });
  }

  if (includeContentStrings && !manageContent) {
    includeParams.push(getContentStringQuery(req, getQueryLanguages(req), 'lesson'));
  }

  if (identifyingPolicyLesson) {
    includeParams.push({
      model: LessonCard,
      attributes: ['id', 'cardType', 'list1', 'policyLink'],
      where: {
        cardType: 'policyAcknowledgement',
      },
      include: [
        {
          model: AccountLessonCard,
          attributes: ['id', 'policyType', 'link'],
          where: {
            accountId,
          },
          include: [{
            model: Files,
            attributes: ['id', 'path'],
            as: 'file',
            required: false,
          }],
          required: false,
        }],
      required: false,
    });
  }

  if (includeLessonCards && !manageContent) {
    const lessonCardIncludes = [
      {
        model: Files,
        as: 'images',
      },
    ];
    if (includeContentStrings && !manageContent) {
      lessonCardIncludes.push(getContentStringQuery(req, getQueryLanguages(req), 'lessonCard'));
    }
    if (user) {
      includeParams.push({
        model: UserLesson,
        where: {
          userId: user.id,
        },
        required: false,
      });
      lessonCardIncludes.push({
        model: LessonLessonCard,
        as: 'bindings',
        include: [{
          model: AnswerCard,
          where: {
            userId: user.id,
          },
        }],
        required: false,
      });
    }
    includeParams.push({
      model: LessonCard,
      include: lessonCardIncludes,
    });
  }
  return includeParams;
};

const lessonCardOrder = [
  { model: LessonCard, through: 'lessonLessonCards' },
  { model: LessonLessonCard },
  'position',
  'asc',
];

const restQueryToSequelize = async (req, user, query, defaults, accountId = null, resourceBundleStatus = null,
  manageContent = false, identifyingPolicyLesson = false) => {
  const whereClause = restOperatorsToSequelize({
    ..._.omit(query, [
      '$limit', '$skip', '$sort', 'deletedAt', 'downloadLanguage',
      'localize', 'allowMT', 'startDate', 'endDate', 'includeSourceLesson',
    ]),
  });
  const includeParams = await getIncludeParams(req, user, {
    accountId,
    includeLessonCards: false,
    resourceBundleStatus,
    manageContent,
    identifyingPolicyLesson,
  });
  // set up the new query that's specific to sequelize.  Combine the defaults, then override the where clause
  // with a combo of the default where clause and the passed in where clause.
  // Then include all the things including the where clause for the tags and topics.
  const newQuery = {
    ...defaults,
    where: { ...defaults.where, ...whereClause },
    include: includeParams,
  };

  // override defaults for limit, skip, and sort if passed in by caller
  if (query) {
    const paginateMax = 700;
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(paginateMax, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      if (query.$sort.hasOwnProperty('editionVersionBuild')) {
        const { ['editionVersionBuild']: value, ...rest } = query.$sort;
        query.$sort = { ...rest, ['edition']: value };
      } else if (query.$sort.hasOwnProperty('variant')) {
        const { ['variant']: value, ...rest } = query.$sort;
        query.$sort = { ...rest, ['isBase']: value };
      } 
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }
  return newQuery;
};

const restGroupQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize(_.omit(query, ['$limit', '$skip', '$sort']));

  const newQuery = {
    ...defaults,
  };
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  return newQuery;
};

const duplicationParams = (req) => {
  return (
    [{
      model: Resource,
      as: 'resource',
    },
    {
      model: LessonCard,
      include: [getContentStringQuery(req, getQueryLanguages(req, req.i18n.language), 'lessonCard')],
    },
    getContentStringQuery(req, getQueryLanguages(req, req.i18n.language), 'lesson'),
    {
      model: Files,
    },
    {
      model: SupportedLanguages,
    }]
  );
};

const duplicate = async (req) => {
  const user = req.user;
  const sourceId = req.body.sourceId;
  let files;

  const includes = await getIncludeParams(req, null, {
    accountId: user && user.accountId,
    includeContentStrings: false,
  });
  const sourceLesson = await Lesson.findByPk(sourceId, {
    include: includes,
    order: [lessonCardOrder],
    paranoid: false, // includes soft deleted (archived) lessons
  });
  if (!sourceLesson) {
    const id = sourceId;
    const err = new Error(req.i18n.t('lessons.lesson_load_Error', { id }));
    err.status = 404;
    throw err;
  }

  // formulate title
  const titleCount = await Lesson.count({
    where: {
      title: {
        [Op.like]: `${sourceLesson.title}%`,
      },
    },
  });
  const newTitle = `${sourceLesson.title} copy ${titleCount}`;
  const sourceLessonLifecycle = (sourceLesson.lifecycle === "publish" || sourceLesson.lifecycle === "review") ? "draft" : sourceLesson.lifecycle;

  // compose new lesson
  const newLesson = {
    published: sourceLesson.published,
    title: newTitle,
    internalName: sourceLesson.internalName,
    description: sourceLesson.description,
    lifecycle: sourceLessonLifecycle,
    catalogId: sourceLesson.catalogId,
    edition: sourceLesson.edition,
    version: sourceLesson.version,
    build: sourceLesson.build,
    isStandalone: sourceLesson.isStandalone ? 1 : 0,
    userId: user.id,
    timeTracking: sourceLesson.timeTracking,
    requiredMinutes: sourceLesson.requiredMinutes,
    hasPolicy: sourceLesson.hasPolicy,
    resource: {
      digestable: 'lessons',
      isBrowsable: sourceLesson.resource.isBrowsable,
    },
    supportedLanguages:
      sourceLesson.supportedLanguages.map(({ language, langSupportable }) => ({ language, langSupportable })),
    sourceId,
  };
  const searchInfo = await deriveSearch(newLesson);
  newLesson.resource.searchText = searchTextFromSearchInfo(searchInfo);

  // create new lesson
  const copiedLesson = await Lesson.create(newLesson, {
    include: [{
      model: Resource,
      as: 'resource',
    }, {
      model: SupportedLanguages,
    }],
  });

  // thumbnail file
  if (sourceLesson.files) {
    files = await Promise.all(sourceLesson.files.map(f => createNewFile({}, f, 'lessons')));
    await copiedLesson.setFiles(files);
  }

  await setResourceAccess(user, copiedLesson.resource);

  // remove card bindings that are no longer part of the lesson
  const validBindings = sourceLesson.lessonCards.filter(card => card.lessonLessonCards.dateRemoved === null);

  // create card bindings
  let position = 0;
  const lessonLessonCards = validBindings.map((card) => {
    position += 1;
    return {
      lessonId: copiedLesson.id,
      lessonCardId: card.id,
      position,
    };
  });
  await LessonLessonCard.bulkCreate(lessonLessonCards);

  // localized content strings
  const contentStrings = await ContentStrings.findAll({
    where: {
      contentId: sourceId,
      model: 'lesson',
    },
  });
  if (contentStrings) {
    await ContentStrings.bulkCreate(contentStrings.map((cs) => {
      const newString = cs.get({ plain: true });
      delete newString.id;
      delete newString.updatedAt;
      delete newString.createdAt;
      newString.contentId = copiedLesson.id;
      return newString;
    }));
  }

  // reload lesson
  const finalCopy = await Lesson.findByPk(copiedLesson.id, {
    include: duplicationParams(req),
    order: [lessonCardOrder],
  });
  return finalCopy;
};

const isLessonInCampaign = async (lessonId) => {
  // first check if the lesson is directly in a campaign
  const campaignItems = await db.campaignItem.findAll({
    where: {
      itemId: lessonId,
      itemType: 'lesson',
    },
  });
  if (campaignItems.length > 0) {
    return true;
  }

  // check if the lesson is in a campaign through a program association.
  const programCampaignItems = await db.campaignItem.findAll({
    where: {
      itemType: 'program',
    },
    include: [{
      model: db.programs,
      required: true,
      include: [{
        model: db.lessons,
        where: {
          id: lessonId,
        },
        required: true,
      }],
    }],
  });
  if (programCampaignItems.length > 0) {
    return true;
  }
  return false;
};

/**
 * @openapi
 * /lessons?$limit={limit}&$skip={skip}&downloadLanguage=en:
 *   get:
 *     summary: Get Lessons List.
 *     tags:
 *       - Lesson
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Limit (integer) for number of records
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *         description: Offset or skip position before returning results
 *       - name: downloadLanguage
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           example: en
 *         description: Language code for content download
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   description: Array of lesson objects
 *                   items:
 *                     $ref: '#/components/schemas/lessons'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       default:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     lessons:
 *       type: object
 *       properties:
 *         resource:
 *           $ref: '#/components/schemas/resources'
 *         topics:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/topics'
 *         tags:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/tags'
 *         supportedLanguages:
 *           $ref: '#/components/schemas/supportedLanguages'
 *         lessonCards:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/lessonCards'
 *         contentStrings:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/contentStrings'
 */

/**
 * @openapi
 * /lessons?$limit={limit}&$skip={skip}&accountId={accountId}&downloadLanguage=en:
 *   get:
 *     summary: Get Content Library (Lessons)
 *     tags:
 *       - Content Library
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         description: Number of records to return
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *       - name: $skip
 *         in: query
 *         description: Number of records to skip
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *       - name: accountId
 *         in: query
 *         description: Account ID to filter lesson records
 *         required: true
 *         schema:
 *           type: integer
 *           example: 123
 *       - name: downloadLanguage
 *         in: query
 *         description: Language code for downloadable content
 *         required: false
 *         schema:
 *           type: string
 *           example: en
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records
 *                   example: 100
 *                 limit:
 *                   type: integer
 *                   example: 10
 *                 skip:
 *                   type: integer
 *                   example: 0
 *                 data:
 *                   type: array
 *                   description: Array of lesson objects
 *                   items:
 *                     $ref: '#/components/schemas/lessons'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       default:
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  let resourceBundleStatus = null;
  let manageContent = false;
  if (req.query && req.query.resourceBundleStatus) {
    resourceBundleStatus = req.query.resourceBundleStatus;
  }
  const identifyingPolicyLesson = req.query?.identifyingPolicies === 'true';
  if (req.query && req.query.manageContent) {
    manageContent = req.query.manageContent;
  }
  delete req.query.resourceBundleStatus;
  delete req.query.manageContent;
  delete req.query.identifyingPolicies;

  try {
    const lessonLifecycleQuery = await lifecycleByUser(req, req.user, 'lessons');
    const defaults = {
      order: [['updatedAt', 'DESC']],
      limit: config.paginate.default,
      offset: 0,
      where: {
        [Op.or]: lessonLifecycleQuery,
      },
    };

    const allowedPermissions = await getAllowedPermissions(
      req,
      req.user ? req.user.id : null,
      'admin',
      req.tokenPayload,
    );

    // You have to be an admin to view account specific programs
    if (req.query.accountId && !allowedPermissions.admin.includes('read')) {
      const err = new Error(req.i18n.t('lessons.lesson_no_permissions_Error'));
      err.status = 401;
      throw err;
    }
    const fixedQuery = _.omit(req.query, ['accountId']);

    const finalQuery = await restQueryToSequelize(
      req,
      req.user,
      fixedQuery,
      defaults,
      req.query.accountId,
      resourceBundleStatus,
      manageContent,
      identifyingPolicyLesson,
    );
    finalQuery.order = [[...finalQuery.order]];

    const pagedResult = {
      limit: finalQuery.limit,
      skip: finalQuery.offset,
    };

    // Do a count query without skip and limit to get total
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
      include: _.pick(finalQuery, ['include']).include,
      distinct: true,
    };
    const downloadLanguage = req.query.downloadLanguage ? req.query.downloadLanguage : req.i18n.language;
    const count = await Lesson.count(countQuery);

    const results = await Lesson.findAll(finalQuery);
    const flattenedData = results.map((lesson) => {
      return flattenLesson(req, lesson, downloadLanguage);
    });

    // include the event information for each qA (helpful, saved, shared, viewed)
    let data = await includeUserResources(req, flattenedData);

    if (req.user && req.user.accountId) {
      const account = await Account.findByPk(req.user.accountId);
      if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
        const accountLessons = await AccountLesson.findAll({ where: { accountId: account.id } });
        const accountLessonCards = await AccountLessonCard.findAll({ where: { accountId: account.id } });

        for (let lesson of data) {
          const matchingAccountLesson = accountLessons.find(accountLesson => accountLesson.lessonId === lesson.id);
          if (data.lessonCards) {
            for (let nextLessonCard of data.lessonCards) {
              if (nextLessonCard.cardType === 'policyAcknowledgement') {
                const matchingAccountLessonCard =
                  accountLessonCards.find(accountLessonCard => accountLessonCard.lessonCardId === nextLessonCard.id);
                if (matchingAccountLessonCard) {
                  // eslint-disable-next-line max-len
                  nextLessonCard = configurePolicyCardForAccount(nextLessonCard, matchingAccountLessonCard, req.user.accountId, false);
                }
              }
            }
          }
          if (matchingAccountLesson) {
            lesson = configureLessonForAccount(lesson, matchingAccountLesson);
          }
        }
      }
    }

    if (identifyingPolicyLesson) {
      for (const lesson of data) {
        if (lesson.hasPolicy) {
          // if account Lesson card not configured
          if (lesson.lessonCards.some((lessardc) => { return lessardc.accountLessonCards?.length === 0; })) {
            lesson.hasPolicyConfigured = false;
          } else {
            // if account Lesson card configured
            lesson.hasPolicyConfigured = true;
            for (const lessonPolicyCard of lesson.lessonCards) {
              const policyCardData = lessonPolicyCard.accountLessonCards.map((lpc) => {
                return lpc.policyType === 'link'
                  ? Boolean(lpc.link?.length)
                  : Boolean(lpc.file?.path);
              });

              if (policyCardData && policyCardData.includes(false)) {
                lesson.hasPolicyConfigured = false;
                break;
              }
            }
          }
        }
        delete lesson.lessonCards; // Remove lessonCards from the object
      }
    }

    if (req.query.accountId && !manageContent) {
      data = await getContentItemsDeployedData(data, req.query.accountId, 'lesson');
    }

    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /lessons/{lessonId}:
 *   get:
 *     summary: Lesson Details By LessonId
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: lessonId
 *         in: path
 *         schema:
 *           type: integer
 *         required: true
 *         description: Lesson ID
 *       - name: downloadLanguage
 *         in: query
 *         schema:
 *           type: string
 *           default: "en"
 *         required: false
 *         description: Language to download
 *       - name: allowMT
 *         in: query
 *         schema:
 *           type: boolean
 *           default: false
 *         required: false
 *         description: Allow machine translation
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/definitions/lessons'
 *                 - type: object
 *                   properties:
 *                     resource:
 *                       allOf:
 *                         - $ref: '#/definitions/resources'
 *                         - type: object
 *                           properties:
 *                             topics:
 *                               type: array
 *                               items:
 *                                 $ref: '#/components/schemas/topics'
 *                             tags:
 *                               type: array
 *                               items:
 *                                 $ref: '#/definitions/tags'
 *                             resourceBundles:
 *                               type: array
 *                               items:
 *                                 $ref: '#/definitions/resourceBundles'
 *                     supportedLanguages:
 *                       type: array
 *                       items:
 *                         $ref: '#/definitions/supportedLanguages'
 *                     contentStrings:
 *                       type: array
 *                       items:
 *                         $ref: '#/definitions/contentStrings'
 *                     userLesson:
 *                       $ref: '#/definitions/userLessons'
 *                     lessonCards:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           lessonLessonCards:
 *                             $ref: '#/definitions/lessonLessonCards'
 *                           contentStrings:
 *                             type: array
 *                             items:
 *                               $ref: '#/definitions/contentStrings'
 *                     userResource:
 *                       $ref: '#/definitions/userResources'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.read = async (req, res, next) => {
  try {
    const lesson = req.lesson;
    const allowedPermissions = await getAllowedPermissions(
      req,
      req.user ? req.user.id : null, 'lessons', req.tokenPayload,
    );

    if (lesson.deletedAt !== null && !allowedPermissions.lessons.includes('readArchived')) {
      const err = new Error(req.i18n.t('lessons.lesson_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    const downloadLanguage = req.query.downloadLanguage ? req.query.downloadLanguage : req.i18n.language;
    const flattenedRecord = flattenLesson(req, lesson, downloadLanguage);
    let data = await includeUserResource(req, flattenedRecord);
    data = await includeNumUsersAccessed(data);
    const cardErrorList = [];

    if (req.user && req.user.accountId) {
      const account = await Account.findByPk(req.user.accountId);

      if (account) {
        const relatedLessonCards = await LessonLessonCard.findAll({
          where: {
            lessonId: lesson?.id,
          },
        });
        const lessonCardIds = relatedLessonCards?.map(lessonCard => lessonCard.lessonCardId);

        const cardsToCheck = await LessonCard.findAll({
          where: {
            id: {
              [Op.in]: lessonCardIds,
            },
            cardType: 'policyAcknowledgement',
          },
          include: [
            {
              model: AccountLessonCard,
              where: {
                accountId: req.user.accountId,
              },
              include: [{
                model: Files,
                as: 'file',
                required: false,
              }],
              required: false,
            },
            {
              model: Files,
              as: 'images',
              required: false,
            }],
          required: true, // only want policyAcknowledgement cards
        });

        for (const currentCard of cardsToCheck) {
          let card = currentCard;
          let customCard = null;
          // take into account card customization for account
          if (card.accountLessonCards.length > 0) {
            customCard = card.accountLessonCards[0];
            if (customCard.file) {
              // move file to the right place
              customCard.images = { en: customCard.file.dataValues.path };
            }
            card = configurePolicyCardForAccount(card, customCard, req.user.accountId, true);
          }
          if (!card.policyLink && card.images.length === 0) {
            cardErrorList.push(card);
          } else if (card.images.length !== 0 && !customCard?.link) {
            const imageName = Array.isArray(card.images) ? card.images[0].path : card.images[Object.keys(card.images)[0]];
            if (imageName && imageName.toLowerCase().includes('policy-placeholder.')) {
              cardErrorList.push(card);
            }
          }
        }
        data.cardErrorList = cardErrorList;

        if (account.accountType === 'customer' || account.accountType === 'internal') {
          const accountLesson = await AccountLesson.findOne({ where: { accountId: account.id, lessonId: lesson.id } });
          const accountLessonCards = await AccountLessonCard.findAll({ where: { accountId: account.id } });
          if (data.lessonCards) {
            for (let nextLessonCard of data.lessonCards) {
              if (nextLessonCard.cardType === 'policyAcknowledgement') {
                const matchingAccountLessonCard =
                  accountLessonCards.find(accountLessonCard => accountLessonCard.lessonCardId === nextLessonCard.id);
                if (matchingAccountLessonCard) {
                  // eslint-disable-next-line max-len
                  nextLessonCard = configurePolicyCardForAccount(nextLessonCard, matchingAccountLessonCard, req.user.accountId, false);
                }
              }
            }
          }
          if (accountLesson) {
            data = configureLessonForAccount(data, accountLesson);
          }
        }
      }
    }

    let programArray = [];
    let programsObj = [];
    const relatedProgramLessons = await LessonProgram.findAll({
      where: {
        lessonId: lesson?.id,
      },
      attributes: ['programId'],
      raw: true,
    });

    programArray = relatedProgramLessons.map(obj => obj.programId);
    const programsLength = programArray.length;
    if (programsLength > 0 && programsLength <= 50) {
      programsObj = await Program.findAll({
        where: { id: { [Op.in]: programArray } },
        attributes: ['id', 'name', 'internalName'],
        raw: true,
      });
    }
    data.programs = programsObj;
    data.programCount = programsLength;
    res.json(data);
  } catch (err) {
    next(err);
  }
};

const updateLessonLifecycle = async (req, lesson, lifecycle) => {
  if (lifecycle && lifecycle !== 'retired') {
    const lessonInCampaign = await isLessonInCampaign(lesson.id);
    if (lessonInCampaign === true) {
      if (lifecycle === 'draft' || lifecycle === 'review') {
        const err = new Error(req.i18n.t('lessons.lesson_in_campaign_forbidden_Error'));
        err.status = 403;
        throw err;
      }
    }
  }
  if (lifecycle && lifecycle === 'retired' && lifecycle !== lesson.lifecycle) {
    await ContentPackageResources.destroy({
      where: {
        resourceId: lesson.resourceId,
      },
    });
    const workDayData = await WorkdayContentData.findAll({ where: { activityId: lesson.id, contentType: 'lesson' } });
    for (const wd of workDayData) {
      wd.queuedForRetirement = true;
      await wd.save();
    }
  }

  await lesson.update({ lifecycle });
  return true;
};

const getFinalLesson = async (req, lessonId, selectedLanguage, includes, lessonOrder) => {
  const finalLesson = await Lesson.findByPk(lessonId, {
    include: includes,
    order: lessonOrder,
  });

  const flattenedRecord = flattenLesson(req, finalLesson, selectedLanguage);

  return flattenedRecord;
};

/**
 * @openapi
 * /lessons?downloadLanguage=en:
 *   post:
 *     summary: Add Duplicate Lesson
 *     tags:
 *       - Lesson
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: downloadLanguage
 *         in: query
 *         description: Language code for downloading
 *         required: false
 *         schema:
 *           type: string
 *           example: en
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sourceId:
 *                 type: integer
 *                 example: 10
 *             required:
 *               - sourceId
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/lessons'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       default:
 *         description: Unexpected error
 */

/**
 * @openapi
 * /lessons?downloadLanguage=en&localize=false&allowMT=false:
 *   post:
 *     summary: Add New Lesson
 *     tags:
 *       - Lesson
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: downloadLanguage
 *         in: query
 *         required: false
 *         description: Language code for downloading
 *         schema:
 *           type: string
 *           example: en
 *       - name: localize
 *         in: query
 *         required: false
 *         description: Whether localization is allowed
 *         schema:
 *           type: boolean
 *           example: false
 *       - name: allowMT
 *         in: query
 *         required: false
 *         description: Whether machine translation is allowed
 *         schema:
 *           type: boolean
 *           example: false
 *     requestBody:
 *       required: true
 *       description: Suggested Fields parameters
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: integer
 *                 example: 10
 *               title:
 *                 type: string
 *                 example: 'Test Lesson'
 *               internalName:
 *                 type: string
 *                 example: ''
 *               description:
 *                 type: string
 *                 example: 'Test description'
 *               topicIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ['1', '2']
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ['Test 1', 'Test 2']
 *               supportedLanguages:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ['en', 'ar', 'hi']
 *               timeTracking:
 *                 type: boolean
 *                 example: false
 *               published:
 *                 type: boolean
 *                 example: false
 *               viewable:
 *                 type: boolean
 *                 example: false
 *               requiredMinutes:
 *                 type: string
 *                 example: '20'
 *               lifecycle:
 *                 type: string
 *                 example: 'draft'
 *             required:
 *               - userId
 *               - title
 *               - description
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/lessons'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       default:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  const requestBody = req.body;
  const selectedLanguage = requestBody.selectedLanguage || 'en';
  const { sourceId, supportedLanguages, viewable } = requestBody;
  try {
    let finalLesson;
    if (sourceId) {
      finalLesson = await duplicate(req);
    } else {
      const file = await uploadFile(req, 'lessons', selectedLanguage);
      const reqLangs = _.uniq(supportedLanguages);
      if (!reqLangs.includes('en')) {
        reqLangs.splice(0, 0, 'en'); // make sure we have english
      }

      const baseLesson = {
        resource: {
          digestable: 'lessons',
          isBrowsable: viewable,
        },
        supportedLanguages: reqLangs.map(l => ({ language: l, langSupportable: 'lesson' })),
      };

      requestBody.userId = req.user.id;
      const { updatedBody, errMsg } = assignRequestValues(requestBody, Lesson.rawAttributes, lessonRequestOptions);
      if (errMsg) {
        return res.status(400).json({ message: errMsg });
      }

      const lesson = Object.assign(baseLesson, updatedBody);

      const includes = await getIncludeParams(req, req.user);
      const searchInfo = await deriveSearch(lesson);
      lesson.resource.searchText = searchTextFromSearchInfo(searchInfo);
      const newLesson = await Lesson
        .create(lesson, {
          include: includes,
        });
      await newLesson.setFiles([file]);
      await setResourceAccess(req.user, newLesson.resource);
      await updateElasticSearch(req, newLesson.resource.id, searchInfo);
      finalLesson = await Lesson.findByPk(newLesson.id, {
        include: includes,
        order: [lessonCardOrder],
      });
    }
    if (!sourceId) {
      await updateLastMappedDate('create', finalLesson, 'lessons', {}, {});
    }
    const flattenedRecord = flattenLesson(req, finalLesson, req.i18n.language);
    res.json(flattenedRecord);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /lessons/{lessonId}?downloadLanguage=en&localize=false&allowMT=false:
 *   patch:
 *     summary: Change Life Cycle
 *     tags:
 *       - Lesson
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: lessonId
 *         in: path
 *         required: true
 *         description: The ID of the lesson to update
 *         schema:
 *           type: integer
 *       - name: downloadLanguage
 *         in: query
 *         required: false
 *         description: Language code for downloading
 *         schema:
 *           type: string
 *           example: en
 *       - name: localize
 *         in: query
 *         required: false
 *         description: Whether localization is allowed
 *         schema:
 *           type: boolean
 *           example: false
 *       - name: allowMT
 *         in: query
 *         required: false
 *         description: Whether machine translation is allowed
 *         schema:
 *           type: boolean
 *           example: false
 *     requestBody:
 *       required: true
 *       description: Suggested Fields parameters
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               lifecycle:
 *                 type: string
 *                 example: draft
 *             required:
 *               - lifecycle
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/lessons'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       default:
 *         description: Unexpected error
 */


/**
 * @openapi
 * /lessons/{lessonId}?downloadLanguage=en&localize=false&allowMT=false&includeLessonCards=false&includePrograms=true:
 *   patch:
 *     summary: Update Lesson Details
 *     tags:
 *       - Lesson
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: lessonId
 *         in: path
 *         required: true
 *         description: ID of the lesson to be updated
 *         schema:
 *           type: integer
 *       - name: downloadLanguage
 *         in: query
 *         required: false
 *         description: Language code for download
 *         schema:
 *           type: string
 *           example: en
 *       - name: localize
 *         in: query
 *         required: false
 *         description: Whether to localize the content
 *         schema:
 *           type: boolean
 *           example: false
 *       - name: allowMT
 *         in: query
 *         required: false
 *         description: Allow machine translation
 *         schema:
 *           type: boolean
 *           example: false
 *       - name: includeLessonCards
 *         in: query
 *         required: false
 *         description: Include lesson cards in response
 *         schema:
 *           type: boolean
 *           example: false
 *       - name: includePrograms
 *         in: query
 *         required: false
 *         description: Include programs in response
 *         schema:
 *           type: boolean
 *           example: true
 *     requestBody:
 *       required: true
 *       description: Suggested fields to update a lesson
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: integer
 *                 example: 10
 *               title:
 *                 type: string
 *                 example: 'Test Lesson'
 *               internalName:
 *                 type: string
 *                 example: ''
 *               description:
 *                 type: string
 *                 example: 'Test description'
 *               topicIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["1", "2"]
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["Test 1", "Test 2"]
 *               supportedLanguages:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["en", "ar", "hi"]
 *               timeTracking:
 *                 type: boolean
 *                 example: false
 *               published:
 *                 type: boolean
 *                 example: false
 *               viewable:
 *                 type: boolean
 *                 example: false
 *               requiredMinutes:
 *                 type: string
 *                 example: '20'
 *               lifecycle:
 *                 type: string
 *                 example: 'draft'
 *             required:
 *               - userId
 *               - title
 *               - description
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/lessons'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       default:
 *         description: Unexpected error
 */
module.exports.patch = async (req, res, next) => {
  const lesson = req.lesson;
  const requestBody = req.body;
  const selectedLanguage = requestBody.selectedLanguage || 'en';
  const { contentStrings, supportedLanguages, isPublic } = requestBody;

  try {
    const { updatedBody, errMsg } = assignRequestValues(req.body, Lesson.rawAttributes, lessonRequestOptions);
    if (errMsg) {
      return res.status(400).json({ message: errMsg });
    }
    const { lifecycle } = updatedBody;

    const existingLesson = await Lesson.findByPk(lesson.id);

    // const lessonOrder = [topicOrder, tagOrder];
    const lessonOrder = [];
    const includeLessonCards = !req.query.includeLessonCards || (req.query.includeLessonCards === 'true');
    if (includeLessonCards) {
      lessonOrder.push(lessonCardOrder);
    }
    const includes = await getIncludeParams(req, req.user, {
      includeLessonCards,
    });

    // handle lifecycle change and return the updated lesson
    const isLifecycleChange = lifecycle && lifecycle !== existingLesson.lifecycle;
    if (isLifecycleChange) {
      await updateLessonLifecycle(req, existingLesson, lifecycle);

      const flattenedRecord = await getFinalLesson(req, lesson.id, selectedLanguage, includes, lessonOrder);

      return res.json(flattenedRecord);
    }

    // *** BEGIN UPDATE *** //

    const newFile = await uploadFile(req, 'lessons', selectedLanguage);

    const workdayFields = ['title', 'requiredMinutes', 'description', 'fileId'];

    const lessonWorkdayFields = workdayFields.reduce((acc, field) => {
      if (field in existingLesson) {
        acc[field] = existingLesson[field];
      }
      return acc;
    }, {});

    const updatedBodyWorkdayFields = workdayFields.reduce((acc, field) => {
      if (field in updatedBody) {
        acc[field] = updatedBody[field];
      }
      return acc;
    }, {});

    if(newFile) {
      updatedBodyWorkdayFields.fileId = newFile.id;
    }

    await checkForWorkdayCCLUpdates(lesson.id, 'lesson', lessonWorkdayFields, updatedBodyWorkdayFields);

    // reqestBody.viewable writes to resource.isBrowsable
    if (requestBody.viewable !== undefined) {
      lesson.resource.isBrowsable = requestBody.viewable;
      await lesson.resource.update({ isBrowsable: requestBody.viewable });
    }

    // *** SAVE THE LESSON *** //

    const updatedLesson = await lesson.update(updatedBody, {
      include: includes,
    });

    // *** POST-SAVE TASKS *** //

    if (selectedLanguage !== 'en' && contentStrings) {
      const promises = contentStrings.map((nextContentString) => {
        if (!nextContentString.id) {
          return ContentStrings.create({
            contentId: lesson.id,
            model: 'lesson',
            language: selectedLanguage,
            field: nextContentString.field,
            value: nextContentString.value,
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }
        return ContentStrings.update(
          { value: nextContentString.value },
          { where: { id: nextContentString.id } },
        );
      });
      await Promise.all(promises);
    }

    if (newFile) {
      const currentFiles = updatedLesson.files;
      const newFiles = currentFiles.filter(img => img.language !== selectedLanguage);
      newFiles.push(newFile);
      await updatedLesson.setFiles(newFiles);
    }

    if (supportedLanguages !== undefined) {
      await handleSupportedLanguages('lesson', updatedLesson.id, req.body.supportedLanguages);
    }

    // requestBody.isPublic writes to resource.isPublic
    if (isPublic !== undefined && isPublic !== updatedLesson.resource.isPublic) {
      const newResource = await updatedLesson.resource.update({ isPublic });
      await setPublicAccess(newResource);
    }

    const searchInfo = await deriveSearch(updatedLesson);
    let searchText = searchTextFromSearchInfo(searchInfo);
    if (searchText && searchText.length > SEARCH_TEXT_COL_LIMIT) {
      searchText = searchText.substr(0, SEARCH_TEXT_COL_LIMIT);
      searchText = searchText.substr(0, searchText.lastIndexOf(' '));
    }

    await updatedLesson.resource.update({ searchText });

    await updateElasticSearch(req, updatedLesson.resource.id, searchInfo);

    const finalLesson = await Lesson.findByPk(updatedLesson.id, {
      include: includes,
    });
    await updatedLesson.reload();
    await updateLastMappedDate('edit', updatedLesson, 'lessons', requestBody, req.lesson);
    const flattenedRecord = flattenLesson(req, finalLesson, selectedLanguage);
    res.json(flattenedRecord);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /lessons/{lessonId}?downloadLanguage=en&localize=false&allowMT=false:
 *   delete:
 *     summary: Delete Lesson
 *     tags:
 *       - Lesson
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: lessonId
 *         in: path
 *         required: true
 *         description: Lesson record ID
 *         schema:
 *           type: integer
 *       - name: downloadLanguage
 *         in: query
 *         required: false
 *         description: Language code for download
 *         schema:
 *           type: string
 *           example: en
 *       - name: localize
 *         in: query
 *         required: false
 *         description: Whether to localize the content
 *         schema:
 *           type: boolean
 *           example: false
 *       - name: allowMT
 *         in: query
 *         required: false
 *         description: Allow machine translation
 *         schema:
 *           type: boolean
 *           example: false
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/lessons'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       default:
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  const lesson = req.lesson;
  const lessonId = lesson.id;
  try {
    const lessonInCampaign = await isLessonInCampaign(lesson.id);
    if (lessonInCampaign === true) {
      const err = new Error(req.i18n.t('lessons.lesson_in_campaign_forbidden_Error'));
      err.status = 403;
      throw err;
    }
    const updatedLesson = await lesson.destroy();
    await removeFromElasticSearch(req, updatedLesson.resourceId);
    // remove relationship records since our tables don't have cascade constraints
    await ResourceTag.destroy({
      where: {
        resourceId: updatedLesson.resourceId,
      },
    });
    await ResourceTopic.destroy({
      where: {
        resourceId: updatedLesson.resourceId,
      },
    });
    const llcs = await LessonLessonCard.findAll({ where: { lessonId } });
    const llcIds = llcs.map(llc => llc.id);

    // remove answer cards for all bindings that have this lesson
    await AnswerCard.destroy({
      where: {
        lessonLessonCardId: {
          [Op.in]: llcIds,
        },
      },
    });
    // remove card bindings to this lesson
    await LessonLessonCard.destroy({
      where: {
        lessonId,
      },
    });
    await removeLessonFromCampaigns(updatedLesson.id);
    await removeLessonFromPrograms(updatedLesson.id);
    res.json(updatedLesson);
  } catch (err) {
    next(err);
  }
};

module.exports.readi18n = async (req, res, next) => {
  try {
    const lessonId = req.body.lessonId ? parseInt(req.body.lessonId) : undefined;
    if (!lessonId) {
      const err = new Error(req.i18n.t('lessonCards.lessonId_missing_Error'));
      err.status = 400;
      throw err;
    }
    const language = req.body.language ? req.body.language : 'en';
    const fileName = `lesson_${lessonId}_${language}.json`;
    const lessonHeader = await gatherLessoni18nStrings(language, lessonId);
    res.set({ 'Content-Disposition': `attachment; filename=${fileName}`, 'Content-Type': 'application/json' });
    res.setHeader('Access-Control-Expose-Headers', 'content-disposition');
    res.write(JSON.stringify(lessonHeader));
    res.end();
  } catch (err) {
    next(err);
  }
};

module.exports.writei18n = async (req, res, next) => {
  const accountId = req.user.accountId;
  try {
    let totalRecords = 0;
    let data = '';
    let avResult = null;
    // eslint-disable-next-line no-unused-vars
    req.busboy.on('file', async (fieldname, file, origFileInfo) => {
      // generate a random file name - from the node-temp library
      const now = new Date();
      const filename = [
        now.getFullYear(), now.getMonth(), now.getDate(),
        '-',
        process.pid,
        '-',
        ((Math.random() * 0x100000000) + 1).toString(36),
        '.json',
      ].join('');

      const getWriteStream = () => {
        if (isLocalTestMode()) {
          return {
            fstream: fs.createWriteStream(getLocalTestFilename(filename)),
            uploadPromise: null,
          };
        }
        // Create a write stream of the new file
        const s3UploadStream = ({ Bucket, Key }) => {
          const pass = new stream.PassThrough();
          return {
            fstream: pass,
            uploadPromise: new Upload({
              client: s3Client,
              params: { Bucket, Key, Body: pass },
            }).done(),
          };
        };
        const { bucket, keyPrefix } = getCsvS3Bucket(accountId, 'i18n-uploads');
        return s3UploadStream({
          Bucket: bucket,
          Key: `${keyPrefix}${filename}`,
        });
      };

      const { fstream, uploadPromise } = getWriteStream();
      file
        .on('data', (buffer) => {
          for (let i = 0; i < buffer.length; ++i) {
            if (buffer[i] === LINE_FEED) {
              totalRecords++; // eslint-disable-line no-plusplus
            }
          }
          data += buffer;
        });

      // AV scanning
      const clamScanner = req.app.get('clamScan');
      if (clamScanner) {
        const av = clamScanner.passthrough();
        file.pipe(av).pipe(fstream);
        av.on('scan-complete', (result) => {
          avResult = result;
        });
      } else {
        file.pipe(fstream);
      }

      const onStreamClose = async (filePath) => {
        try {
          logger.info(`Upload of '${filePath}' finished`);
          const i18nData = JSON.parse(data);
          let invalidLanguageFile = false;
          if (i18nData && !validLanguageCodes.includes(i18nData.language)) {
            invalidLanguageFile = true;
          }
          // check for viral infestations
          if ((avResult && avResult.is_infected) || invalidLanguageFile) {
            const { bucket, keyPrefix } = getCsvS3Bucket(accountId, 'csv-uploads');
            const command = new DeleteObjectCommand({
              Bucket: bucket,
              Key: `${keyPrefix}${filename}`,
            });
            const result = await s3Client.send(command);
            logger.verbose("Removed infected file", result);
            // log & throw an error
            let err = '';
            if (invalidLanguageFile) {
              // eslint-disable-next-line max-len
              err = new Error(`Language code ${i18nData.language} is not valid. Please correct the language code and re-upload the translation file.`);
            } else {
              logger.error(`Virus detected: ${avResult.viruses}`);
              err = new Error(`File is infected with the following viruses: ${avResult.viruses}`);
            }
            err.status = 400;
            throw err;
          }
          // Create the import record with the filename
          await updateLessoni18nStrings(i18nData);
          await updateLessonProtectedTraits(i18nData);
          const results = {
            filename,
            totalRecords,
          };
          res.json(results); // TODO: For now, return when the file is done uploading, but really we want to return before it's done and then check status
        } catch (err) {
          logger.error('i18n finished uploading, other error: %j', err);
          next(err);
        }
      };

      // On finish of the upload... The S3 stuff is handled slightly differently
      if (isLocalTestMode()) {
        fstream.on('close', async () => {
          onStreamClose(getLocalTestFilename(filename));
        });
      } else {
        const result = await uploadPromise;
        onStreamClose(result.Location);
      }
    });
    req.busboy.on('field', (fieldname, val, fieldnameTruncated, valTruncated, encoding, mimetype) => { // eslint-disable-line no-unused-vars
      // This is where we get the other fields of the form type.
      // console.log('field:', fieldname, ':', val);
    });
    req.busboy.on('finish', () => {
      // console.log('Busboy finish, Done parsing upload form!');
    });
    req.busboy.on('error', (err) => {
      logger.error('Busboy Error: %j', err);
      next(err);
    });
    req.pipe(req.busboy); // Pipe it through busboy
  } catch (err) {
    next(err);
  }
};

module.exports.generateScormPackage = async (req, res, next) => {
  try {
    const { archiveFilename, clientFilename, tmpFolderPath } = await genAndDownloadScormArchive(req, 'lessons');
    res.setHeader('Access-Control-Expose-Headers', 'content-disposition');
    res.download(archiveFilename, clientFilename, async (err) => {
      if (err) {
        throw err;
      }
      // delete the temporary folder
      await fse.remove(tmpFolderPath);
    });
  } catch (err) {
    next(err);
  }
};

/**
 * user report endpoint for completion report section on the front end
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @todo GDPR
 */
/**
 * @swagger
 * /lessons/{lessonId}/report/users?$limit={limit}&$skip={skip}:
 *   get:
 *     summary: Lesson User Report By Given LessonId
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: lessonId
 *         in: path
 *         schema:
 *           type: integer
 *         required: true
 *         description: Lesson ID
 *       - name: $limit
 *         in: query
 *         schema:
 *           type: integer
 *           example: 10
 *         required: true
 *         description: Limit (integer) for number of records
 *       - name: $skip
 *         in: query
 *         schema:
 *           type: integer
 *           example: 0
 *         required: true
 *         description: Number of records to skip
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       firstName:
 *                         type: string
 *                       lastName:
 *                         type: string
 *                       email:
 *                         type: string
 *                       userStatus:
 *                         type: string
 *                       title:
 *                         type: string
 *                       location:
 *                         type: string
 *                       role:
 *                         type: string
 *                       city:
 *                         type: string
 *                       employeeId:
 *                         type: integer
 *                       supervisor:
 *                         type: string
 *                       exempt:
 *                         type: integer
 *                       hireDate:
 *                         type: string
 *                         format: date-time
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       lastActivityAt:
 *                         type: string
 *                         format: date-time
 *                       managerEmail:
 *                         type: string
 *                       assigned:
 *                         type: string
 *                       completed:
 *                         type: integer
 *                       dueDate:
 *                         type: string
 *                         format: date-time
 *                       group:
 *                         type: string
 *                       campaign:
 *                         type: string
 *                       pctComplete:
 *                         type: integer
 *                       timeComplete:
 *                         type: string
 *                       minutesSpent:
 *                         type: integer
 *                       started:
 *                         type: string
 *                         format: date-time
 *                       status:
 *                         type: string
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.usersReport = async (req, res, next) => {
  const model = 'lesson';
  try {
    const isNewReport = !!(req.query.newReport && req.query.newReport === 'true');
    const minTimeInMinutes = 0; // don't apply lesson min required time on report
    let generatedUserReport = null;
    let limit;
    let offset;
    if (isNewReport) {
      logger.info(`Calling new completion report for lessonId: ${req.params.id}`);
      const defaults = {
        order: [[{ model: Users }, 'createdAt', 'desc']],
        limit: config.paginate.default,
        offset: 0,
      };
      const rqts = await newRestQueryToSequelize(model, req.user.id, req.query, defaults);
      const queryParams = rqts.newQuery;
      queryParams.order = [[...queryParams.order]];
      limit = queryParams.limit;
      offset = queryParams.offset;
      generatedUserReport = await runContentUserReport(
        queryParams,
        req.params.id,
        model,
        req.user.accountId,
        req.query.startDate,
        req.query.endDate,
        rqts.userWhere,
      );
    } else {
      logger.info(`Calling old completion report for lessonId: ${req.params.id}`);
      const defaults = {
        order: [['id', 'DESC']],
        limit: config.paginate.default,
        offset: 0,
      };
      const queryParams = await rqtu(req.user.id, req.query, defaults);
      queryParams.order = [[...queryParams.order]];
      limit = queryParams.limit;
      offset = queryParams.offset;
      generatedUserReport = await runLessonUserReport(
        queryParams,
        req.params.id,
        req.user.accountId,
        minTimeInMinutes,
        req.query.startDate,
        req.query.endDate,
      );
    }
    res.json({
      total: generatedUserReport.total,
      limit,
      skip: offset,
      data: generatedUserReport.data,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /lessons/contentCompletion/usersReport?programIds={programIds}&$limit={limit}&$skip={skip}:
 *  get:
 *     summary: Report Multi-selector for Content
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: programIds
 *         in: query
 *         schema:
 *           type: string
 *           example: "1,2"
 *         description: Comma separated list of program IDs (integers)
 *         required: false
 *       - name: $limit
 *         in: query
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Limit (integer) for number of records
 *         required: true
 *       - name: $skip
 *         in: query
 *         schema:
 *           type: integer
 *           example: 0
 *         description: Number of records to skip
 *         required: true
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       firstName:
 *                         type: string
 *                       lastName:
 *                         type: string
 *                       email:
 *                         type: string
 *                       userStatus:
 *                         type: string
 *                       title:
 *                         type: string
 *                       location:
 *                         type: string
 *                       role:
 *                         type: string
 *                       city:
 *                         type: string
 *                       employeeId:
 *                         type: integer
 *                       supervisor:
 *                         type: string
 *                       exempt:
 *                         type: integer
 *                       hireDate:
 *                         type: string
 *                         format: date-time
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       lastActivityAt:
 *                         type: string
 *                         format: date-time
 *                       managerEmail:
 *                         type: string
 *                       assigned:
 *                         type: string
 *                       completed:
 *                         type: integer
 *                       dueDate:
 *                         type: string
 *                         format: date-time
 *                       group:
 *                         type: string
 *                       campaign:
 *                         type: string
 *                       pctComplete:
 *                         type: integer
 *                       timeComplete:
 *                         type: string
 *                       minutesSpent:
 *                         type: integer
 *                       started:
 *                         type: string
 *                         format: date-time
 *                       status:
 *                         type: string
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

module.exports.contentCompletionReport = async (req, res, next) => {
  const defaults = {
    order: [[{ model: Users }, 'id', 'desc']],
    limit: config.paginate.default,
    offset: 0,
  };
  let model = 'program-lesson';
  let programIds = null;
  let lessonIds = null;
  if (req.query) {
    programIds = req.query.programIds;
    lessonIds = req.query.lessonIds;
    if (req.query.lessonIds && !req.query.programIds) {
      model = 'lesson';
    } else if (!req.query.lessonIds && req.query.lessonIds) {
      model = 'program';
    }
  }
  try {
    const rqts = await newRestQueryToSequelize(model, req.user.id, req.query, defaults);
    const queryParams = rqts.newQuery;
    const limit = queryParams.limit;
    const offset = queryParams.offset;
    const generatedUserReport = await contentCompletionUserReport(
      queryParams,
      req.user.accountId,
      programIds,
      lessonIds,
      rqts.userWhere,
    );
    res.json({
      total: generatedUserReport.total,
      limit,
      skip: offset,
      data: generatedUserReport.data,
      totalUsers: generatedUserReport.totalUsers,
    });
  } catch (err) {
    next(err);
  }
};

module.exports.contentCompletionGroups = async (req, res, next) => {
  try {
    const generatedUserReport = await contentCompletionGroups(
      req.user.accountId,
      req.query,
    );
    res.json({
      data: generatedUserReport.data,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * downloadable csv endpoint for completion report section on the front end
 *
 * @param {{}} req
 * @param {{}} res
 * @param {{}} next
 * @todo GDPR
 */
module.exports.generateCSV = async (req, res, next) => {
  const accountId = req.user.accountId || req.user.accounts[0].id;
  const keepAttributes = req.query.attributes;
  const model = 'lesson';

  const timeZone = req.query.timeZone;
  delete req.query.timeZone;

  try {
    const isNewReport = !!(req.query.newReport && req.query.newReport === 'true');
    // eslint-disable-next-line no-unused-vars
    const [, lesson] = await Promise.all([
      checkAllowedAttributes(req, keepAttributes, accountId),
      Lesson.findByPk(req.params.id, { attributes: ['title', 'requiredMinutes'], raw: true }),
    ]);

    // user can't specify limit or skip for the csv.
    delete req.query.$limit;
    delete req.query.$skip;
    delete req.query.attributes;

    const filename = cleanDownloadFilename(`Emtrain_Completion_Report_${Date.now()}`);
    // const minTime = lesson.requiredMinutes || 0;
    // adding the group & campaign names to csv report details
    const dynamicQueryData = [];
    if (req.query.group) {
      // get groupName by id
      const groupId = parseInt(req.query.group);
      const groupData = await Groups.findByPk(groupId, { attributes: ['name'], raw: true });
      dynamicQueryData.push([`Group: ${groupData.name}`]);
    }
    if (req.query.campaign) {
      // get campaignName by id
      const campId = parseInt(req.query.campaign);
      const campaignData = await Campaigns.findByPk(campId, { attributes: ['name'], raw: true });
      dynamicQueryData.push([`Campaign: ${campaignData.name}`]);
    }
    if (isNewReport) {
      const defaults = {
        order: [[{ model: Users }, 'createdAt', 'desc']],
        limit: 100,
        offset: 0,
      };
      const rqts = await newRestQueryToSequelize(model, req.user.id, req.query, defaults);
      const queryParams = rqts.newQuery;
      queryParams.order = [[...queryParams.order]];
      // eslint-disable-next-line max-len
      await genCSV(keepAttributes, queryParams, filename, res, runContentUserReport, timeZone)(req.params.id, model, accountId, req.query.startDate, req.query.endDate, rqts.userWhere);
    } else {
      const defaults = {
        order: [['id', 'DESC']],
        limit: 100,
        offset: 0,
      };
      const queryParams = await rqtu(req.user.id, req.query, defaults);
      queryParams.order = [[...queryParams.order]];
      // eslint-disable-next-line max-len
      await genNewCSV(keepAttributes, queryParams, filename, res, runLessonUserReport, timeZone, req.query, dynamicQueryData, 'reports')(queryParams, req.params.id, accountId, req.query.startDate, req.query.endDate);
    }
  } catch (err) {
    next(err);
  }
};

module.exports.applyStagedLangStr = async (req, res, next) => {
  try {
    const lessonId = req.query.lessonId ? parseInt(req.query.lessonId) : undefined;
    const language = req.query.language;
    if (!lessonId) {
      const err = new Error('failed to specify lessonId in query');
      err.status = 400;
      throw err;
    }
    if (!language) {
      const err = new Error('failed to specify language in query');
      err.status = 400;
      throw err;
    }
    const lesson = await Lesson.findByPk(lessonId, {
      attributes: [],
      include: {
        attributes: ['id'],
        model: LessonCard,
        required: false,
      },
    });

    const lessonCardIds = lesson.lessonCards.map(lc => lc.id);

    const stats = await swapContentStrings(language, [lessonId], lessonCardIds);

    const mapCardIds = lesson.lessonCards.map(lc => lc.cardType === 'statesMap' && lc.id);

    if (mapCardIds) {
      await swapProtectedTraits(language, lessonCardIds);
    }

    await SupportedLanguages.destroy({
      where: {
        langSupportable: 'lesson',
        langSupportableId: lessonId,
        language: `${language}-review`,
      },
    });
    const existingSupportedLang = await SupportedLanguages.findOne({
      where: {
        language,
        langSupportable: 'lesson',
        langSupportableId: lessonId,
      },
    });
    if (!existingSupportedLang) {
      await SupportedLanguages.create({
        language,
        langSupportable: 'lesson',
        langSupportableId: lessonId,
      });
    }

    res.json({ success: true, stats });
  } catch (error) {
    next(error);
  }
};

// returns an array of lesson cards in the lesson with attached answer card report
// per card that filters answers according to parameters.
// This api replaces old method of using lessonCards list api to get
// this information as that api was becoming too overloaded to function
// as a reporting api.

/**
 * @swagger
 * /lessons/{lessonsId}/report/lessonCards?downloadLanguage=en&campaignId={campaignId}:
 *   get:
 *     summary: Lesson Card Report
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: lessonsId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: downloadLanguage
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           example: en
 *       - name: campaignId
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: total number of records.
 *                 data:
 *                   type: array
 *                   description: Array of report data object.
 *                   items:
 *                     type: object
 *                     properties:
 *                       lessonLessonCards:
 *                         $ref: '#/components/schemas/lessonLessonCards'
 *                       contentStrings:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/lessonLessonCards'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     lessonLessonCards:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         lessonId:
 *           type: integer
 *         lessonCardId:
 *           type: integer
 *         position:
 *           type: integer
 *         isOwner:
 *           type: boolean
 *         dateAdded:
 *           type: string
 *           format: date-time
 *         dateRemoved:
 *           type: string
 *           format: date-time
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */
module.exports.lessonCardReport = async (req, res, next) => {
  req.publishOnly = true; // to be replaced when UI can specify
  const { campaignId, startDate, endDate } = req.query;
  const downloadLanguage = req.query && req.query.downloadLanguage ? req.query.downloadLanguage : req.i18n.language;
  const allowMT = !(req.query.allowMT && req.query.allowMT === 'false'); // default to true, must be set to "false" to disallow
  const lesson = req.lesson;
  const data = [];
  let total = 0;
  const programId = null; // supress program filter since browse/scorm learners don't record the program when taking lessons
  const lessonId = req.lesson ? req.lesson.id : null; // hack to always apply lesson filtering to lesson card reports
  try {
    if (lesson.lessonCards) {
      total = lesson.lessonCards.length;
      for (const card of lesson.lessonCards) {
        let flattenedRecord = card.get({ plain: true });
        flattenedRecord = localizeModelObject(req, flattenedRecord, downloadLanguage, allowMT);
        flattenedRecord = await includeAnswer(
          req, flattenedRecord, lessonId,
          campaignId, programId, startDate, endDate,
        );
        data.push(flattenedRecord);
      }
    }
    res.json({
      total,
      data,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * endpoint for progress report
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @todo GDPR
 */
module.exports.progressReport = async (req, res, next) => {
  try {
    const generatedProgressReport = await generateLessonProgressReport(req, req.lesson);
    res.json(generatedProgressReport);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /lessons/{lessonId}/campaigns:
 *   get:
 *     summary: Lesson Campaigns By Given LessonId
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: lessonId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       name:
 *                         type: string
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.listCampaigns = async (req, res, next) => {
  try {
    const lesson = await Lesson.findByPk(req.params.id, { attributes: ['id'], raw: true });
    if (!lesson) {
      const err = new Error(req.i18n.t('lessons.lesson_load_Error', { id: req.params.id }));
      err.status = 404;
      throw err;
    }
    const accountId = req.user.accountId;
    const campaignsQuery = {
      attributes: ['id', 'name'],
      where: {
        accountId,
        status: {
          [Op.ne]: 'withdrawn',
        },
      },
      include: {
        model: CampaignItem,
        attributes: [],
        as: 'items',
        required: true,
        where: {
          [Op.or]: [
            {
              itemType: 'lesson',
              itemId: req.params.id,
            },
            {
              itemType: 'program',
              itemId: { [Op.in]: db.Sequelize.literal('(SELECT programs.id FROM ' +
              'programs, lessonPrograms WHERE programs.id = lessonPrograms.programId ' +
              'AND programs.deletedAt IS NULL AND lessonPrograms.dateRemoved IS NULL ' +
              `AND lessonPrograms.lessonId = ${req.params.id})`) },
            },
          ],
        },
      },
    };

    const countQuery = {
      where: _.pick(campaignsQuery, ['where']).where,
      include: _.pick(campaignsQuery, ['include']).include,
      distinct: true,
    };

    const data = await Campaigns.findAll(campaignsQuery);
    const count = await Campaigns.count(countQuery);

    res.json({ total: count, data });
  } catch (err) {
    next(err);
  }
};

const applyCustomizations = async (accountId, lessons) => {
  const account = await Account.findByPk(accountId);
  if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
    const accountLessons = await AccountLesson.findAll({ where: { accountId: account.id } });
    const accountLessonCards = await AccountLessonCard.findAll({ where: { accountId: account.id } });

    for (let lesson of lessons) {
      const matchingAccountLesson = accountLessons.find(accountLesson => accountLesson.lessonId === lesson.id);
      if (lesson.lessonCards) {
        for (let nextLessonCard of lesson.lessonCards) {
          if (nextLessonCard.cardType === 'policyAcknowledgement') {
            const matchingAccountLessonCard =
              accountLessonCards.find(accountLessonCard => accountLessonCard.lessonCardId === nextLessonCard.id);
            if (matchingAccountLessonCard) {
              // eslint-disable-next-line max-len
              nextLessonCard = configurePolicyCardForAccount(nextLessonCard, matchingAccountLessonCard, accountId, false);
            }
          }
        }
      }
      if (matchingAccountLesson) {
        lesson = configureLessonForAccount(lesson, matchingAccountLesson);
      }
    }
  }
  return lessons;
};

/**
 * @swagger
 * /lessons/reportList:
 *   get:
 *     summary: Lesson Report List
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: total number of records.
 *                 data:
 *                   type: array
 *                   description: Array of report data object.
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         description: Lesson Id.
 *                       name:
 *                         type: string
 *                         description: Lesson name.
 *                       deployDate:
 *                         type: string
 *                         description: Deploy Date.
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 */
module.exports.reportList = async (req, res, next) => {
  const {
    accountUsers: rAccountUsers,
    lessons: rLessons,
    resourceBundles: rResourceBundles,
    resources: rResources,
    userLessons: rUserLessons,
    accounts: rAccounts,
  } = db.reportingModels;

  const accountId = req.query.accountId;
  const search = req.query.$search;
  const skip = req.query.$skip;
  const limit = req.query.$limit;
  const bundleAccountId = accountId || (req.user ? req.user.accountId : null);
  try {
    const bundleIds = await getResourceBundlesIds(bundleAccountId);
    const userLessonsQuery = `SELECT DISTINCT(lessons.id)
    FROM ${rUserLessons.tableName} AS userLessons
    JOIN ${rLessons.tableName} AS lessons ON (lessons.id = userLessons.resourceId AND userLessons.type = 'lesson' AND lessons.deletedAt IS NULL)
    JOIN ${rAccountUsers.tableName} AS accountUsers ON accountUsers.userId = userLessons.userId
    JOIN ${rAccounts.tableName} AS accounts ON accounts.id = accountUsers.accountId
    WHERE userLessons.type = 'lesson'
    AND userLessons.sourceLifecycle IN('publish', 'retired')
    AND accounts.id = ${bundleAccountId}
    AND accounts.deletedAt IS NULL`;
    const accountUserLessons = await db.sequelize.query(userLessonsQuery, { type: db.sequelize.QueryTypes.SELECT });
    const lessonIds = accountUserLessons.map(ul => ul.id);
    const lessonsListQuery = {
      attributes: ['id', 'title', 'lifecycle'],
      where: {
        lifecycle: {
          [Op.in]: ['publish', 'retired'],
        },
        id: { [Op.in]: lessonIds },
        isStandalone: true,
      },
      include: [{
        attributes: ['id'],
        model: rResources,
        as: 'resource',
        include: [{
          model: rResourceBundles,
          where: {
            bundleId: {
              [Op.in]: bundleIds,
            },
          },
        }],
        required: true,
      }],
      order: [[
        { model: rResources, as: 'resource' },
        { model: rResourceBundles },
        'createdAt',
        'desc',
      ]],
    };
    if (search) {
      lessonsListQuery.where.title = { [Op.like]: `%${search}%` };
    }
    lessonsListQuery.limit = limit
      ? parseInt(limit)
      : config.paginate.max;
    lessonsListQuery.offset = skip !== undefined
      ? parseInt(skip)
      : 0;
    let lessonList = await rLessons.findAll(lessonsListQuery);

    if (req.user && req.user.accountId) {
      lessonList = await applyCustomizations(req.user.accountId, lessonList);
    }

    // construct final list of report items
    const reportList = lessonList.map((lesson) => {
      let deployDate = lesson.resource.resourceBundles.length > 0 ? lesson.resource.resourceBundles[0].createdAt : null;
      const resBundles = lesson.resource.resourceBundles.filter(resBundle => resBundle.bundleId !== PUBLIC_BUNDLE_ID);
      if (resBundles.length > 0) {
        deployDate = resBundles[0].createdAt;
      }
      return {
        id: lesson.id,
        name: lesson.title,
        deployDate: format(deployDate, 'MM/DD/YYYY'),
      };
    });
    res.json({ total: reportList.length, data: reportList });
  } catch (err) {
    next(err);
  }
};

module.exports.contentsProgressReport = async (req, res, next) => {
  try {
    const generatedProgressReport = await generateContentsProgressReport(req);
    res.json(generatedProgressReport);
  } catch (err) {
    next(err);
  }
};

module.exports.lessonById = async (req, res, next, id) => {
  try {
    const includeLessonCards = !req.query.includeLessonCards || (req.query.includeLessonCards === 'true');
    const lesson = await Lesson.findOne({
      where: {
        id,
      },
      include: await getIncludeParams(req, req.user, {
        includeLessonCards,
      }),
      paranoid: false, // includes soft deleted (archived) lessons
    });
    if (!lesson) {
      const err = new Error(req.i18n.t('lessons.lesson_load_Error', { id }));
      err.status = 404;
      throw err;
    }

    const allowedPermissions = await getAllowedPermissions(
      req,
      req.user ? req.user.id : null, 'lessons', req.tokenPayload,
    );
    if (!canAccessLesson(req.user, allowedPermissions, lesson)) {
      const err = new Error(req.i18n.t('lessons.lesson_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    req.lesson = lesson;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /lessons/{lessonId}/groups?$limit={limit}&$skip={skip}:
 *   get:
 *     summary: Lesson Groups For Given lessonId
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: lessonId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Limit (integer) for number of records
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *         description: Number of skip (integer) records
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       groupId:
 *                         type: integer
 *                       campaignId:
 *                         type: integer
 *                       programId:
 *                         type: integer
 *                       reoccurenceIteration:
 *                         type: integer
 *                       createdAt:
 *                         type: string
 *                       updatedAt:
 *                         type: string
 *                       deletedAt:
 *                         type: string
 *                       group:
 *                         type: string
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.lessonGroupList = async (req, res, next) => {
  // get campaigns based on lessonId
  const accountId = req.user.accountId;
  const campaignsQuery = {
    attributes: ['id', 'name'],
    where: {
      accountId,
      status: {
        [Op.ne]: 'withdrawn',
      },
    },
    include: {
      model: CampaignItem,
      attributes: [],
      as: 'items',
      required: true,
      where: {
        [Op.or]: [
          {
            itemType: 'lesson',
            itemId: req.params.id,
          },
          {
            itemType: 'program',
            itemId: {
              [Op.in]: db.Sequelize.literal('(SELECT programs.id FROM ' +
                'programs, lessonPrograms WHERE programs.id = lessonPrograms.programId ' +
                'AND programs.deletedAt IS NULL AND lessonPrograms.dateRemoved IS NULL ' +
                `AND lessonPrograms.lessonId = ${req.params.id})`),
            },
          },
        ],
      },
    },
  };
  try {
    const CampaignsData = await Campaigns.findAll(campaignsQuery);
    const campaignIds = CampaignsData.map((campaign) => {
      return campaign.id;
    });
    const defaults = {
      attributes: ['groupId'],
      limit: config.paginate.default,
      offset: 0,
      where: {
        campaignId: { [Op.in]: campaignIds },
        programId: null,
      },
      group: ['groupId'],
    };
    const queryParams = restGroupQueryToSequelize(req.query, defaults);
    const finalQuery = {
      ...queryParams,
    };
    const pagedResult = {
      limit: queryParams.limit,
      skip: queryParams.offset,
    };
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
    };
    const count = await GroupAssignments.count(countQuery);
    const groupAssignments = await GroupAssignments.findAll(finalQuery);
    const promises = [];
    // look up items associated with campaign item bindings
    groupAssignments.forEach((assignment) => {
      promises.push(lookupGroup(assignment));
    });
    const results = await Promise.all(promises);
    // attach item to binding
    const data = results.map((assignment) => {
      return flattenAssignment(assignment);
    });
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.generateContentCSV = async (req, res, next) => {
  let model = 'program-lesson';
  let programIds = null;
  let lessonIds = null;
  if (req.query) {
    programIds = req.query.programIds;
    lessonIds = req.query.lessonIds;
    if (req.query.lessonIds && !req.query.programIds) {
      model = 'lesson';
    } else if (!req.query.lessonIds && req.query.lessonIds) {
      model = 'program';
    }
  }
  const accountId = req.user.accountId || req.user.accounts[0].id;
  const keepAttributes = req.query.attributes;
  const timeZone = req.query.timeZone || moment.tz.guess();
  delete req.query.timeZone;
  try {
    // user can't specify limit or skip for the csv.
    delete req.query.$limit;
    delete req.query.$skip;
    delete req.query.attributes;
    const filename = cleanDownloadFilename(`Emtrain_Completion_Report_${moment().tz(timeZone).format('YYYYMMDDHHmmss')}`);
    // const minTime = lesson.requiredMinutes || 0;
    // adding the group & campaign names to csv report details
    const dynamicQueryData = [];
    if (req.query.group) {
      const groupData = await Groups.findAll({
        where: {
          id: { [Op.in]: req.query.group },
        },
      }, { attributes: ['name'], raw: true });

      let groupNames = groupData.map(c => c.name);
      groupNames = groupNames.join(',');
      dynamicQueryData.push([`Group: ${groupNames}`]);
    }
    if (req.query.campaign) {
      // get campaignName by id
      const campaignData = await Campaigns.findAll({
        where: {
          id: { [Op.in]: req.query.campaign },
        },
      }, { attributes: ['name'], raw: true });
      let campaignNames = campaignData.map(c => c.name);
      campaignNames = campaignNames.join(',');
      dynamicQueryData.push([`Campaign: ${campaignNames}`]);
    }
    const contentNames = [];
    if (lessonIds) {
      const lIds = lessonIds.split(',');
      // get lessonsnNames by ids
      const lessonData = await Lesson.findAll({
        where: {
          id: { [Op.in]: lIds },
        },
      }, { attributes: ['title'], raw: true });
      contentNames.push(lessonData.map(l => l.title));
    }
    if (programIds) {
      const pIds = programIds.split(',');
      // get lessonsnNames by ids
      const programData = await Program.findAll({
        where: {
          id: { [Op.in]: pIds },
        },
      }, { attributes: ['name'], raw: true });
      contentNames.push(programData.map(p => p.name));
    }
    if (contentNames.length) {
      const cNames = contentNames.join(',');
      dynamicQueryData.push([`Contents: ${cNames}`]);
    }
    const defaults = {
      order: [[{ model: Users }, 'id', 'desc']],
      limit: 100,
      offset: 0,
    };
    const rqts = await newRestQueryToSequelize(model, req.user.id, req.query, defaults);
    const queryParams = rqts.newQuery;
    queryParams.order = [[...queryParams.order]];
    // eslint-disable-next-line max-len
    await genNewCSV(keepAttributes, queryParams, filename, res, contentCompletionUserReport, timeZone, req.query, dynamicQueryData, 'reports')(accountId, programIds, lessonIds, rqts.userWhere);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /lessons/contentCompletion/campaigns?lessonIds={lessonIds}&programIds={programIds}&$limit={limit}&$skip={skip}:
 *  get:
 *     summary: Lesson/Program Campaigns list
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: lessonIds
 *         in: query
 *         schema:
 *           type: string
 *           example: "1,2"
 *         description: Comma separated lessonIds
 *       - name: programIds
 *         in: query
 *         schema:
 *           type: string
 *           example: "1,2"
 *         description: Comma separated programIds
 *       - name: $limit
 *         in: query
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Limit (integer) for number of records
 *       - name: $skip
 *         in: query
 *         schema:
 *           type: integer
 *           example: 0
 *         description: Number of records to skip
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       name:
 *                         type: string
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.contentCompletionCampaigns = async (req, res, next) => {
  try {
    const lessonIds = req.query.lessonIds ? req.query.lessonIds.split(',') : null;
    const programIds = req.query.programIds ? req.query.programIds.split(',') : null;
    const accountId = req.user.accountId;
    const whereCondition = [];
    if (lessonIds) {
      whereCondition.push({
        itemType: 'lesson',
        itemId: { [Op.in]: lessonIds },
      });
    }
    if (programIds) {
      whereCondition.push({
        itemType: 'program',
        itemId: { [Op.in]: programIds },
      });
    }
    const campaignsQuery = {
      attributes: ['id', 'name', 'status'],
      where: {
        accountId,
        lifecycle: {
          [Op.ne]: 'draft',
        },
      },
      include: {
        model: CampaignItem,
        attributes: [],
        as: 'items',
        required: true,
        where: {
          [Op.or]: whereCondition,
        },
      },
      order: [
        ['name', 'ASC'],
      ],
    };

    const countQuery = {
      where: _.pick(campaignsQuery, ['where']).where,
      include: _.pick(campaignsQuery, ['include']).include,
      distinct: true,
    };

    const data = await Campaigns.findAll(campaignsQuery);
    const count = await Campaigns.count(countQuery);

    res.json({ total: count, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /lessons/duplicateBreakSharedCard:
 *   post:
 *     summary: Duplicate Lesson Break shared card links
 *     tags:
 *       - Lesson
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sourceId:
 *                 type: integer
 *                 example: 10
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/lessons'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       default:
 *         description: Unexpected error
 */
 module.exports.duplicateBreakSharedCard = async (req, res, next) => {
  try {
    let finalLesson;
    const sourceId = req.body.sourceId;
    const user = req.user;
    const selectedLanguage = req.body.selectedLanguage || 'en';
    if (sourceId) {
      const includes = await getIncludeParams(req, null, {
        accountId: user && user.accountId,
        includeContentStrings: false,
      });
      const sourceLesson = await Lesson.findByPk(sourceId, {
        include: includes,
        order: [lessonCardOrder],
        paranoid: false, // includes soft deleted (archived) lessons
      });
      if (!sourceLesson) {
        const id = sourceId;
        const err = new Error(req.i18n.t('lessons.lesson_load_Error', { id }));
        err.status = 404;
        throw err;
      }
      finalLesson = await duplicateNonSharedCard(sourceLesson, user, selectedLanguage);
    } else {
      const err = new Error(req.i18n.t('lessonCards.lessonId_missing_Error'));
      err.status = 400;
      throw err;
    }
    res.json(finalLesson);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * components:
 *   schemas:
 *     lessons:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         published:
 *           type: boolean
 *         title:
 *           type: string
 *         description:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *         lifecycle:
 *           type: string
 *         userId:
 *           type: integer
 *         resourceId:
 *           type: integer
 *         fileId:
 *           type: integer
 *         deletedAt:
 *           type: string
 *           format: date-time
 *         timeTracking:
 *           type: boolean
 *         requiredMinutes:
 *           type: string
 *         internalName:
 *           type: string
 *         lessonCards:
 *           type: array
 *           items:
 *             type: object  # Replace with $ref if you have a schema for lessonCards
 *         topics:
 *           type: array
 *           items:
 *             type: object  # Replace with $ref if you have a schema for topics
 *         contentStrings:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/contentStrings'
 *         viewable:
 *           type: boolean
 *         supportedLanguages:
 *           type: array
 *           items:
 *             type: string

 *     contentStrings:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         contentId:
 *           type: integer
 *         model:
 *           type: string
 *         language:
 *           type: string
 *         field:
 *           type: string
 *         value:
 *           type: string
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

module.exports.downloadVideoTranscript = async (req, res, next) => {
  const accountId = req.user.accountId;
  const lessonCardId = req.params.id;
  const programId = req.body.programId;
  const lessonId = req.body.lessonId;
  const language = req.user.language || 'en';
  let browser;

  try {
    let lessonContentStrings = [];
    const lessonCardData = await LessonCard.findOne({
      where: { id: lessonCardId },
      attributes: ['id', 'title', 'description', 'list4', 'updatedAt'],
    });

    if (language !== 'en') {
      lessonContentStrings = await ContentStrings.findAll({
        where: {
          contentId: lessonCardId,
          model: 'lessonCard',
          [Op.or]: [{ language }, { language: `${language}-act` }, { language: `${language}-mt` }],
        },
      });
    }

    if (lessonCardData) {
      const pdfData = {};
      pdfData.lessonCardId = lessonCardId;
      pdfData.programId = programId;
      pdfData.lessonId = lessonId;
      pdfData.description = lessonCardData.description || '';
      pdfData.list4 = lessonCardData.list4;
      if (language !== 'en' && lessonContentStrings && lessonContentStrings.length) {
        lessonCardData.contentStrings = lessonContentStrings;
        const shouldUseMT = useMachineTranslation(req, lessonCardData, language);

        const translationLang = shouldUseMT
          ? (lessonContentStrings.some(cs => cs.language === `${language}-act`) ? `${language}-act` : `${language}-mt`)
          : language;
        const getLatestValue = (field) => {
          const filtered = lessonContentStrings
            .filter(cs => cs.field === field && cs.language === translationLang)
            .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
          return filtered[0]?.value || null;
        };
        const descriptionObject = getLatestValue('description');
        const list4Object = getLatestValue('list4');
        pdfData.description = descriptionObject || lessonCardData.description;
        pdfData.list4 = list4Object || lessonCardData.list4;
      }

      browser = await getLaunchBrowser(browser);
      const { pdfFilename, clientFilename } = await getPDFVideoTranscript(pdfData, language, browser);
      res.setHeader('Access-Control-Expose-Headers', 'content-disposition');
      res.download(pdfFilename, clientFilename, async (err) => {
        if (err) {
          throw err;
        }
        fs.unlinkSync(pdfFilename);
      });
    } else {
      const err = new Error(`Failed to load description and video transcript for lessonCard Id: ${lessonCardId}`);
      err.status = 404;
      throw err;
    }
  } catch (err) {
    logger.error(`Exception caught in downloadVideoTranscript() for account: ${accountId}, err= ${err}`);
    next(err);
  } finally { 
    setTimeout(async () => {
      if (browser) {
        await browser.close();
        browser = null;
      }
    }, 1000);
  }
};

