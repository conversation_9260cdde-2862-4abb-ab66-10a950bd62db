/* eslint-disable max-len */
const _ = require('lodash');

const db = require('../db');
const config = require('../config/config');
const {
  restOperatorsToSequelize,
} = require('../services/utils/sequelizeUtils');
const { Op, col } = require('sequelize');
const { updateContentNameInContentLibrary, getListingCatalogInactiveItems, addPolicyConfigureData, getLessonProgramsByLessonId } = require('../services/utils/contentLibraryUtils');
const { getResourceBundlesIds, handleResourceValidation, handleAccountContent, updateContentTitle, getPolicyLessonCards, getContentDetails, getPolicyLessonCardsMap } = require('../services/utils/resourceUtils');

const Programs = db.programs;
const Files = db.files;
const Lessons = db.lessons;
const Resource = db.resources;
const ResourceBundles = db.resourceBundles;
const AccountLessons = db.accountLessons;
const AccountPrograms = db.accountPrograms;
const SocialCapitalIndicators = db.socialCapitalIndicators;
const Concepts = db.concepts;
const Categories = db.categories;
const ContentLibrary = db.contentLibrary;
const CatalogItem = db.catalogItems;
const Countries = db.countries;
const Sequelize = db.Sequelize;
const LessonLessonCard = db.lessonLessonCards;
const WorkdayContentData = db.workdayContentData;
const Listings = db.listings;

const customTraining = (accountId) => {
  const customTrainingWhere = {
    accountId,
    [Sequelize.Op.or]: [
      {
        listType: 'listing',
        [Sequelize.Op.or]: [
          Sequelize.literal("JSON_CONTAINS(JSON_EXTRACT(catalogItems, '$[*].programs[*].isCustomized'), '1', '$')"),
          Sequelize.literal("JSON_CONTAINS(JSON_EXTRACT(catalogItems, '$[*].lessons[*].isCustomized'), '1', '$')"),
        ],
      },
      {
        listType: 'catalog',
        isClientSpecific: 1,
      },
      {
        listType: 'catalog',
        instructionalType: 'lesson',
        isClientSpecific: 0,
        [Sequelize.Op.and]: [
          Sequelize.literal("JSON_CONTAINS(JSON_EXTRACT(lessons, '$[*].isCustomized'), '1', '$')"),
        ],
      },
    ],
  };
  return customTrainingWhere;
};

/**
 * converts a rest query from the client to sequelize params
 */
const restQueryToSequelize = (query, defaults, accountId = null) => {
  const whereClause = restOperatorsToSequelize(_.omit(query, ['$limit', '$skip', '$sort']));
  const newQuery = {
    ...defaults,
  };
  if (accountId) {
    newQuery.where = { ...whereClause, accountId };
    // content search
    if (whereClause.title) {
      let keywordSearch = whereClause.title;
      // escape special characters from search
      keywordSearch = keywordSearch.trim().replace(/'/g, "\\'");
      const keyword = `%${keywordSearch}%`;
      newQuery.where[Sequelize.Op.and] = [
        ...(newQuery.where[Sequelize.Op.and] || []),
        {
          [Sequelize.Op.or]: [
            {
              title: {
                [Sequelize.Op.like]: keyword,
              },
            },
            {
              description: {
                [Sequelize.Op.like]: keyword,
              },
            },
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(programs, '$[*].name'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(programs, '$[*].description'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(programs, '$[*].lessons[*].title'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(programs, '$[*].lessons[*].description'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(programs, '$[*].accountProgramName'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(lessons, '$[*].name'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(lessons, '$[*].description'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(lessons, '$[*].accountLessonTitle'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(concepts, '$[*].concept'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(catalogItems, '$[*].lessons[*].title'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(catalogItems, '$[*].lessons[*].description'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(catalogItems, '$[*].lessons[*].accountLessonTitle'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(catalogItems, '$[*].programs[*].name'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(catalogItems, '$[*].programs[*].description'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(catalogItems, '$[*].programs[*].lessons[*].title'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(catalogItems, '$[*].programs[*].lessons[*].description'), 'all', '${keyword}') IS NOT NULL`),
            Sequelize.literal(`JSON_SEARCH(JSON_EXTRACT(catalogItems, '$[*].programs[*].accountProgramName'), 'all', '${keyword}') IS NOT NULL`),
          ],
        },
      ];
      delete newQuery.where.title;
    }
    if (whereClause.pillar) {
      newQuery.where[Sequelize.Op.and] = [
        ...(newQuery.where[Sequelize.Op.and] || []),
        Sequelize.literal(`JSON_CONTAINS(pillars, JSON_OBJECT('id', ${whereClause.pillar}))`),
      ];
      delete newQuery.where.pillar;
    }
    if (whereClause.trainingType) {
      newQuery.where[Sequelize.Op.and] = [
        ...(newQuery.where[Sequelize.Op.and] || []),
        {
          [Sequelize.Op.or]: [
            {
              listType: 'catalog',
              instructionalType: {
                [Sequelize.Op.in]: whereClause.trainingType,
              },
            },
            {
              listType: 'listing',
              type: {
                [Sequelize.Op.in]: whereClause.trainingType,
              },
            },
          ],
        },
      ];
      delete newQuery.where.trainingType;
    }
    if (whereClause.location) {
      newQuery.where[Sequelize.Op.and] = [
        ...(newQuery.where[Sequelize.Op.and] || []),
        {
          countryName: {
            [Sequelize.Op.in]: newQuery.where.location,
          },
        },
      ];
      delete newQuery.where.location;
    }
    if (whereClause.audience) {
      newQuery.where[Sequelize.Op.and] = [
        ...(newQuery.where[Sequelize.Op.and] || []),
        {
          [Sequelize.Op.or]: [
            {
              audience: {
                [Sequelize.Op.in]: newQuery.where.audience,
              },
            },
            Sequelize.literal(`
              EXISTS (
              SELECT 1
              FROM JSON_TABLE(
                JSON_EXTRACT(catalogItems, '$[*]'),
                '$[*]' COLUMNS (
                  audience VARCHAR(255) PATH '$.audience',
                  programs JSON PATH '$.programs',
                  lessons JSON PATH '$.lessons'
                )
              ) cl
              WHERE
                cl.audience IN (${newQuery.where.audience.map(a => `'${a}'`).join(',')})
                AND (JSON_LENGTH(cl.programs) > 0 OR JSON_LENGTH(cl.lessons) > 0)
            )
          `),
          ],
        },
      ];
      delete newQuery.where.audience;
    }
    if (whereClause.indicators) {
      const indicatorConditions = whereClause.indicators.map(id => `JSON_CONTAINS(indicators, JSON_OBJECT('id', ${id}))`);
      newQuery.where[Sequelize.Op.and] = [
        ...(newQuery.where[Sequelize.Op.and] || []),
        Sequelize.literal(`(${indicatorConditions.join(' OR ')})`),
      ];
      delete newQuery.where.indicators;
    }
    if (whereClause.concepts) {
      const conceptConditions = whereClause.concepts.map(id => `JSON_CONTAINS(concepts, JSON_OBJECT('id', ${id}))`);
      newQuery.where[Sequelize.Op.and] = [
        ...(newQuery.where[Sequelize.Op.and] || []),
        Sequelize.literal(`(${conceptConditions.join(' OR ')})`),
      ];
      delete newQuery.where.concepts;
    }
    if (whereClause.isCustomized) {
      const customTrainingWhere = customTraining(accountId);
      newQuery.where[Sequelize.Op.and] = [
        ...(newQuery.where[Sequelize.Op.and] || []),
        customTrainingWhere,
      ];
    }
    delete newQuery.where.isCustomized;
  }
  if (query.$limit !== undefined) {
    const paginateMax = 700;
    const limit = parseInt(query.$limit);
    newQuery.limit = Math.min(paginateMax, limit);
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = query.$sort;
  }
  return newQuery;
};

// contentId search
const contentIdSearchQuery = async (accountId, contentId) => {
  const bundleIds = await getResourceBundlesIds(accountId);
  const resourceBundlesWhere = {
    bundleId: {
      [Op.in]: bundleIds,
    },
    status : 'active',
  };
  const includeResource = [
    {
      model: ResourceBundles,
      attributes: ['id'],
      where: resourceBundlesWhere,
    },
  ];
  const includeParams = [
    {
      model: Resource,
      as: 'resource',
      attributes: ['id'],
      include: includeResource,
      required: true,
    },
    {
      model: CatalogItem,
      attributes: ['id', 'isClientSpecific', 'instructionalType'],
      include: [
        {
          model: Listings,
          attributes: ['id', 'lifecycle'],
          required: false,
        },
      ],
      required: false,
    }
  ];
  const pFile = {
    model: Files,
    as: 'file',
    required: false,
    attributes: ['id', 'path'],
  };
  const lFile = {
    model: Files,
    attributes: ['id', 'path']
  };

  const [program, lesson, accProgram, accLesson] = await Promise.all([
    Programs.findOne({
      attributes: ['id', 'name', 'description', 'isCustomized'],
      where: {
        id: contentId,
        lifecycle: 'publish',
      },
      include: [...includeParams, pFile],
    }),
    Lessons.findOne({
      attributes: ['id', 'title', 'description', 'isStandalone', 'isCustomized'],
      where: {
        id: contentId,
        lifecycle: 'publish',
      },
      include: [...includeParams, lFile],
    }),
    AccountPrograms.findOne({
      where: { accountId, programId: contentId },
    }),
    AccountLessons.findOne({
      where: { accountId, lessonId: contentId },
    }),
  ]);
  let finalProgram = null;
  let finalLesson = null;
  // program filter logic wrt catalog/listing Items
  if (program) {
    const catalogItem = program.catalogItem;
    const listing = catalogItem?.listing || null;

    const hasPublishedListing = listing?.lifecycle === 'published';

    const allowDueToClientSpecific = catalogItem?.isClientSpecific === true;
    const allowDueToInstructional = catalogItem?.instructionalType === 'lesson';

    const isMappedToCatalog = !!catalogItem;

    const meetsCriteria =
      isMappedToCatalog &&
      (hasPublishedListing || allowDueToClientSpecific || allowDueToInstructional);

    if (meetsCriteria) {
      finalProgram = program;
      if (accProgram?.name) {
        finalProgram.name = accProgram.name;
      }
    }
  }
  // lesson filter logic wrt catalog/listing Items
  if (lesson) {
    const catalogItem = lesson.catalogItem;
    const listing = catalogItem?.listing || null;

    const hasPublishedListing = listing?.lifecycle === 'published';
    const allowDueToClientSpecific = catalogItem?.isClientSpecific === true;
    const allowDueToInstructional = catalogItem?.instructionalType === 'lesson';
    const isMappedToCatalog = !!catalogItem;

    let lessonAllowed =
      isMappedToCatalog &&
      (hasPublishedListing || allowDueToClientSpecific || allowDueToInstructional);

    // lesson is part of a program that is mapped to the account
    if (!lessonAllowed) {
      const lessonPrograms = await getLessonProgramsByLessonId(accountId, lesson.id);
      if (lessonPrograms?.programs && lessonPrograms.programs.length > 0) {
        lessonAllowed = true;
      }
    }

    // check for both lessonAllowed(if mapped) and not mapped to catalogItem
    if (lessonAllowed || !catalogItem) {
      finalLesson = lesson;
      if (accLesson?.title) {
        finalLesson.title = accLesson.title;
      }
    }
  }
  return { program: finalProgram, lesson: finalLesson };
};

// sorting mcl
const sortListingMcl = (sortType) => {
  const popular = [
    // Client-Specific Catalog Items – Sorted alphabetically by Title
    [
      Sequelize.literal(`
      CASE
        WHEN listType = 'catalog' AND isClientSpecific = true THEN 1
        WHEN listType = 'listing' THEN 2
        WHEN listType = 'catalog' AND (isClientSpecific = false OR isClientSpecific IS NULL) THEN 3
        ELSE 4
      END
    `),
      'ASC',
    ],

    // Listings – Sorted by their Rank value (ascending, lowest to highest)
    [
      Sequelize.literal(`
      CASE
        WHEN listType = 'catalog' AND isClientSpecific = true
          THEN TRIM(LOWER(title))
        ELSE NULL
      END
    `),
      'ASC',
    ],

    // Course-Lesson Catalog Items -  Can appear in any orde
    [
      Sequelize.literal(`
      CASE
        WHEN listType = 'listing'
          THEN listingRank
        ELSE NULL
      END
    `),
      'ASC',
    ],
  ];

  let sortOrder;
  switch (sortType) {
    case 'popular':
      sortOrder = popular;
      break;
    case 'alphabetical':
      sortOrder = [
        [Sequelize.fn('TRIM', Sequelize.col('title')), 'ASC']
      ];
      break;
    case 'newest':
      sortOrder = [['lastMappedAt', 'DESC']];
      break;
    default:
      sortOrder = [['id', 'DESC']];
  }
  return sortOrder;
};

const accountIncludeResource = async (accountId) => {
  const bundleAccountId = accountId;
  const bundleIds = await getResourceBundlesIds(bundleAccountId);
  const resourceBundlesWhere = {
    bundleId: {
      [Op.in]: bundleIds,
    },
    status : 'active',
  };
  const includeResource = [
    {
      model: ResourceBundles,
      attributes: ['id', 'createdAt'],
      where: resourceBundlesWhere,
    },
  ];
  const includeParams = [
    {
      model: Resource,
      as: 'resource',
      attributes: ['id'],
      include: includeResource,
      required: true,
    },
  ];
  return includeParams;
};

const contentDetailsPage = async (accountId, contentId, type) => {
  const includeParams = await accountIncludeResource(accountId);
  includeParams.push({
    model: CatalogItem,
    attributes: ['id', 'audience', 'lastMappedAt'],
    include: [
      {
        model: Listings,
        attributes: ['id', 'title'],
      },
      {
        model: Countries,
        attributes: ['id', 'countryName'],
      },
    ],
  });
  const pFile = {
    model: Files,
    as: 'file',
    required: false,
    attributes: ['id', 'path'],
  };
  const lFile = { model: Files, attributes: ['id', 'path'] };
  const whereCondition = {
    id: contentId,
    lifecycle: 'publish',
  };
  const contentData = type === 'program' ? Programs.findOne({
    attributes: ['id', 'name', 'description', 'isCustomized', 'edition', 'isTimed', 'minTimeInMinutes', 'duration', 'version', 'build', 'createdAt'],
    where: whereCondition,
    include: [...includeParams, pFile, { model: AccountPrograms, attributes: ['name'] }],
  }) : getLessonProgramsByLessonId(accountId, contentId);

  return contentData;
};

/**
 * @swagger
 * /content-library:
 *   post:
 *     summary: Fetch all the content-library data
 *     tags:
 *       - ContentLibrary
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/getContentLibrary'
 *           example:
 *             $sort: { updatedAt: -1 }
 *             $limit: 10
 *             $skip: 0
 *     responses:
 *       200:
 *         description: Successfully fetched Content Library Item
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 */
// eslint-disable-next-line consistent-return
module.exports.list = async (req, res, next) => {
  try {
    const defaults = {
      order: [['updatedAt', 'DESC']],
      limit: config.paginate.default,
      offset: 0,
    };
    const user = req.user;
    const accountId = user && user.accountId;
    const queryParams = restQueryToSequelize(req.body, defaults, accountId);
    if (queryParams.where?.contentId) {
      // search for contentId assigned to particular account
      const { contentId } = queryParams.where;
      const lesson = await contentIdSearchQuery(accountId, contentId);
      if (!lesson) {
        const err = new Error(req.i18n.t('lessons.lesson_load_Error', { contentId }));
        err.status = 404;
        throw err;
      }
      return res.json(lesson);
    }
    if (queryParams.order) {
      queryParams.order = sortListingMcl(queryParams.order);
    }
    const countQuery = {
      where: _.pick(queryParams, ['where']).where,
      distinct: true,
    };
    const customTrainingWhere = customTraining(accountId);

    const [data, count, isCustomTraining] = await Promise.all([
      ContentLibrary.findAll(queryParams),
      ContentLibrary.count(countQuery),
      ContentLibrary.count({ where: customTrainingWhere }),
    ]);
    res.json({
      total: count,
      limit: queryParams.limit,
      skip: queryParams.offset,
      isCustomTraining,
      data,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /filter/list:
 *   get:
 *     summary: Fetch all the filter Values
 *     tags:
 *       - FilterList
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: Authorization
 *         in: header
 *         required: true
 *         schema:
 *           type: string
 *         description: The authentication token of a user
 *       - name: type
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           enum: [concept, indicator]
 *         description: filter type to be passed (concept/indicator)
 *     responses:
 *       200:
 *         description: Successfully fetched all filter values
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: string
 */
module.exports.filterList = async (req, res, next) => {
  try {
    const filterType = req.query.type;
    delete req.query.type;
    const isConcepts = filterType === 'concept';
    const entity = isConcepts ? Concepts : SocialCapitalIndicators;
    const orderType = isConcepts ? 'concept' : 'id';
    // Set attributes based on entity type
    const attributes = isConcepts
      ? [
        [col('concepts.id'), 'id'],
        [col('concepts.concept'), 'concept'],
        [col('category.name'), 'category'],
      ]
      : ['id', 'name', 'competency'];
    const includeEntity = isConcepts
      ? [{
        model: Categories,
        attributes: [],
      }]
      : [];

    // Default ordering setup
    const defaults = { order: [[orderType, 'ASC']] };
    const queryParams = restQueryToSequelize(req.query, defaults);
    const finalQuery = {
      ...queryParams,
      include: includeEntity,
      order: [[...queryParams.order]],
      attributes,
      raw: true,
    };

    const data = await entity.findAll(finalQuery);
    res.json({ data });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /content-library/details/{type}/{contentId}:
 *   get:
 *     summary: Fetch content library details page
 *     tags: [ContentLibrary]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: Authorization
 *         in: header
 *         required: true
 *         schema:
 *           type: string
 *         description: The authentication token of a user
 *       - name: type
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: Content type to be passed (listing/catalog/program/lesson)
 *       - name: contentId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: Content ID to be passed
 *     responses:
 *       200:
 *         description: Successfully fetched content details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 */
module.exports.details = async (req, res, next) => {
  const { type, contentId: rawContentId } = req.params;
  const contentId = parseInt(rawContentId);
  try {
    const user = req.user;
    const accountId = user && user.accountId;
    let data = {};
    if (type === 'listing' || type === 'catalog') {
      const queryParams = {
        accountId,
        listType: type,
        mclContentId: contentId,
      };
      data = await ContentLibrary.findOne({ where: queryParams });
      if(data) {
        if (type === 'listing') {
          // add inactive catalogItems
          const inActiveCatalogItems = await getListingCatalogInactiveItems(accountId, contentId);
          data = data?.get({ plain: true });
          data.inActiveCatalogItems = inActiveCatalogItems;
        }
        // add policy data
        data = await addPolicyConfigureData(accountId, data);
      }
    } else if (type === 'program' || type === 'lesson') {
      data = await contentDetailsPage(accountId, contentId, type);
    }
    res.json(data);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /courselessons/{courseId}:
 *   get:
 *     summary: Fetch course lessons based on courseId
 *     tags: [ContentLibrary]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: Authorization
 *         in: header
 *         required: true
 *         schema:
 *           type: string
 *         description: The authentication token of a user
 *       - name: courseId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: The course ID to fetch lessons for
 *     responses:
 *       200:
 *         description: Successfully fetched course lessons
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 */
module.exports.courseLessons = async (req, res, next) => {
  const { courseId } = req.params;
  try {
    const user = req.user;
    const accountId = user && user.accountId;
    const includeParams = await accountIncludeResource(accountId);
    const data = await Programs.findOne({
      where: { id: courseId, lifecycle: 'publish' },
      attributes: ['id'],
      include: [
        ...includeParams,
        {
          model: Lessons,
          through: 'lessonPrograms',
          attributes: ['id', 'title', 'description', 'requiredMinutes'],
          required: false,
        },
      ],
    });
    res.json(data);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /content-library/{contentId}/{contentType}/contentConfiguration:
 *   get:
 *     summary: Get content Configuration
 *     tags: [ContentLibrary]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: contentId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *         description: contentId (lessonId or programId)
 *       - name: contentType
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *           example: lesson
 *         description: contentType (lesson or program)
 *     responses:
 *       200:
 *         description: Successfully fetched content configuration details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               # define the structure here or use $ref
 *       404:
 *         description: Not Found
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
// eslint-disable-next-line consistent-return
module.exports.contentConfiguration = async (req, res, next) => {
  try {
    const { user, params, i18n } = req;
    const accountId = user?.accountId;
    const { id, contentType } = params;
    const bundleIds = await getResourceBundlesIds(accountId);

    const validateResource = async (resourceId, type) => {
      await handleResourceValidation(id, resourceId, bundleIds, i18n, type);
    };

    if (contentType === 'lesson') {
      // Fetch lesson details
      const lesson = await getContentDetails(id, 'lesson');
      if (!lesson) {
        throw new Error(i18n.t('accountLessons.retrieval_Error', { id }));
      }

      await validateResource(lesson.resourceId, 'lesson');
      const relatedLessonCards = await LessonLessonCard.findAll({
        where: { lessonId: lesson.id },
      });
      const lessonCardIds = relatedLessonCards.map(card => card.lessonCardId);
      const policyLessonCard = await getPolicyLessonCards(lessonCardIds, accountId);
      const accountLesson = await AccountLessons.findOne({
        where: { accountId, lessonId: id },
      });

      if (accountLesson) {
        lesson.title = accountLesson.title;
      }

      return res.json({
        ...lesson.toJSON(),
        policies: policyLessonCard?.policyLessonCards || [],
      });
    }

    if (contentType === 'program') {
      // Fetch program details
      const program = await getContentDetails(id, 'program');
      if (!program) {
        throw new Error(i18n.t('programs.program_load_Error', { id }));
      }
      await validateResource(program.resourceId, 'program');
      const accountProgram = await AccountPrograms.findOne({
        where: { accountId, programId: id },
      });

      let plainProgram = typeof program.get === 'function'
        ? program.get({ plain: true })
        : program;

      // Handle lessons and policy cards
      if (plainProgram.lessons?.length) {
        const lessonIds = plainProgram.lessons.map(lesson => lesson.id);
        const policyMap = await getPolicyLessonCardsMap(lessonIds, accountId);

        plainProgram.lessons = plainProgram.lessons.map(lesson => ({
          ...lesson,
          policyLessonCards: policyMap[lesson.id] || [],
        }));
      }

      if (accountProgram) {
        plainProgram = {
          ...plainProgram,
          name: accountProgram.name,
          hasCertificate: accountProgram.hasCertificate,
          certificateText: accountProgram.certificateText,
          downloadInstructions: accountProgram.downloadInstructions,
          minTimeInMinutes: accountProgram.minTimeInMinutes,
          minCardTimeInSeconds: accountProgram.minCardTimeInSeconds,
          completedMessage: accountProgram.completedMessage,
          isAccountProgramModified: true
        };
      }

      return res.json({
        ...plainProgram,
        programId: id,
        accountId,
        hasLastCardMessage: plainProgram.isAccountProgramModified ?  accountProgram.hasLastCardMessage : !!plainProgram.completedMessage?.trim(),
      });
    }
    throw new Error(i18n.t('content.invalidType', { contentType }));
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /content-library/{contentId}/{contentType}/manageContentConfiguration:
 *   post:
 *     summary: Update account content configuration
 *     tags: [ContentLibrary]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: contentId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *         description: contentId (lessonId or programId)
 *       - name: contentType
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *           example: lesson
 *         description: contentType (lesson or program)
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/getAccountContentDetails'
 *     responses:
 *       200:
 *         description: Successfully updated content configuration details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       404:
 *         description: Not Found
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 *
 * components:
 *   schemas:
 *     getAccountContentDetails:
 *       description: Account Content Data
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Content Title
 */
module.exports.manageContentConfiguration = async (req, res, next) => {
  try {
    const { user, body, params, i18n } = req;
    const accountId = user?.accountId;
    const id = params?.id;
    const contentType = params?.contentType;
    let response = null;

    const contentTitle = body?.title?.trim();
    if (contentTitle) {
      response = await updateContentTitle(id, contentType, accountId, contentTitle, i18n);
      // background job to update the content titles in contentLibrary
      updateContentNameInContentLibrary(accountId, contentType, id, contentTitle);
    } else {
      // eslint-disable-next-line no-empty
      if (contentType === 'program') {
        const bundleIds = await getResourceBundlesIds(accountId);
        const program = await Programs.findOne({ where: { id } });
        if (!program) {
          throw new Error(i18n.t('programs.program_load_Error', { id }));
        }

        await handleResourceValidation(id, program.resourceId, bundleIds, i18n, 'program');
        response = await handleAccountContent(AccountPrograms, accountId, { programId: id }, body, program, 'program', 'title');
      }
    }

    res.json(response);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /workdayContent/{accountId}:
 *   get:
 *     summary: Fetch workday content data based on accountId
 *     tags:
 *       - WorkdayCCL
 *     parameters:
 *       - name: Authorization
 *         in: header
 *         required: true
 *         schema:
 *           type: string
 *         description: The authentication token of a user
 *       - name: accountId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: accountId to be passed
 *     responses:
 *       200:
 *         description: Successfully fetched workday content data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               # Define your response schema here or $ref to a component schema
 */
module.exports.workdayContentData = async (req, res, next) => {
  try {
    const { accountId } = req.params;

    const workdayContent = await WorkdayContentData.findAll({ where: { accountId } });
    res.json(workdayContent);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * components:
 *   schemas:
 *     getContentLibrary:
 *       type: object
 *       description: Content Library Data
 *       required:
 *         - $sort
 *         - $limit
 *       properties:
 *         $sort:
 *           type: object
 *           description: Sort based on id/updatedAt
 *           example:
 *             updatedAt: -1
 *         $limit:
 *           type: integer
 *           description: Limit the number of records
 *           example: 10
 *         $skip:
 *           type: integer
 *           description: Skip the number of records
 *           example: 0
 */
