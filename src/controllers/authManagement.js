const { get, omit } = require('lodash');

const userUtils = require('../services/utils/userUtils');
const config = require('../config/config');
const email = require('../services/email');
const db = require('../db');
const { ConflictError, BadRequestError, InternalServerError } = require('../services/utils/errors');
const { createSession } = require('../services/utils/sessions');
const { getLongToken, concatIDAndHash, sendVerification, validatePassword, tokenForUser, changeUserPassword } = require('../services/utils/authManagementUtils');
const { accountRecordFromHostname } = require('../services/utils/accountUtils');
const { findUserByEmailAndAccount, findUserByEmployeeIdAndAccount, sanitizeUserForClient } = require('../services/utils/userUtils');
const { isSSOIntegrationType } = require('../services/utils/accountUtils');

const Users = db.users;
const Accounts = db.accounts;
const AccountUsers = db.accountUsers;
const Roles = db.roles;

const { decrypt } = config.encryption;

const deconstructId = (req, token) => {
  if (token.indexOf('___') === -1) {
    const msg = req.i18n.t('authentication.invalid_token_format_Error');
    throw new Error(msg, { errors: { $className: 'badParams' } });
  }
  return token.slice(0, token.indexOf('___'));
};

const getUserData = (req, data, checks) => {
  if (Array.isArray(data) ? data.length === 0 : data.total === 0) {
    throw new Error(
      req.i18n.t('authentication.user_not_found_Error'),
      { errors: { $className: 'badParams' } },
    );
  }

  const users = Array.isArray(data) ? data : data.data || [data];
  const user = users[0];

  if (users.length !== 1) {
    throw new Error(
      req.i18n.t('authentication.too_many_users_Error'),
      { errors: { $className: 'badParams' } },
    );
  }

  if (checks.includes('isNotVerified') && user.isVerified) {
    throw new Error(
      req.i18n.t('authentication.already_verified_Error'),
      { errors: { $className: 'isNotVerified' } },
    );
  }

  if (checks.includes('isNotVerifiedOrHasVerifyChanges') &&
    user.isVerified && !Object.keys(user.verifyChanges || {}).length
  ) {
    throw new Error(
      req.i18n.t('authentication.already_verified_changes_Error'),
      { errors: { $className: 'nothingToVerify' } },
    );
  }

  if (checks.includes('isVerified') && !user.isVerified) {
    throw new Error(
      req.i18n.t('authentication.not_verified_Error'),
      { errors: { $className: 'isVerified' } },
    );
  }

  if (checks.includes('verifyNotExpired') && user.verifyExpires < Date.now()) {
    throw new Error(
      req.i18n.t('authentication.verify_token_expired_Error'),
      { errors: { $className: 'verifyExpired' } },
    );
  }

  if (checks.includes('resetNotExpired') && user.resetExpires < Date.now()) {
    throw new Error(
      req.i18n.t('authentication.reset_token_expired_Error'),
      { errors: { $className: 'resetExpired' } },
    );
  }

  return user;
};

const ensureValuesAreStrings = (req, ...rest) => {
  if (!rest.every(str => typeof str === 'string')) {
    const err = new Error(req.i18n.t('authentication.expected_string_Error'));
    err.status = 400;
    throw err;
  }
};

const ensureObjPropsValid = (req, obj, props, allowNone) => {
  const keys = Object.keys(obj);
  const valid = keys.every(key => props.includes(key) && typeof obj[key] === 'string');
  if (!valid || (keys.length === 0 && !allowNone)) {
    throw new Error(req.i18n.t('authentication.user_info_invalid_Error'), { errors: { $className: 'badParams' } });
  }
};

const eraseVerifyProps = async (user, isVerified, verifyChanges) => {
  const patchToUser = Object.assign({}, verifyChanges || {}, {
    isVerified,
    // verifyToken: null,
    // verifyShortToken: null,
    verifyExpires: null,
    verifyChanges: {},
    canAskExpert: isVerified,
  });

  await user.update(patchToUser);
  return user;
};

const verifySignup = async (req, options, query, tokens) => {
  const data = await Users.findOne({ where: { verifyToken: query } });
  const unverifiedUser = await getUserData(req, data, ['isNotVerifiedOrHasVerifyChanges', 'verifyNotExpired']);

  if (!Object.keys(tokens).every(key => tokens[key] === unverifiedUser[key])) {
    await eraseVerifyProps(unverifiedUser, unverifiedUser.isVerified);
    throw new Error(
      req.i18n.t('authentication.invalid_token_Error'),
      { errors: { $className: 'badParam' } },
    );
  }

  const verifiedUser = await eraseVerifyProps(
    unverifiedUser,
    unverifiedUser.verifyExpires > Date.now(),
    unverifiedUser.verifyChanges || {},
  );
  // TODO: send verification successful email
  const sanitizedUser = sanitizeUserForClient(verifiedUser);
  return sanitizedUser;
};

const verifySignupWithLongToken = async (req, options) => {
  const verifyToken = req.body.value;
  // all this adapted from here: https://github.com/feathers-plus/feathers-authentication-management/blob/master/src/verifySignup.js
  ensureValuesAreStrings(req, verifyToken);

  const user = verifySignup(req, options, verifyToken, { verifyToken });
  return user;
};

const sendResetPwd = async (req, accountId, options) => {
  const identifyUser = req.body.value;
  ensureObjPropsValid(req, identifyUser, options.identifyUserProps);

  const user = await findUserByEmailAndAccount(req, identifyUser.email.toLowerCase(), accountId);

  if (user) {
    const longToken = await getLongToken(config.longTokenLen);
    const resetToken = concatIDAndHash(user.id, longToken);
    const tokenLink = config.authManagement.reset.replace('{token}', resetToken)
      .replace('{subdomain}', user.accounts[0].subdomain);

    await email.sendResetPwd(tokenLink, identifyUser.email, user.firstName, user.lastName);

    const encryptedLongToken = await config.encryption.setPassword(resetToken);

    await user.update({
      resetExpires: Date.now() + config.resetPasswordTimeout,
      resetToken: encryptedLongToken,
      resetDirectLogin: true,
    });
  }

  return {};
};

const resetPassword = async (req, options, query, tokens, password, emailOrId = null, setup) => {
  const checkProps = ['resetNotExpired'];
  let user;

  if (tokens.resetToken) {
    const userId = deconstructId(req, tokens.resetToken);
    user = await Users.findOne({
      where: {
        id: userId,
      },
      include: [{
        model: Accounts,
      }],
    }).then(data => getUserData(req, data, checkProps));
  } else {
    const err = new Error(req.i18n.t('authentication.reset_token_not_found_Error'));
    throw new Error(err);
  }
  let userEmail = '';
  if (user && user.email && user.email !== 'null') {
    userEmail = user.email;
  }
  // If there's an id or email, it must match the user's or bail.
  if (emailOrId && (emailOrId.toLowerCase() !== userEmail.toLowerCase() && emailOrId !== user.employeeId)) {
    throw new ConflictError(req.i18n.t('authentication.bad_user_email_or_id_Error'));
  }

  const promises = [];

  await validatePassword(req, user, user.accounts[0], password);
  try {
    Object.keys(tokens).forEach((key) => {
      promises.push(config.encryption.comparePassword(tokens[key], user[key], () => {
        return new BadRequestError(req.i18n.t('authentication.reset_token_invalid_Error'));
      }));
    });
    await Promise.all(promises);
  } catch (error) {
    if (error.name === 'BadRequestError' && error.message === req.i18n.t('authentication.reset_token_invalid_Error')) {
      throw new BadRequestError(
          req.i18n.t(
            setup 
              ? 'authentication.setup_token_no_longer_valid_Error'
              : 'authentication.reset_token_no_longer_valid_Error'
          )
        );
    } else {
      await user.update({
        resetToken: null,
        resetExpires: null,
        resetDirectLogin: false,
      });
      throw new BadRequestError(req.i18n.t('authentication.reset_token_invalid_Error'));
    }
  }

  const updatedUser = await user.update({
    password,
    resetToken: null,
    resetExpires: null,
    resetPasswordRequired: false,
    failedLoginAttempts: 0,
    failedLoginAt: null,
    accountLocked: 0,
  });

  // send pwd reset success email?

  const sanitizedUser = sanitizeUserForClient(updatedUser);
  return sanitizedUser;
};

const resetPwdWithLongToken = async (req, options) => {
  const resetToken = req.body.value.token;
  const password = req.body.value.password;
  const emailOrId = req.body.value.emailOrId;
  const setup = req.body.value.setup;
  ensureValuesAreStrings(req, resetToken, password);

  const user = await resetPassword(req, options, { resetToken }, { resetToken }, password, emailOrId, setup);
  return user;
};

const passwordChange = async (req, accountId) => {
  const userEmail = req.body.value.user.email && req.body.value.user.email.toLowerCase();
  const employeeId = req.body.value.user.employeeId;
  const oldPassword = req.body.value.oldPassword;
  const password = req.body.value.password;

  let user;
  if (userEmail) {
    user = await findUserByEmailAndAccount(req, userEmail, accountId);
  } else if (employeeId) {
    user = await findUserByEmployeeIdAndAccount({ accountId, employeeId });
  }
  if (!user) {
    return new BadRequestError(req.i18n.t('authentication.user_not_found_Error'));
  }

  await config.encryption.comparePassword(oldPassword, user.password, () => {
    return new BadRequestError(req.i18n.t('authentication.password_incorrect_Error'));
  });

  await validatePassword(req, user, user.accounts[0], password);

  const updatedUser = await user.update({ password });
  const sanitizedUser = sanitizeUserForClient(updatedUser);
  return sanitizedUser;
};

const verifyAndResetPwdWithLongToken = async (req, options) => {
  const resetToken = req.body.value.token;
  const password = req.body.value.password;
  ensureValuesAreStrings(req, resetToken, password);
  /*
  * The assumption here is that all the checks on token validity have been perfomed by the resetPassword function
  * so if that's okay then it's okay to validate the user without checking all over again.
  * We'll pass in a blank verifyChanges object since the user hasn't even been able to log in to make any
  * changes that need validation.
  */

  const user = await resetPassword(req, options, { resetToken }, { resetToken }, password);

  // resetPassword will return a flattened user object so user.update won't work
  const validatedUser = await db.users.findByPk(user.id);
  await eraseVerifyProps(validatedUser, true, {});

  return user;
};

const sendVerify = async (req, accountId, options) => {
  const identifyUser = req.body.value;
  ensureObjPropsValid(req, identifyUser, options.identifyUserProps);

  const user = await findUserByEmailAndAccount(req, identifyUser.email.toLowerCase(), accountId);

  if (user === null) {
    throw new BadRequestError(req.i18n.t('authentication.user_not_found_Error'));
  }

  sendVerification(user, false);

  return user;
};

const sendResetAndVerify = async (req, accountId, options) => {
  const identifyUser = req.body.value;
  ensureObjPropsValid(req, identifyUser, options.identifyUserProps);

  const user = await findUserByEmailAndAccount(req, identifyUser.email.toLowerCase(), accountId);

  if (user === null) {
    throw new BadRequestError(req.i18n.t('authentication.user_not_found_Error'));
  }

  sendVerification(user, true);

  const sanitizedUser = sanitizeUserForClient(user);
  return sanitizedUser;
};

const checkResetTokenExpired = async (req) => {
  const resetToken = req.body.value.token;
  const userId = deconstructId(req, resetToken);
  const reqAction = req.body.value.action;
  let needCheckResetTokenExpired = true;

  const user = await Users.findOne({
    where: {
      id: userId,
    },
    include: [{
      model: Accounts,
    }],
  });


  if (reqAction && reqAction === 'verifyAndReset' && user && user.isVerified) {
    needCheckResetTokenExpired = false;
  }

  if (needCheckResetTokenExpired && (!user || !user.resetToken)) {
    const err = new Error(req.i18n.t('authentication.reset_token_not_found_Error'));
    throw new Error(err);
  }

  if (needCheckResetTokenExpired && user.resetExpires < Date.now()) {
    const err = new Error(req.i18n.t('authentication.reset_token_expired_Error'));
    throw new Error(err);
  }
  const sanitizedUser = sanitizeUserForClient(user);
  return sanitizedUser;
};

const unsubscribeUser = async (req, accountId) => {
  const unsubscribeToken = req.body.value;
  const user = await Users.findOne({
    where: {
      email: unsubscribeToken,
    },
    include: [{
      model: Accounts,
      where: {
        id: accountId,
      },
    }],
  });

  if (user === null) {
    throw new BadRequestError(req.i18n.t('authentication.user_not_found_Error'));
  }
  await user.update({ unsubscribe: true });

  const sanitizedUser = sanitizeUserForClient(user);
  return sanitizedUser;
};

/*
* This function is called only for users who have been sent a notification URL that
* guides them through the password reset workflow path that assumes they have not
* previously verified their account or created a password using the account invitation email.
* This function verifies that their account is still in the same state once they visit the
* notifyReset link.
*/
const verifyUserNotifyResetStillValid = async (req, accountId, options) => {
  const identifyUser = req.body.value;
  ensureObjPropsValid(req, identifyUser, options.identifyUserProps);

  const user = await findUserByEmailAndAccount(req, identifyUser.email.toLowerCase(), accountId);
  if (user === null) {
    throw new BadRequestError(req.i18n.t('authentication.user_not_found_Error'));
  }

  let hasSSOIntegration = false;
  let emailAuthField = true;
  if (user.accounts[0] && user.accounts[0].integrations) {
    const ssoIntegrations = user.accounts[0].integrations.filter(intg =>
      isSSOIntegrationType(intg.integrationType));
    if (ssoIntegrations[0]) {
      hasSSOIntegration = true;
    }
  }
  if (user.accounts[0] && user.accounts[0].authField !== 'email') {
    emailAuthField = false;
  }

  if (!hasSSOIntegration && !user.isVerified && !user.lastActivityAt && emailAuthField) {
    return true;
  }
  return false;
};

async function resetEmployeeIdPwd({ accountId, employeeId }) {
  if (!employeeId) {
    throw new Error('Employee ID required');
  }

  const user = await findUserByEmployeeIdAndAccount({ accountId, employeeId });
  const accountUser = await AccountUsers.findOne({
    where: {
      userId: user.id,
      accountId,
    },
    include: [{
      model: Roles,
      as: 'role',
    }],
  });

  const defaultPassword = get(user, 'accounts.0.defaultPassword');
  const hasUserRole = get(accountUser, 'role.name') === 'user';

  if (!hasUserRole) {
    // eslint-disable-next-line max-len
    throw new ConflictError('Account admin must provide email address to reset password');
  }
  if (defaultPassword && hasUserRole) {
    const userUpdate = {};
    userUpdate.password = decrypt(defaultPassword);
    userUpdate.resetPasswordRequired = true;
    if (user.accountLocked) {
      userUpdate.failedLoginAttempts = 0;
      userUpdate.accountLocked = 0;
      userUpdate.failedLoginAt = null;
    }
    const updatedUser = await user.update(userUpdate);
    return omit(sanitizeUserForClient(updatedUser), ['accounts', 'roles']);
  }

  throw new InternalServerError('Could not reset user password to account default');
}

module.exports.root = async (req, res, next) => {
  try {
    let signInResponse = {};
    let user;
    const accountRecord = await accountRecordFromHostname(req);
    if (accountRecord && accountRecord.status === 'active') {
      const { id: accountId } = accountRecord;
      switch (req.body.action) {
        case 'passwordChange':
          user = await passwordChange(req, accountId);
          break;
        case 'sendResetPwd':
          user = await sendResetPwd(req, accountId, { identifyUserProps: 'email' });
          break;
        case 'verifyNotifyReset': {
          const valid = await verifyUserNotifyResetStillValid(req, accountId, { identifyUserProps: 'email' });
          if (valid) {
            user = await sendResetPwd(req, accountId, { identifyUserProps: 'email' });
          }
          break;
        }
        case 'resetPwdLong': {
          user = await resetPwdWithLongToken(req, {});
          if (user.resetDirectLogin) {
            await createSession(req);
            // We need the accountId for the cookie.
            user.accountId = accountRecord.id;
            signInResponse = { accessToken: tokenForUser(user) };
          }
          break;
        }
        case 'verifySignupLong':
          user = await verifySignupWithLongToken(req, {});
          break;
        case 'resendVerifySignup':
          user = await sendVerify(req, accountId, { identifyUserProps: 'email' });
          break;
        case 'verifyAndResetPwdLong':
          user = await verifyAndResetPwdWithLongToken(req, {});
          break;
        case 'resendVerifyAndResetEmail':
          user = await sendResetAndVerify(req, accountId, { identifyUserProps: 'email' });
          break;
        case 'unsubscribe':
          user = await unsubscribeUser(req, accountId);
          break;
        case 'checkResetTokenExpired': {
          user = await checkResetTokenExpired(req);
          break;
        }
        case 'resetEmployeeIdPwd': {
          user = await resetEmployeeIdPwd({ accountId, employeeId: req.body.value.employeeId });
          break;
        }
        default:
          break;
      }
    }
    if (!user) {
      const action = req.body.action;
      throw new BadRequestError(req.i18n.t('authentication.action_failed_Error', { action }));
    }
    let accDempgraphicFields = null;
    if (accountRecord.enableAccountDemographicsFeature) {
      const demographicFields = await accountRecord.getAccountDemographicField({ raw: true });
      accDempgraphicFields = demographicFields;
    }
    res.send({ user, ...signInResponse, ...{ demographicFields: accDempgraphicFields } });
  } catch (error) {
    next(error);
  }
};
