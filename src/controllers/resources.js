const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const logger = require('../logger');
const path = require('path');
const axios = require('axios');

const { getAllowedPermissions } = require('../services/acl/acl');
const { includeUserResources, includeUserResource, decryptExpert, applySearchSort,
  buildDigestableQueries, buildResourceQuery, getResourceBundlesIds,
  convertToResourceObject } = require('../services/utils/resourceUtils');
const { flattenLesson, configureLessonForAccount } = require('../services/utils/lessonUtils');
const { localizeModelObject } = require('../services/utils/localizationUtils');
const { configureProgramForAccount } = require('../services/utils/programUtils');

const Op = db.Sequelize.Op;
const Resource = db.resources;
const QuestionAnswer = db.questionAnswers;
const Lesson = db.lessons;
const MediaAsset = db.mediaAssets;
const ResourceAsset = db.resourceAssets;
const Program = db.programs;
const ResourceBundles = db.resourceBundles;
const AccountLesson = db.accountLessons;
const AccountProgram = db.accountPrograms;
const Account = db.accounts;

/**
 *   GET /resources (find)
 */

/**
 * @openapi
 * /resources?$limit={limit}&$skip={skip}&filter={filter}&type={type}:
 *   get:
 *     summary: Resource List
 *     tags:
 *       - Resource
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 15
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *       - name: filter
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           example: latest
 *       - name: type
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           example: lessons,programs
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records.
 *                 limit:
 *                   type: integer
 *                   description: Limit records per page.
 *                 data:
 *                   type: array
 *                   description: Array of report data objects.
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       isBrowsable:
 *                         type: boolean
 *                       helpfulCount:
 *                         type: integer
 *                       isPublic:
 *                         type: boolean
 *                       searchText:
 *                         type: string
 *                       digestable:
 *                         type: string
 *                       resourceObject:
 *                         type: object
 *                         allOf:
 *                           - $ref: '#/components/schemas/mediaAssets'
 *                         properties:
 *                           resource:
 *                             type: object
 *                             allOf:
 *                               - $ref: '#/components/schemas/resources'
 *                             properties:
 *                               tags:
 *                                 type: array
 *                                 items:
 *                                   $ref: '#/components/schemas/tags'
 *                               topics:
 *                                 type: array
 *                                 items:
 *                                   $ref: '#/components/schemas/topics'
 *                           contentStrings:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/contentStrings'
 *                       userResource:
 *                         $ref: '#/components/schemas/userResources'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 */

/**
 * @openapi
* /resources?$limit={limit}&$skip={skip}&filter={filter}&type={type}&downloadLanguage=en&questionAnswers[userId]={userId}&questionAnswers[status][$in][0]={status0}&questionAnswers[status][$in][1]={status1}&questionAnswers[status][$in][2]={status2}:
 *   get:
 *     summary: Resource Question Answers List
 *     tags:
 *       - Resource
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 15
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *       - name: filter
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           example: answersForYou
 *       - name: type
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           example: questionAnswers
 *       - name: downloadLanguage
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           example: en
 *       - name: questionAnswers[userId]
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 17
 *       - name: questionAnswers[status][$in][0]
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           example: pending
 *       - name: questionAnswers[status][$in][1]
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           example: assigned
 *       - name: questionAnswers[status][$in][2]
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           example: answered
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records.
 *                 limit:
 *                   type: integer
 *                   description: Limit records per page.
 *                 skip:
 *                   type: integer
 *                   description: Skip records.
 *                 data:
 *                   type: array
 *                   description: Array of report data objects.
 *                   items:
 *                     type: object
 *                     properties:
 *                       resourceBundles:
 *                         $ref: '#/components/schemas/resourceBundles'
 *                       resourceObject:
 *                         type: object
 *                         allOf:
 *                           - $ref: '#/components/schemas/questionAnswers'
 *                         properties:
 *                           resource:
 *                             type: object
 *                             allOf:
 *                               - $ref: '#/components/schemas/resources'
 *                             properties:
 *                               tags:
 *                                 type: array
 *                                 items:
 *                                   $ref: '#/components/schemas/tags'
 *                               topics:
 *                                 type: array
 *                                 items:
 *                                   $ref: '#/components/schemas/topics'
 *                           user:
 *                             $ref: '#/components/schemas/users'
 *                           expert:
 *                             type: object
 *                             allOf:
 *                               - $ref: '#/components/schemas/expert'
 *                             properties:
 *                               accounts:
 *                                 type: array
 *                                 items:
 *                                   type: object
 *                                   allOf:
 *                                     - $ref: '#/components/schemas/accounts'
 *                                   properties:
 *                                     accountUsers:
 *                                       $ref: '#/components/schemas/accountUsers'
 *                           contentStrings:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/contentStrings'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 */

module.exports.list = async (req, res, next) => {
  // For resource queries, we need to set up a general query on the resources table,
  // and then do the specific digestable queries.  Some of the parameters passed in are for the initial
  // query (sort, some filters, type) and some are for the digestable queries.
  const modelFromDigestable = {
    questionAnswers: QuestionAnswer,
    resourceAssets: ResourceAsset,
    lessons: Lesson,
    programs: Program,
  };

  try {
    const allowedPermissions = await getAllowedPermissions(
      req,
      req.user ? req.user.id : null,
      'admin',
      req.tokenPayload,
    );

    // You have to be an admin to view account specific programs
    if (req.query.accountId && !allowedPermissions.admin.includes('read')) {
      const err = new Error(req.i18n.t('resources.resource_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    const userQuery = req.query ? _.omit(req.query, ['accountId']) : {};
    const digestables = ['questionAnswers', 'resourceAssets', 'lessons', 'programs'];
    const paramInfo = await buildResourceQuery(
      req,
      req.user,
      userQuery,
      digestables,
      req.query.accountId,
    );
    let resources = [];
    if (paramInfo) {
      resources = await Resource.findAll(paramInfo.resourceQuery);
    }

    // We now have all the resources to deal with, so start with the digestables
    const digestableQueries = await buildDigestableQueries(req, req.user, userQuery, digestables, resources);
    let promises;
    if (digestableQueries) {
      promises = paramInfo.digestablesToQuery.map((digestable) => {
        if (digestableQueries && digestableQueries[digestable]) {
          return modelFromDigestable[digestable].findAll(digestableQueries[digestable]);
        }
        return Promise.resolve([]);
      });
    } else {
      promises = [Promise.resolve([])];
    }

    Promise.all(promises)
      .then(async (sequelizeResults) => {
        const results = sequelizeResults.map((result) => {
          return result.map(d => d.get({ plain: true }));
        });
        let data = resources.map((record) => {
          const r = record.get({ plain: true });
          const resultsIndex = paramInfo.digestablesToQuery.indexOf(r.digestable);
          let resourceObject = results[resultsIndex].filter(dig => r.id === dig.resourceId)[0];
          if (r.digestable === 'lessons') {
            resourceObject = flattenLesson(req, resourceObject, req.i18n.language, true);
          } else {
            resourceObject = localizeModelObject(req, resourceObject, req.i18n.language, true);
          }
          return Object.assign(r, {
            resourceObject,
          });
        }).filter(r => !!r.resourceObject);
        data = applySearchSort(userQuery, paramInfo ? paramInfo.sortedResourceIds : [], data);

        // Swap in the account configuration for customer accounts
        if (req.user && req.user.accountId) {
          const account = await Account.findByPk(req.user.accountId);
          if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
            for (const nextEntry of data) {
              if (nextEntry.digestable === 'lessons') {
                const accountLesson = await AccountLesson.findOne({ where: { accountId: req.user.accountId,
                  lessonId: nextEntry.resourceObject.id } });
                if (accountLesson) {
                  nextEntry.resourceObject = configureLessonForAccount(nextEntry.resourceObject, accountLesson);
                }
              } else if (nextEntry.digestable === 'programs') {
                const accountProgram = await AccountProgram.findOne({ where: { accountId: req.user.accountId,
                  programId: nextEntry.resourceObject.id } });
                if (accountProgram) {
                  nextEntry.resourceObject = configureProgramForAccount(nextEntry.resourceObject, accountProgram);
                }
              }
            }
          }
        }
        // Paginate and format the result
        const count = data.length;
        const skip = parseInt(userQuery.$skip || 0);
        if (skip > 0) {
          data = data.slice(skip);
        }
        // Changed limit calculation to allow a negative limit to override the config max.
        // This will be removed when we add pagination to the content library.
        let limit = userQuery.$limit !== undefined
          ? Math.min(config.paginate.max, parseInt(userQuery.$limit))
          : config.paginate.default;
        limit = Math.abs(limit);
        data = data.slice(0, limit);
        data = decryptExpert(data);

        // include the event information for each resource (helpful, saved, shared, viewed)
        data = await includeUserResources(req, data);
        if (req.query.filter === 'saved') {
          data = data.filter(resource => resource.userResource)
            .filter(resource => resource.userResource.saved === true);
        }
        res.json({ total: count, limit, skip, data });
      });
  } catch (err) {
    next(err);
  }
};

/**
*   GET /resource/id (get)
*/
module.exports.read = async (req, res, next) => {
  const initialResource = req.resource;
  const modelFromDigestable = {
    questionAnswers: QuestionAnswer,
    resourceAssets: ResourceAsset,
    lessons: Lesson,
  };

  try {
    const digestable = initialResource.digestable;

    const digestableQueries = await buildDigestableQueries(req, req.user, {}, [digestable], initialResource);
    const digestableQuery = digestableQueries[digestable];

    // Move the order parameter to the outer resource query and add in the digestable model
    const order = [
      digestableQuery.order[0],
      [{ model: modelFromDigestable[digestable] }, ...digestableQuery.order[1]],
      [{ model: modelFromDigestable[digestable] }, ...digestableQuery.order[2]],
    ];
    if (digestableQuery.order[3]) {
      order.push([{ model: modelFromDigestable[digestable] }, ...digestableQuery.order[3]]);
    }
    delete digestableQuery.order;

    const query = {
      where: { id: initialResource.id },
      include: [digestableQuery],
      order,
    };
    logger.debug('query for resource on get:', query);

    const sequelizeResource = await Resource.findOne(query);
    if (!sequelizeResource) {
      const id = initialResource.id;
      const err = new Error(req.i18n.t('resource.resource_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    const resource = convertToResourceObject(req, sequelizeResource);
    const decryptedData = decryptExpert(resource);

    // include the event information for each resource (helpful, saved, shared, viewed)
    const finalResource = await includeUserResource(req, decryptedData);

    res.json(finalResource);
  } catch (err) {
    next(err);
  }
};

module.exports.downloadResourcefile = async (req, res, next) => {
  try {
    const imageUrl = req.body.imageUrl || null;
    if (!imageUrl) {
      const err = new Error('Invalid resource file or URL');
      err.status = 401;
      throw err;
    }
    const imageName = path.basename(imageUrl);
    const imgRes = await axios.head(imageUrl);
    const contentType = imgRes.headers['content-type'];
    const response = await axios({
      method: 'GET',
      url: imageUrl,
      responseType: 'stream',
    });
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename=${imageName}`);
    res.setHeader('Access-Control-Expose-Headers', 'content-disposition');

    response.data.pipe(res);
  } catch (err) {
    next(err);
  }
};

module.exports.resourceById = async (req, res, next, id) => {
  try {
    let resource;
    const allowedPermissions = await getAllowedPermissions(
      req,
      req.user ? req.user.id : null, 'resources', req.tokenPayload,
    );

    if (allowedPermissions.resources.includes('read')) {
      resource = await Resource.findByPk(id);
    } else if (allowedPermissions.resources.includes('readAccount')) {
      const bundleIds = await getResourceBundlesIds(req.user ? req.user.accountId : null);
      resource = await Resource.findByPk(id, {
        include: [{
          model: ResourceBundles,
          where: {
            bundleId: {
              [Op.in]: bundleIds,
            },
          },
          required: true,
        }],
      });
    }

    if (!resource) {
      const err = new Error(req.i18n.t('resources.resource_load_Error', { id }));
      err.status = 404;
      throw err;
    }

    req.resource = resource;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /programs/{programId}?downloadLanguage=en&allowMT=false:
 *  get:
 *   summary: Program Report By ProgramId
 *   tags: [Reports]
 *   security:
 *     - JWT: []
 *   parameters:
 *    - name: programId
 *      in: path
 *      required: true
 *      schema:
 *        type: integer
 *    - name: downloadLanguage
 *      in: query
 *      required: false
 *      schema:
 *        type: string
 *        default: en
 *    - name: allowMT
 *      in: query
 *      required: false
 *      schema:
 *        type: boolean
 *        default: false
 *   responses:
 *    200:
 *      description: Successful operation.
 *      content:
 *        application/json:
 *          schema:
 *            allOf:
 *              - $ref: '#/components/schemas/programs'
 *              - type: object
 *                properties:
 *                  resource:
 *                    allOf:
 *                      - $ref: '#/components/schemas/resources'
 *                      - type: object
 *                        properties:
 *                          tags:
 *                            type: array
 *                            items:
 *                              $ref: '#/components/schemas/tags'
 *                          topics:
 *                            type: array
 *                            items:
 *                              $ref: '#/components/schemas/resourceTopics'
 *                          resourceBundles:
 *                            type: array
 *                            items:
 *                              $ref: '#/components/schemas/resourceBundles'
 *                  supportedLanguages:
 *                    type: array
 *                    items:
 *                      $ref: '#/components/schemas/supportedLanguages'
 *                  contentStrings:
 *                    type: array
 *                    items:
 *                      $ref: '#/components/schemas/contentStrings'
 *                  lessons:
 *                    type: array
 *                    items:
 *                      allOf:
 *                        - $ref: '#/components/schemas/lessons'
 *                        - type: object
 *                          properties:
 *                            lessonPrograms:
 *                              $ref: '#/components/schemas/lessonPrograms'
 *                            contentStrings:
 *                              type: array
 *                              items:
 *                                $ref: '#/components/schemas/contentStrings'
 *    404:
 *      description: Not Found
 *    401:
 *      description: Unauthorized
 *    400:
 *      description: Bad Request
 *    5XX:
 *      description: Unexpected error
 *
 * components:
 *   schemas:
 *     programs:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         # ... add other properties as per your programs schema
 * 
 *     resources:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         digestable:
 *           type: string
 *         helpfulCount:
 *           type: integer
 *         searchText:
 *           type: string
 *         isPublic:
 *           type: boolean
 *         isBrowsable:
 *           type: boolean
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 *         tags:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/tags'
 *         topics:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/resourceTopics'
 *         resourceBundles:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/resourceBundles'
 * 
 *     resourceTopics:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         resourceId:
 *           type: integer
 *         topicId:
 *           type: integer
 *         order:
 *           type: integer
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 *
 *     tags:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 * 
 *     supportedLanguages:
 *       type: object
 *       properties:
 *         code:
 *           type: string
 *         name:
 *           type: string
 * 
 *     contentStrings:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         key:
 *           type: string
 *         value:
 *           type: string
 * 
 *     lessons:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         lessonPrograms:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/lessonPrograms'
 *         contentStrings:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/contentStrings'
 * 
 *     lessonPrograms:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         programId:
 *           type: integer
 * 
 *     resourceBundles:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         languageCode:
 *           type: string
 *         content:
 *           type: string
 * 
 *     expert:
 *       type: object
 *       properties:
 *         firstName:
 *           type: string
 *         lastName:
 *           type: string
 *         title:
 *           type: string
 *         description:
 *           type: string
 *         bannerId:
 *           type: integer
 *         avatarId:
 *           type: integer
 *         accounts:
 *           type: array
 */
