/* eslint-disable no-param-reassign */
const _ = require('lodash');
const urlparse = require('url-parse');
const pathlib = require('path');
const db = require('../db');
const logger = require('../logger');
const config = require('../config/config');
const { restSortToSequelize, searchTextFromSearchInfo,
  updateElasticSearch, SEARCH_TEXT_COL_LIMIT } = require('../services/utils/resourceUtils');
const { deriveSearch, launchedByUsersCount,
  expandProtectedTraits, updateCardProtectedTraits,
  deleteProtectedTraits, securePolicyCardLinks, configurePolicyCardForAccount, updateVideoTranscripts } =
  require('../services/utils/lessonUtils');
const { findAnswerCard, includeAnswer, includeHasAnswer } = require('../services/utils/answerCardUtils');
const { reorderLLCs } = require('../services/utils/lessonLessonCardUtils');
const { localizeModelObject, getContentStringQuery,
  getQueryLanguages } = require('../services/utils/localizationUtils');
const { uploadFile, getExistingFile, moveFile } = require('../services/utils/fileUtils');
const { getAllowedPermissions } = require('../services/acl/acl');
const { genTextToSpeechLessonCard } = require('../services/textToSpeech/textToSpeech');
const { addSpeechUrls, includeProtectedTraitsOneCard,
  includeVideoTranscript } = require('../services/utils/lessonCardUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');

const LessonCards = db.lessonCards;
const AnswerCards = db.answerCards;
const LessonLessonCards = db.lessonLessonCards;
const ProtectedTraits = db.protectedTraits;
const Lessons = db.lessons;
const Resources = db.resources;
const ContentStrings = db.contentStrings;
const SpeechAudios = db.speechAudios;
const Files = db.files;
const AccountLessonCards = db.accountLessonCards;
const Accounts = db.accounts;
const Programs = db.programs;
const LastViewedLessonCard = db.lastViewedLessonCards;
const AssessmentItems = db.assessmentItems;
const VideoTranscripts = db.videoTranscripts;
const Op = db.Sequelize.Op;

const questionTypes = ['quizBoolean', 'quizSlider', 'quizSingleChoice',
  'quizMultiChoice', 'quizFreeformText', 'quizColorSpectrum'];

function quizCardIncludes() {
  const includes = [
    { model: AssessmentItems, attributes: ['id', 'questionText'] },
    { model: db.users, as: 'createdByUserData', attributes: ['id', 'firstName', 'lastName'] },
    { model: db.users, as: 'updatedByUserData', attributes: ['id', 'firstName', 'lastName'] },
  ];
  return includes;
}
const restQueryToSequelize = (query, defaults) => {
  const omittedTerms = ['$limit', '$skip', '$sort', 'topics', 'tags', 'deletedAt', 'lessonId',
    'programId', 'downloadLanguage', 'localize', 'allowMT', 'includeAnswers',
    'sourceLifecycle', 'isPreview'];
  let whereClause = restOperatorsToSequelize(_.omit(
    query,
    omittedTerms,
  ));
  if (whereClause.filter && whereClause.filter === 'quizCard' && !whereClause.cardType) {
    const quizCardType = {
      [Op.in]: questionTypes,
    };
    whereClause.cardType = quizCardType;
  }
  if (whereClause.filter && whereClause.filter === 'quizCard') {
    delete whereClause.filter;
  }
  if (whereClause.responseOptions) {
    const responseOption = {
      [Op.or]: [
        {
          list1: {
            [Op.like]: `%${whereClause.responseOptions}%`,
          },
        },
        {
          list2: {
            [Op.like]: `%${whereClause.responseOptions}%`,
          },
        },
        {
          list3: {
            [Op.like]: `%${whereClause.responseOptions}%`,
          },
        },
        {
          list4: {
            [Op.like]: `%${whereClause.responseOptions}%`,
          },
        },
      ],
    };
    delete whereClause.responseOptions;
    whereClause = { ...whereClause, ...responseOption };
  }
  if (whereClause.assessmentItem) {
    let assessmentMap = {
      assessmentItemId: 0,
    };
    if (whereClause.assessmentItem === 'mapped') {
      assessmentMap = {
        assessmentItemId: {
          [Op.ne]: 0,
        },
      };
    }
    delete whereClause.assessmentItem;
    whereClause = { ...whereClause, ...assessmentMap };
  }
  const limitOverride = 500;

  const newQuery = {
    ...defaults,
    where: { ...defaults.where, ...whereClause },
  };

  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(limitOverride, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }
  return newQuery;
};

const refreshLessonSearch = async (req, lesson) => {
  const searchInfo = await deriveSearch(lesson);
  let searchText = searchTextFromSearchInfo(searchInfo);
  // if searchtext is greater than the text column limit, truncate it.
  if (searchText && searchText.length > SEARCH_TEXT_COL_LIMIT) {
    searchText = searchText.substr(0, SEARCH_TEXT_COL_LIMIT);
    searchText = searchText.substr(0, searchText.lastIndexOf(' '));
  }
  await lesson.resource.update({ searchText });
  await updateElasticSearch(req, lesson.resource.id, searchInfo);
};

const checkLessonCardPublished = async (lessonCardId) => {
  const isCardPublished = await LessonCards.count({
    where: { id: lessonCardId },
    include: [{
      model: Lessons,
      where: {
        lifecycle: 'publish',
      },
      required: true,
    }],
  });
  return isCardPublished > 0;
};

/**
 * The client will ask for a lessonId if coming from the admin area. The admin area also needs
 * to know if the lessonCard is shared or not.
 * Get the number of bindings so that the client can determine if this card is shared or not
 *
 * @param {{ id: number, bindings: {}[] }} lessonCard
 * @param {number} [lessonId]
 * @returns {Promise<number>} the total number of lessonCard bindings
 */
const getNumberOfBindings = async (lessonCard, lessonId) => {
  if (lessonId) {
    const numBindings = await LessonLessonCards.count({ where: { lessonCardId: lessonCard.id } });
    return numBindings;
  }
  return lessonCard.bindings.length;
};

/**
 * format the lifecycle clause for the db query. Either for lessons or answercards.
 *
 * @param {string[]} lessonCardPermissions user permissions pertaining to lessonCards
 * @param {boolean} publishOnly only published lessons? Comes from the request
 * @param {'lifecycle'|'sourceLifecycle'} indexSignature fieldName for the lifecycle of the lesson. If this is for lessons, the field is lifecycle. If it's anwercards, it's sourceLifecycle
 * @returns
 */
const getLifecycleClause = (lessonCardPermissions, publishOnly, indexSignature) => {
  const defaultClause = [
    { [indexSignature]: 'publish' },
    { [indexSignature]: 'retired' },
  ];
  if (!publishOnly) {
    if (lessonCardPermissions.includes('update')) {
      return [
        ...defaultClause,
        { [indexSignature]: 'review' },
        { [indexSignature]: 'draft' },
        { [indexSignature]: 'close' },
      ];
    } else if (lessonCardPermissions.includes('review')) {
      return [
        ...defaultClause,
        { [indexSignature]: 'review' },
      ];
    }
  }

  return defaultClause;
};

function getLessonFilterParams(req, allowedPermissions) {
  const lessonId = req.query.lessonId ? parseInt(req.query.lessonId) : undefined;
  const lifecycleClauses = getLifecycleClause(allowedPermissions.lessonCards, false, 'lifecycle');

  const filter = { [Op.or]: lifecycleClauses };
  if (lessonId) {
    filter.id = lessonId;
  }
  return filter;
}

function getAnswerCardFilterParams(req, allowedPermissions) {
  const user = req.user;
  const isPreview = !!(req.query.sourceLifecycle && req.query.sourceLifecycle === 'preview');
  let lifecycleClauses;

  if (isPreview) {
    lifecycleClauses = [
      { sourceLifecycle: 'preview' },
    ];
  } else {
    lifecycleClauses = getLifecycleClause(allowedPermissions.lessonCards, req.publishOnly, 'sourceLifecycle');
  }

  const filter = { [Op.or]: lifecycleClauses };
  if (user) {
    filter.userId = user.id;
  }
  return filter;
}

const includeLesson = async (req, defaults, includeUsers = false) => {
  const user = req.user;
  const languages = getQueryLanguages(req);

  const allowedPermissions = await getAllowedPermissions(
    req,
    user ? user.id : null,
    'lessonCards',
    req.tokenPayload,
  );
  const contentStrings = getContentStringQuery(req, languages, 'lessonCard');

  const speechAudios = {
    model: SpeechAudios,
    where: {
      model: 'lessonCard',
    },
    include: [{
      model: Files,
    }],
    required: false,
  };
  if (languages) {
    speechAudios.where.language = {
      [Op.in]: languages,
    };
  }

  const lessons = {
    model: Lessons,
    where: getLessonFilterParams(req, allowedPermissions),
    include: [getContentStringQuery(req, languages, 'lesson')],
    required: true,
  };

  if (req.query.programId) {
    const programId = parseInt(req.query.programId);
    const programFilter = {
      model: Programs,
      where: { id: programId },
      required: true,
    };
    lessons.include.push(programFilter);
  }

  const lessonLessonCards = {
    model: LessonLessonCards,
    as: 'bindings',
    include: [{
      model: AnswerCards,
      where: getAnswerCardFilterParams(req, allowedPermissions),
      required: false,
    },
    {
      model: Lessons,
      attributes: ['title', 'internalName'],
      required: false,
    }],
    required: false,
  };
  if (req.query.lessonId && req.query.includeAllBindings !== 'true') {
    const lessonId = parseInt(req.query.lessonId);
    lessonLessonCards.where = {
      [Op.or]: [
        { isOwner: true },
        { lessonId },
      ],
    };
  }

  const includes = {
    ...defaults,
    include: [
      lessons,
      lessonLessonCards,
      contentStrings,
      speechAudios,
    ],
  };
  if (req.query.filter && req.query.filter === 'quizCard') {
    const assessmentMapping = {
      model: AssessmentItems,
      attributes: ['id', 'questionText'],
    };
    includes.include.push(assessmentMapping);
  }
  if (includeUsers === true) {
    includes.include.push({ model: db.users, as: 'createdByUserData', attributes: ['id', 'firstName', 'lastName'] });
    includes.include.push({ model: db.users, as: 'updatedByUserData', attributes: ['id', 'firstName', 'lastName'] });
  }
  return includes;
};

const includeCardImages = (query) => {
  const returnQuery = query;
  if (returnQuery.include) {
    returnQuery.include.push({
      model: Files,
      as: 'images',
    });
  } else {
    returnQuery.include = [{
      model: Files,
      as: 'images',
    }];
  }
  return returnQuery;
};

// loads LessonCard with proper includes
const loadLessonCard = async (req, id, inclLesson = true) => {
  let query = inclLesson ? await includeLesson(req, {}) : {
    include: [getContentStringQuery(req, null, 'lessonCard')],
  };
  query = includeCardImages(query);
  return LessonCards.findByPk(id, query);
};

// post processes LessonCard to add other data to record
const flattenLessonCard = async (req, lessonId, lessonCard, enrollmentId, isFlat = false, campaignFilterId = null) => {
  let flattenedRecord = isFlat ? lessonCard : lessonCard.get({ plain: true });
  const includeAnswers = !req.query || !req.query.includeAnswers || !(req.query.includeAnswers === 'false');
  const downloadLanguage = req.query && req.query.downloadLanguage ? req.query.downloadLanguage : req.i18n.language;
  const limitLanguage = req.query.downloadLanguage;
  const allowMT = !(req.query.allowMT && req.query.allowMT === 'false'); // default to true, must be set to "false" to disallow
  const localize = !(req.query && req.query.localize && req.query.localize === 'false');
  const isPreview = !!(req.query.sourceLifecycle && req.query.sourceLifecycle === 'preview');

  if (localize) {
    flattenedRecord = localizeModelObject(req, flattenedRecord, downloadLanguage, allowMT);
  }

  if (isPreview) {
    flattenedRecord.hasAnswers = questionTypes.includes(lessonCard.cardType);
  } else {
    if (req.user) {
      const answerCard = findAnswerCard(lessonId, flattenedRecord, enrollmentId);
      if (answerCard) {
        flattenedRecord.answerCardId = answerCard.id; // eslint-disable-line no-param-reassign
      }
      if (includeAnswers) {
        flattenedRecord.answerCard = answerCard; // eslint-disable-line no-param-reassign
      }
    }
    // attaches a flag to indicate whether card has any answers yet to card
    flattenedRecord = await includeHasAnswer(flattenedRecord, lessonId);
    if (includeAnswers) {
      // attaches answer report to card
      flattenedRecord = await includeAnswer(req, flattenedRecord, lessonId, campaignFilterId);
    }
  }

  flattenedRecord.numBindings = await getNumberOfBindings(lessonCard, lessonId);
  flattenedRecord = await includeProtectedTraitsOneCard(req, flattenedRecord, { language: limitLanguage, allowMT });
  flattenedRecord = addSpeechUrls(req, flattenedRecord, downloadLanguage, allowMT);
  flattenedRecord = await includeVideoTranscript(req, flattenedRecord);

  // localize attached lessons
  if (flattenedRecord.lessons && localize) {
    // eslint-disable-next-line no-unused-vars
    flattenedRecord.lessons.forEach((lesson) => {
      lesson = localizeModelObject(req, lesson, downloadLanguage, allowMT); // eslint-disable-line no-param-reassign
    });
  }
  let langImages = {};
  if (lessonCard.images) {
    lessonCard.images = _.orderBy(lessonCard.images, ['id'], ['desc']);
    langImages = lessonCard.images.reduce((obj, img) => {
      obj[img.language] = img.path;
      return obj;
    }, {});
  }
  flattenedRecord.images = langImages;
  return flattenedRecord;
};

/**
 * @openapi
 * /lesson-cards?lessonId={lessonId}&enrollmentId={enrollmentId}&$limit={limit}:
 *   get:
 *     summary: Lesson Cards
 *     tags:
 *       - Lesson Cards
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: lessonId
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the lesson
 *       - name: enrollmentId
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the enrollment
 *       - name: $limit
 *         in: query
 *         required: false
 *         schema:
 *           type: integer
 *         description: Limit the number of results
 *     responses:
 *       '200':
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: total number of records.
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   description: Array of lesson cards object.
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/lessonCards'
 *                       - type: object
 *                         properties:
 *                           lessons:
 *                             type: array
 *                             items:
 *                               allOf:
 *                                 - $ref: '#/components/schemas/lessons'
 *                                 - type: object
 *                                   properties:
 *                                     lessonLessonCards:
 *                                       $ref: '#/components/schemas/lessonLessonCards'
 *                                     contentStrings:
 *                                       type: array
 *                                       items:
 *                                         $ref: '#/components/schemas/contentStrings'
 *                           bindings:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/lessons'
 *       '401':
 *         description: Unauthorized
 *       '404':
 *         description: Not Found
 *       '400':
 *         description: Bad Request
 *       '5XX':
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  // to be replaced when UI can specify
  req.publishOnly = true;
  const lessonId = req.query.lessonId ? parseInt(req.query.lessonId) : undefined;
  const campaignId = req.query.campaignId; // if a campaignId is specified retrn answerCard data specific to that campaign.
  delete req.query.campaignId; // don't include this in the lessonCard search criteria. This is strictly for answerCards
  const enrollmentId = req.query.enrollmentId ? parseInt(req.query.enrollmentId) : 1;
  delete req.query.enrollmentId; // don't include this in the lessonCard search criteria. This is strictly for answerCards
  const defaults = {
    limit: config.paginate.default,
    offset: 0,
  };

  const withLessons = await includeLesson(req, defaults, true);
  let queryParams = restQueryToSequelize(req.query, withLessons);
  queryParams = includeCardImages(queryParams);
  if (queryParams.order) {
    queryParams.order = [[...queryParams.order]];
  }

  const finalQuery = {
    limit: config.paginate.default,
    ...queryParams,
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };

  try {
    // Do a count query without skip and limit to get total
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
      include: _.pick(finalQuery, ['include']).include,
      distinct: true,
    };
    const count = await LessonCards.count(countQuery);
    let results = await LessonCards.findAll(finalQuery);
    if (lessonId) {
      // apply sort based on binding for lessonId
      const mapped = results.map((el, i) => {
        const bindings = el.bindings.filter(binding => binding.lessonId === lessonId);
        let lessonBinding;
        if (bindings.length === 1) {
          lessonBinding = bindings[0];
        }
        return { index: i, value: lessonBinding };
      });

      const filterRemovedLessonCards = mapped.filter(card => !card.value.dataValues.dateRemoved);

      // sorting the mapped array containing the reduced values
      filterRemovedLessonCards.sort((a, b) => {
        if (a.value.dataValues.position > b.value.dataValues.position) {
          return 1;
        }
        if (a.value.dataValues.position < b.value.dataValues.position) {
          return -1;
        }
        return 0;
      });

      // container for the resulting order
      let cardIdx = 1;
      results = filterRemovedLessonCards.map((el) => {
        const card = results[el.index].get({ plain: true });
        card.lessonId = lessonId;
        card.position = cardIdx;
        cardIdx += 1;
        return card;
      });
    }

    const accountId = req.user ? req.user.accountId : null;
    // post-process card data adding various derived data to api result
    const promises = results.map((record) => {
      return flattenLessonCard(req, lessonId, record, enrollmentId, lessonId, campaignId);
    });
    const flattenedData = await Promise.all(promises);

    if (accountId) {
      const account = await Accounts.findByPk(accountId);
      if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
        const accountLessonCards = await AccountLessonCards.findAll({ where: { accountId } });
        for (let lessonCard of flattenedData) {
          if (lessonCard.cardType === 'policyAcknowledgement') {
            const matchingAccountLessonCard =
              accountLessonCards.find(accountLessonCard => accountLessonCard.lessonCardId === lessonCard.id);
            if (matchingAccountLessonCard) {
              if (matchingAccountLessonCard.policyType === 'file' && matchingAccountLessonCard.fileId) {
                const configuredFile = await Files.findByPk(matchingAccountLessonCard.fileId);
                if (configuredFile) {
                  // only support english language configurations for now.
                  matchingAccountLessonCard.images = { en: configuredFile.dataValues.path };
                }
              }
              lessonCard = configurePolicyCardForAccount(lessonCard, matchingAccountLessonCard, accountId, true);
            }
          }
        }
      }
    }

    const dataWithPolicyLinksSecured = [];
    for (const nextCard of flattenedData) {
      let securedCard = nextCard;
      if (nextCard.cardType === 'policyAcknowledgement') {
        securedCard = await securePolicyCardLinks(accountId, nextCard);
      }
      dataWithPolicyLinksSecured.push(securedCard);
    }
    res.json({ total: count, ...pagedResult, data: dataWithPolicyLinksSecured });
  } catch (err) {
    const sql = err.sql ? err.sql : 'none';
    logger.error(`Exception caught in lessonCards.list(), err = ${err}, sql = ${sql}`);
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  // to be replaced when UI can specify
  req.publishOnly = true;
  let securedCard;
  try {
    if (req.query.filter && req.query.filter === 'quizCard') {
      securedCard = req.lessonCard.get({ plain: true });
      securedCard.isLessonCardPublished = await checkLessonCardPublished(req.lessonCard.id);
    } else {
      let flattenedCard = await flattenLessonCard(req, req.lessonId, req.lessonCard);
      const accountId = req.user ? req.user.accountId : null;
      if (accountId) {
        const account = await Accounts.findByPk(accountId);
        // eslint-disable-next-line max-len
        if (account && (account.accountType === 'customer' || account.accountType === 'internal') && flattenedCard.cardType === 'policyAcknowledgement') {
          const accountLessonCard =
            await AccountLessonCards.findOne({ where: { accountId, lessonCardId: req.lessonCard.id } });
          if (accountLessonCard) {
            if (accountLessonCard.policyType === 'file' && accountLessonCard.fileId) {
              const configuredFile = await Files.findByPk(accountLessonCard.fileId);
              if (configuredFile) {
                // only support english language configurations for now.
                accountLessonCard.images = { en: configuredFile.dataValues.path };
              }
            }
            flattenedCard = configurePolicyCardForAccount(flattenedCard, accountLessonCard, accountId, true);
          }
        }
      }
      securedCard = flattenedCard;
      if (flattenedCard.cardType === 'policyAcknowledgement') {
        securedCard = await securePolicyCardLinks(accountId, flattenedCard);
      }
    }
    res.json(securedCard);
  } catch (err) {
    next(err);
  }
};

module.exports.create = async (req, res, next) => {
  const newCard = req.body;
  newCard.createdBy = req.user.id;
  // grab off lessonId, position etc which aren't stored with the lesson card anymore
  const lessonId = newCard.lessonId;
  const sourceId = newCard.sourceId;
  const selectedLanguage = newCard.selectedLanguage || 'en';
  let position = newCard.position;
  const lessonCompleteCardBindingId = newCard.lessonCompleteCardBindingId;
  delete newCard.lessonId;
  delete newCard.sourceId; // source cardId when splitting shared card
  delete newCard.position;
  delete newCard.selectedLanguage;
  delete newCard.lessonCompleteCardBindingId; // id of the binding of the lessonComplete card
  const hasPolicyCard = req.body?.cardType === 'policyAcknowledgement';

  try {
    if (!lessonId) {
      const err = new Error(req.i18n.t('lessonCards.lessonId_missing_Error'));
      err.status = 404;
      throw err;
    }

    let fileFolder = 'lesson-cards';
    if (req.body.cardType && req.body.cardType === 'policyAcknowledgement' && req.body.list4) {
      fileFolder = `accounts/${req.body.list4}/documents`;
    }
    const image = await uploadFile(req, fileFolder, selectedLanguage);
    if (req.body.cardType === 'statesMap' && req.body.protectedTraits) {
      req.body.protectedTraits = expandProtectedTraits(req.body.protectedTraits, selectedLanguage, req.body.list2);
    }

    let lessonCard = await LessonCards.create(newCard, {
      include: [{
        model: ProtectedTraits,
      }],
    });

    let oldLessonCard;
    if (image) {
      lessonCard.setImages([image]);
    } else if (sourceId) {
      // duplicate files when creating a new card from a shared card
      oldLessonCard = await loadLessonCard(req, sourceId, false);
      const oldImages = await oldLessonCard.getImages();
      if (oldLessonCard && oldImages) {
        const fileDataPromises = oldImages.map(async (file) => {
          const fileData = await getExistingFile(file);
          return fileData;
        });
        const files = await Promise.all(fileDataPromises);

        const uploadPromises = files.map(async (file, idx) => {
          const uploadedFile = await uploadFile({ file }, fileFolder, oldImages[idx].language);
          return uploadedFile;
        });
        const uploadedFiles = await Promise.all(uploadPromises);

        if (uploadedFiles) {
          lessonCard.setImages(uploadedFiles);
        }
      }
    }

    // if lesson specified, then set up binding
    if (lessonId) {
      let insertPosition = position;

      const lesson = await Lessons.findByPk(lessonId, {
        attributes: ['id', 'title', 'description', 'lifecycle'],
        include: [{
          attributes: ['id'],
          model: Resources,
          as: 'resource',
        }],
      });
      // if this lesson has already been published, we should record the dateAdded for the lessonCard
      let dateAdded = null;
      const launchByUsersCount = await launchedByUsersCount(lessonId);
      if (launchByUsersCount > 0) {
        dateAdded = Date.now();
        // delete lastViewedLessonCards records
        await LastViewedLessonCard.destroy({ where: { lessonId, sourceLifecycle: 'publish' } });
      }
      if (!insertPosition) {
        // stick it at the end
        insertPosition = await LessonLessonCards.count({
          where: {
            lessonId,
          },
        }) + 1;
      }
      const newBinding = await LessonLessonCards.create({
        lessonId,
        lessonCardId: lessonCard.id,
        position: insertPosition,
        isOwner: true,
        dateAdded,
      });
      position = newBinding.position;
      // move lessonCompleteCard to end
      if (!sourceId && lessonCompleteCardBindingId && lessonCard.cardType !== 'lessonComplete') {
        const lessonCompleteBinding = await LessonLessonCards.findByPk(lessonCompleteCardBindingId);
        const lcPos = position + 1;
        await lessonCompleteBinding.update({ position: lcPos });
      }
      if (sourceId) {
        // remove the binding of the copied card or set dateRemoved if launchByUsersCount
        const removeWhere = { lessonId, lessonCardId: sourceId };
        if (launchByUsersCount > 0) {
          await LessonLessonCards.update({ dateRemoved: Date.now() }, { where: removeWhere });
        } else {
          await LessonLessonCards.destroy({ where: removeWhere });
        }
        // copy translations, probably only one of the languages here
        let newContentStrings = [];
        if (newCard && newCard.contentStrings) {
          newContentStrings = newCard.contentStrings.map((cs) => {
            return {
              contentId: lessonCard.id,
              model: 'lessonCard',
              language: cs.language,
              field: cs.field,
              value: cs.value,
              createdAt: new Date(),
              updatedAt: new Date(),
            };
          });
          await ContentStrings.bulkCreate(newContentStrings);
        }
        // get the rest of the translations from the source card
        if (oldLessonCard && oldLessonCard.contentStrings) {
          const moreNewContentStrings = oldLessonCard.contentStrings
            .filter(ocs => !ocs.language.endsWith('-mt') && !ocs.language.endsWith('-act')
              && !newContentStrings.find(ncs => ocs.language === ncs.language && ocs.field === ncs.field))
            .map((cs) => {
              return {
                contentId: lessonCard.id,
                model: 'lessonCard',
                language: cs.language,
                field: cs.field,
                value: cs.value,
                createdAt: new Date(),
                updatedAt: new Date(),
              };
            });
          if (moreNewContentStrings.length > 0) {
            await ContentStrings.bulkCreate(moreNewContentStrings);
          }
        }
      }
      await refreshLessonSearch(req, lesson);
      if (hasPolicyCard) {
        await Lessons.update({ hasPolicy: true }, { where: { id: lessonId } });
      }
    }
    if (req.body.cardType === 'video' && req.body.videoId && lessonCard && lessonCard.list4) {
      await updateVideoTranscripts(req.body.videoId, lessonCard.list4);
    }
    lessonCard = await loadLessonCard(req, lessonCard.id);
    lessonCard = await flattenLessonCard(req, lessonId, lessonCard);
    // add lessonId and position back in response
    if (lessonId) {
      lessonCard.lessonId = lessonId;
    }
    if (position) {
      lessonCard.position = position;
    }
    res.json(lessonCard);
  } catch (err) {
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  const lessonCard = req.lessonCard;
  const position = req.body.position;
  const lessonId = req.body.lessonId;
  const fontColor = req.body.fontColor || null;
  const selectedLanguage = req.body.selectedLanguage || 'en';
  delete req.body.selectedLanguage;
  req.body.updatedBy = req.user.id;
  try {
    let fileFolder = 'lesson-cards';
    if (req.body.cardType && req.body.cardType === 'policyAcknowledgement' && req.body.list4) {
      fileFolder = `accounts/${req.body.list4}/documents`;
    }
    const image = await uploadFile(req, fileFolder, selectedLanguage);
    // This if clause handles the special case where the following are true.
    // 1. It's a policyAcknowledgement card.
    // 2. An account id was NOT previously specified for the card.
    // 3. An account id has now been added.
    // 4. A file was previously uploaded that is available to all accounts.
    // 5. The file must now be moved so that it is restricted to the newly specified account.
    if (req.body.cardType && req.body.cardType === 'policyAcknowledgement' &&
      req.body.list4 && req.body.list4 !== lessonCard.list4 &&
      !image && lessonCard.images && lessonCard.images.length > 0) {
      const parsedURL = urlparse(lessonCard.images[0].path);
      const parsedPath = pathlib.parse(parsedURL.pathname);
      const relativePath = parsedURL.pathname.startsWith('/') ? parsedURL.pathname.substr(1) : parsedURL.pathname;
      await moveFile(relativePath, `${fileFolder}/${parsedPath.base}`);
      // Now replace the images for this card with an image that has the new location.
      lessonCard.images[0].path = lessonCard.images[0].path.replace(relativePath, `${fileFolder}/${parsedPath.base}`);
      Files.update({ path: lessonCard.images[0].path }, { where: { id: lessonCard.images[0].id } });
    }
    if (req.body.cardType === 'statesMap') {
      await updateCardProtectedTraits(req.body.id, req.body.protectedTraits, selectedLanguage, lessonCard.list2);
    }
    if (req.body.cardType === 'video') {
      if (req.body.list4) {
        await updateVideoTranscripts(req.body.videoId, req.body.list4, lessonCard.list4);
      } else if (lessonCard.videoId !== req.body.videoId) {
        req.body.list4 = null;
      }
    }
    // update card position if indicated
    const lessonbinding = lessonCard.bindings.find(b => b.lessonId === lessonId);
    const oldPosition = lessonbinding && lessonbinding.position;
    if (position && oldPosition && position !== oldPosition) {
      if (!lessonId) {
        const err = new Error(req.i18n.t('lessonCards.lessonId_missing_Error'));
        err.status = 400;
        throw err;
      }
      await reorderLLCs(lessonId, oldPosition, position);
      await lessonbinding.update({ position });
    }
    const reqData = { ...req.body, fontColor };
    if (['listBullets', 'listNumbers', 'quizSingleChoice', 'quizMultiChoice', 'listCheckboxes', 'clickExpand'].includes(reqData.cardType)) {
      const fieldsToCheck = ["list1", "list2", "list3", "list4", "list5", "list6", "list7", "list8", "list9", "list10", "list1Detail", "list1Detail", "list3Detail", "list4Detail"];
      fieldsToCheck.forEach((field) => {
        reqData[field] = reqData[field] ? reqData[field] : null;
      });
    }
    // update card
    let newCard = await lessonCard.update(reqData, {
      silent: selectedLanguage !== 'en',
    });
    if (selectedLanguage !== 'en' && lessonCard.contentStrings) {
      const promises = lessonCard.contentStrings.map((nextContentString) => {
        if (!nextContentString.id) {
          return ContentStrings.create({
            contentId: lessonCard.id,
            model: 'lessonCard',
            language: selectedLanguage,
            field: nextContentString.field,
            value: nextContentString.value,
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }
        return ContentStrings.update(
          { value: nextContentString.value },
          { where: { id: nextContentString.id } },
        );
      });
      await Promise.all(promises);
    }

    if (image) {
      const newImage = image;
      const currentImages = await lessonCard.getImages();
      const newImages = currentImages.filter(img => img.language !== selectedLanguage);
      newImages.push(newImage);
      await lessonCard.setImages(newImages);
    }

    // update Lessons search info for any lesson that has this card
    const lessons = await Lessons.findAll({
      attributes: ['id', 'title', 'description'],
      include: [
        {
          attributes: ['id'],
          model: Resources,
          as: 'resource',
          required: true,
        },
        {
          attributes: [],
          model: LessonCards,
          where: {
            id: lessonCard.id,
          },
        },
      ],
    });
    await Promise.all(lessons.map(l => refreshLessonSearch(req, l)));

    // reload card
    newCard = await loadLessonCard(req, lessonCard.id);
    newCard = await flattenLessonCard(req, lessonId, newCard);

    // add lessonId and position back in response
    if (lessonId) {
      newCard.lessonId = lessonId;
    }
    if (position) {
      newCard.position = position;
    }
    res.json(newCard);
  } catch (err) {
    next(err);
  }
};

module.exports.textToSpeech = async (req, res, next) => {
  try {
    const lessonCard = req.lessonCard;
    const downloadLanguage = (req.query && req.query.downloadLanguage) || 'en';
    await genTextToSpeechLessonCard(false, lessonCard, downloadLanguage);

    // load card
    let card = await loadLessonCard(req, lessonCard.id);
    card = await flattenLessonCard(req, null, card);
    res.json(card.speechUrls);
  } catch (err) {
    next(err);
  }
};

module.exports.lessonCardById = async (req, res, next, id) => {
  const defaults = {
    where: { id },
  };
  let queryParams = {};
  if (req.query.filter && req.query.filter === 'quizCard') {
    defaults.where.cardType = { [Op.in]: questionTypes };
    const quizIncludes = quizCardIncludes();
    queryParams = {
      ...defaults, ...{ include: quizIncludes },
    };
  } else {
    queryParams = await includeLesson(req, defaults, true);
    queryParams = includeCardImages(queryParams);
  }
  try {
    req.lessonId = req.query.lessonId ? parseInt(req.query.lessonId) : undefined;
    const lessonCard = await LessonCards.findOne(queryParams);
    if (!lessonCard) {
      const err = new Error(req.i18n.t('lessonCards.lessonCard_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.lessonCard = lessonCard;
    next();
  } catch (err) {
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  let lessonCard = req.lessonCard;
  const lessonCardId = lessonCard.id;
  const lessonId = req.lessonId || req.query.lessonId;
  try {
    if (!lessonId) {
      const err = new Error(req.i18n.t('lessonCards.lessonId_missing_Error'));
      err.status = 400;
      throw err;
    }

    // if this lesson has already been published, we should record the dateAdded for the lessonCard
    let dateRemoved = null;
    const launchByUsersCount = await launchedByUsersCount(lessonId);
    if (launchByUsersCount > 0) {
      dateRemoved = Date.now();
    }
    // find and delete binding for this lesson
    const llcs = await LessonLessonCards.findAll({ where: { lessonCardId } });
    const bindingToDelete = llcs.filter(binding => binding.lessonId === lessonId);
    if (bindingToDelete.length === 1) {
      if (bindingToDelete[0].dateAdded) {
        const id = bindingToDelete[0].lessonId;
        const err = new Error(req.i18n.t('lessonCards.card_added_cannot_be_removed_Error', { id }));
        err.status = 400;
        throw err;
      }
      await reorderLLCs(lessonId, bindingToDelete[0].position);
      if (dateRemoved) {
        bindingToDelete[0].dateRemoved = dateRemoved;
        await bindingToDelete[0].save();
      } else {
        await bindingToDelete[0].destroy();
      }
    }

    // if last binding deleted, also delete card
    if (!dateRemoved && llcs.length <= 1) {
      if (lessonCard.cardType === 'statesMap') {
        await deleteProtectedTraits(lessonCard.id);
      }

      // update Lessons search info for any lesson that has this card
      const lessons = await Lessons.findAll({
        attributes: ['id', 'title', 'description'],
        include: [
          {
            attributes: ['id'],
            model: Resources,
            as: 'resource',
            required: true,
          },
          {
            attributes: [],
            model: LessonCards,
            where: {
              id: lessonCard.id,
            },
          },
        ],
      });
      await Promise.all(lessons.map(l => refreshLessonSearch(req, l)));

      // remove answer cards for all bindings that have this lesson card
      const llcIds = llcs.map(llc => llc.id);
      await AnswerCards.destroy({
        where: {
          lessonLessonCardId: {
            [Op.in]: llcIds,
          },
        },
      });

      lessonCard = await lessonCard.destroy();
    }
    if (lessonCard && lessonCard.cardType === 'policyAcknowledgement') {
      const LessonData = await Lessons.findOne({
        attributes: ['id', 'title'],
        where: { id: lessonId },
        include: [
          {
            model: LessonCards,
            attributes: ['id', 'cardType'],
            include: [
              {
                model: LessonLessonCards,
                as: 'bindings',
                required: true,
                where: {
                  dateRemoved: null,
                },
              },
            ],
            required: true,
          },
        ],
      });

      if (LessonData) {
        const hasPolicy = LessonData.lessonCards.some(card => card.cardType === 'policyAcknowledgement');
        if (!hasPolicy) {
          await Lessons.update(
            { hasPolicy },
            { where: { id: lessonId } },
          );
        }
      }
    }

    res.json(lessonCard);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /lesson-cards/{cardId}/lessonCardAssessment:
 *   post:
 *     summary: Update Lesson Cards
 *     tags:
 *       - Lesson Cards
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: cardId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the lesson card
 *     requestBody:
 *       description: The model for the parameters are described below.
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/lessonCardAssessment'
 *           example:
 *             assessmentId: 123
 *             optionType: quizBoolean
 *     responses:
 *       '200':
 *         description: LessonCard updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               description: Response object
 *
 * components:
 *   schemas:
 *     lessonCardAssessment:
 *       type: object
 *       description: Model to update LessonCard
 *       required:
 *         - assessmentId
 *         - optionType
 *       properties:
 *         assessmentId:
 *           type: integer
 *           description: Unique Id of an assessmentItem
 *         optionType:
 *           type: string
 *           description: The question type of the Assessment item (quizBoolean/quizSlider/quizSingleChoice/quizMultiChoice)
 */
module.exports.lessonCardAssessmentMapping = async (req, res, next) => {
  try {
    const lessonCardData = req.lessonCard;
    const assessmentId = req.body.assessmentId;
    const optionType = req.body.optionType;
    const updateData = {
      assessmentItemId: assessmentId,
    };
    const assessmentData = await AssessmentItems.findByPk(assessmentId);
    if (optionType === 'questionText' || optionType === 'both') {
      updateData.question1 = assessmentData.questionText;
    }
    if (optionType === 'responseOption' || optionType === 'both') {
      updateData.list1 = assessmentData.response1;
      updateData.list2 = assessmentData.response2;
      updateData.list3 = assessmentData.response3;
      updateData.list4 = assessmentData.response4;
    }
    await LessonCards.update(updateData, {
      where: {
        id: lessonCardData.id,
      },
    });
    const lessonCard = await loadLessonCard(req, lessonCardData.id);
    res.json(lessonCard);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * components:
 *   schemas:
 *     lessonCards:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         title:
 *           type: string
 *         description:
 *           type: string
 *         imageId:
 *           type: integer
 *         instructionalPurpose:
 *           type: string
 *         cardType:
 *           type: string
 *         duration:
 *           type: string
 *         videoEmbed:
 *           type: integer
 *         list1:
 *           type: string
 *         list2:
 *           type: string
 *         list3:
 *           type: string
 *         list4:
 *           type: string
 *         question1:
 *           type: string
 *         question2:
 *           type: string
 *         descriptionAlignment:
 *           type: string
 *         list5:
 *           type: string
 *         list1Detail:
 *           type: string
 *         list2Detail:
 *           type: string
 *         list3Detail:
 *           type: string
 *         list4Detail:
 *           type: string
 *         isGated:
 *           type: boolean
 *         choice1:
 *           type: boolean
 *         choice2:
 *           type: boolean
 *         choice3:
 *           type: boolean
 *         choice4:
 *           type: boolean
 *         choice5:
 *           type: boolean
 *         choice6:
 *           type: boolean
 *         choice7:
 *           type: boolean
 *         choice8:
 *           type: boolean
 *         choice9:
 *           type: boolean
 *         choice10:
 *           type: boolean
 *         choiceFeedback:
 *           type: string
 *         choiceError:
 *           type: string
 *         fontColor:
 *           type: string
 *         videoId:
 *           type: integer
 *         deletedAt:
 *           type: string
 *         policyLink:
 *           type: string
 *         backgroundColor:
 *           type: string
 *         textOffset:
 *           type: string
 *         healthyResponse:
 *           type: string
 *         lessons:
 *           type: array
 *           items:
 *             type: object
 *         contentStrings:
 *           type: array
 *           items:
 *             type: object
 *         bindings:
 *           type: array
 *           items:
 *             type: object
 */
