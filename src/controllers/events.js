const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { getLessonCardSourceLifecycle } = require('../services/utils/lessonUtils');
const { restSortToSequelize } = require('../services/utils/resourceUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { trackLicenseUsage } = require('../services/utils/licenseUtils');
const {
  propagateLessonCardEvent,
  setLastViewedLessonCard,
  maybeUpdateViewContinue,
} = require('../services/utils/eventUtils');

const Event = db.events;
const ViewLessonCardEvent = db.viewLessonCardEvents;
const Link = db.sendLinks;

const restQueryToSequelize = (query) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };

  const unpaginatedQuery = restOperatorsToSequelize(_.omit(query, ['$limit', '$skip', '$sort']));

  let newQuery = {};
  if (query) {
    newQuery = { ...defaults, ...unpaginatedQuery };
    // override defaults for limit, skip, sort if passed in by caller
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }

    newQuery.where = unpaginatedQuery;
  }
  return [newQuery, { where: unpaginatedQuery }];
};

module.exports.list = async (req, res, next) => {
  const [query, countQuery] = restQueryToSequelize(req.query);
  const pagedResult = {
    limit: query.limit,
    skip: query.offset,
  };

  const finalQuery = {
    ...query,
    // Override max limits
    limit: req.query.$limit ? Math.min(1000, parseInt(req.query.$limit)) : query.limit,
  };

  try {
    const count = await Event.count(countQuery);
    const data = await Event.findAll(finalQuery);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /events:
 *   post:
 *     summary: Add New Event
 *     tags:
 *       - Events
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 example: "view"
 *               userId:
 *                 type: integer
 *                 example: 15
 *               trackableType:
 *                 type: string
 *                 example: "lessonCard"
 *               lessonCardId:
 *                 type: integer
 *                 example: 1
 *               lessonId:
 *                 type: integer
 *                 example: 1
 *               enrollmentId:
 *                 type: integer
 *                 example: 1
 *               sourceLifecycle:
 *                 type: string
 *                 example: "preview"
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/events'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.read = (req, res) => {
  res.json(req.event);
};

module.exports.create = async (req, res, next) => {
  const event = req.body;
  const mustTrackEventTypes = ['view', 'viewcontinue'];
  const { trackableType } = event;

  try {
    // check if user allows event creation
    if (req.user && req.user.noConsent && !mustTrackEventTypes.includes(event.type)) {
      const err = new Error(req.i18n.t('users.user_has_opted_out_Msg'));
      err.status = 400;
      throw err;
    }

    if (trackableType === 'sendLinks' && event.trackableData.linkToken) {
      event.trackableData = event.trackableData.linkToken;
      const token = await Link.find({
        where: {
          linkToken: event.trackableData,
        },
      });
      event.trackableId = token.id;
    }

    if (trackableType === 'lessonCard' && !event.sourceLifecycle) {
      event.sourceLifecycle = await getLessonCardSourceLifecycle(event.lessonId, event.lessonCardId);
    }
    event.userId = (req.user ? req.user.id : null) ||
      ((event.type === 'signup' || event.type === 'workprompt' || event.type === 'useradd') ?
        event.userId : null);

    event.accountId = (req.user ? req.user.accountId : null);
    event.sessionId = req.sessionId;

    if (event.type === 'view' && trackableType === 'lessonCard') {
      await setLastViewedLessonCard(event);
      await trackLicenseUsage(req, event.lessonId, 'lesson');
    }
    let updatedEvent = null;
    if (event.type === 'viewcontinue' && trackableType === 'lessonCard') {
      updatedEvent = await maybeUpdateViewContinue(event);
    }

    let returnedEvent = null;
    let timeIncrement = null;
    if (updatedEvent) {
      timeIncrement = updatedEvent.timeIncrement;
      returnedEvent = updatedEvent.event; // updated viewcontinue
    }

    if (!returnedEvent) {
      // new Event
      if (trackableType === 'lessonCard') {
        const newEvent = {
          ...event,
        };
        delete newEvent.trackableType;
        returnedEvent = await ViewLessonCardEvent.create(newEvent);
      } else {
        returnedEvent = await Event.create(event);
      }
    }
    if (trackableType === 'lessonCard') {
      await propagateLessonCardEvent(req, returnedEvent, { timeIncrement });
    }
    res.json(returnedEvent);
  } catch (err) {
    next(err);
  }
};

module.exports.eventById = async (req, res, next, id) => {
  try {
    const event = await Event.findByPk(id);
    if (!event) {
      const err = new Error(req.i18n.t('events.event_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.event = event;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * definitions:
 *  events:
 *    type: object
 *    properties:
 *      id:
 *        type: integer
 *      createdAt:
 *        type: string
 *      type:
 *        type: string
 *      action:
 *        type: boolean
 *      userId:
 *        type: integer
 *      trackableId:
 *        type: integer
 *      recipientId:
 *        type: integer
 *      trackableType:
 *        type: string
 *      trackableData:
 *        type: string
 *      updatedAt:
 *        type: string
 *      sessionId:
 *        type: integer
 *      accountId:
 *        type: integer
 *      sourceLifecycle:
 *        type: string
 */
