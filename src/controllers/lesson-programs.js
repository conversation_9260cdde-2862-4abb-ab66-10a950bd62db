const db = require('../db');
const config = require('../config/config');
const userUtils = require('../services/utils/userUtils');

const Lessons = db.lessons;
const LessonPrograms = db.lessonPrograms;

async function lookupLesson(lessonBinding) {
  const lesson = await Lessons.findByPk(lessonBinding.lessonId);
  return lesson;
}

async function lookupLessons(programId) {
  const queryParams = {
    order: [['position', 'ASC']],
    where: { programId },
    include: [
      {
        model: Lessons,
      },
    ],
  };
  return LessonPrograms.findAll(queryParams);
}

async function reorderProgram(programId, newOrder) {
  let i = 0;
  const newBindings = newOrder.map((lesson) => {
    i += 1;
    return { lessonId: lesson.id, programId, position: i };
  });
  await LessonPrograms.destroy({ where: { programId } });

  const promises = [];
  newBindings.forEach((binding) => {
    promises.push(LessonPrograms.create(binding));
  });

  await Promise.all(promises);
}

// updates position attribute of program bindings where needed
async function refreshOrder(programId) {
  const queryParams = {
    order: [['position', 'ASC']],
    where: { programId },
  };
  const itemBindings = await LessonPrograms.findAll(queryParams);
  const promises = [];
  let i = 1;
  itemBindings.forEach((binding) => {
    if (binding.position !== i) {
      promises.push(binding.update({ position: i }));
    }
    i += 1;
  });
  await Promise.all(promises);
}

module.exports.list = async (req, res, next) => {
  const program = req.program;
  const defaults = {
    order: [['position', 'ASC']],
    limit: config.paginate.default,
    offset: 0,
    where: { programId: program.id },
    include: [
      {
        model: Lessons,
      },
    ],
  };
  const queryParams = userUtils.restQueryToSequelize(req.query, defaults);
  queryParams.order = [[...queryParams.order]];

  const finalQuery = {
    ...queryParams,
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };

  try {
    const count = await LessonPrograms.count({ where: { programId: program.id } });
    const lessonBindings = await LessonPrograms.findAll(finalQuery);
    const data = lessonBindings.map(binding => binding.lesson);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res) => {
  res.json(await lookupLesson(req.lessonBinding));
};

module.exports.create = async (req, res, next) => {
  const newBinding = {
    programId: req.program.id,
    lessonId: req.body.lessonId,
    position: req.body.position,
  };
  try {
    const oldBinding = await LessonPrograms.findOne({
      where:
        {
          programId: newBinding.programId,
          lessonId: newBinding.lessonId,
        },
    });

    if (oldBinding) {
      const id = newBinding.programId;
      const err = new Error(req.i18n.t('programs.lesson_already_exists_Error', { id }));
      err.status = 400;
      throw err;
    }

    const newLesson = await lookupLesson(newBinding);
    const oldLessons = await lookupLessons(newBinding.programId);
    if (newBinding.position === undefined) {
      newBinding.position = oldLessons.length + 1; // add on end
    }
    let insertionPoint = newBinding.position - 1; // zero-base position
    if (insertionPoint > oldLessons.length) {
      insertionPoint = oldLessons.length;
    } else if (insertionPoint < 0) {
      insertionPoint = 0;
    }

    const newLessons = new Array(oldLessons.length + 1);
    newLessons[insertionPoint] = newLesson;

    let i = 0;
    oldLessons.forEach((binding) => {
      if (newLessons[i]) {
        i += 1;
      }
      newLessons[i] = binding.lesson;
      i += 1;
    });

    await reorderProgram(newBinding.programId, newLessons);
    res.json(newLesson);
  } catch (err) {
    next(err);
  }
};

module.exports.patch = async (req, res) => {
  const program = req.program;
  const newOrder = req.body;
  const count = await LessonPrograms.count({ where: { programId: program.id } });

  if (newOrder.length !== count) {
    const id = program.id;
    const err = new Error(req.i18n.t('programs.lesson_list_length_Error', { id }));
    err.status = 400;
    throw err;
  }

  await reorderProgram(program.id, newOrder);
  const lessonBindings = await lookupLessons(program.id);
  const data = lessonBindings.map(binding => binding.lesson);
  res.json(data);
};

module.exports.delete = async (req, res, next) => {
  const lessonBinding = req.lessonBinding;
  try {
    const data = await lessonBinding.destroy();
    refreshOrder(req.program.id);
    res.json(await lookupLesson(data));
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve a specific program member when an id is passed in the route
module.exports.programLessonById = async function (req, res, next, id) {
  try {
    const queryParams = {
      where: { lessonId: id },
    };
    const lessonBinding = await LessonPrograms.findOne(queryParams);
    if (!lessonBinding) {
      const err = new Error(req.i18n.t('programs.lesson_binding_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.lessonBinding = lessonBinding;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * components:
 *   schemas:
 *     lessonPrograms:
 *       type: object
 *       properties:
 *         lessonId:
 *           type: integer
 *         programId:
 *           type: integer
 *         position:
 *           type: integer
 *         dateAdded:
 *           type: string
 *           format: date-time
 *         dateRemoved:
 *           type: string
 *           format: date-time
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */
