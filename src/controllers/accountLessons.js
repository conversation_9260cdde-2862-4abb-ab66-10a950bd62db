const db = require('../db');
const { updateElasticSearch } = require('../services/utils/resourceUtils');
const { deriveSearch } = require('../services/utils/lessonUtils');
const { getContent, putContent, authenticate } = require('../services/utils/workdayCCLHelpers.js');
const { getWorkdayCCLAccount } = require('../services/utils/workdayCclUtils.js');

const AccountLessons = db.accountLessons;
const Lessons = db.lessons;
const WorkdayContentData = db.workdayContentData;

const updateInWorkdayCCL = async (workdayCCLAccount, data) => {
  try {
    const { activityId, contentType, name, resourceId } = data;
    const existingWorkdayContent = await WorkdayContentData.findOne({
      where: {
        accountId: workdayCCLAccount.id,
        activityId: activityId,
        contentType: contentType,
        isDeleted: false
      }
    });

    if (!existingWorkdayContent) {
      return;
    }

    const tenantAlias = workdayCCLAccount?.integrations?.[0]?.tenantAlias;
    const clientId = workdayCCLAccount?.integrations?.[0]?.clientId;
    const clientSecret = workdayCCLAccount?.integrations?.[0]?.integrationKey;

    const auth = await authenticate({
      clientId,
      clientSecret,
      tenantAlias
    });

    if (!auth.ok) {
      console.error(`ERROR: Workday Authentication Error for account id ${workdayCCLAccount?.id}`);
      return;
    }

    const responseData = await auth.json();
    const accessToken = responseData?.access_token;

    const response = await getContent({
      accessToken,
      contentId: resourceId,
      tenantAlias
    });

    response[0].title = name;
    response[0].lifecycle.lastUpdatedDate = new Date().toISOString();

    const result = await putContent({
      accessToken,
      contentItems: response,
      tenantAlias,
    });

    if (result === 'success') {
      await existingWorkdayContent.update({ updatedAt: new Date().toISOString() });
    } else {
      console.error(`ERROR: Failed to update workday content id: ${resourceId} for account id ${workdayCCLAccount?.id}`);
    }
  } catch (error) {
    console.error('Error in updateInWorkdayCCL:', {
      error: error.message,
      accountId: workdayCCLAccount?.id,
      activityId: data?.activityId,
      resourceId: data?.resourceId
    });
  }
};

module.exports.create = async (req, res, next) => {
  try {
    if (!req.body.accountId) {
      req.body.accountId = req.user.accountId;
    }

    const accountId = req.body.accountId;
    const existingAccountLesson = await
    AccountLessons.findOne({ where: { accountId: req.body.accountId, lessonId: req.body.lessonId } });

    let accountLesson;
    if (existingAccountLesson) {
      req.body.id = existingAccountLesson.id;
      accountLesson = await existingAccountLesson.update(req.body);
    } else {
      accountLesson = await AccountLessons.create(req.body);
    }

    const lesson = await Lessons.findOne({ where: { id: req.body.lessonId } });
    const workdayCCLAccount = await getWorkdayCCLAccount(accountId);

    if (workdayCCLAccount) {
      const data = { 
        activityId: req?.body?.lessonId, 
        contentType: 'lesson', 
        resourceId: lesson?.resourceId,
        name: req?.body?.title, 
      };      

      await updateInWorkdayCCL(workdayCCLAccount, data);
    }    
    
    const searchInfo = await deriveSearch(lesson);
    await updateElasticSearch(req, lesson.resourceId, searchInfo);
    res.json(accountLesson);
  } catch (err) {
    if (err.name === 'SequelizeValidationError') {
      // statements to handle TypeError exceptions
      err.status = 422;
    }
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  const existingAccountLesson = req.accountLesson;
  const modifiedAccountLesson = req.body;
  const accountId = existingAccountLesson?.accountId

  try {
    const updatedAccountLesson = await existingAccountLesson.update(modifiedAccountLesson);
    const finalAccountLesson = await AccountLessons.findByPk(updatedAccountLesson.id);

    const lesson = await Lessons.findOne({ where: { id: finalAccountLesson.lessonId } });
    const workdayCCLAccount = await getWorkdayCCLAccount(accountId);

    if (workdayCCLAccount) {
      const data = { 
        activityId: req?.body?.lessonId, 
        contentType: 'lesson', 
        resourceId: lesson?.resourceId,
        name: req?.body?.title, 
      };      

      await updateInWorkdayCCL(workdayCCLAccount, data);
    }

    const searchInfo = await deriveSearch(lesson);
    await updateElasticSearch(req, lesson.resourceId, searchInfo);
    res.json(finalAccountLesson);
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  try {
    res.json(req.accountLesson);
  } catch (err) {
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  const accountLesson = req.accountLesson;

  try {
    const data = await accountLesson.destroy();
    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.accountLessonByLessonId = async (req, res, next, id) => {
  try {
    const accountId = req.user.accountId;
    const accountLesson = await AccountLessons.findOne({ where: { accountId, lessonId: id } });
    if (!accountLesson) {
      const err = new Error(req.i18n.t('accountLessons.retrieval_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.accountLesson = accountLesson;
    next();
  } catch (err) {
    next(err);
  }
};
