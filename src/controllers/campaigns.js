/* eslint-disable array-callback-return */
const _ = require('lodash');
const db = require('../db');
const { LambdaClient, InvokeCommand } = require("@aws-sdk/client-lambda");
const config = require('../config/config');
const moment = require('moment');
const logger = require('../logger');
const { createAuditLog } = require('./auditLogs');
const { format } = require('date-fns');
const { deleteCampaign, calcCampaignCompletionStats, calcRestartDate, withdrawCampaign,
  getCampaignResourceIds, getParticipants, deleteAllGroupChangesForCampaigns }
  = require('../services/utils/campaignUtils');
const { recordCampaignLifecycleEvent } = require('../services/utils/calcUtils');
const { calcCampaignTriggerDate } = require('../services/utils/notificationsUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { restSortToSequelize, sanitizeUserForClient } = require('../services/utils/userUtils');
const { datesEqual, isNotANumber } = require('../services/utils/jsUtils');
const { configurePolicyCardForAccount } = require('../services/utils/lessonUtils');
const { getAllowedPermissions } = require('../services/acl/acl');

const AccountFields = db.accountFields;
const AccountLessonCards = db.accountLessonCards;
const Campaigns = db.campaigns;
const CampaignNotifications = db.campaignNotifications;
const CampaignItems = db.campaignItem;
const CampaignGroupChanges = db.campaignGroupChanges;
const Files = db.files;
const Groups = db.groups;
const GroupAssignments = db.groupAssignments;
const GroupRules = db.groupRules;
const GroupRuleValues = db.groupRuleValues;
const LessonCards = db.lessonCards;
const LessonLessonCards = db.lessonLessonCards;
const Lessons = db.lessons;
const Programs = db.programs;
const UserAccountFields = db.userAccountFields;
const Users = db.users;
const Op = db.Sequelize.Op;

const CAMPAIGNS_MAX_DURATION = 9999;

const lambdaClient = new LambdaClient({
  region: "us-east-1",
  credentials: {
    accessKeyId: config.s3.accessKeyId,
    secretAccessKey: config.s3.secretAccessKey,
  },
});


const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize(_.omit(query, ['$limit', '$skip', '$sort', 'accountId', 'restartDate']));

  const newQuery = {
    ...defaults,
  };
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }
  if (query.id) {
    const campaignIds = query.id.split(',');
    newQuery.where.id = { [Op.in]: campaignIds };
  }
  if (query.$limit !== undefined) {
    newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  return newQuery;
};

async function getFilterParams(req) {
  const accountClause = req.user ? { accountId: req.user.accountId } : null;
  const allowedPermissions = await getAllowedPermissions(
    req,
    req.user ? req.user.id : null, 'campaigns', req.tokenPayload,
  );
  const lifecycleClauses = [{ lifecycle: 'active' }];
  if (allowedPermissions.campaigns.includes('update') || allowedPermissions.campaigns.includes('updateAccount')) {
    lifecycleClauses.push({ lifecycle: 'review' });
    lifecycleClauses.push({ lifecycle: 'draft' });
    lifecycleClauses.push({ lifecycle: 'close' });
  } else if (allowedPermissions.campaigns.includes('review')) {
    lifecycleClauses.push({ lifecycle: 'review' });
  }
  // don't have the user id on engagement report csv download
  if (req.query && req.query.token && !lifecycleClauses.find(lc => lc.lifecycle === 'close')) {
    lifecycleClauses.push({ lifecycle: 'close' });
  }

  return { ...accountClause, [Op.or]: lifecycleClauses };
}

function flattenItem(campaign) {
  const summary = campaign.summary;
  const flattenedItem = campaign.get({ plain: true });
  return Object.assign(flattenedItem, { summary });
}

const includeCampaignSummary = async (campaign) => {
  // items (only includes content items, i.e. not programs)
  const resources = await getCampaignResourceIds(campaign);

  // pending changes count
  const groupChangesCount = await CampaignGroupChanges.count({
    where: {
      campaignId: campaign.id,
      processedDate: null,
    },
  });

  // groups
  const groupAssignments = await GroupAssignments.findAll({
    where: {
      campaignId: campaign.id,
      reoccurenceIteration: campaign.reoccurenceIteration,
      programId: null,
    },
  });

  // participants
  const participants = await getParticipants(campaign);
  const stats = await calcCampaignCompletionStats(campaign);

  // percent complete
  const summary = {
    items: resources.length,
    groups: groupAssignments.length,
    participants: participants.length,
    completed: stats.percentComplete,
    pastDueCount: stats.pastDueUsersCount,
    groupChangesCount,
  };

  Object.assign(campaign, { summary });
  return campaign;
};

const duplicate = async (req) => {
  const sourceId = req.body.sourceId;

  const filterParams = await getFilterParams(req);
  const sourceCampaign = await Campaigns.findByPk(sourceId, {
    where: filterParams,
    include: [{
      model: CampaignNotifications,
      as: 'notifications',
    }],
    paranoid: false, // includes soft deleted (archived) campaigns
  });
  if (!sourceCampaign) {
    const id = sourceId;
    const err = new Error(req.i18n.t('campaigns.campaign_load_Error', { id }));
    err.status = 404;
    throw err;
  }

  // create new campaign
  let copiedCampaign;
  await db.sequelize.transaction(async (transact) => {
    const {
      type, duration, sequenced, reportFrequency, retrainingPeriod, objective, accountId, colorTheme,
      applyAssignmentChanges,
    } = sourceCampaign;
    const newCampaign = {
      name: `Copy of ${sourceCampaign.name}`,
      lifecycle: 'draft',
      status: 'scheduled',
      startDate: null,
      endDate: null,
      type,
      duration: (duration <= CAMPAIGNS_MAX_DURATION && duration > 0) ? duration : null,
      sequenced,
      reportFrequency,
      retrainingPeriod,
      objective,
      accountId,
      colorTheme,
      applyAssignmentChanges,
    };
    copiedCampaign = await Campaigns.create(newCampaign, { transaction: transact });

    // Create notifications
    for (const notification of sourceCampaign.notifications) {
      const {
        notificationType, notificationTiming, timingDays, notificationText, notificationCCManager,
        notificationSubject, notificationFrom, notificationFromName, notificationReplyTo,
      } = notification;
      const newNotification = {
        campaignId: copiedCampaign.id,
        notificationType,
        notificationTiming,
        timingDays,
        notificationText,
        notificationSubject,
        notificationFrom,
        notificationFromName,
        notificationReplyTo,
        notificationCCManager,
      };
      await CampaignNotifications.create(newNotification, { transaction: transact });
    }
  });

  // re-query to get everything
  const copiedCampaignFull = await Campaigns.findByPk(copiedCampaign.id, {
    where: filterParams,
    include: [
      {
        model: CampaignNotifications,
        as: 'notifications',
        required: false,
      },
    ],
    order: [
      [{ model: CampaignNotifications, as: 'notifications' }, 'updatedAt', 'DESC'],
    ],
    paranoid: false, // includes soft deleted (archived) campaigns
  });
  if (!copiedCampaignFull) {
    const err = new Error(req.i18n.t('campaigns.campaign_load_Error', { id: copiedCampaign.id }));
    err.status = 404;
    throw err;
  }
  return copiedCampaignFull;
};

/**
 * @openapi
 * /campaigns?$limit={limit}&$skip={skip}:
 *   get:
 *     summary: Get All Campaign List
 *     tags: [Campaigns]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         description: Number of records per page
 *         schema:
 *           type: integer
 *           example: 10
 *       - name: $skip
 *         in: query
 *         required: true
 *         description: Number of records to skip
 *         schema:
 *           type: integer
 *           example: 0
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records
 *                 limit:
 *                   type: integer
 *                   description: Records per page
 *                 skip:
 *                   type: integer
 *                   description: Number of records skipped
 *                 data:
 *                   type: array
 *                   description: Array of campaign objects
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/campaigns'
 *                       - type: object
 *                         properties:
 *                           summary:
 *                             $ref: '#/components/schemas/summary'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };

  const queryParams = restQueryToSequelize(req.query, defaults);
  queryParams.order = [[...queryParams.order]];

  const filterParams = await getFilterParams(req);
  const finalQuery = {
    ...queryParams,
    where: { ...queryParams.where, ...filterParams },
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  const countQuery = {
    where: _.pick(finalQuery, ['where']).where,
  };
  if (req.query.lifecycle && req.query.status) {
    const lifecycleStatus = [];
    // eslint-disable-next-line arrow-parens, array-callback-return
    req.query.lifecycle.map(o => { lifecycleStatus.push({ lifecycle: o, status: ['inProgress', 'scheduled'] }); });
    // eslint-disable-next-line arrow-parens
    req.query.status.map(o => { lifecycleStatus.push({ status: o }); });
    finalQuery.where[Op.or] = lifecycleStatus;
    delete finalQuery.where.lifecycle;
    delete finalQuery.where.status;
  } else if (req.query.lifecycle && !req.query.status) {
    finalQuery.where.status = ['inProgress', 'scheduled'];
  }

  try {
    const count = await Campaigns.count(countQuery);
    const campaigns = await Campaigns.findAll(finalQuery);

    // get and attach summary data
    const promises = campaigns.map((campaign) => {
      return includeCampaignSummary(campaign);
    });

    const summarys = await Promise.all(promises);
    const data = summarys.map((summary) => { return flattenItem(summary); });

    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /campaigns/{campaignId}:
 *   get:
 *     summary: Fetch Campaign Details Based On CampaignId
 *     tags: [Campaigns]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         description: Campaign ID of the existing campaign
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/campaigns'
 *                 - type: object
 *                   properties:
 *                     summary:
 *                       $ref: '#/components/schemas/summary'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.read = (req, res) => {
  res.json(flattenItem(req.campaign));
};


/**
 * @openapi
 * /campaigns:
 *   post:
 *     summary: Add New Campaign
 *     tags: [Campaigns]
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Test Campaign"
 *               type:
 *                 type: string
 *                 example: "scheduled"
 *               reportFrequency:
 *                 type: string
 *                 example: "weekly"
 *               applyAssignmentChanges:
 *                 type: string
 *                 example: "approval"
 *               sequenced:
 *                 type: boolean
 *                 example: false
 *               duration:
 *                 type: string
 *                 nullable: true
 *                 example: null
 *               startDate:
 *                 type: string
 *                 example: "2022-06-20"
 *               endDate:
 *                 type: string
 *                 example: "2022-06-25"
 *               lifecycle:
 *                 type: string
 *                 example: "draft"
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/campaigns'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */

/**
 * @openapi
 * /campaigns//: 
 *   post:
 *     summary: Add Duplicate Campaign
 *     tags: [Campaigns]
 *     security:
 *       - JWT: []
 *     requestBody:
 *       description: Suggested Duplicate Campaign Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sourceId:
 *                 type: integer
 *                 example: 5
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/campaigns'
 *       404:
 *         description: Not Found
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  try {
    let newCampaign;
    if (req.body.sourceId) {
      newCampaign = await duplicate(req);
    } else {
      const campaign = req.body;
      // automatically set account to logged-in user's account
      campaign.accountId = req.user.accountId;
      // only set restartDate if campaign is configured for reoccurence
      if (campaign.retrainingPeriod && campaign.retrainingPeriod > 0) {
        campaign.restartDate = calcRestartDate(req, campaign);
      }
      if (campaign.lifecycle && campaign.lifecycle === 'active') {
        campaign.deployedAt = Date.now();
        campaign.deployedById = req.user.id;
      }
      newCampaign = await Campaigns.create(campaign);
    }
    await recordCampaignLifecycleEvent(newCampaign, newCampaign.lifecycle, req.user.id, req.sessionId);
    res.json(newCampaign);
  } catch (err) {
    next(err);
  }
};

const validateStatusChange = ((req, oldStatus, newStatus) => {
  if (newStatus === 'withdrawn' && oldStatus === 'withdrawn') {
    const err = new Error(req.i18n.t('campaigns.already_withdrawn_Error'));
    err.status = 404;
    throw err;
  } else if ((newStatus === 'closed') &&
    (oldStatus !== 'scheduled' && oldStatus !== 'inProgress')) {
    const err = new Error(req.i18n.t('campaigns.close_Error'));
    err.status = 404;
    throw err;
  }
});

const validateLifecycleChange = ((req, oldLifecycle, newLifecycle) => {
  if (newLifecycle !== oldLifecycle) {
    // Don't allow started campaigns to go back to draft or review
    if ((oldLifecycle === 'active' || oldLifecycle === 'close') &&
      (newLifecycle === 'draft' || newLifecycle === 'review')) {
      const err = new Error(req.i18n.t('campaigns.already_started_Error'));
      err.status = 404;
      throw err;
    }
  }
});

const getCampaignById = async (req, id) => {
  const queryParams = {
    where: {
      id,
    },
  };
  const filterParams = await getFilterParams(req);
  const finalQuery = {
    where: { ...queryParams.where, ...filterParams },
    include: [
      {
        model: Users,
        as: 'deployedBy',
        required: false,
        include: [{ model: db.accountUsers, required: false }],
      },
      {
        model: Users,
        as: 'statusChangedBy',
        required: false,
        include: [{ model: db.accountUsers, required: false }],
      },
    ],
  };
  const campaign = await Campaigns.findOne(finalQuery);
  if (!campaign) {
    const err = new Error(req.i18n.t('campaigns.campaign_load_Error', { id }));
    err.status = 404;
    throw err;
  }
  if (campaign.deployedBy) {
    campaign.deployedBy = sanitizeUserForClient(campaign.deployedBy);
  }
  if (campaign.statusChangedBy) {
    campaign.statusChangedBy = sanitizeUserForClient(campaign.statusChangedBy);
  }
  const result = await includeCampaignSummary(campaign);
  return result;
};

// validate duration for open campaigns (can be expanded to other form values in the future)
const durationIsValid = (duration) => {
  if (!duration) {
    return false;
  }
  const number = Number(duration);
  if (isNotANumber(number)) {
    return false;
  }
  return number && number > 0 && number <= CAMPAIGNS_MAX_DURATION;
};

/**
 * @openapi
 * /campaigns/{campaignId}:
 *   patch:
 *     summary: Close Campaign
 *     tags: [Campaigns]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 example: 'closed'
 *             required:
 *               - status
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               allOf:
 *                 - $ref: '#/components/schemas/campaigns'
 *                 - type: object
 *                   properties:
 *                     summary:
 *                       $ref: '#/components/schemas/summary'
 *                     statusChangedBy:
 *                       $ref: '#/components/schemas/users'
 *                     deployedBy:
 *                       $ref: '#/components/schemas/users'
 *       404:
 *         description: Not Found
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */

/**
 * @openapi
 * /campaigns/{campaignId}/:
 *   patch:
 *     summary: Withdraw Campaign
 *     tags: [Campaigns]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       description: Suggested Fields Parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 example: 'withdrawn'
 *             required:
 *               - status
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               allOf:
 *                 - $ref: '#/components/schemas/campaigns'
 *                 - type: object
 *                   properties:
 *                     summary:
 *                       $ref: '#/components/schemas/summary'
 *                     statusChangedBy:
 *                       $ref: '#/components/schemas/users'
 *                     deployedBy:
 *                       $ref: '#/components/schemas/users'
 *       404:
 *         description: Not Found
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.patch = async (req, res, next) => {
  const originalCampaign = req.campaign;
  const modifiedCampaign = req.body;

  // wh-fronted creates the deploy, withdraw and close auditLogs
  let auditLogHandledByClient = false;
  if (modifiedCampaign && Object.keys(modifiedCampaign) && Object.keys(modifiedCampaign).length === 1 &&
    Object.keys(modifiedCampaign)[0] === 'lifecycle') {
    auditLogHandledByClient = true;
  }

  try {
    // audit log
    const logItems = [
      'name',
      'type',
      'lifecycle',
      'startDate',
      'status',
      'applyAssignmentChanges',
      'assignmentChangesApproved',
      'sequenced',
      'duration',
      'endDate',
    ];
    const startData = _.pick(originalCampaign, logItems);
    const logData = {
      feature: 'campaigns',
      object: 'campaigns',
      objectId: originalCampaign.id,
      originalData: {},
      updatedData: {},
    };

    validateLifecycleChange(req, originalCampaign.lifecycle, modifiedCampaign.lifecycle);
    // eslint-disable-next-line max-len
    if (modifiedCampaign.status && originalCampaign.status === 'inProgress' && modifiedCampaign.status === 'scheduled') {
      modifiedCampaign.status = originalCampaign.status;
    }
    validateStatusChange(req, originalCampaign.status, modifiedCampaign.status);
    if (modifiedCampaign.status && originalCampaign.status !== modifiedCampaign.status) {
      modifiedCampaign.statusChangedAt = Date.now();
      modifiedCampaign.statusChangedById = req.user.id;
    }
    if (modifiedCampaign.lifecycle
      && originalCampaign.lifecycle !== modifiedCampaign.lifecycle
      && modifiedCampaign.lifecycle === 'active') {
      modifiedCampaign.deployedAt = Date.now();
      modifiedCampaign.deployedById = req.user.id;
    }

    if (modifiedCampaign.status && (modifiedCampaign.status === 'withdrawn' || modifiedCampaign.status === 'closed')) {
      const campaignIds = [req.campaign.id];
      await deleteAllGroupChangesForCampaigns(campaignIds);
    }
    if (modifiedCampaign.status && modifiedCampaign.status === 'withdrawn') {
      modifiedCampaign.lifecycle = 'close';
      modifiedCampaign.endDate = Date.now();
      await withdrawCampaign(originalCampaign);
    } else {
      // only set restartDate if campaign is configured for reoccurence
      if (modifiedCampaign.retrainingPeriod && modifiedCampaign.retrainingPeriod > 0) {
        const candidate = _.merge({}, originalCampaign.get({ plain: true }), modifiedCampaign);
        modifiedCampaign.restartDate = calcRestartDate(req, candidate);
      }
      // validate duration for open campaigns
      // we fail silently if duration is invalid because the campaingns form calls .patch on every change
      // note that the client validation should not allow invalid duration values to be passed in the first place
      if (originalCampaign.type === 'open' && !durationIsValid(modifiedCampaign.duration)) {
        if (!modifiedCampaign.isUnitTestOnly) { // make an exception for unit tests which rely on invalid duration values
          delete modifiedCampaign.duration;
        }
      }
      // prevent duration from changing for active campaigns
      if (modifiedCampaign.lifecycle === 'active' || originalCampaign.lifecycle === 'active') {
        if (!modifiedCampaign.isUnitTestOnly) {
          delete modifiedCampaign.duration;
        }
      }
      if (modifiedCampaign.isUnitTestOnly) {
        delete modifiedCampaign.isUnitTestOnly;
      }
      // fetch unsent notifications
      const notifications = await CampaignNotifications.findAll({
        where: {
          notificationId: null,
          campaignId: originalCampaign.id,
        },
      });

      // update notifications that haven't been sent
      if (notifications) {
        const candidate = _.merge({}, originalCampaign.get({ plain: true }), modifiedCampaign);
        const promises = notifications.map((notification) => {
          const triggerDate = calcCampaignTriggerDate(candidate, notification);
          if (!datesEqual(notification.triggerDate, triggerDate)) {
            return notification.update({ triggerDate });
          }
          return Promise.resolve();
        });
        await Promise.all(promises);
      }
    }

    // update
    modifiedCampaign.accountId = req.user.accountId;
    const updatedCampaign = await originalCampaign.update(modifiedCampaign);

    // log lifecycle event
    const lifecycleUpdated = modifiedCampaign.lifecycle && (modifiedCampaign.lifecycle !== originalCampaign.lifecycle);
    if (lifecycleUpdated) {
      await recordCampaignLifecycleEvent(updatedCampaign, updatedCampaign.lifecycle, req.user.id, req.sessionId);
    }

    // audit log
    logData.action = `edit-${startData.lifecycle}-campaign`;
    if (!auditLogHandledByClient) {
      const endData = _.pick(updatedCampaign, logItems);
      Object.keys(startData).forEach((key) => {
        if (key === 'startDate' || key === 'endDate') {
          if (format(startData[key], 'MM/DD/YYYY') !== format(endData[key], 'MM/DD/YYYY')) {
            logData.originalData[key] = startData && startData[key] ? format(startData[key], 'MM/DD/YYYY') : 'null';
            logData.updatedData[key] = endData && endData[key] ? format(endData[key], 'MM/DD/YYYY') : 'null';
          }
        } else if (startData[key] !== endData[key]) {
          logData.originalData[key] = startData && startData[key] ? startData[key].toString() : 'null';
          logData.updatedData[key] = endData && endData[key] ? endData[key].toString() : 'null';
        }
      });
      if (Object.keys(logData.updatedData).length > 0) {
        await createAuditLog(logData, req);
      }
    }
    if (originalCampaign.lifecycle === 'active') {
      if (process.env.NODE_ENV !== 'test' && process.env.NODE_ENV !== 'development') {
        const participants = originalCampaign.summary.participants;
        // eslint-disable-next-line max-len
        if (moment(originalCampaign.startDate).utc().format('YYYY-MM-DD') === moment(originalCampaign.deployedAt).utc().format('YYYY-MM-DD')
          && originalCampaign.deployByLambda === false && participants < 700) {
          await originalCampaign.update({ deployByLambda: true });
          const params = {
            // eslint-disable-next-line max-len
            FunctionName: process.env.NODE_ENV === 'stage' ? 'stage-campaign-activation-sweeper' : 'campaign-activation-sweeper',
            Payload: JSON.stringify({ campaignId: originalCampaign.id }),
            InvocationType: 'Event',
          };
          try {
            await lambdaClient.send(new InvokeCommand(params));
            logger.info(`Lambda invoked for campaign id: ${originalCampaign.id}`);
          } catch (err) {
            await originalCampaign.update({ deployByLambda: false });
            logger.error(`Could not invoke Lambda for campaign id: ${originalCampaign.id}`);
            logger.error(err);
          }
        }
      }
    }
    // reload campaign
    const updatedCampaignWithSummary = await getCampaignById(req, updatedCampaign.id);
    res.json(flattenItem(updatedCampaignWithSummary));
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /campaigns/{campaignId}:
 *   delete:
 *     summary: Delete Campaign
 *     tags: [Campaigns]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/campaigns'
 *       404:
 *         description: Not Found
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  const campaign = req.campaign;
  try {
    // clean up related data for campaign
    await deleteCampaign(campaign);
    const data = await campaign.destroy();
    res.json(data);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the campaign when an id is passed in the route
module.exports.campaignById = async function (req, res, next, id) {
  try {
    req.campaign = await getCampaignById(req, id);
    next();
  } catch (err) {
    next(err);
  }
};

// API for detail view of campaign group assignment changes
module.exports.listCampaignGroupAssignmentChanges = async (req, res, next) => {
  const queryParams = {
    where: {
      campaignId: req.campaign.id,
      processedDate: null,
    },
    order: [['updatedAt', 'DESC']],
  };

  const groupRuleUserFields = [];
  const groupRuleCustomFields = [];
  const userTableDefaultAttributes = ['id', 'firstName', 'lastName', 'email'];
  let groupFilter = null;
  let group = null;
  if (req.query.groupId) {
    groupFilter = { groupId: req.query.groupId };
    group = await Groups.findOne({
      where: { id: req.query.groupId },
      include: [{
        model: GroupRules,
        as: 'groupRules',
        include: [{
          model: GroupRuleValues,
          as: 'values',
        }],
      }],
    });

    // Client table view needs to display fields that are included in the rule
    // Here we ensure that we are including those fields
    if (group && group.groupRules) {
      for (let ruleIdx = 0; ruleIdx < group.groupRules.length; ruleIdx++) {
        if (Users.rawAttributes[group.groupRules[ruleIdx].field] !== undefined) {
          if (!userTableDefaultAttributes.includes(group.groupRules[ruleIdx].field)) {
            groupRuleUserFields.push(group.groupRules[ruleIdx].field);
          }
        } else {
          groupRuleCustomFields.push(group.groupRules[ruleIdx].field);
        }
      }
    }
  }

  const userTableAttributes = [...userTableDefaultAttributes, ...groupRuleUserFields];
  const finalQuery = {
    ...queryParams,
    where: { ...queryParams.where, ...groupFilter },
    include: [{
      model: Groups,
      as: 'group',
      attributes: ['id', 'name'],
    },
    {
      model: Users,
      as: 'user',
      paranoid: false,
      attributes: userTableAttributes,
      include: [{
        model: UserAccountFields,
        attributes: ['value'],
        include: {
          model: AccountFields,
          required: false,
          attributes: ['fieldName'],
          where: {
            accountId: req.user.accountId,
          },
        },
      }],
    }],
  };

  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  const countQuery = {
    where: _.pick(finalQuery, ['where']).where,
  };

  try {
    const count = await CampaignGroupChanges.count(countQuery);
    const campaignGroupChanges = await CampaignGroupChanges.findAll(finalQuery);

    let totalAdded = 0;
    let totalRemoved = 0;
    const changes = campaignGroupChanges.map((change) => {
      if (change.action === 'add') {
        totalAdded += 1;
      } else if (change.action === 'remove') {
        totalRemoved += 1;
      }
      return change;
    });

    if (groupRuleCustomFields.length > 0) {
      const accountId = req.user.accountId;
      const accountFields = await AccountFields.findAll({
        attributes: ['fieldName'],
        where: {
          accountId,
        },
        raw: true,
      });

      // First gather the custom fields included in the rule, before decrypting the encrypted ones.
      const initializeCustomFields = {};
      accountFields.forEach((row) => {
        if (groupRuleCustomFields.includes(row.fieldName)) {
          initializeCustomFields[row.fieldName] = null;
        }
      });
    }

    const data = changes.map((change) => { return flattenItem(change); });
    res.json({ total: count, totalAdded, totalRemoved, group, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /campaigns/{campaignId}/campaignGroupAssignmentChangesSummary:
 *   get:
 *     summary: Campaign Summary By Group Assignment Changes
 *     tags:
 *       - Campaigns
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: required campaignId
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalCount:
 *                   type: integer
 *                   description: total number of records
 *                 groups:
 *                   type: array
 *                   description: Array of group data object
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/groups'
 *                       - type: object
 *                         properties:
 *                           roleGroups:
 *                             $ref: '#/components/schemas/roleGroups'
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */

// API for summary/"by group" view of campaign group assignment changes
module.exports.listCampaignGroupAssignmentChangesSummary = async (req, res, next) => {
  try {
    const where = { campaignId: req.campaign.id, processedDate: null };
    // Query that includes the change count per group.
    const groupAssignmentChangesSummary = await CampaignGroupChanges.findAll({
      attributes: ['groupId', [db.sequelize.fn('count', db.sequelize.col('campaignGroupChanges.id')), 'changeCount']],
      group: ['campaignGroupChanges.groupId'],
      where,
      include: [{
        model: Groups,
        as: 'group',
        attributes: ['id', 'name'],
      }],
    });

    const totalCount = await CampaignGroupChanges.count({ where });
    res.json({ totalCount, groups: groupAssignmentChangesSummary });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /campaigns/reportList:
 *   get:
 *     summary: Campaigns Report List
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: total number of records.
 *                 data:
 *                   type: array
 *                   description: Array of report data object.
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         description: Campaign Id.
 *                       name:
 *                         type: string
 *                         description: Campaign name.
 *                       deployDate:
 *                         type: string
 *                         description: Deploy Date.
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 */

// API for determining list of campaigns eligible for reporting
module.exports.reportList = async (req, res, next) => {
  const accountId = req.query.accountId || (req.user ? req.user.accountId : null);
  const search = req.query.$search;
  const skip = req.query.$skip;
  const limit = req.query.$limit;
  const rCampaigns = db.reportingModels.campaigns;
  try {
    const query = {
      where: {
        accountId,
        status: {
          [Op.not]: 'scheduled',
        },
        lifecycle: {
          [Op.or]: ['active', 'close'],
        },
      },
      order: [['startDate', 'DESC']],
    };
    if (search) {
      query.where.name = { [Op.like]: `%${search}%` };
    }
    query.limit = limit
      ? parseInt(limit)
      : config.paginate.max;
    query.offset = skip !== undefined
      ? parseInt(skip)
      : 0;

    const campaignList = await rCampaigns.findAll(query);

    const reportList = campaignList.map((campaign) => {
      return {
        id: campaign.id,
        name: campaign.name,
        deployDate: format(campaign.startDate, 'MM/DD/YYYY'),
      };
    });
    res.json({ total: reportList.length, data: reportList });
  } catch (err) {
    next(err);
  }
};

// API to validate campaign
module.exports.validate = async (req, res, next) => {
  const campaign = req.campaign;
  const accountId = campaign.accountId;
  try {
    // get campaign items
    const campaignItems = await CampaignItems.findAll({
      where: {
        campaignId: campaign.id,
      },
      include: [{
        model: Programs,
        include: [{
          model: Lessons,
        }],
      }, {
        model: Lessons,
      }],
    });

    const lessonMap = new Map();
    // gather unique lessons in campaign
    for (const item of campaignItems) {
      if (item.itemType === 'program' && item.program && item.program.lessons) {
        const program = item.program;
        for (const lesson of program.lessons) {
          if (!lessonMap.has(lesson.id)) {
            lessonMap.set(lesson.id, { pId: program.id, pName: program.name, lId: lesson.id, lName: lesson.title });
          }
        }
      } else if (item.itemType === 'lesson' && item.lesson) {
        if (!lessonMap.has(item.lesson.id)) {
          lessonMap.set(item.lesson.id, { lId: item.lesson.id, lName: item.lesson.title });
        }
      }
    }

    // for lesson in campaign, check for misconfigured policy cards
    const errorList = [];
    const lessonIter = lessonMap.keys();
    let lessonEntry = lessonIter.next();
    while (!lessonEntry.done) {
      const key = lessonEntry.value;
      const lessonInfo = lessonMap.get(key);
      const lessonCardBindings = await LessonLessonCards.findAll({
        where: {
          lessonId: key,
        },
        include: [{
          model: LessonCards,
          where: {
            cardType: 'policyAcknowledgement',
          },
          include: [{
            model: AccountLessonCards,
            where: {
              accountId,
            },
            include: [{
              model: Files,
              as: 'file',
              required: false,
            }],
            required: false,
          }, {
            model: Files,
            as: 'images',
            required: false,
          }],
          required: true, // only want policyAcknowledgement cards
        }],
      });
      // iterate over policyAcknowledgement cards in lesson
      for (const cardBinding of lessonCardBindings) {
        let card = cardBinding.lessonCard;
        let customCard = null;
        // take into account card customization for account
        if (card.accountLessonCards.length > 0) {
          customCard = card.accountLessonCards[0];
          if (customCard.file) {
            // move file to the right place
            customCard.images = { en: customCard.file.dataValues.path };
          }
          card = configurePolicyCardForAccount(card, customCard, accountId, true);
        }
        if (!card.policyLink && card.images.length === 0) {
          // record error
          errorList.push(lessonInfo);
        } else if (card.images.length !== 0 && !customCard?.link) {
          const imageName = Array.isArray(card.images) ? card.images[0].path : card.images[Object.keys(card.images)[0]];
          if (imageName && imageName.includes('Policy-Placeholder.')) {
            errorList.push(lessonInfo);
          }
        }
      }
      lessonEntry = lessonIter.next();
    }

    res.json({ total: errorList.length, data: errorList });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * components:
 *   schemas:
 *     campaigns:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         lifecycle:
 *           type: string
 *         type:
 *           type: boolean
 *         duration:
 *           type: integer
 *         sequenced:
 *           type: boolean
 *         reportFrequency:
 *           type: string
 *         objective:
 *           type: string
 *         status:
 *           type: string
 *         deletedAt:
 *           type: string
 *         colorTheme:
 *           type: string
 *         accountId:
 *           type: integer
 *         reoccurenceIteration:
 *           type: integer
 *         restartDate:
 *           type: string
 *         applyAssignmentChanges:
 *           type: string
 *         assignmentChangesApproved:
 *           type: boolean
 *         deployedAt:
 *           type: string
 *         deployedById:
 *           type: integer
 *         statusChangedAt:
 *           type: string
 *         descriptionAlignment:
 *           type: string
 *         statusChangedById:
 *           type: integer
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 *         startDate:
 *           type: string
 *         endDate:
 *           type: string
 *
 *     summary:
 *       type: object
 *       properties:
 *         items:
 *           type: integer
 *         groups:
 *           type: integer
 *         participants:
 *           type: integer
 *         completed:
 *           type: number
 *         pastDueCount:
 *           type: integer
 *         groupChangesCount:
 *           type: integer
 *
 *     campaignNotifications:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         campaignId:
 *           type: integer
 *         notificationId:
 *           type: integer
 *         notificationType:
 *           type: string
 *         notificationTiming:
 *           type: string
 *         timingDays:
 *           type: integer
 *         notificationText:
 *           type: string
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 *         triggerDate:
 *           type: string
 *         notificationSubject:
 *           type: string
 *         notificationFrom:
 *           type: string
 *         notificationReplyTo:
 *           type: string
 *         notificationFromName:
 *           type: string
 *         notificationCCManager:
 *           type: boolean
 */