const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { getAllowedPermissions } = require('../services/acl/acl');

const ScormProgram = db.scormPrograms;
const Events = db.events;

const restSortToSequelize = (restSort) => {
  return _.map(restSort, (value, key) => {
    return [key, value === '-1' ? 'DESC' : 'ASC'];
  });
};

const restQueryToSequelize = (query, defaults) => {
  const whereClause = _.omit(query, ['$limit', '$skip', '$sort']);

  const newQuery = {
    ...defaults,
  };
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  return newQuery;
};

module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };
  const queryParams = restQueryToSequelize(req.query, defaults);

  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };

  try {
    const count = await ScormProgram.count();
    const data = await ScormProgram.findAll(queryParams);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = (req, res) => {
  res.json(req.scormProgram);
};

module.exports.create = async (req, res, next) => {
  const text = req.body.text;

  try {
    const newScormProgram = await ScormProgram.create({ text });
    res.json(newScormProgram);
  } catch (err) {
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  const scormProgram = req.scormProgram;

  try {
    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'scormPrograms', req.tokenPayload);
    if (!allowedPermissions.scormPrograms.includes('update')) {
      const err = new Error(req.i18n.t('scormPrograms.scormProgram_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    const updatedScormProgram = await scormProgram.update(req.body);
    res.json(updatedScormProgram);
  } catch (err) {
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  const scormProgram = req.scormProgram;

  try {
    const allowedPermissions = await getAllowedPermissions(req, req.user.id, 'scormPrograms', req.tokenPayload);
    if (!allowedPermissions.scormPrograms.includes('update')) {
      const err = new Error(req.i18n.t('scormPrograms.scormProgram_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    const data = await scormProgram.destroy();
    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.setComplete = async (req, res, next) => {
  const scormProgram = req.scormProgram;
  let updatedScormProgram = scormProgram;
  try {
    if (!scormProgram.completionDate) {
      updatedScormProgram = await scormProgram.update({ completionDate: Date.now() });

      if (!req.body.accountId) {
        req.body.accountId = req.user.accountId;
      }

      // emit event
      await Events.create({
        action: true,
        sourceLifecycle: null,
        type: 'scormProgramCompletion',
        userId: req.user.id,
        trackableId: scormProgram.resourceId,
        trackableType: 'programs',
        trackableData: {
          scormProgram,
          requestingUser: req.user.id,
        },
        accountId: req.user.accountId,
        sessionId: req.sessionId,
      });
    }
    res.json(updatedScormProgram);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the scormProgram when an id is passed in the route
module.exports.scormProgramById = async function (req, res, next, id) {
  try {
    const scormProgram = await ScormProgram.findByPk(id);
    if (!scormProgram || scormProgram.userId !== req.user.id) {
      const err = new Error(req.i18n.t('scormPrograms.scormProgram_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.scormProgram = scormProgram;
    next();
  } catch (err) {
    next(err);
  }
};

module.exports.scormProgramByProgramId = async (req, res, next, id) => {
  try {
    const userId = req.user.id;
    const scormProgram = await ScormProgram.findOne({ where: { userId, resourceId: id, model: 'program' } });
    if (!scormProgram) {
      const err = new Error(req.i18n.t('scormPrograms.scormProgram_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.scormProgram = scormProgram;
    next();
  } catch (err) {
    next(err);
  }
};
