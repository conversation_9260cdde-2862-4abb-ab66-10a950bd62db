const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const userUtils = require('../services/utils/userUtils');
const { genStaticUserCSV } = require('../services/utils/reportingUtils');
const { addGroupMember, removeGroupMember, getCsvFileName } = require('../services/utils/groupUtils');
const { getGroupParents } = require('../services/utils/campaignUtils');

const Groups = db.groups;
const Users = db.users;
const AccountUser = db.accountUsers;
const UserGroups = db.userGroups;
const Op = db.Sequelize.Op;

function flattenMember(member) {
  const flattenedMember = member.get({ plain: true });
  if (flattenedMember.memberType === 'user') {
    delete flattenedMember.group;
  } else {
    delete flattenedMember.user;
  }
  return flattenedMember;
}

function flattenMembers(members) {
  return members.map(member => flattenMember(member));
}

async function lookupMember(id) {
  const queryParams = {
    where: {
      id,
    },
    include: [
      {
        model: Groups,
      }, {
        model: Users,
      },
    ],
  };
  return UserGroups.findOne(queryParams);
}

async function lookupMembers(ids) {
  const queryParams = {
    where: {
      id: {
        [Op.in]: ids,
      },
    },
    include: [
      {
        model: Groups,
      }, {
        model: Users,
      },
    ],
  };
  return UserGroups.findAll(queryParams);
}

async function circularMembershipCheck(groupId, memberId) {
  let check = false;
  const parents = await getGroupParents(groupId);
  if (parents) {
    check = parents.has(memberId);
  }
  return check;
}

// adds incoming members array to group
async function addGroupMembers(req, group, newMembers) {
  const newUserIds = [];
  const newGroupIds = [];
  // validate new set of members
  for (const member of newMembers) {
    if (member.memberType === 'group') {
      if (member.memberId === group.id) {
        const id = group.id;
        const err = new Error(req.i18n.t('groups.cannot_be_member_Error', { id }));
        err.status = 400;
        throw err;
      }
      const isCircular = await circularMembershipCheck(group.id, member.memberId);
      if (isCircular) {
        const id = group.id;
        const err = new Error(req.i18n.t('groups.circular_reference_Error', { id }));
        err.status = 400;
        throw err;
      }
      newGroupIds.push(member.memberId);
    } else {
      newUserIds.push(member.memberId);
    }
  }
  const preexistingMembers = await UserGroups.findAll({
    where: {
      groupId: group.id,
    },
  });
  const preexistingUsers = new Map();
  const preexistingGroups = new Map();

  // split preexisting into user members and group members
  preexistingMembers.forEach((binding) => {
    if (binding.memberType === 'user') {
      if (!preexistingUsers.has(binding.id)) {
        preexistingUsers.set(binding.memberId, binding);
      }
    } else if (!preexistingGroups.has(binding.id)) {
      preexistingGroups.set(binding.memberId, binding);
    }
  });

  // identify users and groups that are not in preexisting
  const addedUserIds = newUserIds.filter((id) => {
    return !preexistingUsers.has(id);
  });

  const addedGroupIds = newGroupIds.filter((id) => {
    return !preexistingGroups.has(id);
  });

  const activeUsers = await userUtils.getActiveUsers(addedUserIds);
  const activeUserIds = activeUsers.map((user) => {
    return user.id;
  });
  // add new users
  const addedUsers = activeUserIds.map((id) => {
    return addGroupMember({
      groupId: group.id,
      memberId: id,
      memberType: 'user',
    });
  });

  // add new groups
  const addedGroups = addedGroupIds.map((id) => {
    return addGroupMember({
      groupId: group.id,
      memberId: id,
      memberType: 'group',
    });
  });

  const data = await Promise.all([...addedUsers, ...addedGroups]);
  return data;
}

/**
 * @openapi
 * /groups/{groupId}/members:
 *   get:
 *     summary: Group Members List
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: groupId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Group ID
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Limit (integer) for number of records
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *         description: Skip (integer) for number of records
 *     responses:
 *       '200':
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records.
 *                 limit:
 *                   type: integer
 *                   description: Limit records per page.
 *                 data:
 *                   type: array
 *                   description: Array of userGroups object.
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/userGroups'
 *                       - type: object
 *                         properties:
 *                           user:
 *                             $ref: '#/components/schemas/users'
 *       '401':
 *         description: Unauthorized
 *       '400':
 *         description: Bad Request
 *       '5XX':
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  const group = req.group;
  const isStaticGroup = req.query.isStaticGroup === 'true';
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
    where: {
      groupId: group.id,
      ...(isStaticGroup ? { memberType: 'user' } : {})
    },
    include: [
      {
        model: Groups,
      }, {
        model: Users,
      },
    ],
  };
  const queryParams = userUtils.restStaticGroupQueryToSequelize(req.query, defaults); 

  queryParams.order = [[...queryParams.order]];

  const finalQuery = {
    ...queryParams,
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  try {
    const count = await UserGroups.count({ where: {
      groupId: group.id,
      ...(isStaticGroup ? { memberType: 'user' } : {})
    } });
    const memberBindings = await UserGroups.findAll(finalQuery);
    const data = memberBindings.map((member) => {
      return flattenMember(member);
    });
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res) => {
  res.json(flattenMember(req.memberBinding));
};

/**
 * @openapi
 * /groups/{groupId}/members:
 *   post:
 *     summary: Add Members To Group
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: groupId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               members:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     memberType:
 *                       type: string
 *                       example: user
 *                     memberId:
 *                       type: integer
 *                       example: 12
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/userGroups'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

/**
 * @openapi
 * /groups/{groupId}/members/:
 *   post:
 *     summary: Add Group Name To Group
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: groupId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       description: Suggested Fields parameters
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               members:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     memberType:
 *                       type: string
 *                       example: group
 *                     memberId:
 *                       type: integer
 *                       example: 12
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/userGroups'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  const group = req.group;
  const membership = {
    groupId: group.id,
    memberId: req.body.memberId,
    memberType: req.body.memberType,
  };
  const newMembers = req.body.members;
  try {
    if (group.groupType === 'dynamic') {
      const id = group.id;
      const err = new Error(req.i18n.t('groups.cannot_add_to_dynamic_Error', { id }));
      err.status = 400;
      throw err;
    }
    if (!_.isNil(newMembers)) {
      const addedMembers = await addGroupMembers(req, group, newMembers);
      const addedMemberIds = addedMembers.map(member => member.id);
      const reloadedMembers = await lookupMembers(addedMemberIds);
      res.json(flattenMembers(reloadedMembers));
    } else {
      if (membership.memberId === group.id && membership.memberType === 'group') {
        const id = group.id;
        const err = new Error(req.i18n.t('groups.cannot_be_member_Error', { id }));
        err.status = 400;
        throw err;
      }
      const oldBinding = await UserGroups.findOne({
        where:
          {
            groupId: membership.groupId,
            memberId: membership.memberId,
            memberType: membership.memberType,
          },
      });

      if (oldBinding) {
        const id = membership.groupId;
        const err = new Error(req.i18n.t('groups.member_already_exists_Error', { id }));
        err.status = 400;
        throw err;
      }

      if (membership.memberType === 'group') { // only groups
        const isCircular = await circularMembershipCheck(membership.groupId, membership.memberId);
        if (isCircular) {
          const id = group.id;
          const err = new Error(req.i18n.t('groups.circular_reference_Error', { id }));
          err.status = 400;
          throw err;
        }
      }
      const newMember = await addGroupMember(membership);
      const addedMember = await lookupMember(newMember.id);
      res.json(await flattenMember(addedMember));
    }
  } catch (err) {
    next(err);
  }
};
/**
 * @openapi
 * /groups/{groupId}/members/{userGroupsId}:
 *   delete:
 *     summary: Delete Member From Group
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: groupId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: userGroupsId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/userGroups'
 *                 - type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/users'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

/**
 * @openapi
 * /groups/{groupId}:
 *   delete:
 *     summary: Delete Group.
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: groupId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation.
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/groups'
 *                 - type: object
 *                   properties:
 *                     roles:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/roles'
 *                           - type: object
 *                             properties:
 *                               groupRoles:
 *                                 $ref: '#/components/schemas/groupRoles'
 *                     userGroups:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/userGroups'
 *                           - type: object
 *                             properties:
 *                               user:
 *                                 $ref: '#/components/schemas/users'
 *                     groupRules:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/groupRules'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  const group = req.group;
  const memberBinding = req.memberBinding;
  try {
    if (group.groupType === 'dynamic') {
      const id = group.id;
      const err = new Error(req.i18n.t('groups.cannot_delete_from_dynamic_Error', { id }));
      err.status = 400;
      throw err;
    }
    const deletedMember = await removeGroupMember(memberBinding);
    let repo = flattenMember(deletedMember);
    const count = await UserGroups.count({ where: {
      groupId: group.id,
      memberType: 'user' 
    } });
    repo.userCount = count;
    res.json(repo);
  } catch (err) {
    next(err);
  }
}; 

module.exports.generateCSV = async (req, res, next) => {
  const group = req.group;
  const accountId = req.user.accountId;
  const reportColumns = ['firstName', 'lastName', 'email', 'employeeId', 'createdAt'];
  const timeZone = req.query.timeZone;
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
    where: {
      groupId: group.id,
      memberType: 'user', 
    },
    include: [
      {
        model: Groups,
      }, {
        model: Users,
        attributes: ['id', 'firstName', 'lastName', 'email', 'employeeId'],
      },
    ],
  };
  const queryParams = userUtils.restStaticGroupQueryToSequelize(req.query, defaults);
  queryParams.accountId = accountId;
  queryParams.order = [[...queryParams.order]];

  const finalQuery = {
    ...queryParams,
  }; 
  try {
    const filename = getCsvFileName(group?.name);
    const count = await UserGroups.count({ where: {
      groupId: group.id,
      memberType: 'user',
    } });
    const memberBindings = await UserGroups.findAll(finalQuery);
    const data = memberBindings.map((member) => {
      return flattenMember(member);
    });
    await genStaticUserCSV(reportColumns, queryParams, filename, res, timeZone, {
      data: data,
    })(accountId); 
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /groups/{groupId}/findMembers:
 *   get:
 *     summary: Search users from the account who are not in this group.
 *     tags:
 *       - Groups
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: groupId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *         description: Group ID
 *       - name: search
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           example: 'test'
 *         description: (String) ex. name, email or employeeId
 *     responses:
 *       '200':
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: Array of userGroups object.
 *                   items:
 *                    type: object
 *                    properties:
 *                      id:
 *                        type: integer
 *                      firstName:
 *                        type: string 
 *                      lastName:
 *                        type: string
 *                      email:
 *                        type: string 
 *                      employeeId:
 *                        type: string 
 *       '401':
 *         description: Unauthorized
 *       '400':
 *         description: Bad Request
 *       '5XX':
 *         description: Unexpected error
 */

module.exports.search = async (req, res, next) => {
  try {
    const groupId = req.group?.id;
    const accountId = req.user?.accountId;
    const search = (req.query.search || '').trim();

    const excludedIds = await UserGroups.findAll({
      attributes: ['memberId'],
      where: {
        groupId,
        memberType: 'user'
      },
      raw: true
    }).then(rows => rows.map(row => row.memberId));

    const searchWords = search ? search.split(/\s+/) : [];
    const searchConditions = searchWords.length > 0
      ? searchWords.map(word => ({
          [Op.or]: [
            { firstName: { [Op.like]: `%${word}%` } },
            { lastName: { [Op.like]: `%${word}%` } },
            { email: { [Op.like]: `%${word}%` } },
            { employeeId: { [Op.like]: `%${word}%` } }
          ]
        }))
      : [];

    // Query Users
    const users = await Users.findAll({
      limit: 100,
      offset: 0,
      attributes: ['id', 'firstName', 'lastName', 'email', 'employeeId'],
      where: {
        [Op.and]: [
          ...searchConditions,
          { id: { [Op.notIn]: excludedIds } },
          { leaveStartedAt: null },
        ]
      },
      include: [{
        model: AccountUser,
        attributes: [],
        where: { accountId }
      }],
      order: [
        ['lastName', 'ASC'],
        ['firstName', 'ASC']
      ],
      raw: true
    });

    // Step 4: Respond
    res.json({ data: users });
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve a specific group member when an id is passed in the route
module.exports.memberById = async function (req, res, next, id) {
  try {
    const memberBinding = await lookupMember(id);
    if (!memberBinding) {
      const err = new Error(req.i18n.t('groups.group_binding_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.memberBinding = memberBinding;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * components:
 *   schemas:
 *     groups:
 *     roleGroups:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         roleId:
 *           type: integer
 *         groupId:
 *           type: integer
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

