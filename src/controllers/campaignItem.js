const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { createAuditLog } = require('./auditLogs');
const { restQueryToSequelize } = require('../services/utils/userUtils');
const { refreshCampaignOrder, campaignItemRemoved, campaignItemAdded,
  lookupCampaignItem, lookupCampaignItems, getCampaignGroupForContent } = require('../services/utils/campaignUtils');
const { configureProgramForAccount } = require('../services/utils/programUtils');
const { configureLessonForAccount } = require('../services/utils/lessonUtils');

const CampaignItem = db.campaignItem;
const AccountPrograms = db.accountPrograms;
const AccountLessons = db.accountLessons;
const Accounts = db.accounts;
const GroupAssignments = db.groupAssignments;
const Op = db.Sequelize.Op;

function flattenItem(itemBinding) {
  const item = itemBinding.item;
  const flattenedBinding = itemBinding.get({ plain: true });
  return Object.assign(flattenedBinding, { item });
}

// attaches refered item to itemBinding
async function lookupItem(itemBinding) {
  const item = await lookupCampaignItem(itemBinding);
  return flattenItem(item);
}

// returns ordered list of items in campaign
async function lookupItems(campaignId) {
  const items = await lookupCampaignItems(campaignId);
  return items.map((binding) => {
    return flattenItem(binding);
  });
}

// update (1-based) positions of items in campaign starting at (zero-based) idx
// to move items down one spot
async function moveItemsDown(campaignId, idx1, idx2) {
  if (idx1 < idx2) {
    await db.sequelize.query(
      'UPDATE `campaignItem` SET position = position + 1 WHERE campaignId = ? AND position >= ? AND position < ?',
      {
        replacements: [campaignId, idx1, idx2],
        type: db.sequelize.QueryTypes.UPDATE,
        raw: true,
      },
    );
  }
}

async function moveItemsUp(campaignId, idx1, idx2) {
  if (idx1 < idx2) {
    await db.sequelize.query(
      'UPDATE `campaignItem` SET position = position - 1 WHERE campaignId = ? AND position > ? AND position <= ?',
      {
        replacements: [campaignId, idx1, idx2],
        type: db.sequelize.QueryTypes.UPDATE,
        raw: true,
      },
    );
  }
}

/**
 * @openapi
 * /campaigns/{campaignId}/items?$limit={limit}&$skip={skip}:
 *   get:
 *     summary: Campaign Items For Given CampaignId
 *     tags:
 *       - Campaigns
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *         description: Limit for number of records
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *         description: Number of records to skip
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       campaignId:
 *                         type: integer
 *                       itemId:
 *                         type: integer
 *                       itemType:
 *                         type: string
 *                       position:
 *                         type: integer
 *                       createdAt:
 *                         type: string
 *                       updatedAt:
 *                         type: string
 *                       item:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           resourceId:
 *                             type: integer
 *                           published:
 *                             type: boolean
 *                           title:
 *                             type: string
 *                           internalName:
 *                             type: string
 *                           description:
 *                             type: string
 *                           lifecycle:
 *                             type: string
 *                           userId:
 *                             type: integer
 *                           timeTracking:
 *                             type: string
 *                           requiredMinutes:
 *                             type: string
 *                           createdAt:
 *                             type: string
 *                           updatedAt:
 *                             type: string
 *                           deletedAt:
 *                             type: string
 *                           lessons:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/lessons'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  let campaignIds = req.params.campaignId;
  if (campaignIds) {
    campaignIds = campaignIds.split(',');
  }
  const defaults = {
    order: [['position', 'ASC']],
    limit: config.paginate.default,
    offset: 0,
    where: { campaignId: { [Op.in]: campaignIds } },
  };
  const queryParams = restQueryToSequelize(req.query, defaults);
  queryParams.order = [[...queryParams.order]];

  const finalQuery = {
    ...queryParams,
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };

  try {
    const count = await CampaignItem.count({ where: { campaignId: { [Op.in]: campaignIds } } });
    const itemBindings = await CampaignItem.findAll(finalQuery);
    const promises = [];
    // look up items associated with campaign item bindings
    itemBindings.forEach((binding) => {
      promises.push(lookupItem(binding));
    });
    const data = await Promise.all(promises);
    if (req.user && req.user.accountId) {
      const account = await Accounts.findByPk(req.user.accountId);
      if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
        for (const nextItem of data) {
          if (nextItem.itemType === 'program') {
            const accountProgram = await AccountPrograms.findOne({ where: { accountId: req.user.accountId,
              programId: nextItem.itemId } });
            if (accountProgram) {
              nextItem.item = configureProgramForAccount(nextItem.item, accountProgram);
            }
            for (let nextLesson of nextItem.item.lessons) {
              const accountLesson = await AccountLessons.findOne({ where: { accountId: req.user.accountId,
                lessonId: nextLesson.id } });
              if (accountLesson) {
                nextLesson = configureLessonForAccount(nextLesson, accountLesson);
              }
            }
          } else if (nextItem.itemType === 'lesson') {
            const accountLesson = await AccountLessons.findOne({ where: { accountId: req.user.accountId,
              lessonId: nextItem.itemId } });
            if (accountLesson) {
              nextItem.item = configureLessonForAccount(nextItem.item, accountLesson);
            }
          }
        }
      }
    }
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res) => {
  const data = await lookupItem(req.itemBinding);
  res.json(data);
};

module.exports.create = async (req, res, next) => {
  const newBinding = {
    campaignId: req.campaign.id,
    itemId: req.body.itemId,
    itemType: req.body.itemType,
    position: req.body.position,
  };
  try {
    const oldBinding = await CampaignItem.findOne({
      where:
        {
          campaignId: newBinding.campaignId,
          itemId: newBinding.itemId,
          itemType: newBinding.itemType,
        },
    });

    if (oldBinding) {
      const campaignId = newBinding.campaignId;
      const err = new Error(req.i18n.t('campaigns.item_already_exists_Error', { campaignId }));
      err.status = 400;
      throw err;
    }

    const oldItems = await lookupItems(newBinding.campaignId);

    // figure out where to stick the newbie
    if (_.isNil(newBinding.position) || newBinding.position > oldItems.length) {
      newBinding.position = oldItems.length + 1;
    } else if (newBinding.position < 1) {
      newBinding.position = 1;
    }

    if (newBinding.position < oldItems.length) {
      await moveItemsDown(req.campaign.id, newBinding.position, oldItems.length + 1);
    }
    const createdBinding = await CampaignItem.create(newBinding);
    await campaignItemAdded(createdBinding);
    const createdItem = await lookupItem(createdBinding);
    const logData = {
      action: 'draft-campaign-add-item',
      feature: 'campaigns',
      object: 'campaigns',
      objectId: createdItem.campaignId,
      childObject: 'campaignItem',
      childObjectId: createdItem.id,
      // eslint-disable-next-line max-len
      updatedData: { campaignItemId: createdItem.id, type: createdItem.itemType, itemId: createdItem.itemId, position: createdItem.position },
    };
    await createAuditLog(logData, req);
    res.json(createdItem);
  } catch (err) {
    next(err);
  }
};

module.exports.patch = async (req, res) => {
  const campaign = req.campaign;
  const newItem = req.body;
  const oldItem = req.itemBinding;

  if (newItem.position < oldItem.position) {
    // move up
    // move down items from new position to old position
    await moveItemsDown(campaign.id, newItem.position, oldItem.position);
  } else if (newItem.position > oldItem.position) {
    // move down
    // move up items from new position to old position
    await moveItemsUp(campaign.id, oldItem.position, newItem.position);
  }
  // itemId changes
  if (newItem.itemId !== oldItem.itemId) {
    if (newItem.itemType === oldItem.itemType && oldItem.itemType === 'program') {
      // same type, type is program, different ids
      // remove groupAssignments for old program
      await GroupAssignments.destroy({
        where: {
          campaignId: campaign.id,
          programId: oldItem.itemId,
          reoccurenceIteration: campaign.reoccurenceIteration,
        },
      });
      // replacing lesson with program
      const groups = await GroupAssignments.findAll({
        where: {
          campaignId: campaign.id,
          programId: null,
          reoccurenceIteration: campaign.reoccurenceIteration,
        },
      });
      // add new program in groupAssignment for each group in campaign
      const newGroupAssignments = [];
      for (const group of groups) {
        newGroupAssignments.push({
          groupId: group.groupId,
          campaignId: campaign.id,
          programId: newItem.itemId,
          reoccurenceIteration: campaign.reoccurenceIteration,
        });
      }
      await GroupAssignments.bulkCreate(newGroupAssignments);
    }
  }

  // itemType changes
  if (newItem.itemType && newItem.itemType !== oldItem.itemType) {
    if (oldItem.itemType === 'program') {
      // replacing program with lesson
      // remove groupAssignments for old program
      await GroupAssignments.destroy({
        where: {
          campaignId: campaign.id,
          programId: oldItem.itemId,
          reoccurenceIteration: campaign.reoccurenceIteration,
        },
      });
    } else if (newItem.itemType === 'program') {
      // replacing lesson with program
      const groups = await GroupAssignments.findAll({
        where: {
          campaignId: campaign.id,
          programId: null,
          reoccurenceIteration: campaign.reoccurenceIteration,
        },
      });
      // add new groupAssignment for each group in campaign
      const newGroupAssignments = [];
      for (const group of groups) {
        newGroupAssignments.push({
          groupId: group.groupId,
          campaignId: campaign.id,
          programId: newItem.itemId,
          reoccurenceIteration: campaign.reoccurenceIteration,
        });
      }
      await GroupAssignments.bulkCreate(newGroupAssignments);
    }
  }

  const updatedItem = await oldItem.update(newItem);
  const data = await lookupItem(updatedItem);
  res.json(data);
};

module.exports.delete = async (req, res, next) => {
  try {
    const logData = {
      action: 'draft-campaign-delete-item',
      feature: 'campaigns',
      object: 'campaigns',
      objectId: req.itemBinding.campaignId,
      childObject: 'campaignItem',
      childObjectId: req.itemBinding.id,
      // eslint-disable-next-line max-len
      updatedData: { campaingItemId: req.itemBinding.id, type: req.itemBinding.itemType, itemId: req.itemBinding.itemId },
      // updatedData: { campaignItemId: createdItem.id, type: createdItem.itemType, itemId: createdItem.itemId, position: createdItem.position },
    };
    const data = await lookupItem(req.itemBinding);
    await campaignItemRemoved(data);
    await req.itemBinding.destroy();
    await refreshCampaignOrder(req.campaign.id);
    await createAuditLog(logData, req);
    res.json(data);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve a specific campaign member when an id is passed in the route
module.exports.campaignItemById = async function (req, res, next, id) {
  const campaignId = req.campaign.id;
  try {
    const queryParams = {
      where: {
        id,
        campaignId,
      },
    };
    const itemBinding = await CampaignItem.findOne(queryParams);
    if (!itemBinding) {
      const err = new Error(req.i18n.t('campaigns.binding_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.itemBinding = itemBinding;
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /campaigns/{contentId}/contentCampaignGroupSummary?contentType={contentType}:
 *   get:
 *     summary: Campaign and Group details For Given contentId
 *     tags:
 *       - Campaigns
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: contentId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: contentType
 *         in: query
 *         required: true
 *         description: lesson or program
 *         schema:
 *           type: string
 *           example: lesson
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       name:
 *                         type: string
 *                       startDate:
 *                         type: string
 *                       status:
 *                         type: string
 *                       duration:
 *                         type: integer
 *                       groupAssignments:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                             group:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: integer
 *                                 name:
 *                                   type: string
 *                                 groupType:
 *                                   type: string
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

// API to retrieve all campaigns and related groups details for selected content
module.exports.itemCampaignGroups = async (req, res, next) => {
  try {
    const contentId = req.params.contentId;
    const contentType = req.query.contentType;
    if (!contentId || !contentType) {
      const err = new Error('contentId and contentType are required');
      err.status = 400;
      throw err;
    }
    const contentCampaignItemGroups = await getCampaignGroupForContent(req.user.accountId, contentId, contentType);
    res.json({ data: contentCampaignItemGroups });
  } catch (err) {
    next(err);
  }
};

