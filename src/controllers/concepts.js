/* eslint-disable no-param-reassign */
const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { restSortToSequelize } = require('../services/utils/resourceUtils');
const { Op, col } = require('sequelize');

const Categories = db.categories;
const Concepts = db.concepts;
const Listing = db.listings;
const CatalogItems = db.catalogItems;

/**
 * converts a rest query from the client to sequelize params
 */
const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize(_.omit(
    query,
    ['$limit', '$skip', '$sort'],
  ));
  const newQuery = {
    ...defaults,
  };
  // eslint-disable-next-line no-undef
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }
  if (query.$limit !== undefined) {
    const paginateMax = 700;
    const limit = parseInt(query.$limit);
    newQuery.limit = Math.min(paginateMax, limit);
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  return newQuery;
};


const getIncludeParams = () => {
  const includeParams = [
    {
      model: Categories,
      attributes: ['id', 'name'],
    },
    {
      model: Listing,
      attributes: [],
      order: [],
    },
    {
      model: CatalogItems,
      attributes: ['id', 'title'],
    },
    {
      model: Listing,
      attributes: ['id', 'title', 'type'],
    },
    {
      model: db.users,
      attributes: ['id', 'firstName', 'lastName'],
      as: 'conceptCreatedBy',
    },
    {
      model: db.users,
      attributes: ['id', 'firstName', 'lastName'],
      as: 'conceptUpdatedBy',
    },
  ];
  return includeParams;
};

const handleFormValues = (data) => {
  data.categoryId = parseInt(data.categoryId) || null;
  return data;
};

const checkUniqueName = async (data, id = null) => {
  const query = {
    concept: data.concept,
  };
  if (id) {
    query.id = {
      [Op.ne]: id,
    };
  }
  const conceptCount = await Concepts.count({
    where: query,
  });
  if (conceptCount) {
    const err = new Error('Concept Name must be unique');
    err.status = 400;
    throw err;
  }
};
/**
 * @openapi
 * /concepts:
 *   post:
 *     summary: Create concept
 *     tags:
 *       - Concepts
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateConcept'
 *           example:
 *             concept: "Sample Concept"
 *             categoryId: 1
 *     responses:
 *       200:
 *         description: Concept added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Concept added successfully
 *
 * components:
 *   schemas:
 *     CreateConcept:
 *       type: object
 *       required:
 *         - concept
 *         - categoryId
 *       properties:
 *         concept:
 *           type: string
 *           description: The Concept name
 *         categoryId:
 *           type: integer
 *           description: The categoryId of the concept
 */
module.exports.create = async (req, res, next) => {
  const data = req.body;
  try {
    await checkUniqueName(data);
    data.createdBy = req.user.id;
    const newConcept = await Concepts.create(handleFormValues(data));
    res.json(newConcept);
  } catch (err) {
    if (err.message === 'Validation error' || err.message.includes('notNull Violation')) {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /concepts/{conceptId}:
 *   patch:
 *     summary: Update Concept
 *     tags:
 *       - Concepts
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: conceptId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the existing Concept
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateConcept'
 *           example:
 *             concept: "Updated Concept Name"
 *             categoryId: 2
 *     responses:
 *       200:
 *         description: Concept updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Concept updated successfully
 */
module.exports.patch = async (req, res, next) => {
  try {
    const data = req.body;
    const { conceptId } = req.params;
    await checkUniqueName(data, conceptId);
    data.updatedBy = req.user.id;
    await Concepts.update(data, {
      where: {
        id: conceptId,
      },
    });
    await Concepts.findByPk(conceptId);
    const updatedConcept = await Concepts.findByPk(conceptId, {
      include: getIncludeParams(),
    });
    res.json(updatedConcept);
  } catch (err) {
    if (err.message === 'Validation error' || err.message.includes('notNull Violation')) {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /concepts:
 *   get:
 *     summary: Fetch all the Concept Values
 *     tags:
 *       - Concepts
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successfully fetched all Concepts
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   concept:
 *                     type: string
 *                     example: Sample Concept
 *                   categoryId:
 *                     type: integer
 *                     example: 1
 */
module.exports.list = async (req, res, next) => {
  try {
    const defaults = {
      order: [['updatedAt', 'DESC']],
      limit: config.paginate.default,
      offset: 0,
    };
    const queryParams = restQueryToSequelize(req.query, defaults);
    queryParams.order = [[...queryParams.order]];
    const finalQuery = {
      ...queryParams,
      where: queryParams.where,
      include: getIncludeParams(),
      attributes: [
        [col('concepts.id'), 'id'],
        [col('concepts.concept'), 'concept'],
        [db.sequelize.literal('(SELECT COUNT(*) FROM `listingConcepts` WHERE `listingConcepts`.`conceptId` = `concepts`.`id`)'), 'listingCount'],
        [db.sequelize.literal('(SELECT COUNT(*) FROM `catalogItemConcepts` WHERE `catalogItemConcepts`.`conceptId` = `concepts`.`id`)'), 'catalogItemCount'],
      ],
      subQuery: false,
      group: ['id'],
    };
    finalQuery.order = queryParams.order[0];
    const pagedResult = {
      limit: queryParams.limit,
      skip: queryParams.offset,
    };
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
      include: _.pick(finalQuery, ['include']).include,
      distinct: true,
    };
    const count = await Concepts.count(countQuery);
    const data = await Concepts.findAll(finalQuery);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /concepts/{conceptId}:
 *   get:
 *     summary: Fetch Concept based on conceptId
 *     tags:
 *       - Concepts
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: conceptId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the existing Concept
 *     responses:
 *       200:
 *         description: Successfully fetched particular Concept based on its ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 concept:
 *                   type: string
 *                   example: Sample Concept
 *                 categoryId:
 *                   type: integer
 *                   example: 1
 */
module.exports.read = async (req, res, next) => {
  try {
    const conceptData = req.concept.get({ plain: true });
    res.json(conceptData);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /concepts/{conceptId}:
 *   delete:
 *     summary: Delete an existing concept
 *     tags:
 *       - Concepts
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: conceptId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the concept to delete
 *     responses:
 *       200:
 *         description: Concept deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Concept deleted successfully
 */
module.exports.delete = async (req, res, next) => {
  try {
    const { conceptId } = req.params;
    const data = await Concepts.destroy({
      where: {
        id: conceptId,
      },
    });
    res.json(data);
  } catch (err) {
    if (err.message.includes('Cannot delete or update a parent row')) {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

// Middleware to retrieve the concept when an id is passed in the route
module.exports.conceptById = async function (req, res, next, id) {
  const queryParams = {
    where: { id },
  };
  try {
    const finalQuery = {
      ...queryParams,
      where: { ...queryParams.where },
      include: getIncludeParams(),
    };
    const concept = await Concepts.findOne(finalQuery);
    if (!concept) {
      const err = new Error(req.i18n.t('concept.concept_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.concept = concept;
    next();
  } catch (err) {
    next(err);
  }
};

