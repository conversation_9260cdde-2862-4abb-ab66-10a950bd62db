const _ = require('lodash');
const db = require('../db');

const Users = db.users;
const UserRoles = db.userRoles;
const AccountUsers = db.accountUsers;
const Roles = db.roles;
const Integrations = db.integrations;

const Op = db.Sequelize.Op;

const getScormIntegrations = async (accountId) => {
  const integrations = await Integrations.findAll({
    attributes: ['id'],
    where: {
      accountId,
      integrationType: ['scorm', 'scorm20043rd', 'scorm20044th'],
    },
  });
  return integrations;
};

const getRoleId = async (name) => {
  const role = await Roles.findOne({
    attributes: ['id'],
    where: {
      name,
    },
  });
  return role && role.id;
};

const getTotalAccountScormUserCount = async (accountId, scormRoleId) => {
  const count = await Users.count({
    include: [{
      model: AccountUsers,
      where: {
        accountId,
        roleId: scormRoleId,
      },
      required: true,
    }],
  });
  return count;
};

const getTotalAccountUserCount = async (accountId) => {
  const count = await Users.count({
    include: [{
      model: AccountUsers,
      where: {
        accountId,
      },
      required: true,
    }],
  });
  return count;
};

const getScormUsersWithNoEmail = async (accountId, scormRoleId) => {
  const users = await Users.findAll({
    attributes: ['id'],
    where: {
      email: null,
    },
    include: [{
      model: AccountUsers,
      attributes: [],
      where: {
        accountId,
        roleId: scormRoleId,
      },
      required: true,
    }],
  });
  return users;
};

const getDuplicateScormIds = async (accountId, scormRoleId) => {
  // Get the duplicate scorm Id's in the account. MySQL is strict in handling group by, so we can only get the scormId
  // as an attribute in the first query.
  // Then we have to query again with the resulting scormIds to get the user id column.
  // The include isn't totally necessary in the 2nd query, but will optimize the query to not fully scan the user table.
  const scormDupes = await Users.findAll({
    attributes: ['scormId'],
    group: ['scormId'],
    having: db.sequelize.where(db.sequelize.fn('count', db.sequelize.col('scormId')), {
      [Op.gte]: 2,
    }),
    include: [{
      attributes: [],
      model: AccountUsers,
      where: {
        accountId,
        roleId: scormRoleId,
      },
      required: true,
    }],
    raw: true,
  });
  const scormIds = scormDupes.map(r => r.scormId);

  const users = await Users.findAll({
    attributes: ['id', 'email', 'scormId'],
    where: {
      scormId: scormIds,
    },
    include: {
      model: AccountUsers,
      attributes: [],
      where: {
        accountId,
        roleId: scormRoleId,
      },
    },
  });

  return users;
};

module.exports.getInfo = async (req, res, next) => {
  const account = req.account;
  try {
    const scormIntegrations = await getScormIntegrations(account.id);
    const isScormAccount = scormIntegrations && scormIntegrations.length > 0;

    let result = {
      isScormAccount,
    };

    if (isScormAccount) {
      const scormRoleId = await getRoleId('scorm');
      const totalAccountScormUserCount = await getTotalAccountScormUserCount(account.id, scormRoleId);
      const totalAccountUserCount = await getTotalAccountUserCount(account.id, scormRoleId);
      const scormUsersWithNoEmail = await getScormUsersWithNoEmail(account.id, scormRoleId);
      const duplicateScormIdUsers = await getDuplicateScormIds(account.id, scormRoleId);
      const userIdsToDeactivate = _.union( // merge the lists but no duplicates please
        scormUsersWithNoEmail.map(user => user.id),
        duplicateScormIdUsers.map(user => user.id),
      );

      result = {
        ...result,
        totalAccountUserCount,
        totalAccountScormUserCount,
        scormUsersWithNoEmailCount: scormUsersWithNoEmail.length,
        scormUsersWithDuplicateScormIdsCount: duplicateScormIdUsers ? duplicateScormIdUsers.length : 0,
        usersToBeDeactivatedCount: userIdsToDeactivate.length,
      };
    }

    res.json(result);
  } catch (err) {
    next(err);
  }
};

module.exports.migrate = async (req, res, next) => {
  const account = req.account;
  try {
    // Make sure account is a scorm account
    const scormIntegrations = await getScormIntegrations(account.id);
    const isScormAccount = scormIntegrations && scormIntegrations.length > 0;

    if (!isScormAccount) {
      const err = new Error('This account is not a SCORM account');
      err.status = 422;
      throw err;
    }
    const scormRoleId = await getRoleId('scorm');
    const userRoleId = await getRoleId('user');
    const scormUsersWithNoEmail = await getScormUsersWithNoEmail(account.id, scormRoleId);
    const duplicateScormIdUsers = await getDuplicateScormIds(account.id, scormRoleId);

    const userIdsToDeactivate = _.union( // merge the lists but no duplicates please
      scormUsersWithNoEmail.map(user => user.id),
      duplicateScormIdUsers.map(user => user.id),
    );

    // Let's do this. Do it in a transaction in case something goes wonky on updating the users
    let updatedCountArray;
    await db.sequelize.transaction(async (transact) => {
      // Update all the scorm role accountUser records for the account.
      // Need to query the users first, since the update doesn't do a join when include is specified.
      const accountUsers = await AccountUsers.findAll({
        attributes: ['userId'],
        where: {
          accountId: account.id,
          roleId: scormRoleId,
        },
        include: [{
          model: Users,
          attributes: [],
          required: true,
        }],
      }, { transaction: transact });
      updatedCountArray = await AccountUsers.update({
        roleId: userRoleId,
      }, {
        where: {
          accountId: account.id,
          roleId: scormRoleId,
          userId: accountUsers.map(u => u.userId),
        },
      }, { transaction: transact });

      // Get all the users in the account with a scorm role as a user role.
      const users = await Users.findAll({
        attributes: ['id'],
        include: [{
          model: AccountUsers,
          attributes: [],
          where: {
            accountId: account.id,
          },
          required: true,
        }, {
          model: UserRoles,
          attributes: [],
          where: {
            roleId: scormRoleId,
          },
          required: true,
        }],
      }, { transaction: transact });
      // Update the userRole records for the users we retrieved earlier based on account and scorm user role
      await UserRoles.update({
        roleId: userRoleId,
      }, {
        where: { // there's an index on (roleId, userId), so take advantage of that
          roleId: scormRoleId,
          userId: users.map(u => u.id),
        },
      }, { transaction: transact });
      // Deactivate the users (paranoid deletion). Do this last so the users have roles set up properly if we later
      // re-activate them.
      await Users.destroy({
        where: {
          id: userIdsToDeactivate,
        },
      }, { transaction: transact });
    });

    // delete the scorm integration(s)
    await Integrations.destroy({
      where: {
        id: scormIntegrations.map(i => i.id),
      },
    });

    const result = {
      deactivatedUserCount: userIdsToDeactivate.length,
      usersMigrated: updatedCountArray ? updatedCountArray[0] - userIdsToDeactivate.length : 0,
    };

    res.json(result);
  } catch (err) {
    next(err);
  }
};
