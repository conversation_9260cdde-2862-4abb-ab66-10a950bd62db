/* eslint-disable max-len */
const _ = require('lodash');
const db = require('../db');
const { format } = require('date-fns');
const { getAdminRoleUserScope } = require('../services/utils/userUtils');

const {
  users: Users,
  accounts: Accounts,
  events: Events,
  viewLessonCardEvents: ViewLessonCardEvents,
  resources: Resources,
  userLessons: UserLessons,
  lessonCards: LessonCards,
} = db.reportingModels;

const Op = db.Sequelize.Op;
const reportingDb = db.sequelizeReportingDb;

/**
 * a function to merge the data from our 3 queries into the {@link TitleData} array
 * @param {ResourceIds} resourceIds lists of ids by resource
 * @param {Object} event data to be merged into titleData
 * @param {ResourceTitles[][]} titleData resource titles and the array which will ultimately be returned to the user
 * @param {'questionAnswers'|'lessons'|'mediaAssets'|'programs'|'resourceAssets'} digestable the resource's digegestable type
 * @param {'trackableId'|'lessonId'|'resourceId'} sourceKey the foreignKey which will be used for the merge
 * @param {'lessonId'| null} sourceSubKey a sub key for the sourceKey if needed
 * @param {'resourceId'|'id'} targetKey the key in titleData which will be used for the merge
 * @returns {ReturnResource} the merged data array and idx properties which can be used to perform further calculations
 */
const findResourceAndCombine = (resourceIds, event, titleData, digestable, sourceKey, sourceSubKey, targetKey) => {
  const dataIdx = Object.keys(resourceIds).indexOf(digestable);
  const resourceData = titleData[dataIdx];
  let idx;
  if (digestable === 'questionAnswers') {
    idx = resourceData.findIndex(q => q[targetKey] === event[sourceKey]);
    Object.assign(resourceData[idx], { ...event, type: 'questionAnswer' });
  } else if (digestable === 'lessons') {
    idx = resourceData.findIndex(l => l[targetKey] === event[sourceKey]);
    // If it's not found, the original event is associated with a deleted lesson, so skip it.
    if (idx !== -1) {
      Object.assign(resourceData[idx], { ...event, type: 'lesson' });
    }
  } else if (digestable === 'resourceAssets') {
    idx = resourceData.findIndex(m => m[targetKey] === event[sourceKey]);
    Object.assign(resourceData[idx], { ...event, type: 'resourceAsset' });
  } else if (digestable === 'resourceAssets') {
    idx = resourceData.findIndex(m => m[targetKey] === event[sourceKey]);
    Object.assign(resourceData[idx], { ...event, type: 'resourceAsset' });
  } else if (digestable === 'programs') {
    idx = resourceData.findIndex(p => p[targetKey] === event[sourceKey]);
    Object.assign(resourceData[idx], { ...event, type: 'program' });
  }
  return { resourceData, idx };
};

/**
 * set up a properly formatted `attributes` clause for sequelize for a specifc model
 * @param {'lessons'|'mediaAssets'|'questionAnswers'|'programs'|'resourceAssets'} modelName name of the model you want attributes for
 * @returns {string[]|[][]} sequelize formatted attributes array
 */
const attributesHelper = (modelName) => {
  switch (modelName) {
    case 'lessons':
    case 'resourceAssets':
      return ['title', 'resourceId', 'id'];
    case 'questionAnswers':
      return [['questionEdited', 'title'], 'resourceId', 'id'];
    case 'programs':
      return [['name', 'title'], 'id'];
    default:
      return [];
  }
};

/**
 * set up a properly formatted `where` clause for sequelize for a single resource
 * @param {IdData} idData contains the ids and/or trackable ids for a single resource
 * @returns {Object} sequelize formatted where clause
 */
const whereHelper = (idData) => {
  const whereClause = {
    [Op.or]: {},
  };

  if (idData) {
    if (idData.ids) {
      whereClause[Op.or].id = { [Op.in]: idData.ids };
    }
    if (idData.trackableIds) {
      whereClause[Op.or].resourceId = { [Op.in]: idData.trackableIds };
    }
    return whereClause;
  }
  return null;
};

/**
 * Sequelize has limited support for mysql functions. In order to get around this limitation
 * and to keep from having to make too many db calls, we'll build the string from scratch.
 *
 * These queries will be used to calculate the number of Assigned, In Progress, Completed, and
 * Not Started statistics.
 *
 * Assigned and Not Started stats are coupled with `createdAt`
 * In Progress and Completed are coupled with `updatedAt`
 *
 * All four statistics are unique user counts.
 * @param {'assigned'|'open'|'inProgress'|'completed'} type corresponds to userLesson type
 * @param {string} startDate query start date
 * @param {string} endDate query end date
 * @returns {string} properly formatted MySQL string that will be used to call
 * [sequelize.literal](http://docs.sequelizejs.com/class/lib/sequelize.js~Sequelize.html#static-method-literal)
 */
const sequelizeLiteralStringHelper = (type, startDate, endDate) => {
  if (type === 'assigned') {
    return `DISTINCT CASE WHEN DATE(\`userLessons\`.\`createdAt\`) BETWEEN '${format(
      startDate,
      'YYYY-MM-DD',
    )}' AND '${format(
      endDate,
      'YYYY-MM-DD',
    )}' THEN \`userLessons\`.\`userId\` END`;
  }
  const createdOrUpdated = type === 'open' ? 'createdAt' : 'updatedAt';
  return `DISTINCT CASE WHEN \`userLessons\`.\`status\`='${type}' AND DATE(\`userLessons\`.\`${createdOrUpdated}\`) BETWEEN '${format(
    startDate,
    'YYYY-MM-DD',
  )}' AND '${format(
    endDate,
    'YYYY-MM-DD',
  )}' THEN \`userLessons\`.\`userId\` END`;
};

/**
 * This is the main Content Report function which returns content statistics for a given account.
 * Since the data is spread out on multiple tables which all need to conform to the
 * date params, the primary goal here is to make as few queries as possible and offload the data
 * calculation to the sql server as much as possible.
 *
 * ### General Strategy
 * the function executes as follows:
 * - find and calculate the data in the given date range in 3 queries
 *   - determine `viewCount`, `shareCount`, and `favoriteCount` from the `events` table
 *   - `lessons` needs to be given special attention since we count views from the `lessonCard` data as well
 *   - determine `assignedCount`, `numOpen`, `progressCount`, and `completedCount` from the `userLessons` table
 * - extract the ids from that data
 * - query the `title` data using those ids
 * - recombine the data and return
 *
 * Since all the records here are aggregated data I don't see how to accurately handle paging/sorting/filtering.
 * All of the stats are calculated within the date range and then we'll send a max of 9999 entries back to the client
 * where paging will be handled there.
 * @async
 * @param {string} startDate
 * @param {string} endDate
 * @param {number} accountId
 * @returns Promise representing an array of the engagement report data for a given account.
 */
const generateContentReport = async (reqUser, startDate, endDate, accountId) => {
  const eventQuery = {
    includeIgnoreAttributes: false,
    attributes: [
      'trackableId',
      'trackableType',
      'resource.digestable',
      // count distinct user events
      [reportingDb.fn('COUNT', reportingDb.literal('DISTINCT CASE WHEN `events`.`type`=\'view\' THEN `events`.`userId` END')), 'viewCount'],
      [reportingDb.fn('COUNT', reportingDb.literal('DISTINCT CASE WHEN `events`.`type`=\'share\' THEN `events`.`userId` END')), 'shareCount'],
      [reportingDb.fn('COUNT', reportingDb.literal('DISTINCT CASE WHEN `events`.`type`=\'save\' THEN `events`.`userId` END')), 'favoriteCount'],
    ],
    where: {
      createdAt: {
        [Op.gte]: startDate,
        [Op.lte]: endDate,
      },
      trackableType: 'resource',
      sourceLifecycle: 'publish',
      action: 1,
      type: {
        [Op.in]: ['view', 'share', 'save'],
      },
    },
    include: [
      {
        model: Users,
        include: [{
          model: Accounts,
          where: {
            id: accountId,
          },
        }],
        required: true,
      },
      {
        model: Resources,
      },
    ],
    group: ['trackableId'],
    raw: true,
  };
  const lcEventQuery = {
    includeIgnoreAttributes: false,
    attributes: [
      'lessonId',
      [reportingDb.fn('COUNT', reportingDb.literal('DISTINCT CASE WHEN `viewLessonCardEvents`.`type`=\'view\' THEN `viewLessonCardEvents`.`userId` END')), 'viewCount'],
    ],
    where: {
      createdAt: {
        [Op.gte]: startDate,
        [Op.lte]: endDate,
      },
      sourceLifecycle: 'publish',
      type: 'view',
    },
    include: [
      // inner join to limit results to the user's account
      {
        model: Users,
        include: [{
          model: Accounts,
          where: {
            id: accountId,
          },
        }],
        required: true,
      },
      {
        model: LessonCards,
      },
    ],
    group: ['lessonId'],
    raw: true,
  };
  const userLessonQuery = {
    includeIgnoreAttributes: false,
    attributes: [
      'resourceId',
      'type',
      [reportingDb.fn('COUNT', reportingDb.fn('DISTINCT', reportingDb.col('`userLessons`.`userId`'))), 'uniqueUserLessonsCount'],
      [reportingDb.fn('COUNT', reportingDb.literal(sequelizeLiteralStringHelper('assigned', startDate, endDate))), 'assignedCount'],
      [reportingDb.fn('COUNT', reportingDb.literal(sequelizeLiteralStringHelper('open', startDate, endDate))), 'numOpen'],
      [reportingDb.fn('COUNT', reportingDb.literal(sequelizeLiteralStringHelper('inProgress', startDate, endDate))), 'progressCount'],
      [reportingDb.fn('COUNT', reportingDb.literal(sequelizeLiteralStringHelper('completed', startDate, endDate))), 'completedCount'],
    ],
    where: {
      [Op.or]: {
        updatedAt: {
          [Op.gte]: startDate,
          [Op.lte]: endDate,
        },
        createdAt: {
          [Op.gte]: startDate,
          [Op.lte]: endDate,
        },
      },
      status: {
        [Op.in]: ['inProgress', 'open', 'completed'],
      },
      sourceLifecycle: 'publish',
      assignmentId: {
        // count only non-self-directed views
        [Op.ne]: null,
      },
    },
    include: [
      {
        model: Users,
        include: [{
          model: Accounts,
          where: {
            id: accountId,
          },
        }],
        required: true,
      },
    ],
    group: ['resourceId', 'type'],
    raw: true,
  };

  // filter query with any user subset to which requesting user is restricted
  const userScope = await getAdminRoleUserScope(reqUser);
  if (userScope) {
    const userClause = { id: { [Op.in]: [...userScope] } };
    eventQuery.include[0].where = userClause;
    lcEventQuery.include[0].where = userClause;
    userLessonQuery.include[0].where = userClause;
  }

  // make the first 3 queries to the events and userLessons table
  const [
    events,
    lessonEvents,
    userLessons,
  ] = await Promise.all([
    Events.findAll(eventQuery),
    ViewLessonCardEvents.findAll(lcEventQuery),
    UserLessons.findAll(userLessonQuery),
  ]);

  /**
   * consolidate identifiers by type (`ids` and `trackableIds`) so we can search
   * for all of them in one go.
   * This is part 1 of id consolidation
   * @type {{lessons?: { ids: number[], trackableIds: number[] }, mediaAssets?: { ids: number[], trackableIds: number[] }, resourceAssets?: { ids: number[], trackableIds: number[] }, programs?: { ids: number[], trackableIds: number[] }, questionAnswers?: { ids: number[], trackableIds: number[] }}}
   */
  const resourceIds = {};
  for (const item of events) {
    const digestable = item.digestable;
    if (!(digestable in resourceIds)) {
      // eslint-disable-next-line no-param-reassign
      resourceIds[digestable] = {
        trackableIds: [item.trackableId],
      };
    } else {
      resourceIds[digestable].trackableIds.push(item.trackableId);
    }
  }

  // part 2
  resourceIds.lessons = {
    ...resourceIds.lessons,
    ids: lessonEvents.map(event => event.lessonId),
  };

  // part 3
  for (const item of userLessons) {
    const digestable = `${item.type}s`;
    if (!(digestable in resourceIds)) {
      // eslint-disable-next-line no-param-reassign
      resourceIds[digestable] = {
        ids: [item.resourceId],
      };
    } else {
      resourceIds[digestable].ids.push(item.resourceId);
    }
  }


  /**
   * Query for getting titles for each resource. This needs to be dynamically
   * generated in order to avoid making empty queries which will return all entries in the
   * db. This array will also be used as the source for when we merge the other
   * statistics back into the fold.
   * @type {ResourceTitles[][]}
   */
  const titleData = await Promise.all(Object.keys(resourceIds).map((modelName) => {
    return db[modelName].findAll({
      attributes: attributesHelper(modelName),
      where: whereHelper(resourceIds[modelName]),
      raw: true,
    });
  }));

  /**
   * Combine the data from all the different sources. We'll calculate the `selfDirected`
   * count here. We can't query for the `selfDirected` count directly so it will be inferred
   * from the `view`, `inProgress`, and `completed` counts.
   * This loop is part 1 of the combine process
    */
  for (const event of events) {
    const { resourceData, idx } = findResourceAndCombine(resourceIds, event, titleData, event.digestable, 'trackableId', null, 'resourceId');
    // start off assuming all views are self-directed
    resourceData[idx].selfDirected = resourceData[idx].viewCount;
  }

  // part 2
  for (const eventLesson of lessonEvents) {
    const { resourceData, idx } = findResourceAndCombine(resourceIds, eventLesson, titleData, 'lessons', 'lessonId', null, 'id');
    if (idx !== -1) {
      resourceData[idx].selfDirected = resourceData[idx].viewCount;
    }
  }

  // part 3
  for (const userLesson of userLessons) {
    const { resourceData, idx } = findResourceAndCombine(resourceIds, userLesson, titleData, `${userLesson.type}s`, 'resourceId', null, 'id');
    // attribute the user's views to userLesson activity so that
    // "total views" = "self directed" + "in progress" + "completed"
    resourceData[idx].selfDirected -= resourceData[idx].uniqueUserLessonsCount;
    if (resourceData[idx].selfDirected < 0) {
      resourceData[idx].selfDirected = 0;
    }

    if (_.isNil(resourceData[idx].shareCount)) {
      resourceData[idx].shareCount = 0;
    }
    if (_.isNil(resourceData[idx].favoriteCount)) {
      resourceData[idx].favoriteCount = 0;
    }
  }

  // concatenate all the resource data arrays
  // react-table docs say it can efficiently handle 10K+ records
  return [].concat(...titleData).slice(0, 9999);
};

module.exports.contentReport = async (req, res, next) => {
  try {
    const queryKeys = Object.keys(req.query);
    if (!queryKeys.includes('startDate') || !queryKeys.includes('endDate')) {
      const error = new Error(req.i18n.t('reports.missing_date_Error'));
      error.status = 400;
      throw error;
    }

    const re = new RegExp(/(\d{4})-(\d{2})-(\d{2})/);

    const startMatch = re.test(req.query.startDate);
    const endMatch = re.test(req.query.endDate);
    if (!startMatch || !endMatch) {
      const error = new Error(req.i18n.t('reports.date_format_Error'));
      error.status = 400;
      throw error;
    }
    const { startDate, endDate } = req.query;
    const { id: userId, accountId } = req.user;
    const contentData = await generateContentReport(userId, startDate, endDate, accountId);
    res.json(contentData);
  } catch (err) {
    next(err);
  }
};
