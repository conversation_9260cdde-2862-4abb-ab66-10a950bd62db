const _ = require('lodash');
const db = require('../db');
const logger = require('../logger');
const { restQueryToSequelize, runCampaignUserReport, genCSV, newRestQueryToSequelize,
  cleanDownloadFilename, runContentUserReport, runCampaignUserReportNew, genNewCSV, getCampaignGraphInfo } = require('../services/utils/reportingUtils');
const {
  getCampaignResourceIds,
  getParticipants,
  getGroupChildrenIds,
} = require('../services/utils/campaignUtils');
const { gleanStatus, calcPercentComplete } = require('../services/utils/calcUtils');
const { dashboardPopularContent } = require('../services/utils/dashboardUtils');
const config = require('../config/config');
const moment = require('moment');

const {
  groupAssignments: GroupAssignments,
  programs: Programs,
  userLessons: UserLessons,
  lessons: Lessons,
  mediaAssets: MediaAssets,
  resourceAssets: ResourceAssets,
  questionAnswers: QuestionAnswers,
  accountFields: AccountFields,
  users: Users,
  userGroups: UserGroups,
  campaignGroupChanges: CampaignGroupChanges,
  accountPrograms: AccountPrograms,
  accountLessons: AccountLessons,
  accounts: Accounts,
} = db.reportingModels;
const Campaigns = db.campaigns;
const Groups = db.groups;
const Op = db.Sequelize.Op;

function getResourceName(assignment) {
  let name;
  if (assignment.type === 'lesson' && assignment.lesson) {
    name = assignment.lesson.title;
  } else if (assignment.type === 'resourceAsset' && assignment.resourceAsset) {
    name = assignment.resourceAsset.title;
  } else if (assignment.type === 'questionAnswer' && assignment.questionAnswer) {
    name = assignment.questionAnswer.question;
  }
  return name;
}

// makes a composite key out of resource type, resource id and parent program id
function makeKey(assignment) {
  return `${assignment.type}:${assignment.groupAssignment.programId}:${assignment.resourceId}`;
}
function getId(key) {
  return key.split(':')[2];
}
function getType(key) {
  return key.split(':')[0];
}
/*
 * Return a report if campaign progress organized by users and items.
 * Aggregates includes total number of distinct users in each status: {open, inProgress, completed, incomplete, closed}
 * with detail break down by user
 *
 * Aggregates includes total number of distinct item in each status: {open, inProgress, completed, incomplete, closed}
 * with detail break down by item
 *
 * report: {
    users: {
      total:
      open:
      inprogress:
      completed:
      incomplete:
      closed:
      detail: [
        {
          userId:
          total:
          open:
          inProgress:
          completed:
          incomplete:
          closed:
        },{ ... }
      ]
    },
    item: {
      total:
      open:
      inprogress:
      completed:
      incomplete:
      closed:
      detail: [
        {
          itemId:
          programId:
          programName:
          resourceName:
          total:
          open:
          inProgress:
          completed:
          incomplete:
          closed:
        },{ ... }
      ]
    }
  }
 */
async function generateProgressReport(req, campaign) {
  // empty report
  const report = {
    users: {
      total: 0,
      open: 0,
      inProgress: 0,
      completed: 0,
      incomplete: 0,
      closed: 0,
      elapsedTimeInSeconds: 0,
      percentComplete: 0,
      detail: [],
    },
    items: {
      total: 0,
      open: 0,
      inProgress: 0,
      completed: 0,
      incomplete: 0,
      closed: 0,
      elapsedTimeInSeconds: 0,
      percentComplete: 0,
      detail: [],
    },
  };

  if (_.isNil(campaign)) {
    return report;
  }

  // scheduled campaigns have no assignments yet
  if (campaign.status === 'scheduled') {
    const participants = await getParticipants(campaign);
    const resources = await getCampaignResourceIds(campaign);
    report.users.total = participants.length;
    report.items.total = resources.length;
  } else {
    // identify groupAssignment ids for current iteration of campaign
    const groupAssignments = await GroupAssignments.findAll({
      where: {
        campaignId: campaign.id,
        reoccurenceIteration: campaign.reoccurenceIteration,
      },
    });
    const assignmentIds = groupAssignments.map(assignment => assignment.id);
    const asnGroupIds = groupAssignments.map(a => a.groupId);
    const childIds = await getGroupChildrenIds(asnGroupIds);
    const allGroupIds = [...asnGroupIds, ...childIds];

    // identify all assignments generated from group assignments
    const assignmentsQuery = {
      where: {
        type: {
          [Op.ne]: 'campaign',
        },
        assignmentId: {
          [Op.in]: assignmentIds,
        },
      },
      include: [
        {
          model: GroupAssignments,
          required: true,
          include: {
            model: Programs,
            required: false,
          },
        }, {
          model: Lessons,
          required: false,
        }, {
          model: ResourceAssets,
          required: false,
        }, {
          model: QuestionAnswers,
          required: false,
        }, {
          // user must not be deleted
          model: Users,
          attributes: ['id'],
          paranoid: true,
          required: true,
          include: [
            {
              model: UserGroups,
              attributes: ['groupId'],
              required: false,
              where: {
                memberType: 'user',
                groupId: allGroupIds,
              },
            },
            {
              model: CampaignGroupChanges,
              attributes: ['id'],
              required: false,
              where: {
                processedDate: null,
                campaignId: campaign.id,
                groupId: allGroupIds,
              },
            },
          ],
        },
      ],
      order: [['userId', 'ASC']],
    };

    if (campaign.lifecycle === 'active') {
      Object.assign(assignmentsQuery.where, { sourceLifecycle: 'publish' });
    }

    const userAssignments = await UserLessons.findAll(assignmentsQuery);

    const programAssignments = userAssignments
      .filter(ua => ua.type === 'program');
    const nonProgramAssignments = userAssignments
      .filter(ua => ua.type !== 'program');

    const programAssignmentMap = new Map();
    for (const assignment of programAssignments) {
      programAssignmentMap.set(`${assignment.userId}:${assignment.resourceId}`, assignment);
    }

    // post process query results to get metrics
    const userMap = new Map(); // key = userId, value = status object
    const itemMap = new Map(); // key = resourceType:resourceId, value = status.object

    // aggregate assignment status by userId and resourceId
    const filteredUserAssignments = nonProgramAssignments
      // user must be currently in an assigned campaign group.
      // If the user was removed from a group but the campaign group changes are pending, still include them.
      .filter(ua => ua.user.userGroups.length > 0 || ua.user.campaignGroupChanges.length > 0);

    let account = null;
    let customerOrInternalAccount = false;
    if (req.user && req.user.accountId) {
      account = await Accounts.findByPk(req.user.accountId);
      if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
        customerOrInternalAccount = true;
      }
    }

    for (const assignment of filteredUserAssignments) {
      if (!userMap.has(assignment.userId)) {
        const newStatus = {
          id: assignment.userId,
          total: 0,
          open: 0,
          inProgress: 0,
          completed: 0,
          incomplete: 0,
          closed: 0,
          elapsedTimeInSeconds: 0,
          percentComplete: 0,
        };
        userMap.set(assignment.userId, newStatus);
      }

      let configuredProgramName = null;
      let configuredLessonTitle = null;
      if (customerOrInternalAccount) {
        if (assignment.groupAssignment.program) {
          const accountProgram = await AccountPrograms.findOne({ where: { accountId: req.user.accountId,
            programId: assignment.groupAssignment.programId } });
          if (accountProgram && accountProgram.name) {
            configuredProgramName = accountProgram.name;
          }
        }
        if (assignment.type === 'lesson' && assignment.lesson) {
          const accountLesson = await AccountLessons.findOne({ where: { accountId: req.user.accountId,
            lessonId: assignment.lesson.id } });
          if (accountLesson && accountLesson.title) {
            configuredLessonTitle = accountLesson.title;
          }
        }
      }

      if (!itemMap.has(makeKey(assignment))) {
        const newStatus = {
          id: assignment.resourceId,
          programId: assignment.groupAssignment.programId,
          programName: configuredProgramName ||
            (assignment.groupAssignment.program ? assignment.groupAssignment.program.name : null),
          resourceName: configuredLessonTitle || getResourceName(assignment),
          total: 0,
          open: 0,
          inProgress: 0,
          completed: 0,
          incomplete: 0,
          closed: 0,
          elapsedTimeInSeconds: 0,
          percentComplete: 0,
        };
        itemMap.set(makeKey(assignment), newStatus);
      }
      const userStatus = userMap.get(assignment.userId);
      const itemStatus = itemMap.get(makeKey(assignment));

      // if this userLesson is associated with a parent program, the status and percentComplete
      // are overridden by the parent in the context of this campaign.
      if (assignment.groupAssignment.program) {
        const parentAssignment =
          programAssignmentMap.get(`${assignment.userId}:${assignment.groupAssignment.programId}`);
        if (parentAssignment) {
          assignment.status = parentAssignment.status;
        }
      }
      userStatus.total += 1;
      itemStatus.total += 1;
      switch (assignment.status) {
        case 'open': {
          userStatus.open += 1;
          itemStatus.open += 1;
          break;
        }
        case 'inProgress': {
          userStatus.inProgress += 1;
          itemStatus.inProgress += 1;
          break;
        }
        case 'completed': {
          userStatus.completed += 1;
          itemStatus.completed += 1;
          break;
        }
        case 'incomplete': {
          userStatus.incomplete += 1;
          itemStatus.incomplete += 1;
          break;
        }
        case 'closed': {
          userStatus.closed += 1;
          itemStatus.closed += 1;
          break;
        }
        default:
          break;
      }
      userStatus.elapsedTimeInSeconds += assignment.elapsedTimeInSeconds;
      itemStatus.elapsedTimeInSeconds += assignment.elapsedTimeInSeconds;
      userStatus.percentComplete += assignment.percentComplete;
      itemStatus.percentComplete += assignment.percentComplete;
    }

    // generate report content from user aggregates
    const userIter = userMap.keys();
    let userEntry = userIter.next();
    while (!userEntry.done) {
      // top-level totals
      report.users.total += 1;
      const key = userEntry.value;
      const status = userMap.get(key);
      const userStatus = gleanStatus(status);
      if (userStatus.status === 'open') {
        report.users.open += 1;
      } else if (userStatus.status === 'inProgress') {
        report.users.inProgress += 1;
      } else if (userStatus.status === 'completed') {
        report.users.completed += 1;
      } else if (userStatus.status === 'incomplete') {
        report.users.incomplete += 1;
      } else if (userStatus.status === 'closed') {
        report.users.closed += 1;
      }
      report.users.elapsedTimeInSeconds += userStatus.elapsedTimeInSeconds;
      report.users.percentComplete += userStatus.percentComplete;

      // detail record for each user
      report.users.detail.push({
        userId: key,
        total: status.total,
        open: status.open,
        inProgress: status.inProgress,
        completed: status.completed,
        incomplete: 0,
        closed: status.closed,
        elapsedTimeInSeconds: status.elapsedTimeInSeconds,
        percentComplete: calcPercentComplete(status.percentComplete, status.total),
      });
      userEntry = userIter.next();
    }
    report.users.percentComplete = calcPercentComplete(report.users.percentComplete, report.users.total);

    // generate report content from item aggregates
    const itemIter = itemMap.keys();
    let itemEntry = itemIter.next();
    while (!itemEntry.done) {
      // top-level totals
      report.items.total += 1;
      const key = itemEntry.value;
      const status = itemMap.get(key);
      const itemStatus = gleanStatus(status);
      if (itemStatus.status === 'open') {
        report.items.open += 1;
      } else if (itemStatus.status === 'inProgress') {
        report.items.inProgress += 1;
      } else if (itemStatus.status === 'completed') {
        report.items.completed += 1;
      } else if (itemStatus.status === 'incomplete') {
        report.items.incomplete += 1;
      } else if (itemStatus.status === 'closed') {
        report.items.closed += 1;
      }
      report.items.elapsedTimeInSeconds += itemStatus.elapsedTimeInSeconds;
      report.items.percentComplete += itemStatus.percentComplete;

      // detail record for each item
      report.items.detail.push({
        resourceId: getId(key),
        resourceType: getType(key),
        resourceName: status.resourceName,
        programId: status.programId,
        programName: status.programName,
        total: status.total,
        open: status.open,
        inProgress: status.inProgress,
        completed: status.completed,
        incomplete: 0,
        closed: status.closed,
        elapsedTimeInSeconds: status.elapsedTimeInSeconds,
        percentComplete: calcPercentComplete(status.percentComplete, status.total),
      });
      itemEntry = itemIter.next();
    }
  }
  report.items.percentComplete = calcPercentComplete(report.items.percentComplete, report.items.total);
  return report;
}

/**
 * Make sure the user is requesting allowable fields
 *
 * @param {string[]} params user requested attributes
 */
const checkAllowedAttributes = async (req, params, accountId) => {
  if (!params || params.length < 1) {
    const err = new Error(req.i18n.t('campaigns.missing_attribute_Error'));
    err.status = 400;
    throw err;
  }

  const accountFields = await AccountFields.findAll({
    attributes: ['fieldName'],
    where: {
      accountId,
    },
    raw: true,
  });

  const allowed = ['id', 'userId', 'firstName', 'lastName', 'email', 'role', 'title', 'location', 'group',
    'status', 'pctComplete', 'percentComplete', 'assigned', 'started', 'completed', 'removed', 'dueDate', 'city',
    'employeeId', 'remote', 'supervisor', 'exempt', 'hireDate', 'createdAt', 'lastActivityAt', 'managerEmail',
    'minutesSpent', 'campaign', 'timeComplete', 'userStatus', 'language', 'stateCode',
    'countryCode', 'contentTitle', 'notificationEmail', 'jobGroup', 'department', 'orgLevel',
    ...accountFields.map(af => af.fieldName)];

  for (const attribute of params) {
    if (!allowed.includes(attribute)) {
      const err = new Error(req.i18n.t('campaigns.invalid_attribute_Error', { attribute }));
      err.status = 400;
      throw err;
    }
  }
};

module.exports.checkAllowedAttributes = checkAllowedAttributes;

/**
 * @openapi
 * /campaigns/{campaignId}/report/progress:
 *   get:
 *     summary: Campaign Progress Report For Given CampaignId
 *     tags: [Reports]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 report:
 *                   type: object
 *                   properties:
 *                     users:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         open:
 *                           type: integer
 *                         inProgress:
 *                           type: integer
 *                         completed:
 *                           type: integer
 *                         incomplete:
 *                           type: integer
 *                         closed:
 *                           type: integer
 *                         elapsedTimeInSeconds:
 *                           type: integer
 *                         percentComplete:
 *                           type: integer
 *                         detail:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               userId:
 *                                 type: integer
 *                               total:
 *                                 type: integer
 *                               open:
 *                                 type: integer
 *                               inProgress:
 *                                 type: integer
 *                               completed:
 *                                 type: integer
 *                               incomplete:
 *                                 type: integer
 *                               elapsedTimeInSeconds:
 *                                 type: integer
 *                               percentComplete:
 *                                 type: integer
 *                     items:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         open:
 *                           type: integer
 *                         inProgress:
 *                           type: integer
 *                         completed:
 *                           type: integer
 *                         incomplete:
 *                           type: integer
 *                         closed:
 *                           type: integer
 *                         elapsedTimeInSeconds:
 *                           type: integer
 *                         percentComplete:
 *                           type: integer
 *                         detail:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               resourceId:
 *                                 type: integer
 *                               resourceType:
 *                                 type: string
 *                               resourceName:
 *                                 type: string
 *                               total:
 *                                 type: integer
 *                               open:
 *                                 type: integer
 *                               inProgress:
 *                                 type: integer
 *                               incomplete:
 *                                 type: integer
 *                               closed:
 *                                 type: integer
 *                               elapsedTimeInSeconds:
 *                                 type: integer
 *                               percentComplete:
 *                                 type: integer
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.progressReport = async (req, res, next) => {
  try {
    const report = await generateProgressReport(req, req.campaign);
    res.json({ report });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /campaigns/{campaignId}/report/popular:
 *   get:
 *     summary: Campaign Popular Report
 *     tags: [Reports]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 report:
 *                   type: object
 *                   properties:
 *                     popularContent:
 *                       type: array
 *                       description: Array of report data object.
 *                       items:
 *                         type: object
 *                         description: A single popular content item (schema details can be expanded)
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

module.exports.popularReport = async (req, res, next) => {
  try {
    const resources = await getCampaignResourceIds(req.campaign);
    const uniqueIds = new Set(resources);
    const resIds = [...uniqueIds];
    const content = await dashboardPopularContent(req, resIds.length, resIds);
    const report = {
      popularContent: content,
    };
    res.json({ report });
  } catch (err) {
    next(err);
  }
};

/**
 * user report endpoint for completion report section on the front end
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @todo GDPR
 */

/**
 * @openapi
 * /campaigns/{campaignId}/report/users:
 *   get:
 *     summary: Campaign Users Report
 *     tags: [Reports]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       firstName:
 *                         type: string
 *                       lastName:
 *                         type: string
 *                       email:
 *                         type: string
 *                       userStatus:
 *                         type: string
 *                       title:
 *                         type: string
 *                       location:
 *                         type: string
 *                       role:
 *                         type: string
 *                       city:
 *                         type: string
 *                       employeeId:
 *                         type: integer
 *                       supervisor:
 *                         type: string
 *                       exempt:
 *                         type: integer
 *                       hireDate:
 *                         type: integer
 *                       lastActivityAt:
 *                         type: integer
 *                       managerEmail:
 *                         type: string
 *                       assigned:
 *                         type: string
 *                       completed:
 *                         type: string
 *                       dueDate:
 *                         type: string
 *                       group:
 *                         type: string
 *                       campaign:
 *                         type: string
 *                       pctComplete:
 *                         type: integer
 *                       timeComplete:
 *                         type: string
 *                       minutesSpent:
 *                         type: string
 *                       started:
 *                         type: string
 *                       status:
 *                         type: string
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.usersReport = async (req, res, next) => {
  const model = 'campaign';
  try {
    const isNewReport = !!(req.query.newReport && req.query.newReport === 'true');
    let generatedUserReport = null;
    let limit;
    let offset;
    if (isNewReport) {
      const defaults = {
        order: [[{ model: Users }, 'createdAt', 'desc']],
        limit: config.paginate.default,
        offset: 0,
      };
      logger.info(`Calling new completion report for campaignId: ${req.campaign.id}`);
      const rqts = await newRestQueryToSequelize(model, req.user.id, req.query, defaults);
      const queryParams = rqts.newQuery;
      limit = queryParams.limit;
      offset = queryParams.offset;
      generatedUserReport = await runContentUserReport(
        queryParams,
        req.campaign.id,
        model,
        req.campaign.accountId,
        req.query.startDate,
        req.query.endDate,
        rqts.userWhere,
      );
    } else {
      const defaults = {
        order: [['id', 'DESC']],
        limit: config.paginate.default,
        offset: 0,
      };
      logger.info(`Calling old completion report for campaignId: ${req.campaign.id}`);
      const queryParams = await restQueryToSequelize(req.user.id, req.query, defaults);
      queryParams.order = [[...queryParams.order]];
      limit = queryParams.limit;
      offset = queryParams.offset;
      generatedUserReport = await runCampaignUserReport(
        queryParams,
        req.campaign.id,
        req.campaign.accountId,
        req.query.startDate,
        req.query.endDate,
      );
    }

    res.json({
      total: generatedUserReport.total,
      limit,
      skip: offset,
      data: generatedUserReport.data,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * downloadable csv endpoint for completion report section on the front end
 *
 * @param {{}} req
 * @param {{}} res
 * @param {{}} next
 * @todo GDPR
 */
module.exports.generateCSV = async (req, res, next) => {
  const keepAttributes = req.query.attributes;
  const model = 'campaign';
  const { campaignId } = req.params;
  try {
    const [, acct] = await Promise.all([
      checkAllowedAttributes(req, keepAttributes, req.campaign.accountId),
      req.campaign.getAccount({ attributes: ['name'], raw: true }),
    ]);

    // user can't specify limit or skip for the csv.
    delete req.query.$limit;
    delete req.query.$skip;
    delete req.query.attributes;

    const timeZone = req.query.timeZone || moment.tz.guess();
    delete req.query.timeZone;

    const isNewReport = !!(req.query.newReport && req.query.newReport === 'true');

    const filename = cleanDownloadFilename(`Emtrain_Completion_Report_${moment().tz(timeZone).format('YYYYMMDDHHmmss')}`);
    // adding the group & campaign names to csv report details
    const dynamicQueryData = [];
    if (req.query.group) {
      const groupData = await Groups.findAll({
        where: {
          id: { [Op.in]: req.query.group },
        },
      }, { attributes: ['name'], raw: true });

      let groupNames = groupData.map(c => c.name);
      groupNames = groupNames.join(',');
      dynamicQueryData.push([`Group: ${groupNames}`]);
    }
    if (campaignId) {
      const campIds = campaignId.split(',');
      // get campaignName by id
      const campaignData = await Campaigns.findAll({
        where: {
          id: { [Op.in]: campIds },
        },
      }, { attributes: ['name'], raw: true });
      let campaignNames = campaignData.map(c => c.name);
      campaignNames = campaignNames.join(',');
      dynamicQueryData.push([`Campaign: ${campaignNames}`]);
    }
    if (isNewReport) {
      const defaults = {
        order: [[{ model: Users }, 'id', 'desc']],
        limit: 100,
        offset: 0,
      };
      const rqts = await newRestQueryToSequelize(model, req.user.id, req.query, defaults);
      const queryParams = rqts.newQuery;
      queryParams.order = [[...queryParams.order]];
      // eslint-disable-next-line max-len
      genCSV(keepAttributes, queryParams, filename, res, runContentUserReport, timeZone)(req.campaign.id, model, req.campaign.accountId, req.query.startDate, req.query.endDate, rqts.userWhere);
    } else {
      const defaults = {
        order: [[{ model: Users }, 'id', 'desc']],
        limit: 100,
        offset: 0,
      };
      const rqts = await newRestQueryToSequelize(model, req.user.id, req.query, defaults);
      const queryParams = rqts.newQuery;
      queryParams.order = [[...queryParams.order]];
      // eslint-disable-next-line max-len
      genNewCSV(keepAttributes, queryParams, filename, res, runCampaignUserReportNew, timeZone, req.query, dynamicQueryData, 'reports')(campaignId, req.campaign.accountId, req.query.startDate, req.query.endDate, rqts.userWhere);
    }
  } catch (err) {
    next(err);
  }
};

/**
 * new user report endpoint for completion report section on the front end
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @todo GDPR
 */

/**
 * @swagger
 * /campaigns/{campaignId}/newreport/users:
 *   get:
 *     summary: Campaign Users Report
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       firstName:
 *                         type: string
 *                       lastName:
 *                         type: string
 *                       email:
 *                         type: string
 *                       userStatus:
 *                         type: string
 *                       title:
 *                         type: string
 *                       location:
 *                         type: string
 *                       role:
 *                         type: string
 *                       city:
 *                         type: string
 *                       employeeId:
 *                         type: integer
 *                       supervisor:
 *                         type: string
 *                       exempt:
 *                         type: integer
 *                       hireDate:
 *                         type: integer
 *                       lastActivityAt:
 *                         type: integer
 *                       managerEmail:
 *                         type: string
 *                       assigned:
 *                         type: string
 *                       completed:
 *                         type: string
 *                       dueDate:
 *                         type: string
 *                       group:
 *                         type: string
 *                       campaign:
 *                         type: string
 *                       pctComplete:
 *                         type: integer
 *                       timeComplete:
 *                         type: string
 *                       minutesSpent:
 *                         type: string
 *                       started:
 *                         type: string
 *                       status:
 *                         type: string
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.newUsersReport = async (req, res, next) => {
  const defaults = {
    order: [[{ model: Users }, 'id', 'desc']],
    limit: config.paginate.default,
    offset: 0,
  };
  const model = 'campaign';
  try {
    const rqts = await newRestQueryToSequelize(model, req.user.id, req.query, defaults);
    const queryParams = rqts.newQuery;
    const limit = queryParams.limit;
    const offset = queryParams.offset;
    const { campaignId } = req.params;
    const generatedUserReport = await runCampaignUserReportNew(
      queryParams,
      campaignId,
      req.campaign.accountId,
      req.query.startDate,
      req.query.endDate,
      rqts.userWhere,
    );

    res.json({
      total: generatedUserReport.total,
      limit,
      skip: offset,
      data: generatedUserReport.data,
      totalUsers: generatedUserReport.totalUsers,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * user report endpoint for completion report section on the front end
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @todo GDPR
 */

/**
 * @swagger
 * /campaigns/{campaignId}/report/graphInfo:
 *   get:
 *     summary: Campaign Users Report graph data
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: campaignId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 name:
 *                   type: string
 *                 value:
 *                   type: integer
 *                 assignments:
 *                   type: integer
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.reportGraphInfo = async (req, res, next) => {
  try {
    const { campaignId } = req.params;
    const generatedUserReport = await getCampaignGraphInfo(
      campaignId,
      req.campaign.accountId,
    );

    res.json(generatedUserReport);
  } catch (err) {
    next(err);
  }
};
