/* eslint-disable no-param-reassign */
const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { restSortToSequelize, customSorting } = require('../services/utils/resourceUtils');
const { uploadFile } = require('../services/utils/fileUtils');
const { getCatalogGeography } = require('../services/utils/catalogUtils');
const { updateListingLastMappedDate } = require('../services/utils/commonUtils');

const CatalogItems = db.catalogItems;
const CatalogItemPillars = db.catalogItemPillars;
const CatalogItemIndicators = db.catalogItemIndicators;
const CatalogItemConcepts = db.catalogItemConcepts;
const CatalogItemAccounts = db.catalogItemAccounts;
const Accounts = db.accounts;
const Regions = db.regions;
const Countries = db.countries;
const States = db.states;
const SupportedLanguages = db.supportedLanguages;
const Programs = db.programs;
const Lessons = db.lessons;
const SocialCapitalPillars = db.socialCapitalPillars;
const SocialCapitalIndicators = db.socialCapitalIndicators;
const Listings = db.listings;
const Concepts = db.concepts;
const Files = db.files;
const Op = db.Sequelize.Op;

const validateCode = (req) => {
  const re = new RegExp(/^[a-z0-9-]+$/i); // alphanumeric and dash only
  if (req.body.code && !re.test(req.body.code)) {
    throw new Error('Invalid Code');
  }
  return null;
};

const validateEdition = (req) => {
  if (req.body.contentType === 'program') {
    const edition = parseInt(req.body.edition) || undefined;
    if (!edition) {
      throw new Error('Edtion required for programs');
    }
  }
};

const formatSocialData = (id, data, key) => {
  const finalArray = data.map((n) => {
    const createObj = {};
    createObj.catalogItemId = id;
    createObj[key] = parseInt(n);
    return createObj;
  });
  return finalArray;
};

/**
 * converts form data empty values to null and clears edition for non-programs
 * @param {req} the req
 * @returns {req.body} req.body with updated values
 */
const handleFormValues = (req, isUpdate = false) => {
  // allow reset to null incl with multipart form data
  req.body.duration = parseInt(req.body.duration) || null;
  req.body.edition = parseInt(req.body.edition) || null;
  req.body.regionId = parseInt(req.body.regionId) || null;
  req.body.countryId = parseInt(req.body.countryId) || null;
  req.body.stateId = parseInt(req.body.stateId) || null;
  req.body.listingId = parseInt(req.body.listingId) || null;
  req.body.code = req.body.code || null;
  req.body.part = req.body.part || null;
  req.body.frequency = req.body.frequency || null;
  req.body.title = req.body.title || null;
  // edition is for programs only
  if (req.body.contentType !== 'program') {
    req.body.edition = null;
  }
  if (isUpdate) {
    if(req.body.contentType === 'lesson') {
      req.body.edition = null;
      req.body.part = null;
      req.body.frequency = null;
    }
  }
  return req.body;
};

const saveSocialPillars = async (id, pillarsData) => {
  const saveData = formatSocialData(id, pillarsData, 'socialCapitalPillarId');
  await CatalogItemPillars.bulkCreate(saveData);
};

const saveSocialIndicators = async (id, indicatorsData) => {
  const saveData = formatSocialData(id, indicatorsData, 'socialCapitalIndicatorId');
  await CatalogItemIndicators.bulkCreate(saveData);
};

const saveCatalogItemConcepts = async (id, conceptsData) => {
  const saveData = formatSocialData(id, conceptsData, 'conceptId');
  await CatalogItemConcepts.bulkCreate(saveData);
};

const saveAccounts = async (id, accountsData) => {
  const saveData = formatSocialData(id, accountsData, 'accountId');
  await CatalogItemAccounts.bulkCreate(saveData);
};

const updateSocialPillars = async (id, pillarsData) => {
  const catalogPillars = await CatalogItemPillars.findAll({
    attributes: ['socialCapitalPillarId', 'catalogItemId'],
    where: {
      catalogItemId: id,
    },
  });
  let existingCatalogPillars = catalogPillars.map(cpr => cpr.socialCapitalPillarId);
  existingCatalogPillars = [...new Set(existingCatalogPillars)];

  const newCatalogPillars = pillarsData.filter((pillarId) => {
    return !existingCatalogPillars.find(cc => cc === pillarId);
  });

  const deletedCatalogPillars = existingCatalogPillars.filter((pillarId) => {
    return !pillarsData.find(cc => cc === pillarId);
  });

  if (deletedCatalogPillars.length) {
    await CatalogItemPillars.destroy({ where: { socialCapitalPillarId: deletedCatalogPillars, catalogItemId: id } });
  }

  if (newCatalogPillars.length) {
    const saveData = formatSocialData(id, newCatalogPillars, 'socialCapitalPillarId');
    await CatalogItemPillars.bulkCreate(saveData);
  }
};

const updateSocialIndicators = async (id, indicatorsData) => {
  const catalogIndicator = await CatalogItemIndicators.findAll({
    attributes: ['socialCapitalIndicatorId', 'catalogItemId'],
    where: {
      catalogItemId: id,
    },
  });
  let existingCatalogIndicators = catalogIndicator.map(cpr => cpr.socialCapitalIndicatorId);
  existingCatalogIndicators = [...new Set(existingCatalogIndicators)];

  const newCatalogIndicators = indicatorsData.filter((pillarId) => {
    return !existingCatalogIndicators.find(cc => cc === pillarId);
  });

  const deletedCatalogIndicators = existingCatalogIndicators.filter((pillarId) => {
    return !indicatorsData.find(cc => cc === pillarId);
  });

  if (deletedCatalogIndicators.length) {
    // eslint-disable-next-line max-len
    await CatalogItemIndicators.destroy({ where: { socialCapitalIndicatorId: deletedCatalogIndicators, catalogItemId: id } });
  }

  if (newCatalogIndicators.length) {
    const saveData = formatSocialData(id, newCatalogIndicators, 'socialCapitalIndicatorId');
    await CatalogItemIndicators.bulkCreate(saveData);
  }
};

const updateSocialConcepts = async (id, conceptsData) => {
  const catalogConcepts = await CatalogItemConcepts.findAll({
    attributes: ['conceptId', 'catalogItemId'],
    where: {
      catalogItemId: id,
    },
  });
  let existingCatalogConcepts = catalogConcepts.map(cpr => cpr.conceptId);
  existingCatalogConcepts = [...new Set(existingCatalogConcepts)];

  const newCatalogConcepts = conceptsData.filter((conceptId) => {
    return !existingCatalogConcepts.find(cc => cc === conceptId);
  });

  const deletedCatalogConcepts = existingCatalogConcepts.filter((conceptId) => {
    return !conceptsData.find(cc => cc === conceptId);
  });

  if (deletedCatalogConcepts.length) {
    // eslint-disable-next-line max-len
    await CatalogItemConcepts.destroy({ where: { conceptId: deletedCatalogConcepts, catalogItemId: id } });
  }

  if (newCatalogConcepts.length) {
    const saveData = formatSocialData(id, newCatalogConcepts, 'conceptId');
    await CatalogItemConcepts.bulkCreate(saveData);
  }
};

const updateAccounts = async (id, accountsData) => {
  const catalogAccounts = await CatalogItemAccounts.findAll({
    attributes: ['accountId', 'catalogItemId'],
    where: {
      catalogItemId: id,
    },
  });
  let existingCatalogAccounts = catalogAccounts.map(cpr => cpr.accountId);
  existingCatalogAccounts = [...new Set(existingCatalogAccounts)];
  const newCatalogAccounts = accountsData.filter((accountId) => {
    return !existingCatalogAccounts.find(cc => cc === accountId);
  });

  const deletedCatalogAccounts = existingCatalogAccounts.filter((accountId) => {
    return !accountsData.find(cc => cc === accountId);
  });

  if (accountsData.length) {
    // eslint-disable-next-line max-len
    await CatalogItemAccounts.destroy({ where: { accountId: deletedCatalogAccounts, catalogItemId: id } });
  }

  if (newCatalogAccounts.length) {
    const saveData = formatSocialData(id, newCatalogAccounts, 'accountId');
    await CatalogItemAccounts.bulkCreate(saveData);
  }
};

const findRegionById = (regionName, regions) => {
  return regions.find(region => region.region === regionName);
};
const findCountryById = (countryName, countries) => {
  return countries.find(country => country.countryName === countryName);
};
const findStateById = (stateName, states) => {
  return states.find(state => state.stateName === stateName);
};
const saveCatalogItemPillars = async (catalogId, pillarNames) => {
  const splitPillars = pillarNames.split(',');
  const pillarData = await SocialCapitalPillars.findAll({
    where: {
      name: {
        [Op.in]: splitPillars,
      },
    },
  });
  const pillarIds = pillarData.map((data) => { return data.id; });
  if (pillarIds.length) {
    await pillarIds.map(async (id) => {
      await CatalogItemPillars.upsert({ catalogItemId: catalogId, socialCapitalPillarId: id });
    });
  }
  return pillarData;
};
const saveCatalogItemIndicators = async (catalogId, indicatorNames) => {
  const splitIndicators = indicatorNames.split(',');
  const indicatorsData = await SocialCapitalIndicators.findAll({
    where: {
      name: {
        [Op.in]: splitIndicators,
      },
    },
  });
  const indicatorsIds = indicatorsData.map((data) => { return data.id; });
  if (indicatorsIds.length) {
    await indicatorsIds.map(async (id) => {
      await CatalogItemIndicators.upsert({ catalogItemId: catalogId, socialCapitalIndicatorId: id });
    });
  }
  return indicatorsData;
};

/**
 * converts a rest query from the client to sequelize params
 */
const restQueryToSequelize = (query, defaults, catalogItemId = null) => {
  const whereClause = restOperatorsToSequelize(_.omit(
    query,
    ['$limit', '$skip', '$sort', 'state.stateName'],
  ));
  if (catalogItemId) {
    whereClause.catalogId = catalogItemId;
  }
  // search for catalogItems where part is null
  if (query && query.part && query.part === 'none') {
    whereClause.part = null;
  }
  const newQuery = {
    ...defaults,
  };
  // eslint-disable-next-line no-undef
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    const paginateMax = 700;
    const limit = parseInt(query.$limit);
    newQuery.limit = Math.min(paginateMax, limit);
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  if (query.indicatorId) {
    delete newQuery.where.indicatorId;
  }
  return newQuery;
};

const getIncludeForCatalogListing = async (filters = null, isClientSpecific, isListing = false) => {
  const includeParams = [
    {
      association: CatalogItems.associations.file,
    },
  ];
  const includeListings = {
    model: Listings,
    attributes: ['id', 'type', 'title', 'description', 'subtitle'],
    include: [
      {
        model: Countries,
        attributes: ['id', 'countryName'],
      },
    ],
  };
  const countryIncludes = (isClientSpecific && filters && filters?.countryId) ?
    {
      model: Countries,
      attributes: ['id', 'countryName', 'countryCode'],
      where: {
        id: { [Op.eq]: filters.countryId },
      },
    } :
    {
      model: Countries,
      attributes: ['id', 'countryName', 'countryCode'],
    };
  const stateIncludes = (filters && filters?.state) ?
    {
      model: States,
      attributes: ['id', 'stateName', 'stateCode'],
      where: {
        stateName: { [Op.like]: `%${filters.state}%` },
      },
    } :
    {
      model: States,
      attributes: ['id', 'stateName', 'stateCode'],
    };
  const accountIncludes = (filters && filters?.clientId) ?
    {
      model: Accounts,
      attributes: ['id', 'name'],
      where: {
        id: { [Op.like]: `%${filters.clientId}%` },
      },
    } :
    {
      model: Accounts,
      attributes: ['id', 'name'],
    };

  includeParams.push(countryIncludes);
  if (isClientSpecific && isListing) {
    includeParams.push(accountIncludes);
    includeParams.push(includeListings);
  } else if (isClientSpecific) {
    includeParams.push(accountIncludes);
  } else {
    includeParams.push(includeListings);
  }
  includeParams.push(stateIncludes);

  return includeParams;
};

const getIncludeParams = async (includeUsers = false, indicatorId = false) => {
  const includeParams = [
    {
      association: CatalogItems.associations.file,
      attributes: ['id', 'path', 'type']
    },
    {
      model: SocialCapitalPillars,
      attributes: ['id', 'name'],
      through: { attributes: [] },
    },
    {
      model: SocialCapitalIndicators,
      attributes: ['id', 'name'],
      through: { attributes: [] },
    },
    {
      model: Concepts,
      attributes: ['id', 'concept'],
      through: { attributes: [] },
    },
    {
      model: Accounts,
      attributes: ['id', 'name'],
    },
    {
      model: Regions,
      attributes: ['id', 'region', 'subRegion'],
    },
    {
      model: Countries,
      attributes: ['id', 'countryName', 'countryCode'],
    },
    {
      model: States,
      attributes: ['id', 'stateName', 'stateCode'],
    },
    {
      model: Listings,
      attributes: ['id', 'type', 'title', 'description', 'subtitle'],
      include: [
        {
          model: Countries,
          attributes: ['id', 'countryName'],
        },
      ],
    },
  ];
  const indicatorIncludes = {
    model: SocialCapitalIndicators,
    attributes: ['id', 'name'],
    through: { attributes: [] },
  };
  if (indicatorId) {
    indicatorIncludes.through.id = indicatorId;
    indicatorIncludes.required = true;
  }
  includeParams.push(indicatorIncludes);
  if (includeUsers) {
    includeParams.push({ model: db.users, as: 'createdByUser', attributes: ['id', 'firstName', 'lastName'] });
    includeParams.push({ model: db.users, as: 'updatedByUser', attributes: ['id', 'firstName', 'lastName'] });
  }
  return includeParams;
};

// get the languages based on contentType(lesson/program)
const getLanguages = async (contentType, catalogId) => {
  let languagesData = [];
  let uniqueLanguageData = [];
  if (contentType === 'program') {
    const programsData = await Programs.findAll({
      where: { catalogId },
      include: [
        {
          model: SupportedLanguages,
        },
      ],
    });
    languagesData = programsData.map(program => program.supportedLanguages);
  } else if (contentType === 'lesson') {
    const lessonsData = await Lessons.findAll({
      where: { catalogId },
      include: [
        {
          model: SupportedLanguages,
        },
      ],
    });
    languagesData = lessonsData.map(lesson => lesson.supportedLanguages);
  }
  if (languagesData.length) {
    uniqueLanguageData = languagesData.reduce((prev, next) => {
      return prev.concat(next);
    });
    uniqueLanguageData = [
      ...new Map(uniqueLanguageData.map(item => [item.language, item])).values(),
    ];
  }
  return uniqueLanguageData;
};
// get the Mapped data based on contentType(lesson/program)
const getMappedData = async (contentType, catalogId) => {
  let mappedData = [];
  if (contentType === 'program') {
    mappedData = await Programs.findAll({
      where: { catalogId },
      attributes: ['name', 'internalName', 'edition', 'isBase', 'isClone', 'isCustomized'],
    });
  } else if (contentType === 'lesson') {
    mappedData = await Lessons.findAll({
      where: { catalogId },
      attributes: ['title', 'internalName', 'edition', 'isBase', 'isClone', 'isCustomized'],
    });
  }
  return mappedData;
};

/**
 * @openapi
 * /catalog-items:
 *   post:
 *     summary: Create Catalog Item
 *     tags:
 *       - CatalogItems
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/createCatalogItem'
 *     responses:
 *       200:
 *         description: CatalogItem added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 message: Catalog item created successfully
 *
 * components:
 *   schemas:
 *     createCatalogItem:
 *       type: object
 *       required:
 *         - code
 *         - contentType
 *         - title
 *         - edition
 *         - audience
 *         - countryId
 *       properties:
 *         code:
 *           type: string
 *           description: The code of the catalog item
 *         contentType:
 *           type: string
 *           description: The content type of the catalog item
 *         instructionalType:
 *           type: string
 *           description: The instructional type of the catalog item
 *         title:
 *           type: string
 *           description: Title of the catalog item
 *         description:
 *           type: string
 *           description: Description of the catalog item
 *         edition:
 *           type: string
 *           description: Edition of the catalog item
 *         audience:
 *           type: string
 *           description: Audience of the catalog item
 *         duration:
 *           type: string
 *           description: Duration of the catalog item
 *         countryId:
 *           type: string
 *           description: Country ID of the catalog item
 *         stateId:
 *           type: string
 *           description: State ID of the catalog item
 *         regionId:
 *           type: string
 *           description: Region ID of the catalog item
 *         frequency:
 *           type: string
 *           description: Frequency of the catalog item
 *         part:
 *           type: string
 *           description: Part of the catalog item
 *         isOffering:
 *           type: string
 *           description: Whether the item is an offering
 *         isClientSpecific:
 *           type: boolean
 *           description: Whether the item is client-specific
 *         clientId:
 *           type: string
 *           description: Client ID for the catalog item
 *         pillars:
 *           type: string
 *           description: Pillars of the catalog item
 *         indicators:
 *           type: string
 *           description: Indicators of the catalog item
 *         recommendType:
 *           type: string
 *           enum: [recommend, toprecommend, notrecommend]
 *           description: Recommended type of the catalog item
 */
module.exports.create = async (req, res, next) => {
  let newCatalogItem;
  try {
    const file = await uploadFile(req, 'catalogItems');
    if (file) {
      req.body.fileId = file.id;
    }
    // validations
    validateCode(req);
    validateEdition(req);

    req.body.createdBy = req.user.id;
    // set default values for couple of fiels, if not set by UI
    req.body.isClientSpecific = req.body.isClientSpecific || false;
    req.body.isOffering = req.body.isOffering || true;
    req.body.contentType = req.body.contentType || 'lesson';
    req.body.instructionalType = req.body.instructionalType || 'microlesson';

    newCatalogItem = await CatalogItems.create(handleFormValues(req));

    // association save
    if (newCatalogItem.id) {
      if (req.body.socialCapitalPillars) {
        await saveSocialPillars(newCatalogItem.id, req.body.socialCapitalPillars);
      }
      if (req.body.socialCapitalIndicators) {
        await saveSocialIndicators(newCatalogItem.id, req.body.socialCapitalIndicators);
      }
      if (req.body.socialCapitalConcepts) {
        await saveCatalogItemConcepts(newCatalogItem.id, req.body.socialCapitalConcepts);
      }
      if (req.body.accounts) {
        await saveAccounts(newCatalogItem.id, req.body.accounts);
      }
    }

    const finalCatalogItem = await CatalogItems.findByPk(newCatalogItem.id, {
      include: await getIncludeParams(),
    });
    await updateListingLastMappedDate('create', finalCatalogItem, {}, {});
    res.json(finalCatalogItem);
  } catch (err) {
    if (err.message === 'Validation error') {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /catalog-items/{catalogId}:
 *   patch:
 *     summary: Update the Catalog Item based on Id
 *     tags:
 *       - CatalogItems
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: catalogId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: catalogItemId of the existing catalogItem
 *     requestBody:
 *       description: The model for the parameters are described below.
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/editCatalogItem'
 *     responses:
 *       200:
 *         description: CatalogItem updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 message: Catalog item updated successfully
 *
 * components:
 *   schemas:
 *     editCatalogItem:
 *       type: object
 *       required:
 *         - code
 *         - contentType
 *         - title
 *         - edition
 *         - audience
 *         - countryId
 *       properties:
 *         code:
 *           type: string
 *           description: The code of the catalog item
 *         contentType:
 *           type: string
 *           description: The content type of the catalog item
 *         instructionalType:
 *           type: string
 *           description: The instructional type of the catalog item
 *         title:
 *           type: string
 *           description: Title of the catalog item
 *         description:
 *           type: string
 *           description: Description of the catalog item
 *         edition:
 *           type: string
 *           description: Edition of the catalog item
 *         audience:
 *           type: string
 *           description: Audience of the catalog item
 *         duration:
 *           type: string
 *           description: Duration of the catalog item
 *         countryId:
 *           type: string
 *           description: Country ID of the catalog item
 *         stateId:
 *           type: string
 *           description: State ID of the catalog item
 *         regionId:
 *           type: string
 *           description: Region ID of the catalog item
 *         frequency:
 *           type: string
 *           description: Frequency of the catalog item
 *         part:
 *           type: string
 *           description: Part of the catalog item
 *         isOffering:
 *           type: string
 *           description: Whether the item is an offering
 *         isClientSpecific:
 *           type: string
 *           description: Client specific flag of the catalog item
 *         clientId:
 *           type: string
 *           description: Client ID for the catalog item
 *         pillars:
 *           type: string
 *           description: Pillars of the catalog item
 *         indicators:
 *           type: string
 *           description: Indicators of the catalog item
 *         recommendType:
 *           type: string
 *           enum: [recommend, toprecommend, notrecommend]
 *           description: Recommended type of the catalog item
 */
module.exports.patch = async (req, res, next) => {
  try {
    const newFile = await uploadFile(req, 'catalogItems');
    const { catalogItemId } = req.params;
    req.body.updatedBy = req.user.id;
    if (newFile) {
      req.body.fileId = newFile.id;
    }

    // validations
    validateCode(req);
    validateEdition(req);

    await CatalogItems.update(handleFormValues(req, true), {
      where: {
        id: catalogItemId,
      },
    });
    // association save
    if (req.body.socialCapitalPillars) {
      await updateSocialPillars(catalogItemId, req.body.socialCapitalPillars);
    } else {
      await CatalogItemPillars.destroy({ where: { catalogItemId } });
    }
    if (req.body.socialCapitalIndicators) {
      await updateSocialIndicators(catalogItemId, req.body.socialCapitalIndicators);
    } else {
      await CatalogItemIndicators.destroy({ where: { catalogItemId } });
    }
    if (req.body.socialCapitalConcepts) {
      await updateSocialConcepts(catalogItemId, req.body.socialCapitalConcepts);
    } else {
      await CatalogItemConcepts.destroy({ where: { catalogItemId } });
    }
    if (req.body.accounts) {
      await updateAccounts(catalogItemId, req.body.accounts);
    } else {
      CatalogItemAccounts.destroy({ where: { catalogItemId } });
    }
    const finalCatalogItem = await CatalogItems.findByPk(catalogItemId, {
      include: await getIncludeParams(),
    });
    await updateListingLastMappedDate('update', finalCatalogItem, req.body, req.catalogItem);
    res.json(finalCatalogItem);
  } catch (err) {
    if (err.message === 'Validation error') {
      res.status(400).send({
        message: err.message,
        errors: err.errors,
      });
    } else {
      next(err);
    }
  }
};

/**
 * @openapi
 * /catalog-items:
 *   get:
 *     summary: Fetch all the Catalog Item Values
 *     tags:
 *       - CatalogItems
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successfully fetched all Catalog Items
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     example: "123"
 *                   name:
 *                     type: string
 *                     example: "Item Name"
 *                   description:
 *                     type: string
 *                     example: "A description of the item"
 */
module.exports.list = async (req, res, next) => {
  try {
    const defaults = {
      order: [['updatedAt', 'DESC']],
      limit: config.paginate.default,
      offset: 0,
    };

    let shortedByState = false;
    let shortedByCountry = false;
    let orderByState = 'ASC';
    const extraFilters = {};

    let isClientSpecific = false;
    let isListing = false;
    if (req.query && (req.query.isClientSpecific || req.query.catalogProgram)) {
      isClientSpecific = req.query.isClientSpecific === '1' || req.query.catalogProgram === '1';
      if(req.query.catalogProgram) {
        isListing = true;
      }
      delete req.query.catalogProgram;
    }
    if (req.query.hasOwnProperty('state.stateName')) {
      extraFilters.state = req.query['state.stateName'].$like;
    }
    if (req.query.hasOwnProperty('title')) {
      extraFilters.title = req.query.title.$like;
    }
    if (req.query.hasOwnProperty('clientId')) {
      extraFilters.clientId = req.query.clientId;
    }
    if (req.query.hasOwnProperty('countryId')) {
      extraFilters.countryId = req.query.countryId;
    }
    if (req.query && req.query?.$sort) {
      shortedByState = Object.keys(req.query.$sort).includes('state.stateName');
      shortedByCountry = Object.keys(req.query.$sort).includes('country.countryName');
      orderByState = req.query.$sort[Object.keys(req.query.$sort)] === '1' ? 'ASC' : 'DESC';
    }

    const queryParams = restQueryToSequelize(req.query, defaults);
    queryParams.order = [[...queryParams.order]];
    if (shortedByState) {
      queryParams.order = [['state', 'stateName', orderByState]];
    }
    if (shortedByCountry) {
      queryParams.order = [['country', 'countryCode', orderByState]];
    }
    if (queryParams.where && queryParams.where.clientId) {
      delete queryParams.where.clientId;
    }
    let catalogItemsFilters = {};
    let countryCatalogFilter = {};

    if (queryParams.where && queryParams.where.title) {
      const catalogItemTitle = req.query.title.$like.slice(1, -1).trim();
      catalogItemsFilters = {
        [db.Sequelize.Op.or]: (isClientSpecific) ? [
          db.sequelize.literal(`\`catalogItems\`.\`title\` LIKE "%${catalogItemTitle}%"`),
        ] : [
          db.sequelize.literal(`\`catalogItems\`.\`title\` LIKE "%${catalogItemTitle}%"`),
          db.sequelize.literal(`\`listing\`.\`title\` LIKE "%${catalogItemTitle}%"`),
        ],
      };
      delete queryParams.where.title;
    }
    if (queryParams.where && queryParams.where.countryId) {
      countryCatalogFilter = {
        [db.Sequelize.Op.or]: (isClientSpecific) ? [
          db.sequelize.literal(`\`country\`.\`id\` = "${req.query.countryId}"`),
        ] : [
          db.sequelize.literal(`\`country\`.\`id\` = "${req.query.countryId}"`),
          db.sequelize.literal(`\`listing->country\`.\`id\` = "${req.query.countryId}"`),
        ],
      };
      if (!isClientSpecific) {
        const countryListFilter = {
          [db.Sequelize.Op.and]: countryCatalogFilter,
        };
        catalogItemsFilters = { ...catalogItemsFilters, ...countryListFilter };
        delete queryParams.where.countryId;
      }
    }
    const combinedCatalogFilter = { ...queryParams.where, ...catalogItemsFilters };
    const finalQuery = {
      subQuery: isClientSpecific,
      group: ['catalogItems.id'],
      ...queryParams,
      include: await getIncludeForCatalogListing(extraFilters, isClientSpecific, isListing),
      where: combinedCatalogFilter,
    };
    const pagedResult = {
      limit: queryParams.limit,
      skip: queryParams.offset,
    };
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
      include: _.pick(finalQuery, ['include']).include,
      distinct: true,
    };
    const count = await CatalogItems.count(countQuery);
    const data = await CatalogItems.findAll(finalQuery);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.mappedItems = async (req, res, next) => {
  try {
    const { catalogItemId } = req.params;
    const { query } = req;
    const contentType = query.contentType === 'program' ? 'program' : 'lesson';
    const attributes = query.contentType === 'program'
      ? ['id', 'name', 'internalName', 'edition', 'isBase', 'isClone', 'isCustomized']
      : ['id', 'title', 'internalName', 'edition', 'isBase', 'isClone', 'isCustomized'];
    delete (query.contentType);

    const defaults = {
      order: [['updatedAt', 'DESC']],
      limit: config.paginate.default,
      offset: 0,
    };
    const queryParams = restQueryToSequelize(req.query, defaults, catalogItemId);
    queryParams.order = [[...queryParams.order]];

    const finalQuery = {
      ...queryParams,
      attributes,
    };

    const pagedResult = {
      limit: queryParams.limit,
      skip: queryParams.offset,
    };
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
      include: _.pick(finalQuery, ['include']).include,
      distinct: true,
    };
    let count;
    let data;
    if (contentType === 'program') {
      count = await Programs.count(countQuery);
      data = await Programs.findAll(finalQuery);
    } else if (contentType === 'lesson') {
      count = await Lessons.count(countQuery);
      data = await Lessons.findAll(finalQuery);
    }
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /catalog-items/{catalogId}:
 *   get:
 *     summary: Fetch Catalog Item based on catalogId
 *     tags:
 *       - CatalogItems
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: catalogId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: catalogItemId of the existing catalogItem
 *     responses:
 *       200:
 *         description: Successfully fetched particular Catalog Item based on its Id
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 id: "123"
 *                 code: "CAT-001"
 *                 title: "Introduction to Cataloging"
 *                 description: "Basic catalog item details."
 */
module.exports.read = async (req, res, next) => {
  try {
    const catalogItemData = req.catalogItem.get({ plain: true });
    catalogItemData.supportedLanguages = await getLanguages(catalogItemData.contentType, catalogItemData.id);
    catalogItemData.mappedData = await getMappedData(catalogItemData.contentType, catalogItemData.id);
    res.json(catalogItemData);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /catalog-items/{catalogId}:
 *   delete:
 *     summary: Delete the existing CatalogItem
 *     tags:
 *       - CatalogItems
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: catalogId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the existing CatalogItem
 *     responses:
 *       200:
 *         description: Successfully deleted catalogItem record
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 message: CatalogItem deleted successfully
 */
module.exports.delete = async (req, res, next) => {
  try {
    const { catalogItemId } = req.params;
    const data = await CatalogItems.destroy({
      where: {
        id: catalogItemId,
      },
    });
    await CatalogItemPillars.destroy({ where: { catalogItemId } });
    await CatalogItemIndicators.destroy({ where: { catalogItemId } });
    await CatalogItemConcepts.destroy({ where: { catalogItemId } });
    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.uploadCsv = async (req, res, next) => {
  try {
    if (!req.file) {
      const err = new Error(req.i18n.t('catalogItems.catalogItem_csv_import_Error'));
      err.status = 400;
      throw err;
    }
    let catalogData = req.file.buffer;
    catalogData = catalogData.toString('utf8');
    catalogData = catalogData.split('\n');

    const regionList = await Regions.findAll();
    const countryList = await Countries.findAll();
    const stateList = await States.findAll();
    const id = 'catalogId';
    // await catalogData.map(async (row) => {
    for await (const row of catalogData) {
      const fields = row.split('|');
      const record = fields[0];
      const splitRecord = record.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);
      const catalogId = 0; const countryCode = 1;
      const contentType = 2; const instructionalType = 3;
      const title = 4;
      const description = 6; const pillarId = 8;
      const indicatorId = 13; const countryIndexId = 15;
      const stateIndexId = 16; const regionIndexId = 17;
      const edition = 18; const duration = 19;
      const audience = 20; const frequency = 21;
      const part = 22; const isOffering = 25;
      const isClientSpecific = 26; const clientId = 27;
      if (splitRecord[catalogId] && splitRecord[catalogId] !== id) {
        let regionId = findRegionById(splitRecord[regionIndexId], regionList);
        let countryId = findCountryById(splitRecord[countryIndexId], countryList);
        let stateId = findStateById(splitRecord[stateIndexId], stateList);
        regionId = regionId ? regionId.id : null;
        countryId = countryId ? countryId.id : null;
        stateId = stateId ? stateId.id : null;

        const catalogObj = {
          id: +splitRecord[catalogId],
          code: splitRecord[countryCode],
          contentType: splitRecord[contentType],
          instructionalType: splitRecord[instructionalType],
          title: splitRecord[title],
          description: splitRecord[description],
          regionId,
          countryId,
          stateId,
          edition: splitRecord[edition],
          duration: +splitRecord[duration],
          audience: splitRecord[audience],
          frequency: splitRecord[frequency],
          part: splitRecord[part],
          isOffering: splitRecord[isOffering],
          isClientSpecific: +splitRecord[isClientSpecific],
          clientId: splitRecord[clientId],
          createdBy: req.user.id,
        };
        await db.catalogItem.upsert(catalogObj);
        await saveCatalogItemPillars(catalogObj.id, splitRecord[pillarId]);
        await saveCatalogItemIndicators(catalogObj.id, splitRecord[indicatorId]);
      }
    }
    // await Promise.all(promises);
    res.json({ success: true });
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /catalog-items/catalogMasterData:
 *   get:
 *     summary: Fetch all dropdown values for the CatalogItem page
 *     tags:
 *       - CatalogItems
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successfully fetched all the dropdown values
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 countries: [{ id: 1, name: "USA" }]
 *                 states: [{ id: 5, name: "California" }]
 *                 regions: [{ id: 12, name: "West Coast" }]
 */
module.exports.catalogMasterData = async (req, res, next) => {
  try {
    const regionList = await Regions.findAll({ attributes: ['id', 'region', 'subRegion'], order: [['region', 'ASC']] });
    const countryList = await getCatalogGeography();
    const stateList = await States.findAll({
      attributes: ['id', 'stateName', 'stateCode', 'countryId'],
      order: [['stateName', 'ASC']],
    });
    const pillarsList = await SocialCapitalPillars.findAll({
      attributes: ['id', 'name'],
      order: [['name', 'ASC']],
    });
    const indicatorsList = await SocialCapitalIndicators.findAll({
      attributes: ['id', 'name'],
      order: [['name', 'ASC']],
    });
    const conceptsList = await Concepts.findAll({
      attributes: ['id', 'concept'],
      order: [['concept', 'ASC']],
    });
    const listings = await Listings.findAll({
      attributes: ['id', 'type', 'title', 'description', 'subtitle'],
      include: [
        {
          model: Countries,
          attributes: ['id', 'countryName'],
        },
      ],
      order: [['title', 'ASC']],
    });
    res.json({
      regionList,
      countryList,
      stateList,
      pillarsList,
      indicatorsList,
      conceptsList,
      listings,
    });
  } catch (err) {
    next(err);
  }
};
/**
 * @openapi
 * /catalog-items/catalogGeographyData:
 *   get:
 *     summary: Fetch all the dropdown values for catalogGeography
 *     tags:
 *       - CatalogItems
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successfully fetched all the catalogGeography dropdown values
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               example:
 *                 countries: [{ id: 1, name: "USA" }]
 *                 states: [{ id: 10, name: "Texas" }]
 *                 regions: [{ id: 7, name: "Southwest" }]
 */
module.exports.catalogGeographyData = async (req, res, next) => {
  try {
    const getCatalogGeographyData = await getCatalogGeography();
    res.json({
      data: getCatalogGeographyData,
    });
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the catalogItem when an id is passed in the route
module.exports.catalogItemsById = async function (req, res, next, id) {
  const queryParams = {
    where: { id },
    include: await getIncludeParams(true),
  };
  try {
    const finalQuery = {
      ...queryParams,
      where: { ...queryParams.where },
    };

    const catalogItem = await CatalogItems.findOne(finalQuery);

    if (!catalogItem) {
      const err = new Error(req.i18n.t('catalogItems.catalogItem_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.catalogItem = catalogItem;
    next();
  } catch (err) {
    next(err);
  }
};
