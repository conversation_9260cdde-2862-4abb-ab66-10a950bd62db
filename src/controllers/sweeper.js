const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const { invokeSweeperTask } = require('../services/sweeper/sweeperTasks');
const { startAutomationSingleCampaign } = require('../services/utils/campaignSweeper');

const Semaphores = db.semaphores;

const restSortToSequelize = (restSort) => {
  return _.map(restSort, (value, key) => {
    return [key, value === '-1' ? 'DESC' : 'ASC'];
  });
};

const restQueryToSequelize = (query, defaults) => {
  const whereClause = _.omit(query, ['$limit', '$skip', '$sort']);

  const newQuery = {
    ...defaults,
  };
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  return newQuery;
};

module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };
  const queryParams = restQueryToSequelize(req.query, defaults);

  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };

  try {
    const count = await Semaphores.count();
    const data = await Semaphores.findAll(queryParams);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = (req, res) => {
  res.json(req.semaphore);
};

module.exports.patch = async (req, res, next) => {
  const semaphore = req.semaphore;
  try {
    let updatedSemaphore = semaphore;
    const oldStatus = semaphore.status;
    const newStatus = req.body.status;
    const newLockingUUID = req.body.lockingUUID ? req.body.lockingUUID : '';
    if (newStatus && (oldStatus !== newStatus)) {
      updatedSemaphore = await semaphore.update({
        status: newStatus,
        lockingUUID: newLockingUUID,
      });
    }
    res.json(updatedSemaphore);
  } catch (err) {
    next(err);
  }
};

module.exports.launchSweeperTask = async (req, res, next) => {
  const semaphore = req.semaphore;
  try {
    if (semaphore) {
      await invokeSweeperTask(semaphore.type, req.body.lockingUUID);
    }
    res.json(await Semaphores.findByPk(semaphore.id));
  } catch (err) {
    next(err);
  }
};

module.exports.semaphoreById = async function (req, res, next, id) {
  try {
    const semaphore = await Semaphores.findByPk(id);
    if (!semaphore) {
      const err = new Error(req.i18n.t('tags.tag_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.semaphore = semaphore;
    next();
  } catch (err) {
    next(err);
  }
};

module.exports.automationCampaignStart = async (req, res, next) => {
  try {
    const { campaignId } = req.params;
    const selectedCampaign = await db.campaigns.findByPk(campaignId);
    if (!selectedCampaign) {
      const error = new Error('Record Not Found');
      error.status = 404;
      throw error;
    } else if (selectedCampaign.status !== 'scheduled') {
      const error = new Error('Campaign is not scheduled');
      error.status = 400;
      throw error;
    }
    await startAutomationSingleCampaign(campaignId);
    res.json('OK');
  } catch (error) {
    next(error);
  }
};
