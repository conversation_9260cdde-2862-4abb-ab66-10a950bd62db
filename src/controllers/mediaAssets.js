const db = require('../db');
const _ = require('lodash');
const { uploadFile, replaceFile } = require('../services/utils/fileUtils');
const { updateAttachedTopics, updateAttachedTags, restSortToSequelize,
  getMediaAssetsIsPrivateClause, searchTextFromSearchInfo, setResourceAccess,
  updateElasticSearch, getResourceBundlesIds, setPublicAccess } = require('../services/utils/resourceUtils');
const config = require('../config/config');
const { deriveSearch } = require('../services/utils/mediaAssetUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { getAllowedPermissions } = require('../services/acl/acl');
const { removeMediaAssetFromCampaigns } = require('../services/utils/campaignUtils');
const { localizeModelObject } = require('../services/utils/localizationUtils');

const MediaAssets = db.mediaAssets;
const Resource = db.resources;
const Tag = db.tags;
const ResourceTag = db.resourceTags;
const ResourceTopic = db.resourceTopics;
const ResourceBundles = db.resourceBundles;
const ContentStrings = db.contentStrings;
const Topic = db.topics;
const Op = db.Sequelize.Op;

const topicOrder = [
  { model: Resource, as: 'resource' },
  { model: Topic, through: 'resourceTopics' },
  { model: ResourceTopic },
  'order',
  'asc',
];

const tagOrder = [
  { model: Resource, as: 'resource' },
  { model: Tag, through: 'resourceTags' },
  { model: ResourceTag },
  'order',
  'asc',
];

const getIncludeParams = async (user) => {
  const bundleIds = await getResourceBundlesIds(user ? user.accountId : null);

  const includeParams = [{
    association: MediaAssets.associations.resource,
    include: [
      {
        model: Topic,
        through: 'resourceTopics',
      },
      {
        model: Tag,
        through: 'resourceTags',
      },
      {
        model: ResourceBundles,
        where: {
          bundleId: {
            [Op.in]: bundleIds,
          },
        },
      },
    ],
    required: true,
  },
  {
    association: MediaAssets.associations.file,
  },
  {
    model: ContentStrings,
    where: {
      model: 'mediaAsset',
    },
    required: false,
  },
  ];

  return includeParams;
};

const restQueryToSequelize = async (req, defaults) => {
  const query = req.query;
  const whereClause = restOperatorsToSequelize({ ..._.omit(query, ['$limit', '$skip', '$sort', 'topics', 'tags', 'deletedAt']) });

  // includeParams has the default include params.  In this case, we want to add a where clause to the
  // topics and tags includes because the user is asking for specific tags and/or topics.
  const includeParams = await getIncludeParams(req.user);
  let topicsInclude = includeParams[0].include[0];
  if (query.topics) {
    const topicIds = query.topics.split(',');
    topicsInclude = {
      ...includeParams[0].include[0],
      where: {
        id: {
          [Op.in]: topicIds,
        },
      },
    };
  }

  let tagsInclude = includeParams[0].include[1];
  if (query.tags) {
    const tagIds = query.tags.split(',');
    tagsInclude = {
      ...includeParams[0].include[1],
      where: {
        id: {
          [Op.in]: tagIds,
        },
      },
    };
  }

  // set up the new query that's specific to sequelize.  Combine the defaults, then override the where clause
  // with a combo of the default where clause and the passed in where clause.
  // Then include all the things including the where clause for the tags and topics.
  const newQuery = {
    ...defaults,
    where: { ...defaults.where, ...whereClause },
    include: [{ ...includeParams[0], include: [topicsInclude, tagsInclude] }, includeParams[1], includeParams[2]],
  };

  // make sure to pick up ResourceBundle include
  if (includeParams[0].include[2]) {
    newQuery.include[0].include.push(includeParams[0].include[2]);
  }

  // override defaults for limit, skip, and sort if passed in by caller
  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }
  return newQuery;
};

const getFilterParams = async (req, user) => {
  const allowedPermissions = await getAllowedPermissions(
    req,
    user ? user.id : null, 'mediaAssets', req.tokenPayload,
  );
  const isPrivateClause = getMediaAssetsIsPrivateClause(user, allowedPermissions);

  return { ...isPrivateClause };
};

/**
 * @openapi
 * /media-assets:
 *   get:
 *     summary: List Resource
 *     tags:
 *       - Resource
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *       - name: skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: total number of records
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   description: Array of media asset objects
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/mediaAssets'
 *                       - type: object
 *                         properties:
 *                           resource:
 *                             $ref: '#/components/schemas/resources'
 *                           topics:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/topics'
 *                           tags:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/tags'
 *                           supportedLanguages:
 *                             $ref: '#/components/schemas/supportedLanguages'
 *                           lessonCards:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/lessonCards'
 *                           contentStrings:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/contentStrings'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       400:
 *         description: Bad Request
 *       5XX:
 *         description: Unexpected error
 */

module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };
  const queryParams = await restQueryToSequelize(req, defaults);
  const filterParams = await getFilterParams(req, req.user);
  const finalQuery = {
    ...queryParams,
    where: {
      ...queryParams.where,
      ...filterParams,
    },
  };

  finalQuery.order = [[...finalQuery.order], topicOrder, tagOrder];
  const pagedResult = {
    limit: finalQuery.limit,
    skip: finalQuery.offset,
  };
  try {
    const bundleIds = await getResourceBundlesIds(req.user ? req.user.accountId : null);
    const countQuery = {
      distinct: 'id',
      include: [{
        model: Resource,
        as: 'resource',
        include: {
          model: ResourceBundles,
          where: {
            bundleId: {
              [Op.in]: bundleIds,
            },
          },
        },
        required: true,
      }],
    };

    countQuery.where = _.pick(finalQuery, ['where']).where;
    const count = await MediaAssets.count(countQuery);
    const results = await MediaAssets.findAll(finalQuery);
    const data = results.map((record) => {
      return localizeModelObject(req, record.get({ plain: true }), req.i18n.language, false);
    });
    // include the event information for each qA (helpful, saved, shared, viewed)
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = async (req, res, next) => {
  try {
    const allowedPermissions = await getAllowedPermissions(
      req,
      req.user ? req.user.id : null, 'mediaAssets', req.tokenPayload,
    );

    if (req.asset.deletedAt !== null &&
      !allowedPermissions.mediaAssets.includes('readArchived')) {
      const err = new Error(req.i18n.t('mediaAssets.asset_no_permissions_Error'));
      err.status = 401;
      throw err;
    }
    if (req.asset.isPrivate &&
      !allowedPermissions.mediaAssets.includes('readPrivate')) {
      const err = new Error(req.i18n.t('mediaAssets.asset_no_permissions_Error'));
      err.status = 401;
      throw err;
    }
    res.json(req.asset);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /media-assets:
 *   post:
 *     summary: Add New Resource
 *     tags:
 *       - Resource
 *     security:
 *       - JWT: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: 'Resource Test Title'
 *               description:
 *                 type: string
 *                 example: 'description...'
 *               link:
 *                 type: string
 *                 example: 'https://meyerweb.com/eric/tools/dencoder/'
 *               topicIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["2", "4", "6"]
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["Help", "Job"]
 *               action:
 *                 type: string
 *                 example: 'link'
 *               mediaType:
 *                 type: string
 *                 nullable: true
 *                 example: null
 *               isPrivate:
 *                 type: boolean
 *                 example: true
 *               isPublic:
 *                 type: boolean
 *                 example: true
 *               pages:
 *                 type: integer
 *                 example: 20
 *               durationSeconds:
 *                 type: integer
 *                 example: 100
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/mediaAssets'
 *                 - type: object
 *                   properties:
 *                     resource:
 *                       $ref: '#/components/schemas/resources'
 *                     topics:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/topics'
 *                     tags:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/tags'
 *                     supportedLanguages:
 *                       $ref: '#/components/schemas/supportedLanguages'
 *                     lessonCards:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/lessonCards'
 *                     contentStrings:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/contentStrings'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  delete req.body.id;
  const searchInfo = await deriveSearch(req.body);
  const searchText = searchTextFromSearchInfo(searchInfo);

  try {
    const file = await uploadFile(req, 'media-assets');
    const asset = {
      title: req.body.title,
      description: req.body.description,
      action: req.body.action,
      link: req.body.link,
      mediaType: req.body.mediaType,
      durationSeconds: req.body.durationSeconds,
      topicIds: req.body.topicIds,
      isPrivate: req.body.isPrivate,
      fileId: file ? file.id : null,
      resource: {
        digestable: 'mediaAssets',
        searchText,
        tags: req.body.tags,
        topicIds: req.body.topicIds,
        isPublic: req.body.isPublic,
      },
    };
    const newAsset = await MediaAssets
      .create(asset, {
        include: MediaAssets.associations.resource,
      });
    await setResourceAccess(req.user, newAsset.resource);
    await updateAttachedTopics(req, newAsset);
    await updateAttachedTags(req, newAsset);
    await updateElasticSearch(req, newAsset.resource.id, searchInfo);

    const finalAsset = await MediaAssets.findByPk(newAsset.id, {
      include: await getIncludeParams(req.user),
      order: [topicOrder, tagOrder],
    });
    res.json(finalAsset);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /media-assets/{resourceId}:
 *   patch:
 *     summary: Update Resource
 *     tags:
 *       - Resource
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: resourceId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: 'Resource Test Title'
 *               description:
 *                 type: string
 *                 example: 'description...'
 *               link:
 *                 type: string
 *                 example: 'https://meyerweb.com/eric/tools/dencoder/'
 *               topicIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["2", "4", "6"]
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["Help", "Job"]
 *               action:
 *                 type: string
 *                 example: 'link'
 *               mediaType:
 *                 type: string
 *                 nullable: true
 *                 example: null
 *               isPrivate:
 *                 type: boolean
 *                 example: true
 *               isPublic:
 *                 type: boolean
 *                 example: true
 *               pages:
 *                 type: integer
 *                 example: 20
 *               durationSeconds:
 *                 type: integer
 *                 example: 100
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/mediaAssets'
 *                 - type: object
 *                   properties:
 *                     resource:
 *                       $ref: '#/components/schemas/resources'
 *                     topics:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/topics'
 *                     tags:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/tags'
 *                     supportedLanguages:
 *                       $ref: '#/components/schemas/supportedLanguages'
 *                     lessonCards:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/lessonCards'
 *                     contentStrings:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/contentStrings'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.patch = async (req, res, next) => {
  const asset = req.asset;
  const file = req.file;
  let updatedFiles;
  delete req.body.resourceId;
  delete req.body.resource;

  try {
    if (file) {
      updatedFiles = await replaceFile(req, req.body.fileId);
      req.body.fileId = updatedFiles.id;
    }
    const updatedAsset = await asset.update(req.body, {
      include: await getIncludeParams(req.user),
      order: [topicOrder, tagOrder],
    });
    // check if public access has changed
    if (req.body.isPublic !== undefined &&
        req.body.isPublic !== updatedAsset.resource.isPublic) {
      const newResource = await updatedAsset.resource.update({ isPublic: req.body.isPublic });
      await setPublicAccess(newResource);
    }
    const searchInfo = await deriveSearch(updatedAsset);
    const searchText = searchTextFromSearchInfo(searchInfo);
    await updatedAsset.resource.update({ searchText });
    await updateElasticSearch(req, updatedAsset.resource.id, searchInfo);
    await updateAttachedTopics(req, updatedAsset);
    await updateAttachedTags(req, updatedAsset);
    const finalAsset = await MediaAssets.findByPk(updatedAsset.id, {
      include: await getIncludeParams(req.user),
      order: [topicOrder, tagOrder],
    });
    res.json(finalAsset);
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * /media-assets/{resourceId}:
 *   delete:
 *     summary: Archive Resource
 *     tags:
 *       - Resource
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: resourceId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/mediaAssets'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  const asset = req.asset;
  try {
    const updatedAsset = await asset.destroy();
    await ResourceTag.destroy({
      where: {
        resourceId: updatedAsset.resourceId,
      },
    });
    await ResourceTopic.destroy({
      where: {
        resourceId: updatedAsset.resourceId,
      },
    });
    const finalAsset = await MediaAssets.findOne({
      where: {
        id: updatedAsset.id,
      },
      paranoid: false, // includes soft deleted (archived) records
    });
    await removeMediaAssetFromCampaigns(asset.id);
    res.json(finalAsset);
  } catch (err) {
    next(err);
  }
};

module.exports.mediaById = async (req, res, next, id) => {
  try {
    const asset = await MediaAssets.findOne({
      where: { id },
      include: await getIncludeParams(req.user),
      order: [topicOrder, tagOrder],
      paranoid: false, // includes soft deleted (archived) records
    });
    if (!asset) {
      const err = new Error(req.i18n.t('mediaAssets.asset_no_permissions_Error', { id }));
      err.status = 404;
      throw err;
    }
    req.asset = localizeModelObject(req, asset, req.i18n.language, false);
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @openapi
 * components:
 *   schemas:
 *     mediaAssets:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         title:
 *           type: string
 *         description:
 *           type: string
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 *         fileId:
 *           type: integer
 *         userId:
 *           type: integer
 *         action:
 *           type: string
 *         link:
 *           type: string
 *         mediaType:
 *           type: string
 *         durationSeconds:
 *           type: integer
 *         pages:
 *           type: integer
 *         resourceId:
 *           type: integer
 *         isPrivate:
 *           type: boolean
 *         deletedAt:
 *           type: string
 *         contentStrings:
 *           type: array
 *           items:
 *             type: object
 */
