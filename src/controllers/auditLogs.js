const { omit } = require('lodash');
const jwt = require('jwt-simple');
const config = require('./../config/config');
const db = require('../db');
const { EMTRAIN_ACCOUNT_IDS } = require('../../src/constants');

const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { restSortToSequelize } = require('../services/utils/resourceUtils');
const { sign } = require('openpgp');

const { getPublicKey } = require('../services/utils/loadAuthKeys');
const publicKey = getPublicKey();

const auditLogs = db.auditLogs;
const Op = db.Sequelize.Op;

/**
 * converts a rest query from the client to sequelize params
 */
const restQueryToSequelize = (query, defaults) => {
  const paginateMax = 100;

  const unpaginatedQuery = restOperatorsToSequelize(omit(query, ['$limit', '$skip', '$sort']), true);

  let newQuery = {};
  if (query) {
    newQuery = { ...defaults, ...unpaginatedQuery };
    // override defaults for limit, skip, sort if passed in by caller
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(paginateMax, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
    unpaginatedQuery.userAccountId = query.userAccountId;
    newQuery.where = unpaginatedQuery;
  }
  return [newQuery, { where: unpaginatedQuery }];
};

// to be called from within the server
module.exports.createAuditLog = async (logData, req, signInLogData) => {
  const { userId, accountId, adminAccountId } = jwt.decode(req.headers.authorization, publicKey, true, 'RS256');
  const { feature, action } = logData;
  logData.adminId = userId;
  logData.adminAccountId = signInLogData?.adminAccountId || adminAccountId;
  logData.userAccountId = signInLogData?.userAccountId || accountId;

  try {
    if (!EMTRAIN_ACCOUNT_IDS.includes(logData.adminAccountId)) {
      return null;
    }
    if (!logData.adminId || !logData.adminAccountId || !logData.userAccountId || !feature || !action) {
      return null;
    }
    const auditLog = await auditLogs.create(logData);
    return auditLog;
  } catch {} // eslint-disable-line no-empty

  return null;
};

// called by the client
module.exports.create = async (req, res, next) => {
  try {
    const logData = req.body;
    let signInLogData = null;
    if (req.body.action === 'account-signin' && EMTRAIN_ACCOUNT_IDS.includes(logData.adminAccountId)) {
      // the jwt token does not exist yet. adjust the data accordingly
      signInLogData = logData;
    }

    const auditLog = await this.createAuditLog(logData, req, signInLogData);

    res.json(auditLog);
  } catch (err) {
    err.status = 422;
    next(err);
  }
};

module.exports.list = async (req, res, next) => {
  const adminEmailSearch = req.query.adminEmail;
  delete (req.query.adminEmail);
  const defaults = {
    order: [['createdAt', 'DESC']],
    limit: 10,
    offset: 0,
  };
  try {
    const [queryParams, unpaginatedQuery] = restQueryToSequelize(req.query, defaults);
    queryParams.limit = req.query.$limit ? parseInt(req.query.$limit) : queryParams.limit;
    const pagedResult = {
      limit: queryParams.limit,
      skip: queryParams.offset,
    };

    unpaginatedQuery.attributes = [];
    // eslint-disable-next-line max-len
    queryParams.attributes = ['id', 'adminId', 'adminAccountId', 'userId', 'userAccountId', 'feature', 'action', 'object', 'objectId', 'childObject', 'childObjectId', 'field', 'originalData', 'updatedData', 'createdAt'];

    queryParams.include = [
      {
        model: db.users,
        required: true,
        attributes: ['id', 'email'],
        as: 'admin',
        paranoid: false,
      },
    ];
    if (adminEmailSearch) {
      queryParams.include[0].where = { email: { [Op.like]: `%${adminEmailSearch}%` } };
    }

    const promises = [];
    promises.push(auditLogs.count(unpaginatedQuery));
    promises.push(auditLogs.findAll(queryParams));

    const [auditLogsCount, auditLogsData] = await Promise.all(promises);

    res.json({ total: auditLogsCount, ...pagedResult, data: auditLogsData });
  } catch (error) {
    next(error);
  }
};

