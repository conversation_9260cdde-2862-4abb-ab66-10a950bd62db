const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const fs = require('fs');
const fse = require('fs-extra');
const stream = require('stream');
const path = require('path');
const { S3Client, DeleteObjectCommand } = require("@aws-sdk/client-s3");
const { Upload } = require("@aws-sdk/lib-storage");
const puppeteer = require('puppeteer');
const logger = require('../logger');
const { format } = require('date-fns');
const { restQueryToSequelize: rqtu, genCSV, cleanDownloadFilename, newRestQueryToSequelize,
  runProgramUserReport, runContentUserReport, genNewCSV } = require('../services/utils/reportingUtils');
const { checkAllowedAttributes } = require('../controllers/campaignReporting');
const { localizeProgram, gatherProgrami18nStrings, swapContentStrings,
  updateProgrami18nStrings, getContentStringQuery, getQueryLanguages } = require('../services/utils/localizationUtils');
const { removeProgramFromCampaigns, getContentItemsDeployedData } = require('../services/utils/campaignUtils');
const { getResourceBundlesIds, setResourceAccess, setPublicAccess, restSortToSequelize,
  removeFromElasticSearch, searchTextFromSearchInfo, updateElasticSearch,
  PUBLIC_BUNDLE_ID } = require('../services/utils/resourceUtils');
const { restOperatorsToSequelize } = require('../services/utils/sequelizeUtils');
const { uploadFile, getExistingFile, getCsvS3Bucket, getLaunchBrowser } = require('../services/utils/fileUtils');
const { assignRequestValues } = require('../services/utils/controllerUtils');
const { getAllowedPermissions } = require('../services/acl/acl');
const { deriveSearch, configureProgramForAccount, includeNumUsersAccessed,
  isProgramInCampaign, generateProgramProgressReport } = require('../services/utils/programUtils');
const { configureLessonForAccount, configurePolicyCardForAccount } = require('../services/utils/lessonUtils');
const { getNullableIntValue } = require('../services/utils/jsUtils');
const { genAndDownloadScormArchive } = require('../services/scorm/scormPackage');
const { genAndDownloadCompletionCertificate } = require('../services/pdf/programCompletion');
const { handleSupportedLanguages } = require('../services/utils/localizationUtils');
const { getProgramRequiredTime } = require('../services/utils/calcUtils');
const { validLanguageCodes } = require('../services/utils/localizationUtils.js');
const { updateLastMappedDate } = require('../services/utils/commonUtils');
const { checkForWorkdayCCLUpdates } = require('../services/utils/workdayCclUtils.js');

const Programs = db.programs;
const Resources = db.resources;
const Lessons = db.lessons;
const LessonCards = db.lessonCards;
const AccountLessonCards = db.accountLessonCards;
const LessonLessonCards = db.lessonLessonCards;
const LessonPrograms = db.lessonPrograms;
const Files = db.files;
const ResourceBundles = db.resourceBundles;
const ScormPrograms = db.scormPrograms;
const Topics = db.topics;
const Tags = db.tags;
const ContentStrings = db.contentStrings;
const SupportedLanguages = db.supportedLanguages;
const ResourceTopic = db.resourceTopics;
const ResourceTag = db.resourceTags;
const UserLessons = db.userLessons;
const AccountPrograms = db.accountPrograms;
const AccountLessons = db.accountLessons;
const Accounts = db.accounts;
const Campaigns = db.campaigns;
const CampaignItem = db.campaignItem;
const ContentPackageResources = db.contentPackageResources;
const CatalogItem = db.catalogItems;
const GroupAssignments = db.groupAssignments;
const Groups = db.groups;
const Users = db.users;
const WorkdayContentData = db.workdayContentData;

const { Op, fn, col } = db.Sequelize;

const LINE_FEED = '\n'.charCodeAt(0);
let browser;

const programRequestOptions = {
  name: { required: true },
  duration: { min: 0, allowNull: false, defaultValue: 0 },
  minTimeInMinutes: { min: 0, allowNull: true, defaultValue: null },
  minCardTimeInSeconds: { min: 0, allowNull: true, defaultValue: null },
  edition: { min: 0, allowNull: true, defaultValue: null },
  version: { min: 0, allowNull: true, defaultValue: null },
  build: { min: 0, allowNull: true, defaultValue: null },
  lifecycle: { defaultValue: 'draft' },
};

const s3Client = new S3Client({
  region: "us-east-1",
  credentials: {
    accessKeyId: config.s3.accessKeyId,
    secretAccessKey: config.s3.secretAccessKey,
  },
});

const isLocalTestMode = () => {
  return (process.env.NODE_ENV === 'test' && (!config.s3.secretAccessKey || !config.s3.accessKeyId));
};

const getLocalTestFilename = (filename) => {
  const uploadFolder = '../../testuploads/';
  const uploadPath = path.join(__dirname, uploadFolder);
  return (path.join(uploadPath, filename));
};

const createNewFile = async (object, file, folder) => {
  object.file = await getExistingFile(file); // eslint-disable-line no-param-reassign
  const newFile = await uploadFile(object, folder);
  return newFile;
};

const lessonOrder = [
  { model: Lessons, through: 'lessonPrograms' },
  { model: LessonPrograms },
  'position',
  'asc',
];

function flattenAssignment(groupAssignment) {
  const group = groupAssignment.group;
  const flattenedAssignment = groupAssignment.get({ plain: true });
  return Object.assign(flattenedAssignment, { group });
}

// looks up Group from groupAssignment
async function lookupGroup(groupAssignment, paranoid = true) {
  const group = await Groups.findByPk(groupAssignment.groupId, { paranoid });
  Object.assign(groupAssignment, { group });
  return groupAssignment;
}

const getIncludeParams = async (req, user, {
  accountId = null,
  resourceBundleStatus = null,
  includeLessons = true,
  includeContentStrings = true,
  includeSourceProgram,
  manageContent = false,
  identifyingPolicyLesson = false,
} = {}) => {
  const bundleAccountId = accountId || (user ? user.accountId : null);
  const bundleIds = await getResourceBundlesIds(bundleAccountId);
  const resourceBundlesWhere = {
    bundleId: {
      [Op.in]: bundleIds,
    },
  };
  if (resourceBundleStatus && ['active', 'retired'].includes(resourceBundleStatus)) {
    resourceBundlesWhere.status = resourceBundleStatus;
  }
  const includeResource = [{
    model: ResourceBundles,
    where: resourceBundlesWhere,
  }];
  if (!manageContent) {
    includeResource.push(
      { model: Topics, through: 'resourceTopics' },
      { model: Tags, through: 'resourceTags' },
    );
  }
  const includeParams = [
    {
      model: Resources,
      as: 'resource',
      include: includeResource,
      required: true,
    },
  ];

  if (!manageContent) {
    includeParams.push(
      { association: Programs.associations.file },
      { model: SupportedLanguages },
      { model: CatalogItem, attributes: ['id', 'title', 'listingId'] },
    );
  }
  if (includeContentStrings && !manageContent) {
    includeParams.push(getContentStringQuery(req, getQueryLanguages(req), 'program'));
  }
  if (includeLessons) {
    const lessonInclude = {
      model: Lessons,
      through: 'lessonPrograms',
      required: false,
    };

    if (identifyingPolicyLesson) {
      lessonInclude.include = [
        {
          model: LessonCards,
          attributes: ['id', 'cardType', 'list1', 'policyLink'],
          where: { cardType: 'policyAcknowledgement' },
          include: [
            {
              model: AccountLessonCards,
              attributes: ['id', 'policyType', 'link'],
              where: { accountId: bundleAccountId },
              required: false,
              include: [
                {
                  model: Files,
                  attributes: ['id', 'path'],
                  as: 'file',
                  required: false,
                },
              ],
            },
          ],
          required: false,
        },
      ];
    }
    if (includeContentStrings && !manageContent) {
      const contentStringQuery = getContentStringQuery(req, getQueryLanguages(req), 'lesson');

      if (identifyingPolicyLesson) {
        lessonInclude.include.push(contentStringQuery);
      } else {
        lessonInclude.include = [contentStringQuery];
      }
    }
    includeParams.push(lessonInclude);
  }
  if (includeSourceProgram) {
    includeParams.push({
      model: Programs,
      as: 'sourceProgram',
      required: false,
      attributes: ['id', 'name', 'internalName', 'deletedAt'],
      paranoid: false,
    });
  }
  return includeParams;
};

async function getFilterParams(req) {
  const allowedPermissions = await getAllowedPermissions(
    req,
    req.user ? req.user.id : null, 'programs', req.tokenPayload,
  );
  const lifecycleClauses = [{ lifecycle: 'publish' }, { lifecycle: 'retired' }];
  if (allowedPermissions.programs.includes('update')) {
    lifecycleClauses.push({ lifecycle: 'review' });
    lifecycleClauses.push({ lifecycle: 'draft' });
    lifecycleClauses.push({ lifecycle: 'close' });
  } else if (allowedPermissions.programs.includes('review')) {
    lifecycleClauses.push({ lifecycle: 'review' });
  }

  return { [Op.or]: lifecycleClauses };
}

/**
 * converts a rest query from the client to sequelize params
 */
const restQueryToSequelize = (query, defaults) => {
  const whereClause = restOperatorsToSequelize(_.omit(
    query,
    ['$limit', '$skip', '$sort', 'accountId', 'downloadLanguage', 'includeLessons',
      'startDate', 'endDate', 'includeSourceProgram'],
  ));

  const newQuery = {
    ...defaults,
  };
  if (!_.isEmpty(whereClause)) {
    newQuery.where = whereClause;
  }

  if (query.$limit !== undefined) {
    const paginateMax = 700;
    const limit = parseInt(query.$limit);
    newQuery.limit = Math.min(paginateMax, limit);
  }
  if (query.$skip !== undefined) {
    newQuery.offset = parseInt(query.$skip) || 0;
  }
  if (query.$sort !== undefined) {
    newQuery.order = restSortToSequelize(query.$sort);
  }
  return newQuery;
};

/**
 * @swagger
 * /programs?$limit={limit}&$skip={skip}&downloadLanguage=en&includeLessons=false:
 *  get:
 *     summary: Lessons List For Added To Package
 *     tags: [Program]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 10
 *       - name: $skip
 *         in: query
 *         required: true
 *         schema:
 *           type: integer
 *           example: 0
 *         description: Number of records to skip
 *       - name: downloadLanguage
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           example: en
 *       - name: includeLessons
 *         in: query
 *         required: false
 *         schema:
 *           type: boolean
 *           default: false
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/programs'
 *                       - type: object
 *                         properties:
 *                           resource:
 *                             $ref: '#/components/schemas/resources'
 *                           supportedLanguages:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/supportedLanguages'
 *                           contentStrings:
 *                             type: array
 *                             items:
 *                               $ref: '#/components/schemas/contentStrings'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */

/**
 * @swagger
 * /programs?$limit={limit}&$skip={skip}&downloadLanguage=en:
 *  get:
 *   summary: Get all program list
 *   tags: [Program]
 *   security:
 *     - JWT: []
 *   parameters:
 *     - name: $limit
 *       in: query
 *       description: Number of records to return
 *       required: true
 *       schema:
 *         type: integer
 *     - name: $skip
 *       in: query
 *       description: Number of records to skip
 *       required: true
 *       schema:
 *         type: integer
 *         example: 0
 *     - name: downloadLanguage
 *       in: query
 *       description: Language code for download (e.g., en)
 *       required: false
 *       schema:
 *         type: string
 *         default: en
 *   responses:
 *     200:
 *       description: Successfully fetched program list
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               $ref: '#/components/schemas/programs'
 *     400:
 *       description: Bad Request
 *     401:
 *       description: Unauthorized
 *     5XX:
 *       description: Unexpected error
 */

/**
 * @openapi
 * /programs?$limit={limit}&$skip={skip}&resourceBundleStatus=active&accountId={accountId}&downloadLanguage=en:
 *  get:
 *     summary: Get Content Library (programs)
 *     tags:
 *       - Content Library
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: $limit
 *         in: query
 *         required: true
 *         description: Limit (integer) for number of records
 *         schema:
 *           type: integer
 *       - name: $skip
 *         in: query
 *         required: true
 *         description: Skip (integer) for number of records
 *         schema:
 *           type: integer
 *           example: 0
 *       - name: accountId
 *         in: query
 *         required: true
 *         description: Account ID for filtering programs
 *         schema:
 *           type: integer
 *       - name: resourceBundleStatus
 *         in: query
 *         required: false
 *         description: Filter by resource bundle status
 *         schema:
 *           type: string
 *           example: active
 *       - name: downloadLanguage
 *         in: query
 *         required: false
 *         description: Language code for content
 *         schema:
 *           type: string
 *           example: en
 *     responses:
 *       200:
 *         description: Successfully fetched program list
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/programs'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       default:
 *         description: Unexpected error
 */
module.exports.list = async (req, res, next) => {
  let resourceBundleStatus = null;
  let manageContent = false;
  const identifyingPolicyLesson = req.query?.identifyingPolicies === 'true';
  if (req.query && req.query.resourceBundleStatus) {
    resourceBundleStatus = req.query.resourceBundleStatus;
  }
  if (req.query && req.query.manageContent) {
    manageContent = req.query.manageContent;
  }
  delete req.query.resourceBundleStatus;
  delete req.query.manageContent;
  delete req.query.identifyingPolicies;

  const allowedPermissions = await getAllowedPermissions(
    req,
    req.user ? req.user.id : null,
    'admin',
    req.tokenPayload,
  );

  try {
    // You have to be an admin to view account specific programs
    if (req.query.accountId && !allowedPermissions.admin.includes('read')) {
      const err = new Error(req.i18n.t('programs.program_no_permissions_Error'));
      err.status = 401;
      throw err;
    }

    const defaults = {
      order: [['updatedAt', 'DESC']],
      limit: config.paginate.default,
      offset: 0,
    };
    const includeLessons = !req.query.includeLessons || !req.query.includeLessons === 'false';
    const queryParams = restQueryToSequelize(req.query, defaults);
    queryParams.order = [[...queryParams.order]];
    if (includeLessons) {
      queryParams.order.push(lessonOrder);
    }

    const filterParams = await getFilterParams(req);
    const finalQuery = {
      ...queryParams,
      where: { ...queryParams.where, ...filterParams },
      include: await getIncludeParams(req, req.user, {
        accountId: req.query.accountId,
        resourceBundleStatus,
        includeLessons,
        manageContent,
        identifyingPolicyLesson,
      }),
    };
    const pagedResult = {
      limit: queryParams.limit,
      skip: queryParams.offset,
    };
    const countQuery = {
      where: _.pick(finalQuery, ['where']).where,
      include: _.pick(finalQuery, ['include']).include,
      distinct: true,
    };

    const count = await Programs.count(countQuery);
    const results = await Programs.findAll(finalQuery);
    let data = results
      .map(program => localizeProgram(req, program, req.i18n.language, true))
      .map(program => program.get({ plain: true }))
      .map((program) => {
        return {
          ...program,
          viewable: program.resource.isBrowsable,
        };
      });

    if (identifyingPolicyLesson) {
      data.map((program) => {
        program.lessons.forEach((programLesson) => {
          // Check if the lesson has a policy
          if (programLesson.hasPolicy) {
            // eslint-disable-next-line no-param-reassign
            programLesson.hasPolicyConfigured = true;
            // Ensure the lesson has lesson cards
            if (programLesson.lessonCards.length) {
              if (programLesson.lessonCards.some(lessonCard => lessonCard.accountLessonCards?.length === 0)) {
                // eslint-disable-next-line no-param-reassign
                programLesson.hasPolicyConfigured = false;
              } else {
                for (const lessonPolicyCard of programLesson.lessonCards) {
                  const policyCardData = lessonPolicyCard.accountLessonCards.map((accountCard) => {
                    return accountCard.policyType === 'link'
                      ? Boolean(accountCard.link?.length)
                      : Boolean(accountCard.file?.path);
                  });

                  if (policyCardData && policyCardData.includes(false)) {
                    // eslint-disable-next-line no-param-reassign
                    programLesson.hasPolicyConfigured = false;
                    break;
                  }
                }
              }
            }
          }
          // eslint-disable-next-line no-param-reassign
          delete programLesson.lessonCards;
        });
        return program;
      });
    }

    if (req.user && req.user.accountId) {
      const account = await Accounts.findByPk(req.user.accountId);
      if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
        const accountPrograms = await AccountPrograms.findAll({ where: { accountId: account.id } });
        const accountLessons = await AccountLessons.findAll({ where: { accountId: account.id } });

        for (let program of data) {
          const matchingAccountProgram =
            accountPrograms.find(accountProgram => accountProgram.programId === program.id);
          if (data.lessons) {
            for (let nextLesson of data.lessons) {
              const matchingAccountLesson =
                accountLessons.find(accountLesson => accountLesson.lessonId === nextLesson.id);
              if (matchingAccountLesson) {
                nextLesson = configureLessonForAccount(nextLesson, matchingAccountLesson);
              }
            }
          }
          if (matchingAccountProgram) {
            program = configureProgramForAccount(program, matchingAccountProgram);
          }
        }
      }
    }
    if (req.query.accountId) {
      data = await getContentItemsDeployedData(data, req.query.accountId, 'program');
    }
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /programs/{programId}:
 *   get:
 *     summary: Program report by programId
 *     tags: [Reports]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: programId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: downloadLanguage
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           example: en
 *       - name: allowMT
 *         in: query
 *         required: false
 *         schema:
 *           type: boolean
 *           example: false
 *     responses:
 *       200:
 *         description: Successful operation.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/programs'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 *
 * definitions:
 *   lessonPrograms:
 *     type: object
 *     properties:
 *       lessonId:
 *         type: integer
 *       programId:
 *         type: integer
 *       position:
 *         type: integer
 *       dateAdded:
 *         type: string
 *       dateRemoved:
 *         type: string
 *       createdAt:
 *         type: string
 *       updatedAt:
 *         type: string
 */
module.exports.read = async (req, res) => {
  let flattenedRecord = req.program.get({ plain: true });
  flattenedRecord.viewable = flattenedRecord.resource.isBrowsable;
  flattenedRecord = await includeNumUsersAccessed(flattenedRecord);
  const cardErrorList = [];

  if (req.user && req.user.accountId) {
    const account = await Accounts.findByPk(req.user.accountId);
    if (account) {
      const lessonPrograms = await LessonPrograms.findAll({
        attributes: ['lessonId'],
        where: { programId: req?.program?.id },
      });
      const lessonProgramIds = lessonPrograms?.map(lessonProgram => lessonProgram.lessonId);

      const relatedLessonCards = await LessonLessonCards.findAll({
        where: {
          lessonId: {
            [Op.in]: lessonProgramIds,
          },
        },
      });
      const lessonCardIds = relatedLessonCards?.map(lessonCard => lessonCard.lessonCardId);

      const cardsToCheck = await LessonCards.findAll({
        where: {
          id: {
            [Op.in]: lessonCardIds,
          },
          cardType: 'policyAcknowledgement',
        },
        include: [
          {
            model: AccountLessonCards,
            where: {
              accountId: req.user.accountId,
            },
            include: [{
              model: Files,
              as: 'file',
              required: false,
            }],
            required: false,
          },
          {
            model: Files,
            as: 'images',
            required: false,
          }],
        required: true, // only want policyAcknowledgement cards
      });

      for (const currentCard of cardsToCheck) {
        let card = currentCard;
        let customCard = null;
        // take into account card customization for account
        if (card.accountLessonCards.length > 0) {
          customCard = card.accountLessonCards[0];
          if (customCard.file) {
            // move file to the right place
            customCard.images = { en: customCard.file.dataValues.path };
          }
          card = configurePolicyCardForAccount(card, customCard, req.user.accountId, true);
        }
        if (!card.policyLink && card.images.length === 0) {
          cardErrorList.push(card);
        } else if (card.images.length !== 0 && !customCard?.link) {
          const imageName = Array.isArray(card.images) ? card.images[0].path : card.images[Object.keys(card.images)[0]];
          if (imageName && imageName.toLowerCase().includes('policy-placeholder.')) {
            cardErrorList.push(card);
          }
        }
      }
      flattenedRecord.cardErrorList = cardErrorList;

      if (account.accountType === 'customer' || account.accountType === 'internal') {
        const accountProgram =
          await AccountPrograms.findOne({ where: { accountId: account.id, programId: req.program.id } });
        const accountLessons = await AccountLessons.findAll({ where: { accountId: account.id } });
        if (flattenedRecord.lessons) {
          for (let nextLesson of flattenedRecord.lessons) {
            const matchingAccountLesson =
              accountLessons.find(accountLesson => accountLesson.lessonId === nextLesson.id);
            if (matchingAccountLesson) {
              nextLesson = configureLessonForAccount(nextLesson, matchingAccountLesson);
            }
          }
        }
        if (accountProgram) {
          flattenedRecord = configureProgramForAccount(flattenedRecord, accountProgram);
        }
      }
    }
  }
  res.json(flattenedRecord);
};

const duplicate = async (req) => {
  const user = req.user;
  const sourceId = req.body.sourceId;
  let newFile;

  const oldProgram = await Programs.findByPk(sourceId, {
    include: await getIncludeParams(req, user, { includeContentStrings: false }), // do contentStrings separately.
    paranoid: false, // includes soft deleted (archived) lessons
  });
  if (!oldProgram) {
    const id = sourceId;
    const err = new Error(req.i18n.t('programs.program_load_Error', { id }));
    err.status = 404;
    throw err;
  }

  const titleCount = await Programs.count({
    where: {
      name: {
        [Op.like]: `${oldProgram.name}%`,
      },
    },
  });

  if (oldProgram.file) {
    newFile = await createNewFile({}, oldProgram.file, 'programs');
  }
  const oldProgramLifecycle
    = (oldProgram.lifecycle === 'publish' || oldProgram.lifecycle === 'review') ? 'draft' : oldProgram.lifecycle;

  const newProgram = {
    name: `${oldProgram.name} copy ${titleCount}`,
    description: oldProgram.description,
    lifecycle: oldProgramLifecycle,
    catalogId: oldProgram.catalogId,
    edition: oldProgram.edition,
    version: oldProgram.version,
    build: oldProgram.build,
    duration: oldProgram.duration,
    isTimed: oldProgram.isTimed,
    isPublic: oldProgram.isPublic,
    minTimeInMinutes: oldProgram.minTimeInMinutes,
    minCardTimeInSeconds: oldProgram.minCardTimeInSeconds,
    internalName: oldProgram.internalName,
    hasCertificate: oldProgram.hasCertificate,
    certificateText: oldProgram.certificateText,
    downloadInstructions: oldProgram.downloadInstructions,
    completedMessage: oldProgram.completedMessage,
    resource: {
      digestable: 'programs',
      isPublic: oldProgram.resource.isPublic,
      isBrowsable: oldProgram.resource.isBrowsable,
    },
    sourceId,
  };

  if (newFile && newFile.id) {
    newProgram.fileId = newFile.id;
  }

  // TODO: Copy tags and topics from the resource

  const copiedProgram = await Programs.create(newProgram, {
    include: [{
      model: Resources,
      as: 'resource',
    }],
  });

  await setResourceAccess(user, copiedProgram.resource);

  // duplicate supported languages
  await SupportedLanguages.bulkCreate(oldProgram.supportedLanguages.map(({ language }) => ({
    language,
    langSupportable: 'program',
    langSupportableId: copiedProgram.id,
  })));

  // remove lesson bindings that are no longer part of the program
  const validBindings = oldProgram.lessons.filter(lesson => lesson.lessonPrograms.dateRemoved === null);

  // duplicate lesson bindings
  if (validBindings && validBindings.length) {
    const promises = validBindings.map(async (lesson) => {
      const newLessonProgramData = {
        lessonId: lesson.lessonPrograms.lessonId,
        programId: copiedProgram.id,
        position: lesson.lessonPrograms.position,
      };
      return LessonPrograms.create(newLessonProgramData);
    });
    await Promise.all(promises);
  }

  // localized content strings
  const contentStrings = await ContentStrings.findAll({
    where: {
      contentId: sourceId,
      model: 'program',
    },
  });
  if (contentStrings) {
    await ContentStrings.bulkCreate(contentStrings.map((cs) => {
      const newString = cs.get({ plain: true });
      delete newString.id;
      delete newString.updatedAt;
      delete newString.createdAt;
      newString.contentId = copiedProgram.id;
      return newString;
    }));
  }

  // reload duplicated lesson
  const includeLessons = !req.query.includeLessons || !req.query.includeLessons === 'false';
  const params = {
    include: await getIncludeParams(req, user, { includeLessons }),
  };
  if (includeLessons) {
    params.order = [lessonOrder];
  }
  const finalProgram = await Programs.findByPk(copiedProgram.id, params);

  return finalProgram;
};

const updateProgramLifecycle = async (req, program, lifecycle) => {
  if (lifecycle && lifecycle !== 'retired') {
    const programInCampaign = await isProgramInCampaign(program.id);
    if (programInCampaign === true) {
      const err = new Error(req.i18n.t('programs.program_in_campaign_forbidden_Error'));
      err.status = 403;
      throw err;
    }
  }
  // delete conent package resources if the program is retired
  if (lifecycle === 'retired') {
    await ContentPackageResources.destroy({
      where: {
        resourceId: program.resourceId,
      },
    });
    const workDayData = await WorkdayContentData.findAll({ where: { activityId: program.id, contentType: 'program' } });
    for (const wd of workDayData) {
      wd.queuedForRetirement = true;
      await wd.save();
    }
  }
  await program.update({ lifecycle });
  return true;
};

const getFinalProgram = async (req, programId) => {
  const includeLessons = !req.query.includeLessons || !req.query.includeLessons === 'false';
  const finalProgram = await Programs.findByPk(programId, {
    include: await getIncludeParams(req, req.user, { includeLessons }),
  });

  const flattenedRecord = finalProgram.get({ plain: true });
  flattenedRecord.viewable = flattenedRecord.resource.isBrowsable;

  return flattenedRecord;
};

/**
 * @swagger
 * /programs:
 *  post:
 *   summary: Add Duplicate Program
 *   tags: [Program]
 *   security:
 *     - JWT: []
 *   parameters:
 *     - name: includeLessons
 *       in: query
 *       required: false
 *       description: Whether to include lessons
 *       schema:
 *         type: boolean
 *         default: false
 *     - name: downloadLanguage
 *       in: query
 *       required: false
 *       description: Language code for downloaded content
 *       schema:
 *         type: string
 *         default: en
 *   requestBody:
 *     required: true
 *     content:
 *       application/json:
 *         schema:
 *           type: object
 *           properties:
 *             sourceId:
 *               type: integer
 *               example: 10
 *   responses:
 *     200:
 *       description: Successful operation
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/programs'
 *     400:
 *       description: Bad Request
 *     401:
 *       description: Unauthorized
 *     404:
 *       description: Not Found
 *     5XX:
 *       description: Unexpected error
 */

/**
 * @swagger
 * /programs:
 *  post:
 *   summary: Add New Program
 *   tags: [Program]
 *   security:
 *     - JWT: []
 *   requestBody:
 *     required: true
 *     content:
 *       application/json:
 *         schema:
 *           type: object
 *           required:
 *             - supportedLanguages
 *             - name
 *             - internalName
 *           properties:
 *             supportedLanguages:
 *               type: array
 *               items:
 *                 type: string
 *               example: ['en']
 *             name:
 *               type: string
 *               example: "Test Program"
 *             internalName:
 *               type: string
 *               example: "Test InternalName"
 *             hasCertificate:
 *               type: boolean
 *               example: true
 *             certificateText:
 *               type: string
 *               example: "Test certificate text"
 *             downloadInstructions:
 *               type: string
 *               example: "Test Certificate Download Instructions"
 *             completedMessage:
 *               type: string
 *               example: "Test End of Program Message"
 *             viewable:
 *               type: boolean
 *               example: true
 *             isTimed:
 *               type: boolean
 *               example: true
 *             minTimeInMinutes:
 *               type: integer
 *               example: 20
 *             duration:
 *               type: integer
 *               example: 20
 *             minCardTimeInSeconds:
 *               type: integer
 *               example: 5
 *             description:
 *               type: string
 *               example: "Test Program Description"
 *             lifecycle:
 *               type: string
 *               example: "draft"
 *             selectedLanguage:
 *               type: string
 *               example: "en"
 *   responses:
 *     200:
 *       description: Successful operation
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/programs'
 *     400:
 *       description: Bad Request
 *     401:
 *       description: Unauthorized
 *     404:
 *       description: Not Found
 *     5XX:
 *       description: Unexpected error
 */
module.exports.create = async (req, res, next) => {
  try {
    let finalProgram;
    if (req.body.sourceId) {
      const duplicateProgram = await duplicate(req);
      finalProgram = localizeProgram(req, duplicateProgram, req.i18n.language, false);
    } else {
      const file = await uploadFile(req, 'programs');
      if (file) {
        req.body.fileId = file.id;
      }
      const reqLangs = _.uniq(req.body.supportedLanguages);
      if (!reqLangs.includes('en')) {
        reqLangs.splice(0, 0, 'en'); // make sure we have english
      }
      const program = {
        ...req.body,
        edition: getNullableIntValue(req.body.edition),
        version: getNullableIntValue(req.body.version),
        build: getNullableIntValue(req.body.build),
        resource: {
          digestable: 'programs',
          isBrowsable: req.body.viewable,
        },
        supportedLanguages: reqLangs.map(l => ({
          language: l,
          langSupportable: 'program',
        })),
      };

      const searchInfo = await deriveSearch(program);
      program.resource.searchText = searchTextFromSearchInfo(searchInfo);

      finalProgram = await Programs.create(
        program,
        {
          include: [{
            model: SupportedLanguages,
          }, {
            association: Programs.associations.resource,
          }],
        },
      );

      await setResourceAccess(req.user, finalProgram.resource);
      await updateElasticSearch(req, finalProgram.resource.id, searchInfo);
    }
    if (!req.body.sourceId) {
      await updateLastMappedDate('create', finalProgram, 'programs', {}, {});
    }
    const flattenedRecord = await getFinalProgram(req, finalProgram.id);

    res.json(flattenedRecord);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /programs/{programId}?includeLessons=false:
 *  patch:
 *   summary: Update Program
 *   tags: [Program]
 *   security:
 *     - JWT: []
 *   parameters:
 *    - name: programId
 *      in: path
 *      schema:
 *        type: integer
 *        required: true
 *    - in: body
 *      name: body
 *      description: Suggested Fields parameters
 *      required: true
 *      schema:
 *        type: object
 *        properties:
 *          supportedLanguages:
 *            type: array
 *            example: ['en']
 *            required: true
 *          name:
 *              type: string
 *              example: "Test program"
 *          internalName:
 *              type: string
 *              example: "Test internalName"
 *          hasCertificate:
 *              type: boolean
 *              example: true
 *          certificateText:
 *              type: string
 *              example: "Test certificate text"
 *          downloadInstructions:
 *              type: string
 *              example: "Test Certificate Download Instructions"
 *          completedMessage:
 *              type: string
 *              example: "Test End of Program Message"
 *          viewable:
 *              type: boolean
 *              example: true
 *          isTimed:
 *              type: boolean
 *              example: true
 *          minTimeInMinutes:
 *              type: integer
 *              example: 20
 *          duration:
 *              type: integer
 *              example: 20
 *          minCardTimeInSeconds:
 *              type: integer
 *              example: 5
 *          description:
 *              type: string
 *              example: "Test Program Description"
 *          lifecycle:
 *              type: string
 *              example: "draft"
 *          selectedLanguage:
 *              type: string
 *              example: "en"
 *   responses:
 *    200:
 *      description: Successful operation
 *      content:
 *        application/json
 *      schema:
 *        $ref: '#/definitions/programs'
 *        properties:
 *          resource:
 *            $ref: '#/definitions/resources'
 *          supportedLanguages:
 *            $ref: '#/definitions/programs'
 *          contentStrings:
 *            $ref: '#/definitions/programs'
 *          lessons:
 *            $ref: '#/definitions/programs'
 *
 *    401:
 *     description: Unauthorized
 *    404:
 *     description: Not Found
 *    400:
 *     description: Bad Request
 *    5XX:
 *     description: Unexpected error
 */
module.exports.patch = async (req, res, next) => {
  const requestBody = req.body;
  const program = req.program;
  const programId = program.id;
  const { selectedLanguage, isPublic, viewable } = requestBody;

  const contentStrings = requestBody.contentStringsJson
    ? JSON.parse(requestBody.contentStringsJson)
    : requestBody.contentStrings;

  try {
    // handle incoming reuestBody values
    const { updatedBody, errMsg } = assignRequestValues(req.body, Programs.rawAttributes, programRequestOptions);
    if (errMsg) {
      return res.status(400).json({ message: errMsg });
    }

    // handle lifecycle change and return the updated program
    const isLifecycleChange = updatedBody?.lifecycle && updatedBody.lifecycle !== program.lifecycle;
    if (isLifecycleChange) {
      if (updatedBody.lifecycle === 'publish') {
        const unpublishedLesson = await Lessons.findOne({
          include: {
            model: Programs,
            where: { id: programId },
            through: { attributes: [] },
            attributes: [],
          },
          where: {
            lifecycle: {
              [Op.ne]: 'publish',
            },
          },
          attributes: ['id'],
        });
        const hasUnpublishedLesson = !!unpublishedLesson;
        if (hasUnpublishedLesson) {
          return res.status(400).json({ message: req.i18n.t('programs.program_lessons_not_published') });
        }
      }
      await updateProgramLifecycle(req, program, updatedBody.lifecycle);

      const flattenedRecord = await getFinalProgram(req, programId);

      return res.json(flattenedRecord);
    }

    // *** BEGIN UPDATE *** //

    if (updatedBody.isTimed) {
      updatedBody.duration = null;
    } else {
      updatedBody.minTimeInMinutes = null;
    }

    const newFile = await uploadFile(req, 'programs');
    if (newFile) {
      updatedBody.fileId = newFile.id;
    }

    const workdayFields = ['name', 'duration', 'isTimed', 'minTimeInMinutes', 'description', 'fileId'];

    const programWorkdayFields = workdayFields.reduce((acc, field) => {
      if (field in program) {
        acc[field] = program[field];
      }
      return acc;
    }, {});

    const updatedBodyWorkdayFields = workdayFields.reduce((acc, field) => {
      if (field in updatedBody) {
        if(field === 'isTimed') {
          acc[field] = Boolean(updatedBody[field]);
        }
        else {
          acc[field] = updatedBody[field];
        }
      }
      return acc;
    }, {});

    await checkForWorkdayCCLUpdates(programId, 'program', programWorkdayFields, updatedBodyWorkdayFields);

    // reqestBody.viewable writes to resource.isBrowsable
    if (viewable !== undefined) {
      program.resource.isBrowsable = viewable;
      await program.resource.update({ isBrowsable: viewable });
    }

    // *** SAVE THE PROGRAM *** //

    let savedProgram;
    if (!selectedLanguage || selectedLanguage === 'en') {
      savedProgram = await program.update(updatedBody);
    }

    // *** POST-SAVE TASKS *** //

    if (selectedLanguage && selectedLanguage !== 'en' && contentStrings) {
      const i18nData = {
        programId,
        language: selectedLanguage,
        strings: contentStrings,
      };
      await updateProgrami18nStrings(i18nData);
    }

    if (requestBody.supportedLanguages) {
      if (!Array.isArray(requestBody.supportedLanguages)) {
        requestBody.supportedLanguages = JSON.parse(requestBody.supportedLanguages);
      }
      await handleSupportedLanguages('program', programId, requestBody.supportedLanguages);
    }

    // requestBody.isPublic writes to resource.isPublic
    if (isPublic !== undefined && isPublic !== program.resource.isPublic) {
      const newResource = await program.resource.update({ isPublic });
      await setPublicAccess(newResource);
    }

    if (selectedLanguage === 'en') {
      const searchInfo = await deriveSearch(savedProgram);
      const searchText = searchTextFromSearchInfo(searchInfo);
      await savedProgram.resource.update({ searchText });
      await updateElasticSearch(req, savedProgram.resource.id, searchInfo);
    }
    if (savedProgram) {
      await savedProgram.reload();
    }
    await updateLastMappedDate('edit', savedProgram, 'programs', requestBody, req.program);
    const flattenedRecord = await getFinalProgram(req, programId);

    res.json(flattenedRecord);
  } catch (err) {
    next(err);
  }
  return null;
};

/**
 * @swagger
 * /programs/{programId}:
 *   delete:
 *     summary: Delete Program
 *     tags: [Program]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: programId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Delete Program successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Program deleted successfully.
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Program not found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.delete = async (req, res, next) => {
  const program = req.program;

  try {
    const programInCampaign = await isProgramInCampaign(program.id);
    if (programInCampaign === true) {
      const err = new Error(req.i18n.t('programs.program_in_campaign_forbidden_Error'));
      err.status = 403;
      throw err;
    }
    const data = await program.destroy();

    // manually remove relationship records since onDelete: 'cascade' doesn't work without creating the tables with
    // the relevant constraints.
    await removeFromElasticSearch(req, program.resourceId);

    await ResourceTag.destroy({
      where: {
        resourceId: program.resourceId,
      },
    });
    await ResourceTopic.destroy({
      where: {
        resourceId: program.resourceId,
      },
    });

    const count = await LessonPrograms.destroy({
      where: {
        programId: program.id,
      },
    });
    logger.debug('Deleting program: %l removed %k stacks', program.name, count);
    await removeProgramFromCampaigns(program.id);
    res.json(data);
  } catch (err) {
    next(err);
  }
};

module.exports.readi18n = async (req, res, next) => {
  try {
    const programId = req.body.programId ? parseInt(req.body.programId) : undefined;
    if (!programId) {
      const err = new Error(req.i18n.t('programs.programId_missing_Error'));
      err.status = 400;
      throw err;
    }
    const language = req.body.language ? req.body.language : 'en';
    const fileName = `program_${programId}_${language}.json`;
    const programHeader = await gatherProgrami18nStrings(language, programId);
    res.set({ 'Content-Disposition': `attachment; filename=${fileName}`, 'Content-Type': 'application/json' });
    res.setHeader('Access-Control-Expose-Headers', 'content-disposition');
    res.write(JSON.stringify(programHeader));
    res.end();
  } catch (err) {
    next(err);
  }
};

module.exports.writei18n = async (req, res, next) => {
  const accountId = req.user.accountId;
  try {
    let totalRecords = 0;
    let data = '';
    let avResult = null;
    // eslint-disable-next-line no-unused-vars
    req.busboy.on('file', async (fieldname, file, origFileInfo) => {
      // generate a random file name - from the node-temp library
      const now = new Date();
      const filename = [
        now.getFullYear(), now.getMonth(), now.getDate(),
        '-',
        process.pid,
        '-',
        ((Math.random() * 0x100000000) + 1).toString(36),
        '.json',
      ].join('');

      const getWriteStream = () => {
        if (isLocalTestMode()) {
          return {
            fstream: fs.createWriteStream(getLocalTestFilename(filename)),
            uploadPromise: null,
          };
        }
        // Create a write stream of the new file
        const s3UploadStream = ({ Bucket, Key }) => {
          const pass = new stream.PassThrough();
          return {
            fstream: pass,
            uploadPromise: new Upload({
              client: s3Client,
              params: { Bucket, Key, Body: pass },
            }).done(),
          };
        };
        const { bucket, keyPrefix } = getCsvS3Bucket(accountId, 'i18n-uploads');
        return s3UploadStream({
          Bucket: bucket,
          Key: `${keyPrefix}${filename}`,
        });
      };

      const { fstream, uploadPromise } = getWriteStream();
      file
        .on('data', (buffer) => {
          for (let i = 0; i < buffer.length; ++i) {
            if (buffer[i] === LINE_FEED) {
              totalRecords++; // eslint-disable-line no-plusplus
            }
          }
          data += buffer;
        });

      // AV scanning
      const clamScanner = req.app.get('clamScan');
      if (clamScanner) {
        const av = clamScanner.passthrough();
        file.pipe(av).pipe(fstream);
        av.on('scan-complete', (result) => {
          avResult = result;
        });
      } else {
        file.pipe(fstream);
      }

      const onStreamClose = async (filePath) => {
        try {
          logger.info(`Upload of '${filePath}' finished`);
          const i18nData = JSON.parse(data);
          let invalidLanguageFile = false;
          if (i18nData && !validLanguageCodes.includes(i18nData.language)) {
            invalidLanguageFile = true;
          }
          // check for viral infestations
          if ((avResult && avResult.is_infected) || invalidLanguageFile) {
            // remove file from S3
            const { bucket, keyPrefix } = getCsvS3Bucket(accountId, 'csv-uploads');
            const command = new DeleteObjectCommand({
              Bucket: bucket,
              Key: `${keyPrefix}${filename}`,
            });
            const result = await s3Client.send(command);
            logger.verbose("Removed infected file", result);

            // log & throw an error
            let err = '';
            if (invalidLanguageFile) {
              // eslint-disable-next-line max-len
              err = new Error(`Language code ${i18nData.language} is not valid. Please correct the language code and re-upload the translation file.`);
            } else {
              logger.error(`Virus detected: ${avResult.viruses}`);
              err = new Error(`File is infected with the following viruses: ${avResult.viruses}`);
            }
            err.status = 400;
            throw err;
          }
          // Create the import record with the filename
          await updateProgrami18nStrings(i18nData);
          const results = {
            filename,
            totalRecords,
          };
          res.json(results); // TODO: For now, return when the file is done uploading, but really we want to return before it's done and then check status
        } catch (err) {
          logger.error('i18n finished uploading, other error: %j', err);
          next(err);
        }
      };

      // On finish of the upload... The S3 stuff is handled slightly differently
      if (isLocalTestMode()) {
        fstream.on('close', async () => {
          onStreamClose(getLocalTestFilename(filename));
        });
      } else {
        const result = await uploadPromise;
        onStreamClose(result.Location);
      }
    });
    req.busboy.on('field', (fieldname, val, fieldnameTruncated, valTruncated, encoding, mimetype) => { // eslint-disable-line no-unused-vars
      // This is where we get the other fields of the form type.
      // console.log('field:', fieldname, ':', val);
    });
    req.busboy.on('finish', () => {
      // console.log('Busboy finish, Done parsing upload form!');
    });
    req.busboy.on('error', (err) => {
      logger.error('Busboy Error: %j', err);
      next(err);
    });
    req.pipe(req.busboy); // Pipe it through busboy
  } catch (err) {
    next(err);
  }
};

module.exports.generateScormPackage = async (req, res, next) => {
  try {
    const { archiveFilename, clientFilename, tmpFolderPath } = await genAndDownloadScormArchive(req, 'programs');
    res.setHeader('Access-Control-Expose-Headers', 'content-disposition');
    res.download(archiveFilename, clientFilename, async (err) => {
      if (err) {
        throw err;
      }
      // delete the temporary folder
      await fse.remove(tmpFolderPath);
    });
  } catch (err) {
    next(err);
  }
};

module.exports.generateCompletionCertificate = async (req, res, next) => {
  const tzOffset = 7; // Timezone is MST / PDT (UTC-7)
  try {
    const userLessonId = req.body.userLessonId;
    const programId = req.params.id;
    const bundleAccountId = req.user.accountId;
    const bundleIds = await getResourceBundlesIds(bundleAccountId);
    const includeParams = [
      {
        model: Resources,
        as: 'resource',
        include: [
          {
            model: ResourceBundles,
            where: {
              bundleId: {
                [Op.in]: bundleIds,
              },
            },
          },
        ],
        required: true,
      },
      {
        model: ContentStrings,
        where: {
          model: 'program',
        },
        required: false,
      },
      {
        model: SupportedLanguages,
      },
    ];

    let program = await Programs.findOne({
      where: {
        id: programId,
      },
      include: includeParams,
    });

    const userLessonWhere = {
      userId: req.user.id,
      type: 'program',
      status: 'completed',
      resourceId: programId,
    };
    if (userLessonId) {
      userLessonWhere.id = parseInt(userLessonId);
    }

    let ul = await UserLessons.findOne({
      attributes: [[fn('CONVERT_TZ', col('completionDate'), '+00:00', `-${tzOffset}:00`), 'completedAt']],
      where: userLessonWhere,
      raw: true,
    });

    const scormProgram = await
    ScormPrograms.findOne({ where: { userId: req.user.id, model: 'program', resourceId: programId } });

    // if the program was taken in a SCORM context or through the browse tab, there will be
    // no userLesson of type 'program', so we need to add an additional section here to handle this.
    if (!ul && (!scormProgram || scormProgram.completionDate)) {
      // Get the userLesson with the latest completionDate date.
      const rawLessons = await db.sequelize.query(
        "SELECT Convert_tz(MAX(`userLessons`.`completiondate`), '+00:00', '-7:00') AS `completedAt` " +
        'FROM `userLessons` AS `userLessons` ' +
        'INNER JOIN `lessons` AS `lesson` ' +
        'ON `userLessons`.`resourceid` = `lesson`.`id` ' +
        'AND ( `lesson`.`deletedat` IS NULL ) ' +
        'WHERE  `userLessons`.`userid` = ? ' +
        "AND `userLessons`.`type` = 'lesson' " +
        "AND `userLessons`.`status` = 'completed' " +
        'AND (SELECT `lesson`.`id` ' +
        'FROM `lessons` AS `lesson` ' +
        'INNER JOIN ( `lessonPrograms` AS `programs->lessonPrograms` ' +
        'INNER JOIN `programs` AS `programs` ' +
        'ON `programs`.`id` = `programs->lessonPrograms`.`programid`) ' +
        'ON `lesson`.`id` = `programs->lessonPrograms`.`lessonid` ' +
        'AND ( `programs`.`deletedat` IS NULL AND `programs`.`id` = ? ) ' +
        'WHERE  ( ( `lesson`.`deletedat` IS NULL ) ' +
        'AND `lesson`.`id` = `userLessons`.`resourceid` ) ' +
        'LIMIT 1) IS NOT NULL',
        {
          replacements: [req.user.id, programId],
          type: db.sequelize.QueryTypes.SELECT,
          raw: true,
        },
      );

      if (rawLessons && rawLessons.length > 0 && rawLessons[0].completedAt) {
        ul = rawLessons[0];
      }
    }

    const today = new Date();
    if (scormProgram && scormProgram.completionDate) {
      program.completedAt = scormProgram.completionDate;
    } else {
      program.completedAt = ul ? ul.completedAt : today.setHours(today.getHours() - 8);
    }

    if (req.user && req.user.accountId) {
      const account = await Accounts.findByPk(req.user.accountId);
      if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
        const accountProgram = await AccountPrograms.findOne({ where: { accountId: account.id, programId } });
        if (accountProgram) {
          program = configureProgramForAccount(program, accountProgram);
        }
      }
    }

    if (!program.hasCertificate) {
      const err = new Error(req.i18n.t('programs.course_no_certificate_Error'));
      err.status = 401;
      throw err;
    }

    browser = await getLaunchBrowser(browser);
    const { pdfFilename, clientFilename } = await genAndDownloadCompletionCertificate(req, program, null, browser);
    res.setHeader('Access-Control-Expose-Headers', 'content-disposition');
    res.download(pdfFilename, clientFilename, async (err) => {
      if (err) {
        throw err;
      }
      // delete the temporary file
      await fs.unlinkSync(pdfFilename);
    });
  } catch (err) {
    next(err);
  } finally { 
    setTimeout(async () => {
      if (browser) {
        await browser.close();
        browser = null;
      }
    }, 1000);
  }
};

module.exports.generateNewCompletionCertificate = async (req, res, next) => {
  const tzOffset = 7; // Timezone is MST / PDT (UTC-7)
  try {
    const userLessonId = req.body.userLessonId;
    const programId = req.params.id;
    const userId = parseInt(req.body.userId);
    const bundleAccountId = req.user.accountId;
    const bundleIds = await getResourceBundlesIds(bundleAccountId);
    const includeParams = [
      {
        model: Resources,
        as: 'resource',
        include: [
          {
            model: ResourceBundles,
            where: {
              bundleId: {
                [Op.in]: bundleIds,
              },
            },
          },
        ],
        required: true,
      },
      {
        model: ContentStrings,
        where: {
          model: 'program',
        },
        required: false,
      },
      {
        model: SupportedLanguages,
      },
    ];

    let program = await Programs.findOne({
      where: {
        id: programId,
      },
      include: includeParams,
    });

    const userLessonWhere = {
      userId,
      type: 'program',
      status: 'completed',
      resourceId: programId,
    };
    if (userLessonId) {
      userLessonWhere.id = parseInt(userLessonId);
    }

    let ul = await UserLessons.findOne({
      attributes: [[fn('CONVERT_TZ', col('completionDate'), '+00:00', `-${tzOffset}:00`), 'completedAt']],
      where: userLessonWhere,
      raw: true,
    });

    const scormProgram = await
    ScormPrograms.findOne({ where: { userId, model: 'program', resourceId: programId } });

    // if the program was taken in a SCORM context or through the browse tab, there will be
    // no userLesson of type 'program', so we need to add an additional section here to handle this.
    if (!ul && (!scormProgram || scormProgram.completionDate)) {
      // Get the userLesson with the latest completionDate date.
      const rawLessons = await db.sequelize.query(
        "SELECT Convert_tz(MAX(`userLessons`.`completiondate`), '+00:00', '-7:00') AS `completedAt` " +
        'FROM `userLessons` AS `userLessons` ' +
        'INNER JOIN `lessons` AS `lesson` ' +
        'ON `userLessons`.`resourceid` = `lesson`.`id` ' +
        'AND ( `lesson`.`deletedat` IS NULL ) ' +
        'WHERE  `userLessons`.`userid` = ? ' +
        "AND `userLessons`.`type` = 'lesson' " +
        "AND `userLessons`.`status` = 'completed' " +
        'AND (SELECT `lesson`.`id` ' +
        'FROM `lessons` AS `lesson` ' +
        'INNER JOIN ( `lessonPrograms` AS `programs->lessonPrograms` ' +
        'INNER JOIN `programs` AS `programs` ' +
        'ON `programs`.`id` = `programs->lessonPrograms`.`programid`) ' +
        'ON `lesson`.`id` = `programs->lessonPrograms`.`lessonid` ' +
        'AND ( `programs`.`deletedat` IS NULL AND `programs`.`id` = ? ) ' +
        'WHERE  ( ( `lesson`.`deletedat` IS NULL ) ' +
        'AND `lesson`.`id` = `userLessons`.`resourceid` ) ' +
        'LIMIT 1) IS NOT NULL',
        {
          replacements: [userId, programId],
          type: db.sequelize.QueryTypes.SELECT,
          raw: true,
        },
      );

      if (rawLessons && rawLessons.length > 0 && rawLessons[0].completedAt) {
        ul = rawLessons[0];
      }
    }

    const today = new Date();
    if (scormProgram && scormProgram.completionDate) {
      program.completedAt = scormProgram.completionDate;
    } else {
      program.completedAt = ul ? ul.completedAt : today.setHours(today.getHours() - 8);
    }

    const account = await Accounts.findByPk(bundleAccountId);
    if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
      const accountProgram = await AccountPrograms.findOne({ where: { accountId: bundleAccountId, programId } });
      if (accountProgram) {
        program = configureProgramForAccount(program, accountProgram);
      }
    }

    if (!program.hasCertificate) {
      const err = new Error(req.i18n.t('programs.course_no_certificate_Error'));
      err.status = 401;
      throw err;
    }
    const userDetails = await Users.findOne({
      where: {
        id: userId,
      },
      attributes: ['firstName', 'lastName'],
      paranoid: false,
    });
    browser = await getLaunchBrowser(browser);
    const userName = `${userDetails.firstName} ${userDetails.lastName}`;
    const { pdfFilename, clientFilename } = await genAndDownloadCompletionCertificate(req, program, userName, browser);
    res.setHeader('Access-Control-Expose-Headers', 'content-disposition');
    res.download(pdfFilename, clientFilename, async (err) => {
      if (err) {
        throw err;
      }
      // delete the temporary file
      await fs.unlinkSync(pdfFilename);
    });
  } catch (err) {
    next(err);
  } finally { 
    setTimeout(async () => {
      if (browser) {
        await browser.close();
        browser = null;
      }
    }, 1000);
  }
};

module.exports.applyStagedLangStr = async (req, res, next) => {
  try {
    const programId = req.query.programId ? parseInt(req.query.programId) : undefined;
    const language = req.query.language;
    if (!programId) {
      const err = new Error('failed to specify programId in query');
      err.status = 400;
      throw err;
    }
    if (!language) {
      const err = new Error('failed to specify language in query');
      err.status = 400;
      throw err;
    }
    const program = await Programs.findByPk(programId, {
      attributes: [],
      include: {
        attributes: ['id'],
        model: Lessons,
        required: false,
        include: {
          attributes: ['id'],
          model: LessonCards,
          required: false,
        },
      },
    });

    const lessonIds = [];
    const lessonCardIds = new Set();

    for (const lesson of program.lessons) {
      lessonIds.push(lesson.id);
      for (const lessonCard of lesson.lessonCards) {
        lessonCardIds.add(lessonCard.id);
      }
    }

    // delete original content strings
    const stats = await swapContentStrings(language, lessonIds, [...lessonCardIds], programId);

    res.json({ success: true, stats });
  } catch (error) {
    next(error);
  }
};

/**
 * user report endpoint for completion report section on the front end
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @todo GDPR
 */

/**
 * @swagger
 * /programs/{programsId}/report/users:
 *   get:
 *     summary: Program User Report By Given ProgramsId
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: programsId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *       - name: $limit
 *         in: query
 *         required: true
 *         description: Limit (integer) for number of records
 *         schema:
 *           type: integer
 *           example: 10
 *       - name: $skip
 *         in: query
 *         required: true
 *         description: Skip (integer) for number of records
 *         schema:
 *           type: integer
 *           example: 0
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *                 skip:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       firstName:
 *                         type: string
 *                       lastName:
 *                         type: string
 *                       email:
 *                         type: string
 *                       userStatus:
 *                         type: string
 *                       title:
 *                         type: string
 *                       location:
 *                         type: string
 *                       role:
 *                         type: string
 *                       city:
 *                         type: string
 *                       employeeId:
 *                         type: integer
 *                       supervisor:
 *                         type: string
 *                       exempt:
 *                         type: integer
 *                       hireDate:
 *                         type: string
 *                       createdAt:
 *                         type: string
 *                       lastActivityAt:
 *                         type: string
 *                       managerEmail:
 *                         type: string
 *                       assigned:
 *                         type: string
 *                       completed:
 *                         type: integer
 *                       dueDate:
 *                         type: string
 *                       group:
 *                         type: string
 *                       campaign:
 *                         type: string
 *                       pctComplete:
 *                         type: integer
 *                       timeComplete:
 *                         type: string
 *                       minutesSpent:
 *                         type: integer
 *                       started:
 *                         type: string
 *                       status:
 *                         type: string
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.usersReport = async (req, res, next) => {
  const accountId = req.user.accountId;
  const model = 'program';
  try {
    const isNewReport = !!(req.query.newReport && req.query.newReport === 'true');
    const requiredTime = await getProgramRequiredTime(req.params.id, accountId);
    let generatedUserReport = null;
    let limit;
    let offset;
    if (isNewReport) {
      logger.info(`Calling new completion report for programId: ${req.params.id}`);
      const defaults = {
        order: [[{ model: Users }, 'createdAt', 'desc']],
        limit: config.paginate.default,
        offset: 0,
      };
      const rqts = await newRestQueryToSequelize(model, req.user.id, req.query, defaults);
      const queryParams = rqts.newQuery;
      queryParams.order = [[...queryParams.order]];
      limit = queryParams.limit;
      offset = queryParams.offset;
      generatedUserReport = await runContentUserReport(
        queryParams,
        req.params.id,
        model,
        req.user.accountId,
        req.query.startDate,
        req.query.endDate,
        rqts.userWhere,
      );
    } else {
      logger.info(`Calling old completion report for programId: ${req.params.id}`);
      const defaults = {
        order: [['id', 'DESC']],
        limit: config.paginate.default,
        offset: 0,
      };
      const queryParams = await rqtu(req.user.id, req.query, defaults);
      queryParams.order = [[...queryParams.order]];
      limit = queryParams.limit;
      offset = queryParams.offset;
      generatedUserReport = await runProgramUserReport(
        queryParams,
        req.params.id,
        req.user.accountId,
        requiredTime,
        req.query.startDate,
        req.query.endDate,
      );
    }
    res.json({
      total: generatedUserReport.total,
      limit,
      skip: offset,
      data: generatedUserReport.data,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * endpoint for progress report
 *
 * @param {*} req
 * @param {*} res
 * @param {*} next
 * @todo GDPR
 */
module.exports.progressReport = async (req, res, next) => {
  try {
    const generatedProgressReport = await generateProgramProgressReport(req, req.program);
    res.json(generatedProgressReport);
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /programs/{programId}/campaigns:
 *   get:
 *     summary: Program Campaigns For Given ProgramId
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: programId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       name:
 *                         type: string
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.listCampaigns = async (req, res, next) => {
  try {
    const program = await Programs.findByPk(req.params.id, { attributes: ['id'], raw: true });
    if (!program) {
      const err = new Error(req.i18n.t('programs.program_load_Error', { id: req.params.id }));
      err.status = 404;
      throw err;
    }
    const accountId = req.user.accountId;
    const campaignsQuery = {
      attributes: ['id', 'name'],
      where: {
        accountId,
        status: {
          [Op.ne]: 'withdrawn',
        },
      },
      include: {
        model: CampaignItem,
        attributes: [],
        as: 'items',
        required: true,
        where: {
          itemType: 'program',
          itemId: req.params.id,
        },
      },
    };

    const countQuery = {
      where: _.pick(campaignsQuery, ['where']).where,
      include: _.pick(campaignsQuery, ['include']).include,
      distinct: true,
    };

    const data = await Campaigns.findAll(campaignsQuery);
    const count = await Campaigns.count(countQuery);

    res.json({ total: count, data });
  } catch (err) {
    next(err);
  }
};

const applyCustomizations = async (accountId, programs) => {
  const account = await Accounts.findByPk(accountId);
  if (account && (account.accountType === 'customer' || account.accountType === 'internal')) {
    const accountPrograms = await AccountPrograms.findAll({ where: { accountId: account.id } });
    const accountLessons = await AccountLessons.findAll({ where: { accountId: account.id } });

    for (let program of programs) {
      const matchingAccountProgram =
        accountPrograms.find(accountProgram => accountProgram.programId === program.id);
      if (program.lessons) {
        for (let nextLesson of program.lessons) {
          const matchingAccountLesson =
            accountLessons.find(accountLesson => accountLesson.lessonId === nextLesson.id);
          if (matchingAccountLesson) {
            nextLesson = configureLessonForAccount(nextLesson, matchingAccountLesson);
          }
        }
      }
      if (matchingAccountProgram) {
        program = configureProgramForAccount(program, matchingAccountProgram);
      }
    }
  }
  return programs;
};

/**
 * @swagger
 * /programs/reportList:
 *   get:
 *     summary: Program Report List
 *     tags:
 *       - Reports
 *     security:
 *       - JWT: []
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                   description: Total number of records.
 *                 data:
 *                   type: array
 *                   description: Array of report data objects.
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         description: Program ID.
 *                       name:
 *                         type: string
 *                         description: Program name.
 *                       deployDate:
 *                         type: string
 *                         description: Deploy date.
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       5XX:
 *         description: Unexpected error
 */
module.exports.reportList = async (req, res, next) => {
  const accountId = req.query.accountId;
  const search = req.query.$search;
  const skip = req.query.$skip;
  const limit = req.query.$limit;
  const bundleAccountId = accountId || (req.user ? req.user.accountId : null);
  const {
    accountUsers: rAccountUsers,
    programs: rPrograms,
    resourceBundles: rResourceBundles,
    resources: rResources,
    scormPrograms: rScormPrograms,
    userLessons: rUserLessons,
  } = db.reportingModels;

  try {
    const accountUsers = await rAccountUsers.findAll({
      attributes: ['userId'],
      where: {
        accountId: bundleAccountId,
      },
    });
    const userIds = accountUsers.map(au => au.userId);
    const bundleIds = await getResourceBundlesIds(bundleAccountId);

    const programsQuery = {
      attributes: ['id', 'name', 'lifecycle'],
      where: {
        lifecycle: {
          [Op.in]: ['publish', 'retired'],
        },
      },
      include: [
        {
          attributes: ['id'],
          model: rResources,
          as: 'resource',
          include: [{
            model: rResourceBundles,
            where: {
              bundleId: {
                [Op.in]: bundleIds,
              },
            },
          }],
          required: true,
        }, {
          model: rUserLessons,
          attributes: ['id', 'type', 'sourceLifecycle', 'userId'],
          where: {
            type: 'program',
            sourceLifecycle: { [Op.col]: 'programs.lifecycle' },
            userId: { [Op.in]: userIds },
          },
          required: true,
        },
      ],
      order: [[
        { model: rResources, as: 'resource' },
        { model: rResourceBundles },
        'createdAt',
        'desc',
      ]],
    };
    if (search) {
      programsQuery.where.name = { [Op.like]: `%${search}%` };
    }
    programsQuery.limit = limit
      ? parseInt(limit)
      : config.paginate.max;
    programsQuery.offset = skip !== undefined
      ? parseInt(skip)
      : 0;

    const userLessonProgramList = await rPrograms.findAll(programsQuery);

    const scormProgramList = await rPrograms.findAll({
      attributes: ['id', 'name', 'lifecycle'],
      where: {
        lifecycle: {
          [Op.in]: ['publish', 'retired'],
        },
      },
      include: [
        {
          attributes: ['id'],
          model: rResources,
          as: 'resource',
          include: [{
            model: rResourceBundles,
            where: {
              bundleId: {
                [Op.in]: bundleIds,
              },
            },
          }],
          required: true,
        }, {
          model: rScormPrograms,
          attributes: ['id'],
          where: {
            model: 'program',
            userId: { [Op.in]: userIds },
          },
          required: true,
        },
      ],
      order: [[
        { model: rResources, as: 'resource' },
        { model: rResourceBundles },
        'createdAt',
        'desc',
      ]],
    });

    // Push programs found in the scormProgram query into the final list.
    // Use the program id to determine if the programs were already found.
    const userLessonProgramIds = userLessonProgramList.map(program => program.id);
    let allProgramList = userLessonProgramList;
    for (const nextScormProgram of scormProgramList) {
      if (!userLessonProgramIds.includes(nextScormProgram.id)) {
        userLessonProgramIds.push(nextScormProgram.id);
        allProgramList.push(nextScormProgram);
      }
    }

    if (req.user && req.user.accountId) {
      allProgramList = await applyCustomizations(req.user.accountId, allProgramList);
    }

    // construct final list of report items
    const reportList = allProgramList.map((program) => {
      let deployDate = program.resource.resourceBundles.length > 0 ?
        program.resource.resourceBundles[0].createdAt : null;
      const resBundles = program.resource.resourceBundles.filter(resBundle => resBundle.bundleId !== PUBLIC_BUNDLE_ID);
      if (resBundles.length > 0) {
        deployDate = resBundles[0].createdAt;
      }
      return {
        id: program.id,
        name: program.name,
        deployDate: format(deployDate, 'MM/DD/YYYY'),
      };
    });
    res.json({ total: reportList.length, data: reportList });
  } catch (err) {
    next(err);
  }
};

/**
 * downloadable csv endpoint for completion report section on the front end
 *
 * @param {{}} req
 * @param {{}} res
 * @param {{}} next
 * @todo GDPR
 */
module.exports.generateCSV = async (req, res, next) => {
  const accountId = req.user.accountId || req.user.accounts[0].id;
  const keepAttributes = req.query.attributes;
  const model = 'program';

  const timeZone = req.query.timeZone;
  delete req.query.timeZone;

  try {
    const isNewReport = !!(req.query.newReport && req.query.newReport === 'true');
    await checkAllowedAttributes(req, keepAttributes, accountId);
    // eslint-disable-next-line no-unused-vars
    const [allowed, program] = await Promise.all([
      checkAllowedAttributes(req, keepAttributes, accountId),
      Programs.findByPk(req.params.id, { attributes: ['name', 'minTimeInMinutes'], raw: true }),
    ]);

    // user can't specify limit or skip for the csv.
    delete req.query.$limit;
    delete req.query.$skip;
    delete req.query.attributes;

    const filename = cleanDownloadFilename(`Emtrain_Completion_Report_${Date.now()}`);
    // adding the group & campaign names to csv report details
    const dynamicQueryData = [];
    if (req.query.group) {
      // get groupName by id
      const groupId = parseInt(req.query.group);
      const groupData = await Groups.findByPk(groupId, { attributes: ['name'], raw: true });
      dynamicQueryData.push([`Group: ${groupData.name}`]);
    }
    if (req.query.campaign) {
      // get campaignName by id
      const campId = parseInt(req.query.campaign);
      const campaignData = await Campaigns.findByPk(campId, { attributes: ['name'], raw: true });
      dynamicQueryData.push([`Campaign: ${campaignData.name}`]);
    }
    if (isNewReport) {
      const defaults = {
        order: [[{ model: Users }, 'createdAt', 'desc']],
        limit: 100,
        offset: 0,
      };
      const rqts = await newRestQueryToSequelize(model, req.user.id, req.query, defaults);
      const queryParams = rqts.newQuery;
      queryParams.order = [[...queryParams.order]];
      // eslint-disable-next-line max-len
      await genCSV(keepAttributes, queryParams, filename, res, runContentUserReport, timeZone)(req.params.id, model, accountId, req.query.startDate, req.query.endDate, rqts.userWhere);
    } else {
      const defaults = {
        order: [['id', 'DESC']],
        limit: 100,
        offset: 0,
      };
      const queryParams = await rqtu(req.user.id, req.query, defaults);
      queryParams.order = [[...queryParams.order]];
      // eslint-disable-next-line max-len
      await genNewCSV(keepAttributes, queryParams, filename, res, runProgramUserReport, timeZone, req.query, dynamicQueryData)(queryParams, req.params.id, accountId, req.query.startDate, req.query.endDate);
    }
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the program when an id is passed in the route
module.exports.programById = async function (req, res, next, id) {
  const includeLessons = !req.query.includeLessons || !req.query.includeLessons === 'false';
  const includeSourceProgram = req.query.includeSourceProgram || false;

  const queryParams = {
    where: { id },
    include: await getIncludeParams(req, req.user, {
      includeLessons,
      includeSourceProgram,
    }),
  };
  if (includeLessons) {
    queryParams.order = [lessonOrder];
  }
  try {
    const filterParams = await getFilterParams(req);
    const finalQuery = {
      ...queryParams,
      where: { ...queryParams.where, ...filterParams },
    };
    const program = await Programs.findOne(finalQuery);

    if (!program) {
      const err = new Error(req.i18n.t('programs.program_load_Error', { id }));
      err.status = 404;
      throw err;
    }

    const downloadLanguage = req.query.downloadLanguage ? req.query.downloadLanguage : req.i18n.language;
    req.program = localizeProgram(req, program, downloadLanguage, true);
    req.resource = req.program.resource; // needed for resource access checks
    next();
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * /programs/{programId}/groups:
 *   post:
 *     summary: Get Program Groups based on programId
 *     tags: [Program]
 *     security:
 *       - JWT: []
 *     parameters:
 *       - name: programId
 *         in: path
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               groupIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *             example:
 *               groupIds: [8, 10]
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: integer
 *                   campaignId:
 *                     type: integer
 *                   reoccurenceIteration:
 *                     type: integer
 *                   groupId:
 *                     type: integer
 *                   updatedAt:
 *                     type: string
 *                   createdAt:
 *                     type: string
 *                   group:
 *                     $ref: '#/components/schemas/groups'
 *       400:
 *         description: Bad Request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Not Found
 *       5XX:
 *         description: Unexpected error
 */
module.exports.programGroupList = async (req, res, next) => {
  const programId = req.params.id;
  const accountId = req.user.accountId;
  const defaults = {
    attributes: ['groupId', 'programId'],
    limit: config.paginate.default,
    offset: 0,
    where: {
      programId,
    },
    group: ['groupId'],
  };
  const queryParams = restQueryToSequelize(req.query, defaults);
  const finalQuery = {
    ...queryParams,
  };
  finalQuery.include = [{
    model: Groups,
    where: { accountId },
    attributes: ['deletedAt'],
    paranoid: false,
  }];
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  const countQuery = {
    where: _.pick(finalQuery, ['where']).where,
  };
  try {
    const count = await GroupAssignments.count(countQuery);
    const groupAssignments = await GroupAssignments.findAll(finalQuery);

    const promises = [];
    // look up items associated with campaign item bindings
    groupAssignments.forEach((assignment) => {
      promises.push(lookupGroup(assignment, false));
    });
    const results = await Promise.all(promises);
    // attach item to binding
    const data = results.map((assignment) => {
      return flattenAssignment(assignment);
    });
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

/**
 * @swagger
 * components:
 *   schemas:
 *     programs:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         lifecycle:
 *           type: string
 *         fileId:
 *           type: integer
 *         isTimed:
 *           type: boolean
 *         minTimeInMinutes:
 *           type: integer
 *         duration:
 *           type: integer
 *         internalName:
 *           type: string
 *         minCardTimeInSeconds:
 *           type: integer
 *         resourceId:
 *           type: integer
 *         deletedAt:
 *           type: string
 *           format: date-time
 *         hasCertificate:
 *           type: boolean
 *         certificateText:
 *           type: string
 *         downloadInstructions:
 *           type: string
 *         description:
 *           type: string
 *         completedMessage:
 *           type: string
 *         supportedLanguages:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/supportedLanguages'
 *         lessons:
 *           type: array
 *           items:
 *             type: object  # Replace with $ref if a lesson schema exists
 *         contentStrings:
 *           type: array
 *           items:
 *             type: object  # Replace with $ref if a contentStrings schema exists

 *     supportedLanguages:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         langSupportable:
 *           type: string
 *         language:
 *           type: string
 *         langSupportableId:
 *           type: integer
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */
