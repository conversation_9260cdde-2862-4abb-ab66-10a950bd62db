const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const userUtils = require('../services/utils/userUtils');
const { executeNotify } = require('../services/utils/notificationsUtils');

const Notifications = db.notifications;
const NotificationRecipients = db.notificationRecipients;

function flattenNotification(notification) {
  const userIds = notification.userIds;
  const groupIds = notification.groupIds;
  let flattenedNotification = notification.get({ plain: true });
  flattenedNotification = Object.assign(flattenedNotification, { userIds });
  return Object.assign(flattenedNotification, { groupIds });
}

function readRecipients(notification) {
  const userIds = [];
  const groupIds = [];
  notification.recipients.forEach((nr) => {
    if (nr.recipientType === 'group') {
      groupIds.push(nr.recipientId);
    } else {
      userIds.push(nr.recipientId);
    }
  });
  Object.assign(notification, { userIds });
  Object.assign(notification, { groupIds });
}

function createRecipients(notification, recipientType, recipientIds) {
  return recipientIds.map((recipientId) => {
    const nr = {
      notificationId: notification.id,
      recipientId,
      recipientType,
    };
    return NotificationRecipients.create(nr);
  });
}

async function fetchNotification(id) {
  const notification = await Notifications.findByPk(id, {
    include: [
      {
        model: db.notificationLog,
        as: 'logs',
      },
      {
        model: db.notificationRecipients,
        as: 'recipients',
      },
    ],
  });
  return notification;
}

// updates recipients list for notification
async function updateRecipients(notification) {
  const userIds = new Set(notification.userIds);
  const groupIds = new Set(notification.groupIds);

  // figure out what recipients should be removed and what recipients should be added
  const preexistingRecipients = await NotificationRecipients.findAll({
    where: { notificationId: notification.id },
  });

  const promises = [];
  // remove any recipient that is no longer in their respective lists
  for (const recipient of preexistingRecipients) {
    if (recipient.recipientType === 'user') {
      if (!userIds.has(recipient.recipientId)) {
        promises.push(recipient.destroy());
      }
      userIds.delete(recipient.recipientId);
    } else {
      if (!groupIds.has(recipient.recipientId)) {
        promises.push(recipient.destroy());
      }
      groupIds.delete(recipient.recipientId);
    }
  }

  // add newbies in
  for (const newUser of userIds) {
    promises.push(NotificationRecipients.create({
      notificationId: notification.id,
      recipientId: newUser,
      recipientType: 'user',
    }));
  }
  for (const newGroup of userIds) {
    promises.push(NotificationRecipients.create({
      notificationId: notification.id,
      recipientId: newGroup,
      recipientType: 'group',
    }));
  }
  const data = await Promise.all(promises);
  return data;
}

module.exports.list = async (req, res, next) => {
  const defaults = {
    include: [
      {
        model: db.notificationLog,
        as: 'logs',
      },
      {
        model: db.notificationRecipients,
        as: 'recipients',
      },
    ],
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };
  const queryParams = userUtils.restQueryToSequelize(req.query, defaults);
  queryParams.order = [[...queryParams.order]];

  const finalQuery = {
    ...queryParams,
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  const countQuery = {
    where: _.pick(finalQuery, ['where']).where,
  };

  try {
    const count = await Notifications.count(countQuery);
    const data = await Notifications.findAll(finalQuery);
    res.json({ total: count, ...pagedResult, data });
  } catch (err) {
    next(err);
  }
};

module.exports.read = (req, res) => {
  res.json(flattenNotification(req.notification));
};

module.exports.create = async (req, res, next) => {
  const notification = req.body;
  try {
    const newNotification = await Notifications.create(notification);
    const promises = [];
    if (notification.userIds) {
      Array.prototype.push.apply(promises, createRecipients(newNotification, 'user', notification.userIds));
    }
    if (notification.groupIds) {
      Array.prototype.push.apply(promises, createRecipients(newNotification, 'group', notification.groupIds));
    }
    await Promise.all(promises);

    if (newNotification.sendDate <= Date.now()) {
      // Let'er rip
      await executeNotify(req.sessionId, req.user.id, newNotification.id);
    }

    // reread notification
    let returnNotification = await fetchNotification(newNotification.id);
    readRecipients(returnNotification);
    returnNotification = flattenNotification(returnNotification);

    res.json(returnNotification);
  } catch (err) {
    next(err);
  }
};

module.exports.patch = async (req, res, next) => {
  const notification = req.notification;
  try {
    if (notification.status !== 'pending') {
      const id = notification.id;
      const err = new Error(req.i18n.t('notifications.notification_update_Error', { id }));
      err.status = 400;
      throw err;
    }
    const updatedNotification = await notification.update(req.body);
    if (notification.userIds || notification.groupIds) {
      // possibly update recipients list
      await updateRecipients(notification);
    }
    if (updatedNotification.sendDate <= Date.now()) {
      // Let'er rip
      executeNotify(req.sessionId, req.user.id, updatedNotification.id);
    }
    // reread notification
    let returnNotification = await fetchNotification(updatedNotification.id);
    readRecipients(returnNotification);
    returnNotification = flattenNotification(returnNotification);

    res.json(returnNotification);
  } catch (err) {
    next(err);
  }
};

module.exports.delete = async (req, res, next) => {
  const notification = req.notification;

  try {
    const data = await notification.destroy();
    // delete recipient records
    await NotificationRecipients.destroy({ where: { notificationId: notification.id } });
    res.json(data);
  } catch (err) {
    next(err);
  }
};

// Middleware to retrieve the group when an id is passed in the route
module.exports.notificationById = async function (req, res, next, id) {
  try {
    const notification = await fetchNotification(id);
    if (!notification) {
      const err = new Error(req.i18n.t('notifications.notification_load_Error', { id }));
      err.status = 404;
      throw err;
    }
    readRecipients(notification);
    req.notification = notification;
    next();
  } catch (err) {
    next(err);
  }
};
