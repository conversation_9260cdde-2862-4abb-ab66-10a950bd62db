const _ = require('lodash');
const db = require('../db');
const config = require('../config/config');
const crypto = require('crypto');
const { restSortToSequelize } = require('../services/utils/resourceUtils');

const Links = db.sendLinks;
const Users = db.users;
const Events = db.events;
const Accounts = db.accounts;

const restQueryToSequelize = (user, query, defaults) => {
  const whereClause = { ..._.omit(query, ['$limit', '$skip', '$sort', 'topics', 'tags', 'deletedAt']) };

  // set up the new query that's specific to sequelize.  Combine the defaults, then override the where clause
  // with a combo of the default where clause and the passed in where clause.
  // Then include all the things including the where clause for the tags and topics.
  const newQuery = {
    ...defaults,
    where: { ...defaults.where, ...whereClause },
  };

  // override defaults for limit, skip, and sort if passed in by caller
  if (query) {
    if (query.$limit !== undefined) {
      newQuery.limit = Math.min(config.paginate.max, parseInt(query.$limit));
    }
    if (query.$skip !== undefined) {
      newQuery.offset = parseInt(query.$skip) || 0;
    }
    if (query.$sort !== undefined) {
      newQuery.order = restSortToSequelize(query.$sort);
    }
  }
  return newQuery;
};

const randomBytes = length => new Promise((resolve, reject) => {
  crypto.randomBytes(length, (err, buf) => (err ? reject(err) : resolve(buf.toString('hex'))));
});
/* eslint-disable no-param-reassign */
function includeConversionData(query) {
  if (!query.include) {
    query.include = [];
  }
  query.include.push(
    {
      model: Users,
      as: 'signUps',
      attributes: ['id'],
    },
    {
      model: Events,
      attributes: ['id'],
    },
  );
  return query;
}

const eventOrder = [
  { model: Events, attributes: ['id'] },
  'id',
  'asc',
];

async function filterByUser(user, query) {
  let accountId = user ? user.accountId : null;

  const hasLinkToken = !!query.linkToken;

  if (!hasLinkToken) {
    if (!accountId) {
      const publicAccount = await Accounts.findOne({
        where: {
          accountType: 'public',
        },
      });
      if (publicAccount) {
        accountId = publicAccount.id;
      }
    }
    query.where = {
      linkType: 'accounts',
      linkTypeId: accountId,
    };
  }
  return query;
}

/* eslint-enable no-param-reassign */

module.exports.list = async (req, res, next) => {
  const defaults = {
    order: [['updatedAt', 'DESC']],
    limit: config.paginate.default,
    offset: 0,
  };
  let queryParams = restQueryToSequelize(req.user, req.query, defaults);
  queryParams = includeConversionData(defaults);
  queryParams = await filterByUser(req.user, queryParams);
  const finalQuery = {
    ...queryParams,
  };
  finalQuery.order = [[...finalQuery.order], eventOrder];
  const countQuery = {
    distinct: 'id',
    include: [{
      model: Users,
      as: 'signUps',
      attributes: ['id'],
    },
    {
      model: Events,
      attributes: ['id'],
    }],
  };
  const pagedResult = {
    limit: queryParams.limit,
    skip: queryParams.offset,
  };
  try {
    const count = await Links.count(countQuery);
    let links = await Links.findAll(finalQuery);
    links = links.map((link) => {
      const newLink = link.get({ plain: true });
      newLink.signUpCount = newLink.signUps.length;
      newLink.visitCount = newLink.events.length;
      newLink.conversionRate = newLink.visitCount === 0 ? 0 : newLink.signUpCount / newLink.visitCount;
      return newLink;
    });
    links = links.filter(link => link.visitCount > 0);
    res.json({ total: count, ...pagedResult, data: links });
  } catch (err) {
    next(err);
  }
};

module.exports.create = async (req, res, next) => {
  const link = {
    linkAction: 'signup',
    linkType: 'accounts',
    linkToken: await randomBytes(30),
  };

  try {
    if (!req.user) {
      const err = new Error(req.i18n.t('links.link_no_permissions_Error'));
      err.status = 401;
      throw err;
    }
    link.linkTypeId = req.user.accountId;
    const sendLink = await Links.create(link);
    res.json(sendLink);
  } catch (err) {
    next(err);
  }
};
