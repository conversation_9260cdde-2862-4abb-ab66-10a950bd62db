const express = require('express');
const XapiContent = require('../controllers/xapiContent');
const { requireAuth } = require('../services/utils/authUtils');

const router = express.Router();

// xAPI content delivery routes - these serve content directly to learners
router.route('/programs/:programId')
  .get(XapiContent.serveProgramContent);

router.route('/lessons/:lessonId')
  .get(XapiContent.serveLessonContent);

// xAPI authentication endpoint (similar to verifyScorm)
router.route('/auth')
  .post(XapiContent.verifyXapi);

module.exports = router;
