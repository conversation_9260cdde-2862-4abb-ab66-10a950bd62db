const db = require('../../db');
const { getAnswerReport, getAnswerReportInsights } = require('../../services/utils/answerCardUtils');

const LessonCards = db.lessonCards;
// eslint-disable-next-line max-len
async function fetchResults(user, lessonId, lessonCardId, campaignId, periodCategory = null, periodId = null, isInsightReport = false, lessonLifecycle = false) {
  const lessonCard = await LessonCards.findOne({ where: { id: lessonCardId } });
  // Note: Adding the campaignid to this call makes it slower.
  // In AI, we don't add the campaign id param. Come back to this to see why.
  const results = await getAnswerReport(
    user,
    lessonCard,
    lessonId,
    true,
    null,
    null,
    periodCategory,
    periodId,
    lessonLifecycle,
    isInsightReport,
  );
  return { cardType: lessonCard.cardType, results, title: lessonCard.title, description: lessonCard.description };
}

async function fetchResultsInsights(user, lessonId, lessonCardId, campaignId, periodCategory = null, periodId = null, isInsightReport = false, lessonLifecycle = false) {
  const lessonCard = await LessonCards.findOne({ where: { id: lessonCardId } });
  // Note: Adding the campaignid to this call makes it slower.
  // In AI, we don't add the campaign id param. Come back to this to see why.
  const results = await getAnswerReportInsights(
    user,
    lessonCard,
    lessonId,
    true,
    null,
    null,
    periodCategory,
    periodId,
    lessonLifecycle,
    isInsightReport,
  );
  return { cardType: lessonCard.cardType, results, title: lessonCard.title, description: lessonCard.description };
}

module.exports = { fetchResults, fetchResultsInsights };
