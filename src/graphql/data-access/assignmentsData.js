const db = require('../../db');
const { get } = require('lodash');
const moment = require('moment');
const logger = require('../../logger');
const { propagateAssignmentChange, updateProgramAssignments, updateCampaignAssignment,
  updateBaseAssignmentProgramUserLesson, getProgramRequiredTime } = require('../../services/utils/calcUtils');
const { calcUserLessonPctComplete } = require('../../services/utils/eventUtils');
const { BadRequestError } = require('../../services/utils/errors');

const Users = db.users;
const UserLessons = db.userLessons;
const BaseAssignments = db.baseAssignments;
const BaseAssignmentUsers = db.baseAssignmentUsers;
const GroupAssignments = db.groupAssignments;
const Programs = db.programs;
const LessonPrograms = db.lessonPrograms;
const Lessons = db.lessons;
const Campaigns = db.campaigns;
const ScormPrograms = db.scormPrograms;
const Integrations = db.integrations;
const Resources = db.resources;
const ResourceBundles = db.resourceBundles;
const Bundles = db.bundles;
const AccountBundles = db.accountBundles;
const Op = db.Sequelize.Op;

const getReviewQuery = (accountId) => {
  return {
    attributes: ['id'],
    where: {
      lifecycle: 'review',
    },
    include: [
      {
        model: Resources,
        as: 'resource',
        attributes: [],
        required: true,
        include: [
          {
            model: ResourceBundles,
            attributes: [],
            required: true,
            include: [
              {
                model: Bundles,
                attributes: [],
                required: true,
                include: [
                  {
                    model: AccountBundles,
                    attributes: [],
                    required: true,
                    where: {
                      accountId,
                      bundleId: {
                      // don't want items on the public account
                        [Op.ne]: 1,
                      },
                    },
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  };
};

const BROWSE_ENROLLMENT_ID = 1;

async function isScormAccount(accountId) {
  const integrations = await Integrations.findAll({
    attributes: ['id'],
    where: {
      accountId,
      integrationType: ['scorm', 'scorm20043rd', 'scorm20044th'],
    },
  });
  if (integrations && integrations.length > 0) {
    return true;
  }
  return false;
}

// This function fetches all of the userLessons for a user that are associated with campaigns.
// eslint-disable-next-line no-unused-vars
async function fetchCampaignUserLessons(userId, completed) {
  const campaignWhereFilter = { status: { [Op.in]: ['inProgress', 'closed', 'withdrawn'] } };

  const activeUserLessons = await UserLessons.findAll({
    where: { userId, status: { [Op.in]: ['open', 'inProgress', 'completed'] }, type: { [Op.ne]: 'campaign' } },
    order: [['assignmentId', 'ASC'], ['sequence', 'ASC']],
    include: [{
      model: GroupAssignments,
      required: true,
      include: [
        {
          model: Campaigns,
          where: campaignWhereFilter,
        },
        {
          model: Programs,
        },
      ],
      order: [['programId', 'ASC']],
    }],
  });

  return activeUserLessons;
}

// This function fetches all of the userLessons for a user that are associated with Base Assignments.
// eslint-disable-next-line no-unused-vars
async function fetchBaseAssignmentUserLessons(userId, completed) {
  const baseAssignmentWhereFilter = { status: { [Op.in]: ['open'] } };

  const activeUserLessons = await UserLessons.findAll({
    where: { userId, status: { [Op.in]: ['open', 'inProgress', 'completed'] }, baseAssignmentId: { [Op.ne]: null } },
    order: [['sequence', 'ASC']],
    include: [{
      model: BaseAssignments,
      where: baseAssignmentWhereFilter,
      required: true,
      include: [
        {
          model: Programs,
        },
      ],
      order: [['programId', 'ASC']],
    }],
  });

  return activeUserLessons;
}

// Fetch the user lessons for a single userLesson assignment.
// If the userLesson is a program, get the associated lesson userLessons too.
async function fetchAssignmentUserLessons(userId, assignmentUserLessonId) {
  const allAssignmentUserLessons = [];

  const userLessonRow = await UserLessons.findOne({
    where: {
      id: assignmentUserLessonId,
      userId,
      type: {
        [Op.ne]: 'campaign',
      },
    },
  });

  if (userLessonRow.assignmentId) {
    const campaignAssignmentUserLesson = await UserLessons.findOne({
      where: {
        id: assignmentUserLessonId,
        userId,
      },
      include: [{
        model: GroupAssignments,
        required: true,
        include: [
          {
            model: Campaigns,
          },
          {
            model: Programs,
            required: false,
          },
        ],
        order: [['programId', 'ASC']],
      }],
    });
    allAssignmentUserLessons.push(campaignAssignmentUserLesson);

    if (campaignAssignmentUserLesson.type === 'program') {
      const lessonUserLessons = await UserLessons.findAll({
        where: {
          userId,
          type: 'lesson',
          status: {
            [Op.not]: 'closed',
          },
        },
        order: [['assignmentId', 'ASC'], ['sequence', 'ASC']],
        include: [{
          model: GroupAssignments,
          required: true,
          where: {
            programId: campaignAssignmentUserLesson.resourceId,
            campaignId: campaignAssignmentUserLesson.groupAssignment.campaign.id,
          },
          include: [
            {
              model: Campaigns,
            },
            {
              model: Programs,
            },
          ],
          order: [['programId', 'ASC']],
        }],
      });
      allAssignmentUserLessons.push(...lessonUserLessons);
    }
  } else if (userLessonRow.baseAssignmentId) {
    const baseAssignmentUserLesson = await UserLessons.findOne({
      where: {
        id: assignmentUserLessonId,
        userId,
      },
      include: [{
        model: BaseAssignments,
        required: true,
        include: [
          {
            model: Programs,
          },
        ],
        order: [['programId', 'ASC']],
      }],
    });
    allAssignmentUserLessons.push(baseAssignmentUserLesson);

    if (baseAssignmentUserLesson.type === 'program') {
      const lessonUserLessons = await UserLessons.findAll({
        where: {
          userId,
          type: 'lesson',
          baseAssignmentId: baseAssignmentUserLesson.baseAssignmentId,
          baseAssignmentParentProgramId: baseAssignmentUserLesson.resourceId,
        },
        include: [{
          model: BaseAssignments,
          required: true,
        }],
      });
      allAssignmentUserLessons.push(...lessonUserLessons);
    }
  }
  return allAssignmentUserLessons;
}

// Check if a userLesson of type lesson is complete.
async function checkAssignmentCompletion(context, lessonId, enrollmentId) {
  const userId = get(context, 'user.id');
  const account = get(context, 'user.accounts[0]');

  let userLesson = await UserLessons.findOne({
    where: {
      userId,
      resourceId: lessonId,
      enrollmentId,
      type: 'lesson',
    },
  });

  if (!userLesson) {
    // eslint-disable-next-line max-len
    throw new BadRequestError(context.i18n.t('userLessons.userLesson_by_lessonId_enrollmentId_load_Error', { lessonId, enrollmentId }));
  }

  if (userLesson && userLesson.percentComplete < 100) {
    const percentComplete =
      await calcUserLessonPctComplete(userId, lessonId, userLesson.sourceLifecycle, enrollmentId);
    // update % complete if changed
    if (userLesson.percentComplete !== percentComplete) {
      userLesson = await userLesson.update({ percentComplete });
      const parents = await propagateAssignmentChange(userLesson);
      await updateProgramAssignments(account.id, parents);
      await updateCampaignAssignment(parents, context.sessionId);
      await updateBaseAssignmentProgramUserLesson(account.id, userId, userLesson);

      // eslint-disable-next-line max-len
      logger.info(`userLesson calculated as complete, updated userLesson: ${userLesson.id} for user: ${userId} and lesson: ${lessonId} to ${percentComplete}`);
    }
  }
  return userLesson.percentComplete === 100;
}

// Get all of the userLessons for a shared assignment of type program.
async function getSharedAssignmentProgramUserLessons(baseAssignmentId, userId, programId, accountId, enrollmentId) {
  const userLessonBase = {
    userId,
    sourceLifecycle: 'publish',
    baseAssignmentId,
  };
  let allUserLessons = [];
  try {
    const lessonPrograms = await LessonPrograms.findAll({
      where: { programId, dateRemoved: { [Op.eq]: null } },
      order: [['position', 'ASC']],
    });
    const programLessonIds = lessonPrograms.map(nextLessonProgram => nextLessonProgram.lessonId);
    const lessonUserLessons = await UserLessons.findAll({
      where: {
        ...userLessonBase,
        type: 'lesson',
        resourceId: { [Op.in]: programLessonIds },
        baseAssignmentParentProgramId: programId,
        status: {
          [Op.and]: [
            { [Op.ne]: 'closed' },
            { [Op.ne]: 'incomplete' },
          ],
        },
      },
    });

    let programUserLesson = await UserLessons.findOne({
      where: {
        ...userLessonBase,
        type: 'program',
        resourceId: programId,
        status: {
          [Op.and]: [
            { [Op.ne]: 'closed' },
            { [Op.ne]: 'incomplete' },
          ],
        },
      },
    });

    for await (const lessonProgram of lessonPrograms) {
      if (!lessonUserLessons.find(nextUserLesson => nextUserLesson.resourceId === lessonProgram.lessonId)) {
        const lessonUserLesson = {
          ...userLessonBase,
          resourceId: lessonProgram.lessonId,
          baseAssignmentParentProgramId: programId,
          type: 'lesson',
          status: 'open',
          sequence: lessonProgram.position,
        };
        const newUserLesson = await UserLessons.create(lessonUserLesson);
        lessonUserLessons.push(newUserLesson);
      }
    }

    // If we already have the existing programUserLesson, there should be nothing to do.
    // If we don not have one existing, create it
    if (!programUserLesson) {
      const programUserLessonData = {
        type: 'program',
        userId,
        baseAssignmentId,
        resourceId: programId,
        sourceLifecycle: 'publish',
        status: 'open',
        percentComplete: 0,
        elapsedTimeInSeconds: 0,
        requiredSeconds: 0,
        requiredSecondsTaken: 0,
        enabled: true,
        enrollmentId,
      };
      let totalPercentageComplete = 0;
      let totalCompleted = 0;

      const programRequiredTime = await getProgramRequiredTime(programId, accountId);
      const programRequiredTimeInSeconds = programRequiredTime * 60;
      for (const nextUserLesson of lessonUserLessons) {
        programUserLessonData.elapsedTimeInSeconds += nextUserLesson.elapsedTimeInSeconds;
        totalPercentageComplete += nextUserLesson.percentComplete;
        if (nextUserLesson.status === 'completed') {
          totalCompleted += 1;
        }
      }
      programUserLessonData.requiredSeconds = programRequiredTime * 60;
      programUserLessonData.requiredSecondsTaken =
      programUserLessonData.elapsedTimeInSeconds > (programRequiredTime * 60) ?
        (programRequiredTime * 60) : programUserLessonData.elapsedTimeInSeconds;
      programUserLessonData.percentComplete = totalPercentageComplete / lessonUserLessons.length;
      if (totalCompleted === lessonUserLessons.length &&
      programUserLessonData.elapsedTimeInSeconds >= programRequiredTimeInSeconds) {
        programUserLessonData.status = 'completed';
      }

      programUserLesson = await UserLessons.create(programUserLessonData);
    }
    allUserLessons = [programUserLesson, ...lessonUserLessons];
  } catch (err) {
    logger.error(err.message);
  }
  return allUserLessons;
}

// Get all of the userLessons for a program assignment that is not saved to the database.
// This includes SCORM programs.
// This is similar to the API that calls getProgramProgress. We have to do it this way because
// there is not a unified design for how program userLessons were treated in the campaign and browse context.
// Campaigns will create program userLessons in the userLessons table, browse mode does not. This is a legacy design issue.
async function getVirtualProgramUserLessons(userId, programId, accountId, lifecycle, previewMode = false) {
  const userLessonBase = {
    userId,
    sourceLifecycle: lifecycle || 'publish',
    enrollmentId: BROWSE_ENROLLMENT_ID,
  };
  const lessonPrograms = await LessonPrograms.findAll({
    where: { programId, dateRemoved: { [Op.eq]: null } },
    order: [['position', 'ASC']],
  });

  if (previewMode && lessonPrograms) {
    const lessonIds = lessonPrograms.map(nextLessonProgram => nextLessonProgram.lessonId);
    await UserLessons.destroy({
      where: {
        userId,
        sourceLifecycle: 'preview',
        enrollmentId: BROWSE_ENROLLMENT_ID,
        resourceId: {
          [Op.in]: lessonIds,
        },
        status: {
          [Op.in]: ['inProgress', 'completed'],
        },
      },
    });
  }

  const lessonUserLessonsQuery = `SELECT
  lessons.id, lessons.title, lessonPrograms.position,
  userLessons.id, userLessons.userId, userLessons.resourceId, userLessons.status, 
  userLessons.createdAt, userLessons.updatedAt, userLessons.type,
  userLessons.assignmentId, userLessons.startDate, userLessons.dueDate,
  userLessons.sequence, userLessons.sourceLifecycle, userLessons.startedAt, 
  userLessons.elapsedTimeInSeconds, userLessons.percentComplete,
  userLessons.completionDate, userLessons.enrollmentId,
  userLessons.requiredSeconds, userLessons.assignedAt, userLessons.removalReason,
  userLessons.removedAt, userLessons.requiredSecondsTaken,
  baseAssignmentId, userLessons.baseAssignmentParentProgramId 
  FROM
    lessonPrograms
        JOIN
    lessons ON lessons.id = lessonPrograms.lessonId
      AND lessons.deletedAt IS NULL
        JOIN
    userLessons ON userLessons.resourceId = lessonPrograms.lessonId
        AND userLessons.type = 'lesson'
        AND userLessons.status NOT IN ('closed' , 'incomplete')
        AND userLessons.userId = ${userId}
        AND userLessons.enrollmentId = ${BROWSE_ENROLLMENT_ID}
        AND userLessons.sourceLifecycle = '${lifecycle || 'publish'}'
  WHERE
    lessonPrograms.programId = ${programId}
        AND lessonPrograms.dateRemoved IS NULL
  ORDER BY lessonPrograms.position ASC;`;
  const lessonUserLessons = await db.sequelize.query(
    lessonUserLessonsQuery,
    { type: db.sequelize.QueryTypes.SELECT, nest: true },
  );
  // We just need a random id for this viruual program userLesson that will be unique + consistent for this user/program.
  // even if the user has two programs with the same set of lessons.
  // This will do for now, but maybe we can come back to do something better here later.
  const virtualProgramId = parseInt(userId) + parseInt(programId);
  for (const lessonProgram of lessonPrograms) {
    if (!lessonUserLessons.find(nextUserLesson => nextUserLesson.resourceId === lessonProgram.lessonId)) {
      const lessonUserLesson = {
        ...userLessonBase,
        resourceId: lessonProgram.lessonId,
        type: 'lesson',
        status: 'open',
        startedAt: Date.now(),
      };
      const newUserLesson = await UserLessons.create(lessonUserLesson);
      lessonUserLessons.push(newUserLesson);
    }
  }
  const calculatedProgramUserLesson = {
    // Creating unique id for the virtual program userLesson.
    id: virtualProgramId,
    type: 'program',
    resourceId: programId,
    sourceLifecycle: lifecycle || 'publish',
    status: 'inProgress',
    percentComplete: 0,
    completionDate: null,
    elapsedTimeInSeconds: 0,
    enabled: true,
  };
  let totalPercentageComplete = 0;
  let totalCompleted = 0;

  const programRequiredTime = await getProgramRequiredTime(programId, accountId);
  const programRequiredTimeInSeconds = programRequiredTime * 60;
  for (const nextUserLesson of lessonUserLessons) {
    calculatedProgramUserLesson.elapsedTimeInSeconds += nextUserLesson.elapsedTimeInSeconds;
    totalPercentageComplete += nextUserLesson.percentComplete;
    if (nextUserLesson.status === 'completed') {
      totalCompleted += 1;
      if (calculatedProgramUserLesson.completionDate === null ||
        moment(nextUserLesson.completionDate).isAfter(moment(calculatedProgramUserLesson.completionDate))) {
        calculatedProgramUserLesson.completionDate = nextUserLesson.completionDate;
      }
    }
  }
  calculatedProgramUserLesson.percentComplete = totalPercentageComplete / lessonUserLessons.length;
  if (totalCompleted === lessonUserLessons.length &&
    calculatedProgramUserLesson.elapsedTimeInSeconds >= programRequiredTimeInSeconds) {
    calculatedProgramUserLesson.status = 'completed';
  } else {
    calculatedProgramUserLesson.completionDate = null;
  }
  const allUserLessons = [calculatedProgramUserLesson, ...lessonUserLessons];
  return allUserLessons;
}

// Get all of the userLessons for a scorm program assignment that is not saved to the database.
// Scorm mode is similar to browse mode in the application in that it does not create program userLessons in the db.
// Campaigns will create program userLessons in the userLessons table, scorm mode does not. This is a legacy design issue.
async function fetchScormAssignmentUserLessons(userId, accountId, scormProgramId) {
  const allAssignmentUserLessons = [];
  const usersScormProgram = await ScormPrograms.findOne({
    where: { id: scormProgramId },
  });

  if (!usersScormProgram || usersScormProgram.length === 0) {
    return allAssignmentUserLessons;
  }

  if (usersScormProgram.model === 'program') {
    const programUserLessons = await getVirtualProgramUserLessons(userId, usersScormProgram.resourceId, accountId);
    return programUserLessons;
  }
  // else, if it's a lesson, just get the lesson userLesson
  const assignmentUserLesson = await UserLessons.findOne({
    where: {
      userId,
      resourceId: usersScormProgram.resourceId,
      type: usersScormProgram.model,
      enrollmentId: BROWSE_ENROLLMENT_ID,
    },
    order: [['assignmentId', 'DESC']],
  });
  if (!assignmentUserLesson || assignmentUserLesson.length === 0) {
    return allAssignmentUserLessons;
  }

  allAssignmentUserLessons.push(assignmentUserLesson);
  return allAssignmentUserLessons;
}

async function fetchUserScormProgram(id, userId, model) {
  const scormProgram = await ScormPrograms.findOne({
    where: {
      id,
      userId,
      model,
    },
    order: [['id', 'DESC']],
  });
  return scormProgram;
}

async function checkScormProgramCompletion(program, scormProgram, scormUserLessons, accountId) {
  if (scormProgram.completionDate) {
    return true;
  }

  const userLessons = scormUserLessons && scormUserLessons.filter(nextUserLesson => nextUserLesson.type === 'lesson');
  let isCompleted = false;
  let totalTime = 0;
  const lessonsWithStatusIncomplete = userLessons.filter(nextUserLesson => nextUserLesson.status !== 'completed');
  if (lessonsWithStatusIncomplete.length > 0) {
    return false;
  }

  const accountProgram = db.accountPrograms.findOne({ where: { accountId, programId: program.id } });
  const minMins = accountProgram && accountProgram.minTimeInMinutes
    ? accountProgram.minTimeInMinutes
    : program.minTimeInMinutes;
  const requiredSeconds = minMins
    ? minMins * 60
    : 0;
  if (requiredSeconds === 0) {
    isCompleted = true;
  } else if (userLessons && userLessons.length > 0) {
    userLessons.forEach((nextUserLesson) => {
      totalTime += nextUserLesson.elapsedTimeInSeconds;
    });
    if (totalTime >= requiredSeconds) {
      isCompleted = true;
    }
  }
  if (isCompleted) {
    await scormProgram.update({ completionDate: Date.now() });
  }
  return isCompleted;
}

// This is used by the scorm entry point API when we may need to create the userLesson for the scorm user if it does not exist.
async function fetchOrCreateScormAssignmentUserLessons(context, programId, lessonId) {
  if (!programId && !lessonId) {
    // eslint-disable-next-line max-len
    throw new BadRequestError(context.i18n.t('userLessons.userLesson_no_resourceId_load_Error'));
  }
  const userId = get(context, 'user.id');
  const accountId = get(context, 'user.accountId');
  const scormUserLessonBase = {
    userId,
    sourceLifecycle: 'publish',
    enrollmentId: BROWSE_ENROLLMENT_ID,
  };
  // This is the case for a program scorm assignment
  if (programId) {
    const programUserLessons = await getVirtualProgramUserLessons(userId, programId, accountId);
    return programUserLessons;
  }
  // This is the case for a lesson scorm assignment
  const existingUserLesson = await UserLessons.findOne({
    where: {
      ...scormUserLessonBase,
      type: 'lesson',
      resourceId: lessonId,
      status: {
        [Op.and]: [
          { [Op.ne]: 'closed' },
          { [Op.ne]: 'incomplete' },
        ],
      },
    },
  });
  if (existingUserLesson) {
    return [existingUserLesson];
  }

  const lessonUserLesson = {
    ...scormUserLessonBase,
    resourceId: lessonId,
    type: 'lesson',
    status: 'open',
    startedAt: Date.now(),
  };
  const newUserLesson = await UserLessons.create(lessonUserLesson);
  return [newUserLesson];
}

// Fetching unpublished userLessons is used by both preview ond review mode.
// Similar to browse mode, the program userLesson is virtual and not maintained in the userLessons db table.
async function fetchUnpublishedUserLessons(userId, accountId, programId, lessonId, lifecycle) {
  const allAssignmentUserLessons = [];

  if (programId) {
    const programUserLessons = await getVirtualProgramUserLessons(userId, programId, accountId, lifecycle);
    return programUserLessons;
  }
  // else, if it's a lesson, just get the lesson userLesson
  const assignmentUserLesson = await UserLessons.findOne({
    where: {
      userId,
      resourceId: lessonId,
      type: 'lesson',
      sourceLifecycle: lifecycle,
      enrollmentId: BROWSE_ENROLLMENT_ID,
    },
    order: [['updatedAt', 'DESC']],
  });
  if (!assignmentUserLesson || assignmentUserLesson.length === 0) {
    return allAssignmentUserLessons;
  }

  allAssignmentUserLessons.push(assignmentUserLesson);
  return allAssignmentUserLessons;
}

// This is used by the preview entry point API when we may need to create the userLesson for the preview user if it does not exist.
async function fetchOrCreatePreviewAssignmentUserLessons(context, programId, lessonId) {
  if (!programId && !lessonId) {
    // eslint-disable-next-line max-len
    throw new BadRequestError(context.i18n.t('userLessons.userLesson_no_resourceId_load_Error'));
  }
  const userId = get(context, 'user.id');
  const accountId = get(context, 'user.accountId');
  const previewUserLessonBase = {
    userId,
    sourceLifecycle: 'preview',
    enrollmentId: BROWSE_ENROLLMENT_ID,
  };
  // This is the case for a program assignment
  if (programId) {
    const programUserLessons = await getVirtualProgramUserLessons(userId, programId, accountId, 'preview', true);
    return programUserLessons;
  }
  // This is the case for a lesson assignment
  if (lessonId) {
    await UserLessons.destroy({
      where: {
        ...previewUserLessonBase,
        type: 'lesson',
        resourceId: lessonId,
      },
    });
  }

  const lessonUserLesson = {
    ...previewUserLessonBase,
    resourceId: lessonId,
    type: 'lesson',
    status: 'open',
    startedAt: Date.now(),
  };
  const newUserLesson = await UserLessons.create(lessonUserLesson);
  return [newUserLesson];
}


// This is used by the review entry point API when we may need to create the userLesson for the review user if it does not exist.
async function fetchOrCreateReviewerAssignments(context) {
  const isContentReviewer = get(context, 'user.accounts[0].accountUsers.roleId') === 7;
  if (!isContentReviewer) {
    // eslint-disable-next-line max-len
    throw new BadRequestError(context.i18n.t('userLessons.userLesson_not_content_reviewer_Error'));
  }
  const userId = get(context, 'user.id');
  const accountId = get(context, 'user.accountId');

  const accountReviewPrograms = await Programs.findAll(getReviewQuery(accountId));
  const accountReviewLessons = await Lessons.findAll(getReviewQuery(accountId));

  const accountProgramLessonLifecycleClause = {
    lifecycle: {
      [Op.or]: [
        { [Op.eq]: 'review' },
        { [Op.eq]: 'publish' },
      ],
    },
  };

  // Only programs where all lessons in the program are also available in the account will be valid.
  // If even one lesson is not present, we should not display the program for the account reviewer.
  // Additionally, we should only display lessons as assignment items, if they are not
  // present in any of the valid programs.
  const validAccountReviewPrograms = [];
  const validAccountReviewLessons = [];
  let lessonIdsPresentInPrograms = [];
  for (const nextAccountProgram of accountReviewPrograms) {
    let allLessonsAvailable = true;
    const lessonPrograms = await LessonPrograms.findAll({ where: { programId: nextAccountProgram.id } });
    const lessonIdList = lessonPrograms.map(nextLessonProgram => nextLessonProgram.lessonId);

    // Now find all the lessons from the lessonIdList that are available to the account.
    const programLessonsQuery = getReviewQuery(accountId);
    programLessonsQuery.where = { ...accountProgramLessonLifecycleClause, ...{ id: { [Op.in]: lessonIdList } } };
    const accountProgramLessons = await Lessons.findAll(programLessonsQuery);

    // check to see if each lesson associated with the program is available in the accountLesson list.
    for (const nextLesson of lessonPrograms) {
      if (!accountProgramLessons.find(nextAccountLesson => nextAccountLesson.id === nextLesson.lessonId)) {
        allLessonsAvailable = false;
      }
    }

    if (allLessonsAvailable) {
      validAccountReviewPrograms.push(nextAccountProgram);
      lessonIdsPresentInPrograms = [...lessonIdsPresentInPrograms, ...lessonIdList];
    }
  }

  for (const nextAccountLesson of accountReviewLessons) {
    if (!lessonIdsPresentInPrograms.find(nextLessonId => nextLessonId === nextAccountLesson.id)) {
      validAccountReviewLessons.push(nextAccountLesson);
    }
  }

  if (!validAccountReviewPrograms && !validAccountReviewLessons) {
    return [];
  }

  const allUserLessons = [];
  const reviewUserLessonBase = {
    userId,
    sourceLifecycle: 'review',
    enrollmentId: BROWSE_ENROLLMENT_ID,
  };

  for (const nextProgram of validAccountReviewPrograms) {
    const programUserLessons = await getVirtualProgramUserLessons(userId, nextProgram.id, accountId, 'review');
    allUserLessons.push(...programUserLessons);
  }
  for (const nextLesson of validAccountReviewLessons) {
    const existingUserLesson = await UserLessons.findOne({
      where: {
        ...reviewUserLessonBase,
        type: 'lesson',
        resourceId: nextLesson.id,
        status: {
          [Op.and]: [
            { [Op.ne]: 'closed' },
            { [Op.ne]: 'incomplete' },
          ],
        },
      },
    });
    if (existingUserLesson) {
      allUserLessons.push(existingUserLesson);
    } else {
      const lessonUserLesson = {
        ...reviewUserLessonBase,
        resourceId: nextLesson.id,
        type: 'lesson',
        status: 'open',
        startedAt: Date.now(),
      };
      const newUserLesson = await UserLessons.create(lessonUserLesson);
      allUserLessons.push(newUserLesson);
    }
  }
  // filtering to remove duplicate lessons from list
  const reviewUserLessons = allUserLessons.filter((obj, index) => {
    return index === allUserLessons.findIndex(lesson => obj.id === lesson.id);
  });

  return reviewUserLessons;
}

// This function is required to create shared assginment userLessons for the shared assignemnt entry point.
async function fetchOrCreateBaseAssignmentUserLessons(context, programId, lessonId, assignmentType) {
  if (!programId && !lessonId) {
    // eslint-disable-next-line max-len
    throw new BadRequestError(context.i18n.t('userLessons.userLesson_no_resourceId_load_Error'));
  }
  const userId = get(context, 'user.id');
  const accountId = get(context, 'user.accountId');

  let baseSharedAssignment = await BaseAssignments.findOne({
    attributes: ['id'],
    where: {
      assignmentType: { [Op.in]: ['shared', 'external'] },
      status: 'open',
    },
    include: [
      {
        model: Users,
        attributes: [],
        where: {
          id: userId,
        },
        required: true,
      },
      {
        model: UserLessons,
        attributes: [],
        where: {
          resourceId: programId || lessonId,
          type: programId ? 'program' : 'lesson',
          baseAssignmentParentProgramId: {
            [Op.eq]: null,
          },
          status: {
            [Op.or]: [
              { [Op.eq]: 'open' },
              { [Op.eq]: 'inProgress' },
            ],
          },
        },
        required: true,
      },
    ],
  });

  if (!baseSharedAssignment) {
    baseSharedAssignment = await BaseAssignments.create({
      accountId,
      assignmentType,
      status: 'open',
      startDate: Date.now(),
    });
    await BaseAssignmentUsers.create({ baseAssignmentId: baseSharedAssignment.id, userId });
  }
  const sharedUserLessonBase = {
    userId,
    sourceLifecycle: 'publish',
  };
  // This is the case for a program assignment
  if (programId) {
    const programUserLessons =
      // eslint-disable-next-line max-len
      await getSharedAssignmentProgramUserLessons(baseSharedAssignment.id, userId, programId, accountId);
    return programUserLessons;
  }
  // This is the case for a lesson assignment
  const existingUserLesson = await UserLessons.findOne({
    where: {
      ...sharedUserLessonBase,
      type: 'lesson',
      resourceId: lessonId,
      baseAssignmentId: baseSharedAssignment.id,
      status: {
        [Op.and]: [
          { [Op.ne]: 'closed' },
          { [Op.ne]: 'incomplete' },
        ],
      },
    },
  });
  if (existingUserLesson) {
    return [existingUserLesson];
  }

  const lessonUserLesson = {
    ...sharedUserLessonBase,
    resourceId: lessonId,
    baseAssignmentId: baseSharedAssignment.id,
    type: 'lesson',
    status: 'open',
  };
  const newUserLesson = await UserLessons.create(lessonUserLesson);
  return [newUserLesson];
}

// Closes a baseAssignment base on the assignment userLesson ID.
async function closeBaseAssignment(userId, assignmentUserLessonId, removalReason, removedAt = null) {
  const userLesson = await UserLessons.findOne({
    where: {
      id: assignmentUserLessonId,
      userId,
      baseAssignmentId: {
        [Op.ne]: null,
      },
    },
  });
  // Close the baseAssignment, and also close all the associated userLessons for this user.
  await BaseAssignments.update({ status: 'closed' }, { where: { id: userLesson.baseAssignmentId } });
  await UserLessons.update(
    { status: 'closed', removalReason, removedAt },
    { where: { userId, baseAssignmentId: userLesson.baseAssignmentId } },
  );
}

async function programExists(programId) {
  const instance = await Programs.findByPk(programId);
  return !!instance;
}

async function lessonExists(lessonId) {
  const instance = await Lessons.findByPk(lessonId);
  return !!instance;
}

async function checkAccountProgramsLessons(accountId, contectId, contentType) {
  let bundleIds = [];
  if (accountId) {
    const bundles = await AccountBundles.findAll({
      attributes: ["bundleId"],
      where: {
        accountId: accountId,
      },
    });
    bundleIds = bundles.map((bundle) => bundle.bundleId);
  }

  const resourceBundlesWhere = {
    bundleId: {
      [Op.in]: bundleIds,
    },
  };

  const includeResource = [
    {
      model: ResourceBundles,
      where: resourceBundlesWhere,
      attributes: [],
    },
  ];

  const includeParams = [
    {
      model: Resources,
      as: "resource",
      where: { digestable: contentType },
      include: includeResource,
      required: true,
      attributes: [],
    },
  ];

  let sourceLessonProgram;
  if (contentType === "programs") {
    sourceLessonProgram = await Programs.findByPk(contectId, {
      attributes: ["id"],
      include: includeParams,
    });
  } else if (contentType === "lessons") {
    sourceLessonProgram = await Lessons.findByPk(contectId, {
      attributes: ["id"],
      include: includeParams,
    });
  }

  if (!sourceLessonProgram) {
    throw new BadRequestError(`Failed to load ${contentType} - ${contectId}`);
  }
}

module.exports = {
  fetchCampaignUserLessons,
  fetchBaseAssignmentUserLessons,
  fetchAssignmentUserLessons,
  checkAssignmentCompletion,
  fetchOrCreateScormAssignmentUserLessons,
  fetchScormAssignmentUserLessons,
  isScormAccount,
  fetchUnpublishedUserLessons,
  fetchOrCreatePreviewAssignmentUserLessons,
  fetchOrCreateReviewerAssignments,
  fetchOrCreateBaseAssignmentUserLessons,
  closeBaseAssignment,
  fetchUserScormProgram,
  checkScormProgramCompletion,
  programExists,
  lessonExists,
  checkAccountProgramsLessons,
};
