extend type Query {
  getLessonCards(lessonId: ID!, enrollmentId: ID!): [LessonCard!]!
  getResultsCard(lessonId: ID!, lessonCardId: ID!, campaignId: ID, lessonLifecycle: String): ResultsReport!
  getInsightReports(contentId: ID!, type: String!, periodCategory: String, periodId: String): InsightReports!
}

enum ReportType {
  BOOLEAN
  SINGLE_CHOICE
  MULTIPLE_CHOICE
  SLIDER
  WORKPLACE_COLOR_SPECTRUM
  WORDCLOUD
}

union ResultsData = BooleanQuizReport | SingleChoiceQuizReport | MultiChoiceQuizReport | SliderQuizReport | ColorSpectrumQuizReport | WordCloudReport

type ResultsReport {
  type: ReportType!
  organization: ResultsData!
  global: ResultsData!
}

type SingleResponseData {
  total: Int!
  percentage: Float!
}

type MultiResponseData {
  total: Int!
  percentages: [Float!]!
}

type ResponseSegmentPercentages {
  response: String!
  percentage: Float!
}

type ResponseSegmentData {
  total: Int!
  percentages: [ResponseSegmentPercentages!]!
}

type WordCloudSegmentData {
  text: String!
  value: Int!
}

type BooleanQuizReport {
  booleanReportData: SingleResponseData!
}

type SingleChoiceQuizReport {
  singleChoiceReportData: MultiResponseData!
}

type MultiChoiceQuizReport {
  multiChoiceReportData: MultiResponseData!
}

type SliderQuizReport {
  sliderReportData: ResponseSegmentData!
}

type ColorSpectrumQuizReport {
  colorSpectrumReportData: ResponseSegmentData!
}

type WordCloudReport {
  freeFormReportData: [WordCloudSegmentData!]!
}

type MultipleAnswer {
  answer1: Boolean!
  answer2: Boolean!
  answer3: Boolean!
  answer4: Boolean!
  answer5: Boolean!
  answer6: Boolean!
  answer7: Boolean!
  answer8: Boolean!
  answer9: Boolean!
  answer10: Boolean!
}

type Answer {
  booleanAnswer: Boolean
  checkboxAnswer: MultipleAnswer
  sliderAnswer: Int
  singleChoiceAnswer: Int
  multipleChoiceAnswer: MultipleAnswer
  freeFormTextAnswer: String
  policyAcknowledgementAnswer: Boolean
  workplaceColorSpectrumAnswer: WorkplaceColorSpectrumAnswer
}

interface LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  translationLanguage: String
}

enum LessonCardType {
  TEXT
  TEXT_WITH_IMAGE
  VIDEO
  BULLET_LIST
  NUMBERED_LIST
  CHECKBOX_LIST
  TEXT_OVERLAY_WITH_IMAGE
  CLICK_EXPAND
  US_MAP
  CANADA_MAP
  BOOLEAN
  SLIDER
  SINGLE_CHOICE
  MULTIPLE_CHOICE
  FREEFORM_TEXT
  POLICY_ACKNOWLEDGEMENT
  WORKPLACE_COLOR_SPECTRUM
  END_OF_LESSON
  BORDERLESS_TEXT_WITH_IMAGE
}

type TextCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  translationLanguage: String
}

"""
This type handles TEXT_WITH_IMAGE, BORDERLESS_TEXT_WITH_IMAGE and TEXT_OVERLAY_WITH_IMAGE types
"""
type TextImageCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  imageUrl: String
  imageAltText: String
  backgroundColor: String
  textOffset: String
  translationLanguage: String
}

type VideoCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  videoId: String!
  translationLanguage: String
  isTranscriptAdded: Boolean
}

"""
This type handles both BULLET_LIST, CHECKBOX_LIST, and NUMBERED_LIST types
"""
type OrderedListCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  listItems: [String!]!
  answer: Answer
  translationLanguage: String
}

"""
Click expand item contents are strings of HTML
"""
type ClickExpandItem {
  text: String!
  expandedText: String!
  speechAudioURL: String
}

type ClickExpandCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  items: [ClickExpandItem!]!
  translationLanguage: String
}

type mapTrait {
  id: ID!
  locationAbbreviation: String!
  locationTraitText: String!
  speechAudioURL: String
}

"""
This type handles both US_MAP and CANADA_MAP types
"""
type MapCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  mapKey: String!
  mapTraits: [mapTrait!]!
  translationLanguage: String
}

type BooleanLabel {
  trueText: String!
  falseText: String!
}

type BooleanCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  booleanLabel: BooleanLabel!
  answer: Answer
  translationLanguage: String
}

type SpectrumText {
  minText: String!
  maxText: String!
}

type SliderCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  sliderMax: Int!
  spectrumText: SpectrumText!
  labelText: [String!]
  answer: Answer
  translationLanguage: String
}

"""
Choice text is HTML
"""
type Choice {
  gatedChoice: Boolean!
  text: String!
}

type SingleChoiceCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  gatedFeedbackSpeechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  gated: Boolean!
  choices: [Choice!]!
  correctChoiceFeedback: String,
  wrongChoiceErrorMessage: String,
  answer: Answer
  translationLanguage: String
}

"""
There is no such thing as a "gated" multiple choice card
"""
type MultipleChoiceCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  choices: [String!]!
  answer: Answer
  translationLanguage: String 
}

type FreeformTextCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  answer: Answer
  translationLanguage: String
}

enum PolicyType {
  LINK
  FILE
}

type PolicyAcknowledgementCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  policyType: PolicyType!
  url: String
  answer: Answer
  translationLanguage: String
}

enum WorkplaceColorSpectrumAnswer {
  GREEN
  YELLOW
  ORANGE
  RED
}

type WorkplaceColorSpectrumCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  regionAbbreviation: String
  correctAnswer: WorkplaceColorSpectrumAnswer
  answer: Answer
  translationLanguage: String
}

type EndOfLessonCard implements LessonCard {
  id: ID!
  type: LessonCardType!
  title: String
  description: String
  speechAudioURL: String
  fontColor: String
  lastViewedCard: Boolean
  imageUrl: String
  imageAltText: String
  restartPrompt: String
  nexLessonPrompt: String
  translationLanguage: String
}
type ResultCards {
  id: ID!
  type: String
  title: String
  description: String
  fontColor: String
  booleanLabel: BooleanLabel
  spectrumText: SpectrumText
  labelText: [String]
  sliderAnswer: Int
  gated: Boolean
  choices: [Choice]
  multipleChoices: [String]
  freeFormTextAnswer: String
  regionAbbreviation: String
  workplaceColorSpectrumAnswer: String
  correctAnswer: WorkplaceColorSpectrumAnswer
  answer: Answer
  organization: ResultsData
  global: ResultsData
}

type LessonCards {
  lessonId: ID!
  lessonName: String
  reportData: [ResultCards]
}

type InsightReports {
  data: [LessonCards]
}
