const { get } = require('lodash');
const { resolveLessonCardType, tranformLessonCard, tranformAnswerCard } = require('../utils/lessonCardUtils');
const { tranformResults, resolveResultsDataType } = require('../utils/resultsReportUtils');
const { fetchLessonCards, fetchLastViewedLessonCard,
  saveAnswer, fetchLessonCardTypeForAnswer, fetchQuizLessonCards } = require('../data-access/lessonCardsData');
const { fetchResults, fetchResultsInsights } = require('../data-access/resultsCardData');
const { fetchLessons } = require('../data-access/lessonData');
const { fetchLessonsForProgram } = require('../data-access/programData');

const BOOLEAN = 'BOOLEAN';
const SLIDER = 'SLIDER';
const SINGLE_CHOICE = 'SINGLE_CHOICE';
const MULTIPLE_CHOICE = 'MULTIPLE_CHOICE';
const FREEFORM_TEXT = 'FREEFORM_TEXT';
const WORDCLOUD = 'WORDCLOUD';
const WORKPLACE_COLOR_SPECTRUM = 'WORKPLACE_COLOR_SPECTRUM';

const queries = {
  getLessonCards: async (prev, params, context) => {
    // 1. Get all lesson cards for a lesson
    // eslint-disable-next-line max-len
    const rawLessonCards = await fetchLessonCards(context, parseInt(params.lessonId), parseInt(params.enrollmentId));
    const lastViewedLessonCard =
      await fetchLastViewedLessonCard(context, parseInt(params.lessonId), parseInt(params.enrollmentId));
    // 2. Transform the cards to map to the graphql schema.
    const transformedSchemaCards =
      // eslint-disable-next-line max-len
      rawLessonCards.map(lessonCard => tranformLessonCard(lessonCard, context, lastViewedLessonCard && lastViewedLessonCard.lessonCardId === lessonCard.id));
    return transformedSchemaCards;
  },
  getResultsCard: async (prev, params, context) => {
    const resultsReport = await fetchResults(context.user, params.lessonId, params.lessonCardId, params.campaignId, params.startDate, params.endDate, true, params.lessonLifecycle);
    const transformedResults = tranformResults(resultsReport);
    return transformedResults;
  },
  getInsightReports: async (prev, params, context) => {
    let contentData = {};
    let lessonsData;
    if (params.type === 'lesson') {
      contentData = await fetchLessons(context, [params.contentId]);
      lessonsData = Object.values(contentData);
    } else {
      contentData = await fetchLessonsForProgram(context, params.contentId, true);
      lessonsData = contentData.size ? contentData.values() : [];
    }
    const allQuizCardsReport = [];
    for await (const lessonData of lessonsData) {
      const resultsReports = [];
      const lessonCards = await fetchQuizLessonCards(context, lessonData.id);
      for await (const lessonCard of lessonCards) {
        const rData = await fetchResultsInsights(context.user, lessonData.id, lessonCard.id, params.campaignId, params.periodCategory, params.periodId, true);
        if (rData.results) {
          const transformedResults = tranformResults(rData);
          const cardTransform = tranformLessonCard(lessonCard, context);
          if (transformedResults.type === BOOLEAN) {
            transformedResults.booleanLabel = cardTransform.booleanLabel;
            transformedResults.answer = {
              booleanAnswer: lessonCard.healthyResponse === 'positive',
            };
          } else if (transformedResults.type === SLIDER) {
            transformedResults.spectrumText = cardTransform.spectrumText;
            transformedResults.labelText = cardTransform.labelText;
            transformedResults.answer = { sliderAnswer: lessonCard.position };
          } else if (transformedResults.type === SINGLE_CHOICE) {
            transformedResults.gated = !!lessonCard.isGated;
            transformedResults.choices = cardTransform.choices;
            transformedResults.answer = {
              singleChoiceAnswer: 0,
            };
          } else if (transformedResults.type === MULTIPLE_CHOICE) {
            transformedResults.multipleChoices = cardTransform.choices;
            transformedResults.answer = {
              multipleChoiceAnswer: {
                answer1: false, answer2: false, answer3: false, answer4: false, answer5: false,
                answer6: false, answer7: false, answer8: false, answer9: false, answer10: false,
              },
            };
          } else if (transformedResults.type === WORDCLOUD) {
            transformedResults.type = FREEFORM_TEXT;
            transformedResults.answer = {
              freeFormTextAnswer: '',
            };
          } else if (transformedResults.type === WORKPLACE_COLOR_SPECTRUM) {
            transformedResults.regionAbbreviation = cardTransform.regionAbbreviation;
            transformedResults.correctAnswer = cardTransform.correctAnswer;
            transformedResults.answer = {
              workplaceColorSpectrumAnswer: cardTransform.correctAnswer,
            };
          }
          transformedResults.id = lessonCard.id;
          transformedResults.title = rData.title;
          transformedResults.fontColor = lessonCard.fontColor;
          transformedResults.description = lessonCard.question1;
          resultsReports.push(transformedResults);
        }
      }
      allQuizCardsReport.push({ lessonId: lessonData.id, lessonName: lessonData.title, reportData: resultsReports });
    }
    return { data: allQuizCardsReport };
  },
};

const mutations = {
  async saveAnswer(_, { input }, context) {
    const answerCard = await saveAnswer(get(context, 'user.id'), input, context);
    const lessonCardType = await fetchLessonCardTypeForAnswer(answerCard);
    return tranformAnswerCard(answerCard, lessonCardType);
  },
};

const resolvers = {
  LessonCard: {
    __resolveType: resolveLessonCardType,
  },
  ResultsData: {
    __resolveType: resolveResultsDataType,
  },
  InsightReports: {
    data: ({ data }) => {
      return data;
    },
  },
  Query: queries,
  Mutation: mutations,
};

module.exports = resolvers;
