/* eslint-disable no-param-reassign */
const { get } = require('lodash');
const { fetchLessons } = require('../data-access/lessonData');
const { fetchPrograms } = require('../data-access/programData');
const { fetchCampaignUserLessons, checkAssignmentCompletion, fetchAssignmentUserLessons,
  fetchOrCreateScormAssignmentUserLessons, isScormAccount,
  fetchScormAssignmentUserLessons, fetchUnpublishedUserLessons,
  fetchOrCreatePreviewAssignmentUserLessons, fetchOrCreateReviewerAssignments,
  fetchUserScormProgram, checkScormProgramCompletion,
  fetchOrCreateBaseAssignmentUserLessons, fetchBaseAssignmentUserLessons, closeBaseAssignment,
  programExists, lessonExists, checkAccountProgramsLessons }
  = require('../data-access/assignmentsData');
const { buildCampaignAssignmentList, buildBaseAssignmentList, buildSingleAssignment,
  buildNonCampaignAssignments, getResourceIdsFromUserLessons,
  lastViewedCardDetails } = require('../utils/assignmentUtils');
const { fetchUserLastViewedLessonCard } = require('../data-access/lessonCardsData');
const { BadRequestError } = require('../../services/utils/errors');
const { getUserOrSssoAdminAccountFromContext } = require('../../services/utils/lessonUtils');

// This psuedo enum is for code clarity where we are dealing with different client side
// scenarios the the assignment list resolver functions.
const ClientModes = Object.freeze({
  DEFAULT: 0,
  PREVIEW: 1,
  REVIEW: 2,
  SCORM: 3,
});

const queries = {
  async getAssignmentList(prev, { completed, scormProgramId, assignmentProgramId, assignmentLessonId, scormEnforceSequence }, context) {
    // Check to see if this user is a content review. The numberic value of the content reviewer role is 7
    const isContentReviewer = get(context, 'user.accounts[0].accountUsers.roleId') === 7;
    const scormAccount = await isScormAccount(context.user.accountId);

    // A preview assignment is a single assignment and must be requested by either programId, or lessonId.
    // This is the only case where we get the list as a single assignmentId. If this changes, we will need
    // to add an additional parameter to identify preview mode.
    const isPreview = assignmentProgramId || assignmentLessonId;

    let clientMode = ClientModes.DEFAULT;
    if (isContentReviewer) {
      clientMode = ClientModes.REVIEW;
    } else if (isPreview) {
      clientMode = ClientModes.PREVIEW;
    } else if (scormAccount) {
      clientMode = ClientModes.SCORM;
    }
    // get lastViewedLessonCards context.user.id
    const lasViewedLesson = await fetchUserLastViewedLessonCard(context);
    switch (clientMode) {
      case ClientModes.PREVIEW: {
        // 1. Get all active items for the user along with matching programs and lessons
        const previewUserLessons =
          // eslint-disable-next-line max-len
          await fetchUnpublishedUserLessons(context.user.id, context.user.accountId, assignmentProgramId, assignmentLessonId, 'preview');
        // 2. Get the associated programs and lessons for the active userLessons
        const programIds = getResourceIdsFromUserLessons(previewUserLessons, 'program');
        const lessonIds = getResourceIdsFromUserLessons(previewUserLessons, 'lesson');

        const programs = await fetchPrograms(context, programIds);
        const lessons = await fetchLessons(context, lessonIds);
        // for the preview case, there is a single assignment that is either a program or a microlesson.
        // there can't be more than a single program. if there isn't a program, it's a microlesson assignment
        const program = Object.keys(programs).length !== 0 ? programs[Object.keys(programs)[0]] : null;
        const microLesson = program ? null : lessons[Object.keys(lessons)[0]];
        // 3. Build the single assignment
        // eslint-disable-next-line max-len
        const assignment = buildSingleAssignment({ program, lessons, microLesson, items: previewUserLessons, assignmentType: 'preview' });
        return [assignment];
      }
      case ClientModes.REVIEW: {
        // 1. Get all active items for the user along with matching programs and lessons
        const reviewUserLessons = await fetchOrCreateReviewerAssignments(context);
        // 2. Get the associated programs and lessons for the active userLessons
        const programIds = getResourceIdsFromUserLessons(reviewUserLessons, 'program');
        const lessonIds = getResourceIdsFromUserLessons(reviewUserLessons, 'lesson');

        const programs = await fetchPrograms(context, programIds);
        const lessons = await fetchLessons(context, lessonIds);
        // 3. Build assignment list
        const assignmentList = await buildNonCampaignAssignments(context, programs, lessons, reviewUserLessons);
        return assignmentList;
      }
      case ClientModes.SCORM: {
        // eslint-disable-next-line max-len
        const scormUserLessons = await fetchScormAssignmentUserLessons(context.user.id, context.user.accountId, scormProgramId);
        // 2. Get the associated programs and lessons for the active userLessons
        const programIds = getResourceIdsFromUserLessons(scormUserLessons, 'program');
        const lessonIds = getResourceIdsFromUserLessons(scormUserLessons, 'lesson');

        const programs = await fetchPrograms(context, programIds);
        const lessons = await fetchLessons(context, lessonIds);
        // for the scorm case, there is a single assignment that is either a program or a microlesson.
        // there can't be more than a single program. if there isn't a program, it's a microlesson assignment
        const program = Object.keys(programs).length !== 0 ? programs[Object.keys(programs)[0]] : null;
        const microLesson = program ? null : lessons[Object.keys(lessons)[0]];
        // 3. Build the single assignment
        // eslint-disable-next-line max-len
        const assignment = buildSingleAssignment({ program, lessons, microLesson, items: scormUserLessons, assignmentType: 'scorm', scormEnforceSequence });
        return [assignment];
      }
      default: {
        // 1. Get all active userLessons for campaigns
        const campaignUserLessons = await fetchCampaignUserLessons(context.user.id, completed);
        // 2. Get the associated programs and lessons for the active userLessons
        const campaignProgramIds = getResourceIdsFromUserLessons(campaignUserLessons, 'program');
        const campaignLessonIds = getResourceIdsFromUserLessons(campaignUserLessons, 'lesson');

        const campaignPrograms = await fetchPrograms(context, campaignProgramIds);
        const campaignLessons = await fetchLessons(context, campaignLessonIds);
        const campaignAssignmentList = await buildCampaignAssignmentList({ programs: campaignPrograms,
          lessons: campaignLessons,
          items: campaignUserLessons,
          completed });

        // 1. Get all active userLessons related to baseAssignments
        const baseAssignmentUserLessons = await fetchBaseAssignmentUserLessons(context.user.id, completed);
        // 2. Get the associated programs and lessons for the active userLessons
        const baseAssignmentProgramIds = getResourceIdsFromUserLessons(baseAssignmentUserLessons, 'program');
        const baseAssignmentLessonIds = getResourceIdsFromUserLessons(baseAssignmentUserLessons, 'lesson');

        const baseAssignmentPrograms = await fetchPrograms(context, baseAssignmentProgramIds);
        const baseAssignmentLessons = await fetchLessons(context, baseAssignmentLessonIds);
        const baseAssignmentList = buildBaseAssignmentList({ programs: baseAssignmentPrograms,
          lessons: baseAssignmentLessons,
          items: baseAssignmentUserLessons,
          completed });

        let allAssignmentList = [...campaignAssignmentList, ...baseAssignmentList];
        allAssignmentList = await lastViewedCardDetails(allAssignmentList, lasViewedLesson);
        return allAssignmentList;
      }
    }
  },
  // eslint-disable-next-line max-len
  async checkAssignmentCompletion(_, { assignmentUserLessonId, lessonId, enrollmentId, scormProgramId, assignmentProgramId, assignmentLessonId, scormEnforceSequence }, context) {
    // 1. First check that the assignment (userLesson) is completed. This function also propagates completion to program/campaign.
    // The progagation means that when we pull the assignments later in the function (step 2), they will all be updated.
    await checkAssignmentCompletion(context, parseInt(lessonId), parseInt(enrollmentId));
    // 2. Get the userLessons for this assignment
    // Check to see if this user is a content review. The numberic value of the content reviewer role is 7
    const isContentReviewer = get(context, 'user.accounts[0].accountUsers.roleId') === 7;
    const scormAccount = await isScormAccount(context.user.accountId);
    // A preview assignment is a single assignment and must be requested by either programId, or lessonId.
    // This is the only case where we get the list as a single assignmentId. If this changes, we will need
    // to add an additional parameter to identify preview mode.
    const isPreview = assignmentProgramId || assignmentLessonId;

    let clientMode = ClientModes.DEFAULT;
    if (isContentReviewer) {
      clientMode = ClientModes.REVIEW;
    } else if (isPreview) {
      clientMode = ClientModes.PREVIEW;
    } else if (scormAccount) {
      clientMode = ClientModes.SCORM;
    }

    switch (clientMode) {
      case ClientModes.PREVIEW: {
        // 1. Get all active items for the user along with matching programs and lessons
        const previewUserLessons =
          // eslint-disable-next-line max-len
          await fetchUnpublishedUserLessons(context.user.id, context.user.accountId, assignmentProgramId, assignmentLessonId, 'preview');
        // 2. Get the associated programs and lessons for the active userLessons
        const programIds = getResourceIdsFromUserLessons(previewUserLessons, 'program');
        const lessonIds = getResourceIdsFromUserLessons(previewUserLessons, 'lesson');

        const programs = await fetchPrograms(context, programIds);
        const lessons = await fetchLessons(context, lessonIds);
        // for the preview case, there is a single assignment that is either a program or a microlesson.
        // there can't be more than a single program. if there isn't a program, it's a microlesson assignment
        const program = Object.keys(programs).length !== 0 ? programs[Object.keys(programs)[0]] : null;
        const microLesson = program ? null : lessons[Object.keys(lessons)[0]];
        // 3. Build the single assignment
        // eslint-disable-next-line max-len
        const assignment = buildSingleAssignment({ program, lessons, microLesson, items: previewUserLessons, assignmentType: 'preview' });
        return assignment;
      }
      case ClientModes.REVIEW: {
        const reviewUserLessons =
          // eslint-disable-next-line max-len
          await fetchUnpublishedUserLessons(context.user.id, context.user.accountId, assignmentProgramId, assignmentLessonId, 'review');
        // 2. Get the associated programs and lessons for the active userLessons
        const programIds = getResourceIdsFromUserLessons(reviewUserLessons, 'program');
        const lessonIds = getResourceIdsFromUserLessons(reviewUserLessons, 'lesson');

        const programs = await fetchPrograms(context, programIds);
        const lessons = await fetchLessons(context, lessonIds);
        // 3. Build assignment list
        const assignmentList = await buildNonCampaignAssignments(context, programs, lessons, reviewUserLessons);
        return assignmentList[0];
      }
      case ClientModes.SCORM: {
        // eslint-disable-next-line max-len
        const scormUserLessons = await fetchScormAssignmentUserLessons(context.user.id, context.user.accountId, scormProgramId);
        // 2. Get the associated programs and lessons for the active userLessons
        const programIds = getResourceIdsFromUserLessons(scormUserLessons, 'program');
        const lessonIds = getResourceIdsFromUserLessons(scormUserLessons, 'lesson');
        const programs = await fetchPrograms(context, programIds);
        const lessons = await fetchLessons(context, lessonIds);
        // for the scorm case, there is a single assignment that is either a program or a microlesson.
        // there can't be more than a single program. if there isn't a program, it's a microlesson assignment
        const program = Object.keys(programs).length !== 0 ? programs[Object.keys(programs)[0]] : null;
        const microLesson = program ? null : lessons[Object.keys(lessons)[0]];
        // 3. Get the scormProgram and check for completion (and set dateCompleted if all lessons are complete and time is met)
        const scormProgram = scormProgramId && await fetchUserScormProgram(scormProgramId, context.user.id, 'program');
        if (program && scormProgram && !scormProgram.completionDate) {
          await checkScormProgramCompletion(program, scormProgram, scormUserLessons, context.user.accountId);
        }
        // 4. Build the single assignment
        // eslint-disable-next-line max-len
        const assignment = buildSingleAssignment({ program, lessons, microLesson, items: scormUserLessons, assignmentType: 'scorm', scormEnforceSequence });
        return assignment;
      }
      default: {
        const userLessons = await fetchAssignmentUserLessons(context.user.id, parseInt(assignmentUserLessonId));
        const primaryAssignmentUserLesson =
          userLessons.find(userLesson => userLesson.id === parseInt(assignmentUserLessonId));

        // 3. Get the associated programs and lessons for the userLessons
        const programIds = getResourceIdsFromUserLessons(userLessons, 'program');
        const lessonIds = getResourceIdsFromUserLessons(userLessons, 'lesson');

        const programs = await fetchPrograms(context, programIds);
        const lessons = await fetchLessons(context, lessonIds);
        // 4. Build the assignment list from items. In this case, it's a single assignment.
        if (primaryAssignmentUserLesson.assignmentId) {
          const assignmentList = await buildCampaignAssignmentList({ programs, lessons, items: userLessons });
          return get(assignmentList, '[0]', null);
        } else if (primaryAssignmentUserLesson.baseAssignmentId) {
          const assignmentList = buildBaseAssignmentList({ programs, lessons, items: userLessons });
          return get(assignmentList, '[0]', null);
        }
        return null;
      }
    }
  },
};

const mutations = {
  async createScormAssignment(_, { programId, lessonId }, context) {
    // 1. First fetch or create the userLessons for the scorm assignment
    const userLessons = await fetchOrCreateScormAssignmentUserLessons(context, parseInt(programId), parseInt(lessonId));

    // 2. Get the associated programs and lessons for the active userLessons
    const programIds = getResourceIdsFromUserLessons(userLessons, 'program');
    const lessonIds = getResourceIdsFromUserLessons(userLessons, 'lesson');

    const programs = await fetchPrograms(context, programIds);
    const lessons = await fetchLessons(context, lessonIds);

    // for the case of scorm, there is a single assignment that is either a program or a microlesson.
    // there can't be more than a single program.
    // if there isn't a program, it's a microlesson assignment
    const program = Object.keys(programs).length !== 0 ? programs[Object.keys(programs)[0]] : null;
    const microLesson = program ? null : lessons[Object.keys(lessons)[0]];
    // 3. Build the single scorm assignment
    // eslint-disable-next-line max-len
    const assignment = buildSingleAssignment({ program, lessons, microLesson, items: userLessons, assignmentType: 'scorm' });
    return [assignment];
  },
  async createPreviewAssignment(_, { programId, lessonId }, context) {
    // 1. First fetch or create the userLessons for the preview assignment
    const userLessons =
      await fetchOrCreatePreviewAssignmentUserLessons(context, parseInt(programId), parseInt(lessonId));

    // 2. Get the associated programs and lessons for the active userLessons
    const programIds = getResourceIdsFromUserLessons(userLessons, 'program');
    const lessonIds = getResourceIdsFromUserLessons(userLessons, 'lesson');

    const programs = await fetchPrograms(context, programIds);
    const lessons = await fetchLessons(context, lessonIds);

    // for the preview case, there is a single assignment that is either a program or a microlesson.
    // there can't be more than a single program.
    // if there isn't a program, it's a microlesson assignment
    const program = Object.keys(programs).length !== 0 ? programs[Object.keys(programs)[0]] : null;
    const microLesson = program ? null : lessons[Object.keys(lessons)[0]];
    // 3. Build the single scorm assignment
    // eslint-disable-next-line max-len
    const assignment = buildSingleAssignment({ program, lessons, microLesson, items: userLessons, assignmentType: 'preview' });
    return [assignment];
  },
  async createSharedAssignment(_, { programId, lessonId }, context) {
    const user = get(context, 'user');
    const account = await getUserOrSssoAdminAccountFromContext(context);
    // shared assignments are not allowed for sso admins
    if (user && user.adminAccountId) {
      return null;
    }
    if (account) {
      const type = programId ? "programs" : "lessons";
      const contectId = programId ? programId : lessonId;
      await checkAccountProgramsLessons(
        account.id,
        contectId,
        type
      );
    }
    // 1. Get all active campaign user lessons for this user to ensure we don't create
    // a shared assignment if it already is assigned to the user in a campaign.
    const campaignUserLessons = await fetchCampaignUserLessons(context.user.id, false);
    for (const userLesson of campaignUserLessons) {
      if (programId && userLesson.type === 'program' &&
        userLesson.resourceId === parseInt(programId) && userLesson.status !== 'completed') {
        return [];
      } else if (lessonId && userLesson.type === 'lesson' &&
        userLesson.resourceId === parseInt(lessonId) && userLesson.status !== 'completed') {
        return [];
      }
    }

    // 2. Get all active userLessons related to baseAssignments
    const baseAssignmentUserLessons = await fetchBaseAssignmentUserLessons(context.user.id, false);
    for (const userLesson of baseAssignmentUserLessons) {
      if (programId && userLesson.type === 'program' &&
        userLesson.resourceId === parseInt(programId) && userLesson.status !== 'completed') {
        return [];
      } else if (lessonId && userLesson.type === 'lesson' &&
        userLesson.resourceId === parseInt(lessonId) && userLesson.status !== 'completed') {
        return [];
      }
    }
    // 3. Fetch or create the userLessons for the shared assignment
    const userLessons =
      await fetchOrCreateBaseAssignmentUserLessons(context, parseInt(programId), parseInt(lessonId), 'shared');

    // 4. Get the associated programs and lessons for the active userLessons
    const programIds = getResourceIdsFromUserLessons(userLessons, 'program');
    const lessonIds = getResourceIdsFromUserLessons(userLessons, 'lesson');

    const programs = await fetchPrograms(context, programIds);
    const lessons = await fetchLessons(context, lessonIds);

    // for the preview case, there is a single assignment that is either a program or a microlesson.
    // there can't be more than a single program.
    // if there isn't a program, it's a microlesson assignment
    const program = Object.keys(programs).length !== 0 ? programs[Object.keys(programs)[0]] : null;
    const microLesson = program ? null : lessons[Object.keys(lessons)[0]];
    // 5. Build the single scorm assignment
    // eslint-disable-next-line max-len
    const assignment = buildSingleAssignment({ program, lessons, microLesson, items: userLessons, assignmentType: 'shared' });
    return [assignment];
  },
  async closeSharedAssignment(_, { assignmentUserLessonId }, context) {
    const userId = get(context, 'user.id');
    const removalReason = 'removedByLearner';
    const removedAt = Date.now();
    closeBaseAssignment(userId, assignmentUserLessonId, removalReason, removedAt);
    return true;
  },
  async launchExternalAssignment(_, { programId, lessonId }, context) {
    const user = get(context, 'user');
    // shared assignments are not allowed for sso admins
    if (user && user.adminAccountId) {
      return null;
    }

    if (programId) {
      const programInstance = await programExists(programId);
      if (!programInstance) {
        throw new BadRequestError(context.i18n.t('programs.program_load_Error', { id: programId }));
      }
    }
    if (lessonId) {
      const lessonInstance = await lessonExists(lessonId);
      if (!lessonInstance) {
        throw new BadRequestError(context.i18n.t('lessons.lesson_load_Error', { id: lessonId }));
      }
    }

    // First get all active campaign user lessons for this user to ensure we don't create
    // a new assignment if it already is assigned to the user in a campaign.
    const campaignUserLessons = await fetchCampaignUserLessons(context.user.id, false);
    for (const userLesson of campaignUserLessons) {
      if (programId && userLesson.type === 'program' &&
        userLesson.resourceId === parseInt(programId) && userLesson.status !== 'completed') {
        // Find all lessons associated with the programId
        const associatedLessons = campaignUserLessons.filter(lesson =>
          lesson.type === 'lessons' && lesson.groupAssignment.program.id === userLesson.resourceId)?.sort((a, b) => {
            if (a.status === 'inProgress' && b.status !== 'inProgress') {
              return -1;
            }
            if (a.status !== 'inProgress' && b.status === 'inProgress') {
              return 1;
            }
            return 0;
          });

        return [{ id: userLesson.id, programId: userLesson.resourceId, lessonId: associatedLessons[0]?.resourceId, assignmentType: 'external' }];
      } else if (lessonId && userLesson.type === 'lesson' &&
        userLesson.resourceId === parseInt(lessonId) && userLesson.status !== 'completed') {
        return [{ id: userLesson.id, lessonId: userLesson.resourceId, assignmentType: 'external' }];
      }
    }

    // No campaign lessons if were here, so now fetch or create a base assignment instead
    const userLessons =
      await fetchOrCreateBaseAssignmentUserLessons(context, parseInt(programId), parseInt(lessonId), 'external');

    const userLessonList = userLessons?.filter(userLesson => userLesson?.type === 'lesson')
      .sort((a, b) => {
        if (a.status === 'inProgress' && b.status !== 'inProgress') {
          return -1;
        }
        if (a.status !== 'inProgress' && b.status === 'inProgress') {
          return 1;
        }
        return 0;
      });

    // If this is a program, return our first lesson, otherwise, it's the lessonId we received in the request
    const returnLessonId = programId ? userLessonList[0]?.resourceId : lessonId;
    const returnProgramId = programId ? userLessons[0]?.resourceId : null;

    return [{ id: userLessons[0]?.id, programId: returnProgramId, lessonId: returnLessonId, assignmentType: 'external' }];
  },
};

const resolvers = {
  AssignmentContent: {
    __resolveType: ({ lessons }) => {
      return lessons ? 'ProgramItem' : 'LessonItem';
    },
  },
  Query: queries,
  Mutation: mutations,
};

module.exports = resolvers;
