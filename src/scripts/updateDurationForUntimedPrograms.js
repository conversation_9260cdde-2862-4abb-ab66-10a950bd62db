const App = require('../app');
const logger = require('../logger');
const db = require('../db');

const { Op } = db.Sequelize;

const log = true;
const logMsg = (msg, override = false) => {
  if (log || override) { logger.info(msg); }
};

const getUntimedPrograms = async () => {
  const programs = await db.programs.findAll({
    attributes: ['id'],
    where: { isTimed: false },
    raw: true,
  });
  return programs.map(program => program.id);
};

const roundToNearest = (num) => {
  if (num < 5 && num > 0) return 5; // Ensure the minimum rounded value is 5
  const nearest5 = Math.round(num / 5) * 5;
  const nearest10 = Math.round(num / 10) * 10;
  // Decide whether to round to 5 or 10 based on proximity
  return Math.abs(num - nearest5) <= Math.abs(num - nearest10) ? nearest5 : nearest10;
};

const updateProgramDuration = async (id) => {
  const lessons = await db.lessonPrograms.findAll({
    attributes: [],
    where: {
      programId: id, 
      dateRemoved: { [Op.eq]: null } 
    },
    include: [{
      model: db.lessons,
      attributes: ['requiredMinutes']
    }],
    raw: true,
  });
  
  // Extract and sum `requiredMinutes`
  const totalMinutes = lessons.reduce((sum, lesson) => {
    const minutes = lesson['lesson.requiredMinutes']; 
    return sum + (minutes ? Number(minutes) : 0); 
  }, 0);
  const roundedMinutes = roundToNearest(totalMinutes);
  logMsg(`Total required minutes (rounded): ${roundedMinutes} for programId: ${id}`);
  await db.programs.update(
    { duration: roundedMinutes }, 
    { where: { id } } 
  );
};

const updateProgramsDuration = async () => {
  const programIds = await getUntimedPrograms();
  for (const programId of programIds) {
    await updateProgramDuration(programId);
  }

};

(async () => {
  const hootApp = new App();
  const start = new Date();

  try {
    await hootApp.init();
    await updateProgramsDuration();

    logMsg(`Elapsed time: ${new Date() - start}ms`);
    logMsg('Done');
    process.exit(0);
  } catch (err) {
    logger.error(`Script failed: ${err.message}`);
    process.exit(1);
  }
})();
