const { v4: uuidv4 } = require('uuid');

module.exports = function (sequelize, DataTypes) {
  const xapiPrograms = sequelize.define('xapiPrograms', {
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    registrationId: {
      type: DataTypes.UUID,
      allowNull: false,
      defaultValue: () => uuidv4(),
    },
    resourceId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    model: {
      type: DataTypes.ENUM('lesson', 'program'),
      defaultValue: 'program',
      allowNull: false,
    },
    suspendData: {
      type: DataTypes.TEXT, // JSON string for bookmark data
      allowNull: true,
    },
    completionDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    // xAPI specific fields
    actorMbox: {
      type: DataTypes.STRING, // actor.mbox from xAPI
      allowNull: true,
    },
    actorName: {
      type: DataTypes.STRING, // actor.name from xAPI
      allowNull: true,
    },
  }, {
    hooks: {
      beforeCount(options) {
        options.raw = true;
      },
      afterCreate: async (xapiProgram) => {
        // Similar to SCORM - create content items for tracking
        await createXapiContentItems(xapiProgram, sequelize);
      },
    },
    timestamps: true,
  });

  xapiPrograms.associate = function (models) {
    xapiPrograms.belongsTo(models.users);
    xapiPrograms.belongsTo(models.programs, { foreignKey: 'resourceId' });
    xapiPrograms.belongsTo(models.lessons, { foreignKey: 'resourceId' });
  };

  return xapiPrograms;
};

async function createXapiContentItems(xapiProgram, sequelize) {
  // Similar to SCORM content items creation
  if (xapiProgram.model === 'lesson' || xapiProgram.model === 'program') {
    const resource = await sequelize.models[`${xapiProgram.model}s`].findOne({
      where: { id: xapiProgram.resourceId },
    });

    const account = await sequelize.models.accountUsers.findOne({
      attributes: ['accountId'],
      where: { userId: xapiProgram.userId },
    });

    let title = '';
    if (xapiProgram.model === 'lesson') {
      const accLessonObj = await sequelize.models.accountLessons.findOne({
        where: { accountId: account.accountId, lessonId: xapiProgram.resourceId },
      });
      title = accLessonObj ? accLessonObj.title : resource?.title ?? '';
    } else {
      const accProgramObj = await sequelize.models.accountPrograms.findOne({
        where: { accountId: account.accountId, programId: xapiProgram.resourceId },
      });
      title = accProgramObj ? accProgramObj.name : resource?.name ?? '';
    }

    const obj = {
      resourceName: title,
      resourceId: xapiProgram.resourceId,
      resourceType: xapiProgram.model,
      accountId: account.accountId,
      isXapi: true, // Flag to distinguish from SCORM
      lifecycle: resource?.lifecycle === 'active' ? 'publish' : resource?.lifecycle,
    };

    await sequelize.models.accountContentItems.findOrCreate({
      where: { 
        resourceId: xapiProgram.resourceId, 
        resourceType: xapiProgram.model, 
        accountId: account.accountId,
        isXapi: true 
      },
      defaults: obj 
    });
  }
}
