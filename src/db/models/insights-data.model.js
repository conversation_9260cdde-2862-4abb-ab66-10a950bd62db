module.exports = (sequelize, DataTypes) => {
  const insightsData = sequelize.define('insightsData', {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
      allowNull: false,
    },
    myOrgOrGlobal: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    accountId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    lessonCardId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    cardType: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    lessonCardTitle: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    question1: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    periodCategory: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    period: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    periodId: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    periodSubCategory: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    val1: { type: DataTypes.INTEGER, allowNull: true },
    val1_pct: { type: DataTypes.DOUBLE, allowNull: true },
    val2: { type: DataTypes.INTEGER, allowNull: true },
    val2_pct: { type: DataTypes.DOUBLE, allowNull: true },
    val3: { type: DataTypes.INTEGER, allowNull: true },
    val3_pct: { type: DataTypes.DOUBLE, allowNull: true },
    val4: { type: DataTypes.INTEGER, allowNull: true },
    val4_pct: { type: DataTypes.DOUBLE, allowNull: true },
    val5: { type: DataTypes.INTEGER, allowNull: true },
    val5_pct: { type: DataTypes.DOUBLE, allowNull: true },
    val6: { type: DataTypes.INTEGER, allowNull: true },
    val6_pct: { type: DataTypes.DOUBLE, allowNull: true },
    val7: { type: DataTypes.INTEGER, allowNull: true },
    val7_pct: { type: DataTypes.DOUBLE, allowNull: true },
    val8: { type: DataTypes.INTEGER, allowNull: true },
    val8_pct: { type: DataTypes.DOUBLE, allowNull: true },
    val9: { type: DataTypes.INTEGER, allowNull: true },
    val9_pct: { type: DataTypes.DOUBLE, allowNull: true },
    val10: { type: DataTypes.INTEGER, allowNull: true },
    val10_pct: { type: DataTypes.DOUBLE, allowNull: true },
    val11: { type: DataTypes.INTEGER, allowNull: true },
    val11_pct: { type: DataTypes.DOUBLE, allowNull: true },
    total: { type: DataTypes.INTEGER, allowNull: true },
    wordCounts: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    timestamps: true,
    paranoid: true,
  });

  insightsData.associate = function(models) {
    insightsData.belongsTo(models.lessonCards);
  };

  return insightsData;
};
