const { v4: uuidv4 } = require('uuid');

const generateKey = () => uuidv4().replace(/-/g, '');

module.exports = function (sequelize, DataTypes) {
  const integrations = sequelize.define('integrations', {
    accountId: {
      allowNull: false,
      type: DataTypes.INTEGER,
    },
    cert: {
      type: DataTypes.TEXT,
    },
    entryPoint: {
      type: DataTypes.STRING,
    },
    clientId: {
      type: DataTypes.STRING,
    },
    tenantAlias: {
      type: DataTypes.STRING,
    },
    integrationType: {
      type: DataTypes.ENUM('okt sso', 'scorm', 'scorm20043rd', 'scorm20044th', 'xapi', 'tincan', 'sftp', 'authentication', 'azure sso', 'google sso', 'onelogin sso', 'centrify sso', 'aragorn', 'workdayCCL'),
      allowNull: false,
    },
    integrationKey: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    ssoLogoutUrl: {
      type: DataTypes.STRING,
    },
  }, {
    hooks: {
      beforeCount(options) {
        options.raw = true;
      },
      beforeValidate(integration, options) { // eslint-disable-line no-unused-vars
        if (!integration.integrationKey) {
          integration.setDataValue('integrationKey', generateKey());
        }
      },
    },
    timestamps: true,
    paranoid: true,
  });

  integrations.associate = function (models) {
    integrations.belongsTo(models.accounts);
  };

  return integrations;
};
