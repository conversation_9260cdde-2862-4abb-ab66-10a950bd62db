/**
 * Utility to generate a large number of resourceTopic objects
 */
const rand = require('./util/rand');

/**
 * Create topic-resource entriese
 * @param {Array} topicIds id of topics related to a resource
 * @param {Number} resourceId id of resource to create events for
 * @returns {Array}
 */
function createTopics(topicIds, resourceId) {
  return topicIds.map((topicId, idx) => ({
    resourceId,
    topicId,
    order: idx + 1,
    createdAt: new Date(),
    updatedAt: new Date(),
  }));
}

module.exports = function (topicIds, numberOfResources) {
  const resourceTopics = [];
  for (let i = 1; i < numberOfResources; i++) {
    const numberOfTopics = rand([1, 2, 3, 4]);
    let topics = rand(topicIds, numberOfTopics);
    topics = Array.isArray(topics) ? topics : [topics];
    resourceTopics.push(...createTopics(topics, i));
  }
  return resourceTopics;
};
