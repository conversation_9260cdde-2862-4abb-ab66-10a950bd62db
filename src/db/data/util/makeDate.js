/**
 * Create a random new date that is some number of days offset from another date.
 * @param {Date | Number} base Date or timestamp to use as the reference.
 * @param {*} numDays maciumum number of days to offset new date from the reference.
 * @return {Date}
 */
module.exports = function makeDate(base, numDays) {
  const baseTimestamp = base instanceof Date ? base.getTime() : base;
  const days = Math.floor(Math.random() * numDays);
  const offset = days * 24 * 60 * 60 * 1000;
  const d = new Date();
  d.setTime(baseTimestamp + offset);
  return d;
};
