/**
 * Get `howMany` random elements from an array.
 * @param {Array} arr array to pick items from
 * @param {Number} howMany number of items
 * @return {Array}
 */
module.exports = function rand(arr, howMany = 1) {
  // Pick a random index in the array bounds
  if (howMany === 1) {
    const rng = Math.floor(Math.random() * arr.length);
    return arr[rng];
  }
  // Pick random indices unti we have enough.
  const picks = [];
  // Ensure there are no duplicates by maintaining an array of the values left.
  const itemsLeft = arr.slice();
  while (picks.length < howMany && itemsLeft.length > 0) {
    const pick = Math.floor(Math.random() * itemsLeft.length);
    if (picks.indexOf(pick) === -1) {
      picks.push(pick);
      itemsLeft.splice(pick, 1);
    }
  }
  return arr.filter((item, i) => picks.indexOf(i) !== -1);
};
