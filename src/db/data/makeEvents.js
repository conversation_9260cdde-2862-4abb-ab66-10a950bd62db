/**
 * Utility to generate a large number of event objects
 */
const rand = require('./util/rand');
const makeDate = require('./util/makeDate');

/**
 * Create a properly formatted event object
 * @param {Number} userId  id of user performing the event
 * @param {Number} resourceId id of resource user is acting on
 * @param {String} type type of event
 * @param {Date} date date of event
 * @returns {Object}
 */
function makeEvent(userId, trackableId, type, date, action = true) {
  return {
    userId,
    trackableId,
    type,
    date,
    action,
  };
}

/**
 * Create viewed, shared, saved and helpful events for a resource
 * @param {Number} trackableId id of resource to create events for
 * @returns {Function}
 */
function createEvents(trackableId) {
  // Return a function that accepts an array to modify and a userId to generate
  // events for
  return (events, userId) => {
    // TODO: Maybe we generate more than one view per user?
    const today = new Date();
    const shouldView = rand([true, false]);
    let shouldSave = false;
    let shouldShare = false;
    let shouldHelpful = false;
    if (shouldView) {
      // Create a view event 30ish days before today
      const viewDate = makeDate(today, -30);
      events.push(makeEvent(userId, trackableId, 'view', viewDate));

      // Determine if user should perform other actions
      // Other actions' date will be 5ish days from the view date
      // There's a rare chance we also add an event to undo a previous event
      shouldSave = rand([true, false, true]);
      shouldShare = rand([true, false, true]);
      shouldHelpful = rand([true, false, true]);

      if (shouldSave) {
        const saveDate = makeDate(viewDate, 2);
        events.push(makeEvent(userId, trackableId, 'save', saveDate));
        if (rand([false, false, true, false, false])) {
          events.push(makeEvent(userId, trackableId, 'save', makeDate(saveDate, 5), false));
        }
      }
      if (shouldShare) {
        const shareDate = makeDate(viewDate, 2);
        events.push(makeEvent(userId, trackableId, 'share', shareDate));
        if (rand([false, false, true, false, false])) {
          events.push(makeEvent(userId, trackableId, 'share', makeDate(shareDate, 5), false));
        }
      }
      if (shouldHelpful) {
        const helpfulDate = makeDate(viewDate, 2);
        events.push(makeEvent(userId, trackableId, 'helpful', makeDate(helpfulDate, 2)));
        if (rand([false, false, true, false, false])) {
          events.push(makeEvent(userId, trackableId, 'helpful', makeDate(helpfulDate, 5), false));
        }
      }
    }
    return events;
  };
}

module.exports = function (userIds, numberOfResources) {
  const events = [];
  for (let i = 1; i < numberOfResources; i++) {
    events.push(...rand(userIds, 4).reduce(createEvents(i), []));
  }
  return events;
};
