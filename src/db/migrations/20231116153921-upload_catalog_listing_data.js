const csvToJson = require('csvtojson');
const path = require('path');
const db = require('..');
const logger = require('../../logger');

const CatalogItems = db.catalogItems;
const States = db.states;

const readFileIntoRows = (filename) => {
  const fullPathcatalogItemsData = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathcatalogItemsData);
};

const getAllStates = async () => {
  const states = await States.findAll({ attributes: ['id', 'stateName', 'stateCode'] });
  return states;
};

const findStateId = (stateCode, states) => {
  // eslint-disable-next-line no-param-reassign
  stateCode = stateCode === '' ? 'ALL' : stateCode;
  return states.find(state => state.stateCode === stateCode);
};

const uploadCatalogItems = async () => {
  const catalogItemsData = await readFileIntoRows('Catalog-Items-Data.csv');
  const stateList = await getAllStates();
  for await (const row of catalogItemsData) {
    if (row.catalogId && row.catalogId !== '') {
      const stateId = findStateId(row.state, stateList);
      const catalog = {
        id: row.catalogId,
        listingId: row.listingId === '' ? null : row.listingId,
        contentType: row.contentType,
        instructionalType: row.instructionalType,
        code: row.code,
        stateId: stateId && stateId.id || null,
        audience: row.audience,
        duration: !row.duration || row.duration === '' ? null : `${row.duration}`,
        edition: !row.edition || row.edition === '' ? null : `${row.edition}`,
        part: !row.part || row.part === '' ? null : `'${row.part}'`,
        frequency: !row.frequency || row.frequency === '' ? null : `'${row.frequency}'`,
        recommendType: row.recommend,
        isClientSpecific: row.isClientSpecific,
      };
      if (catalog.audience === '') {
        catalog.audience = 'all';
      }
      const query = `
        INSERT INTO catalogItems 
        (id,listingId,contentType,instructionalType,code,stateId,audience,duration,edition,part,frequency,recommendType,isClientSpecific,createdAt,updatedAt)
        VALUES (${catalog.id},${catalog.listingId},"${catalog.contentType}","${catalog.instructionalType}","${catalog.code}",${catalog.stateId},"${catalog.audience}",${catalog.duration},${catalog.edition},${catalog.part},${catalog.frequency},"${catalog.recommendType}",${catalog.isClientSpecific},now(), now())
      `;
      await db.sequelize.query(query);
      // await CatalogItems.upsert(catalog, { where: { id: catalog.id } });
    }
  }
};
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // truncate the catalogItems and update catalogId to null for all programs/lessons
      await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 0');
      await queryInterface.sequelize.query('TRUNCATE TABLE catalogItems');
      await queryInterface.sequelize.query('UPDATE programs SET `catalogId` = NULL');
      await queryInterface.sequelize.query('UPDATE lessons SET `catalogId` = NULL');
      await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 1');
      await uploadCatalogItems();
    } catch (err) {
      logger.error(err);
    }
  },
  async down(queryInterface, Sequelize) {
    // no going back
  },
};
