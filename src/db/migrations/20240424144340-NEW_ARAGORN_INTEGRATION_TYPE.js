'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    return queryInterface.changeColumn('integrations', 'integrationType', {
      type: Sequelize.ENUM('okt sso', 'scorm', 'scorm20043rd', 'scorm20044th', 'sftp', 'authentication', 'azure sso', 'google sso', 'onelogin sso', 'centrify sso', 'aragorn'),
      allowNull: false,
    });
  },

  async down (queryInterface, Sequelize) {
    return queryInterface.changeColumn('integrations', 'integrationType', {
      type: Sequelize.ENUM('okt sso', 'scorm', 'scorm20043rd', 'scorm20044th', 'sftp', 'authentication', 'azure sso', 'google sso', 'onelogin sso', 'centrify sso'),
      allowNull: false,
    });
  }
};
