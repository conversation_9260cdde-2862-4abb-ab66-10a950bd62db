module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('concepts', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      priority: {
        allowNull: false,
        type: Sequelize.INTEGER,
      },
      catagory: {
        allowNull: false,
        type: Sequelize.STRING,
      },
      concept: {
        allowNull: false,
        type: Sequelize.STRING,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('concepts');
  },
};
