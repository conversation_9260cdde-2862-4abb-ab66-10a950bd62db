module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      UPDATE countries SET countryName = "International", countryCode = "intl", regionId = "18" WHERE id = "250";
      INSERT INTO countries (countryName, countryCode, regionId) VALUES ("All Countries", "all", "18");
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      UPDATE countries SET countryName = "Global", countryCode = "global", regionId = "18" WHERE (id = "250");
      DELETE FROM countries WHERE countryName = "All Countries";
    `);
  }
};
