module.exports = {
  up: (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.createTable('assessmentItemLessonCards', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      assessmentItemId: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      lessonCardId: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
    }));
    promises.push(queryInterface.createTable('assessmentItems', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      externalId: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
        unique: true,
      },
      assessmentType: {
        type: Sequelize.DataTypes.ENUM('Benchmark', 'Knowledge', 'SJT', 'Survey', 'Climate'),
        allowNull: true,
      },
      questionType: {
        type: Sequelize.DataTypes.ENUM(
          'quizBoolean', 'quizSlider', 'quizSingleChoice',
          'quizMultiChoice', 'quizFreeformText', 'quizColorSpectrum',
        ),
        allowNull: true,
      },
      questionText: {
        type: Sequelize.DataTypes.TEXT,
        allowNull: true,
      },
      auditQuestion: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: true,
      },
      response1: {
        type: Sequelize.DataTypes.STRING,
        allowNull: true,
      },
      response2: {
        type: Sequelize.DataTypes.STRING,
        allowNull: true,
      },
      response3: {
        type: Sequelize.DataTypes.STRING,
        allowNull: true,
      },
      response4: {
        type: Sequelize.DataTypes.STRING,
        allowNull: true,
      },
      healthyResponse: {
        type: Sequelize.DataTypes.STRING,
        allowNull: true,
      },
      indicator: {
        type: Sequelize.DataTypes.ENUM(
          'Accountability', 'Allyship', 'Authenticity & Belonging', 'Demographic Experience',
          'Curiosity & Empathy', 'Decision-Making Processes', 'Unconscious Bias/In-Group Out-Group',
          'Norms & Practices', 'Power Dynamics', 'Pre-Existing Mindsets', 'Social Aptitude', 'Trust',
          'Valuing Differences',
        ),
        allowNull: true,
      },
      competencyEthics: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      competencyInclusion: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      competencyRespect: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      itemExceptions: {
        type: Sequelize.DataTypes.STRING(512),
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
    }));
    return Promise.all(promises);
  },

  // eslint-disable-next-line no-unused-vars
  down: (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.dropTable('assessmentItemLessonCards'));
    promises.push(queryInterface.dropTable('assessmentItems'));
    return Promise.all(promises);
  },
};
