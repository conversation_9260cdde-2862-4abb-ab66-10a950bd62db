module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.addColumn(
      'users',
      'accountSSOToken',
      { type: Sequelize.STRING, defaultValue: null, allowNull: true, after: 'jettSSOToken' },
    );
  },
  // eslint-disable-next-line no-unused-vars
  down: async (queryInterface, Sequelize) => {
    return queryInterface.removeColumn('users', 'accountSSOToken');
  },
};
