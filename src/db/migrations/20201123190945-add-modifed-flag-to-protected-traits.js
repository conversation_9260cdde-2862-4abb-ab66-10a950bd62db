const selectQuery = 'SELECT id, abbr, language, traits FROM protectedTraitsDefaults';
const updateQuery =
  'UPDATE protectedTraits SET isModified = 1, updatedAt = now() WHERE abbr = ? AND language = ? AND traits != ?';
const doWork = async (queryInterface) => {
  const defaultTraits = await queryInterface.sequelize.query(selectQuery, {
    type: queryInterface.sequelize.QueryTypes.SELECT,
  });
  for (const defaultTrait of defaultTraits) {
    await queryInterface.sequelize.query(updateQuery, {
      type: queryInterface.sequelize.QueryTypes.UPDATE,
      replacements: [
        defaultTrait.abbr,
        defaultTrait.language,
        defaultTrait.traits,
      ],
    });
  }
};
module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.addColumn('protectedTraits', 'isModified', {
      type: Sequelize.DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    })
      .then(() => {
        return doWork(queryInterface);
      });
  },

  // eslint-disable-next-line no-unused-vars
  down: (queryInterface, Sequelize) => {
    return queryInterface.removeColumn('protectedTraits', 'isModified');
  },
};
