module.exports = {
  up: async(queryInterface, Sequelize) => {
    const remove_internalName = await queryInterface.removeColumn('accounts', 'internalName');
    const change_enableMachineLanguages_accounts = await queryInterface.changeColumn('accounts', 'enableMachineLanguages', {
      type: Sequelize.DataTypes.BOOLEAN,
      defaultValue: true,
    });
    // update for all accounts
    const set_enableMachineLanguages = await queryInterface.sequelize.query('UPDATE accounts SET enableMachineLanguages=1');
    const remove_colorTheme = await queryInterface.removeColumn('accounts', 'colorTheme');
    const remove_revenue = await queryInterface.removeColumn('accounts', 'revenue');

    const remove_location = await queryInterface.removeColumn('accounts', 'location');
    const remove_employees = await queryInterface.removeColumn('accounts', 'employees');
    const remove_sector = await queryInterface.removeColumn('accounts', 'sector');
    const remove_renewalDate = await queryInterface.removeColumn('accounts', 'renewalDate');
    const remove_companies = await queryInterface.dropTable('companies');
    
    return [
      remove_internalName, 
      change_enableMachineLanguages_accounts, 
      set_enableMachineLanguages,
      remove_colorTheme,
      remove_revenue, 
      remove_location, 
      remove_employees,
      remove_sector,
      remove_renewalDate,
      remove_companies
    ];
  },

  // eslint-disable-next-line no-unused-vars
  down: (queryInterface, Sequelize) => {
    // no going back
  },
};
