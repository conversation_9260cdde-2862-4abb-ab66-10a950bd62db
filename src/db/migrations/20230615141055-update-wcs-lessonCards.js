const db = require('../../db');

const Op = db.Sequelize.Op;

module.exports = {
  async up() {
    // get all current WCS cards that use the old regions 'CA', 'NY'
    const wcsLessonCards = await db.lessonCards.findAll({
      attributes: ['id'],
      where: { cardType: 'quizColorSpectrum', list1: { [Op.in]: ['CA', 'NY'] } },
    });
    const lessonCardIds = wcsLessonCards.map(lessonCardId => lessonCardId.id);
    // update regions for WCS cards
    if (lessonCardIds.length) {
      await db.lessonCards.update(
        { list1: 'US' },
        {
          where: { id: { [Op.in]: lessonCardIds } },
        },
      );
    }
  },

  down: () => {
    // no going back
  },
};
