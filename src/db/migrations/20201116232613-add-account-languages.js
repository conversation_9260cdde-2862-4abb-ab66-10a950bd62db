'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.createTable('accountLanguages', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      accountId: {
        type: Sequelize.DataTypes.INTEGER,
      },
      selectedLanguages: {
        type: Sequelize.DataTypes.STRING(512),
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
      },
    })
      .then(() => {
        return queryInterface.addIndex('accountLanguages', {
          fields: ['accountId'],
        });
      });
  },
  down: (queryInterface, Sequelize) => {
    return queryInterface.removeIndex('accountLanguages', ['accountId'])
      .then(() => {
        return queryInterface.dropTable('accountLanguages');
      });
  },
};
