/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // src: https://coderwall.com/p/gjyuwg/mysql-convert-encoding-to-utf8-without-garbled-data (#4)
    return queryInterface.sequelize.query(`
      UPDATE scormPrograms SET scormId = @txt WHERE char_length(scormId) = LENGTH(@txt := CONVERT(BINARY CONVERT(scormId USING latin1) USING utf8mb4));
      ALTER TABLE scormPrograms MODIFY scormId TEXT CHARACTER SET utf8mb4;
    `);
  },

  // Unexpected results if this is executed. Can't convert UTF8 unicode to latin1 ascii.
  async down(queryInterface, Sequelize) {
    return queryInterface.sequelize.query(`
      ALTER TABLE scormPrograms MODIFY scormId TEXT CHARACTER SET latin1;
    `);
  },
};
