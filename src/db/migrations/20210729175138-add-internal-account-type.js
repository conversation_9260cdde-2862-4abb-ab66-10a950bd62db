module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('accounts', 'accountType', {
      type: Sequelize.ENUM('admin', 'public', 'customer', 'internal'),
      allowNull: false,
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('accounts', 'accountType', {
      type: Sequelize.ENUM('admin', 'public', 'customer'),
      allowNull: false,
    });
  },
};