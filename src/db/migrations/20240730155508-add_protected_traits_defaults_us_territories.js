/* eslint-disable max-len */
// in VSCode type Option Z to toggle line breaks
const ptds = [
  {
    language: 'en',
    abbr: 'GU',
    traits: '<p><a href="https://dol.guam.gov/compliance/" rel="noopener noreferrer" target="_blank">Guam</a></p>',
    country: 'US',
    mapType: 'protected_traits',
  },
  {
    language: 'en',
    abbr: 'VI',
    traits: '<p><a href="https://compliance.vidol.gov/eo/" rel="noopener noreferrer" target="_blank">Employee Rights - Virgin Islands Department of Labor</a></p>',
    country: 'US',
    mapType: 'protected_traits',
  },
  {
    language: 'en',
    abbr: 'PR',
    traits: '<p><a href="https://www.trabajo.pr.gov/unidad_anti_discrimen.asp" rel="noopener noreferrer" target="_blank">Departamento del Trabajo y Recursos Humanos de Puerto Rico.</a></p>',
    country: 'US',
    mapType: 'protected_traits',
  },
  {
    language: 'en',
    abbr: 'AS',
    traits: '<p><a href="https://www.americansamoa.gov/" rel="noopener noreferrer" target="_blank">American Samoa</a></p>',
    country: 'US',
    mapType: 'protected_traits',
  },
  {
    language: 'en',
    abbr: 'MP',
    traits: '<p><a href="https://governor.gov.mp/" rel="noopener noreferrer" target="_blank">Northern Mariana Islands</a></p>',
    country: 'US',
    mapType: 'protected_traits',
  },

  {
    language: 'en',
    abbr: 'GU',
    traits: '<p>Federal wage. Federal hours.</p>',
    country: 'US',
    mapType: 'wage_and_hour',
  },
  {
    language: 'en',
    abbr: 'VI',
    traits: '<p>Higher minimum wage. Overtime for over 8 hours in a day or more than 40 hours in a week or more than 6 days a week. Additional break laws. <a href="https://www.vidol.gov/fair-employment/" rel="noopener noreferrer" target="_blank">Wage and Hour Laws In the U.S. Virgin Islands</a></p>',
    country: 'US',
    mapType: 'wage_and_hour',
  },
  {
    language: 'en',
    abbr: 'PR',
    traits: '<p>Higher minimum wage. Overtime for over 8 hours in a day or more than 40 hours in a week or more than 6 days a week. Additional break laws. <a href="https://www.trabajo.pr.gov/normas_del_trabajo.asp" rel="noopener noreferrer" target="_blank">Wage and Hour Laws In Puerto Rico</a></p',
    country: 'US',
    mapType: 'wage_and_hour',
  },
  {
    language: 'en',
    abbr: 'AS',
    traits: '<p>Federal wage. Federal hours.</p>',
    country: 'US',
    mapType: 'wage_and_hour',
  },
  {
    language: 'en',
    abbr: 'MP',
    traits: '<p>Federal wage. Federal hours.</p>',
    country: 'US',
    mapType: 'wage_and_hour',
  },
];

module.exports = {
  up: (queryInterface) => {
    const inserts = [];
    ptds.forEach(({ language, abbr, traits, country, mapType }) => {
      const trait = {
        language,
        abbr,
        traits,
        country,
        mapType,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      inserts.push(trait);
    });
    return queryInterface.bulkInsert('protectedTraitsDefaults', inserts);
  },

  down: async (queryInterface) => {
    const sql = `DELETE FROM protectedTraitsDefaults 
    WHERE abbr IN('GU', 'VI', 'PR', 'AS', 'MP')
    AND country = 'US'`;

    return queryInterface.sequelize.query(sql, {
      type: queryInterface.sequelize.QueryTypes.DELETE,
    });
  },
};
