const db = require('../../db');
const logger = require('../../logger');

const updateUsersManagerEmail = async (queryInterface) => {
  try {
    await queryInterface.sequelize.query(`
      UPDATE users u1
      JOIN users u2 ON u1.managerId = u2.id
      SET u1.managerEmail = u2.email
      WHERE u1.deletedAt IS NULL AND u1.managerId IS NOT NULL and u1.managerId != 0;
    `);
    return true;
  } catch (err) {
    logger.error(err.message);
    return false;
  }
}

module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // add new column, managerEmail
      await queryInterface.addColumn('users', 'managerEmail', {
        type: Sequelize.STRING,
      });

      // add index for managerEmail
      await queryInterface.addIndex('users', { fields: ['managerEmail'] });

      // update the new column managerEmail based on managerId
      const updateStatus = await updateUsersManagerEmail(queryInterface);

      // delete the managerId column after data updated successfully
      if (updateStatus) {
        await queryInterface.removeColumn('users', 'managerId');
      }

    } catch (err) {
      logger.error(err.message)
    }
  },

  async down() {
    // no going back
  }
};
