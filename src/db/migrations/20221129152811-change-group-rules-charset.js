/* eslint-disable max-len */
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // src: https://coderwall.com/p/gjyuwg/mysql-convert-encoding-to-utf8-without-garbled-data (#4)
    return queryInterface.sequelize.query(`
      UPDATE groupRules SET clause = @txt WHERE char_length(clause) = LENGTH(@txt := CONVERT(BINARY CONVERT(clause USING latin1) USING utf8mb4));
      ALTER TABLE groupRules MODIFY clause TEXT CHARACTER SET utf8mb4;
      UPDATE groupRuleValues SET value = @txt WHERE char_length(value) = LENGTH(@txt := CONVERT(BINARY CONVERT(value USING latin1) USING utf8mb4));
      ALTER TABLE groupRuleValues MODIFY value TEXT CHARACTER SET utf8mb4;
    `);
  },

  // Unexpected results if this is executed. Can't convert UTF8 unicode to latin1 ascii.
  async down(queryInterface, Sequelize) {
    return queryInterface.sequelize.query(`
      ALTER TABLE groupRules MODIFY clause TEXT CHARACTER SET latin1;
      ALTER TABLE groupRuleValues MODIFY value TEXT CHARACTER SET latin1;
    `);
  },
};
