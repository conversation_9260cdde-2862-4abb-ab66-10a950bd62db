module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('users', 'emtrainUserRole', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    return queryInterface.addColumn('users', 'emtrainAdminRole', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('users', 'emtrainUserRole');
    return queryInterface.removeColumn('users', 'emtrainAdminRole');
  },
};
