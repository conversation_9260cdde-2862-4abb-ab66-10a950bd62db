module.exports = {
  up: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.changeColumn('users', 'supervisor', {
      type: Sequelize.STRING,
      defaultValue: null,
    });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.changeColumn('users', 'supervisor', {
      type: Sequelize.STRING,
      defaultValue: 'No',
    });
  },
};
