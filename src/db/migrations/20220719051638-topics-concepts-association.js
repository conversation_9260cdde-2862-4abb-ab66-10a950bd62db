module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('topicConcepts', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      topicId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
      },
      socialCapitalConceptId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex(
      'topicConcepts',
      ['topicId', 'socialCapitalConceptId'],
      { unique: true },
    );
  },

  async down(queryInterface) {
    return queryInterface.dropTable('topicConcepts');
  },
};
