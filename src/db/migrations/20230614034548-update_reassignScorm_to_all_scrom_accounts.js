const db = require('..');

const Op = db.Sequelize.Op;


module.exports = {
  async up() {
    // get all scorm accountIds
    const scromAccounts = await db.integrations.findAll({
      attributes: ['accountId'],
      where: { integrationType: { [Op.in]: ['scorm', 'scorm20043rd'] } },
    });
    const accountIds = scromAccounts.map(accId => accId.accountId);
    // update reassignScorm in account table
    if (accountIds.length) {
      await db.accounts.update(
        { reassignScorm: true },
        {
          where: { id: { [Op.in]: accountIds } },
        },
      );
    }
  },

  down: () => {
    // no going back
  },
};
