module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('regions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      region: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      subRegion: {
        type: Sequelize.STRING,
      },
      createdBy: {
        type: Sequelize.INTEGER,
      },
      updatedBy: {
        type: Sequelize.INTEGER,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    })
      .then(() => {
        return queryInterface.createTable('countries', {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.DataTypes.INTEGER,
          },
          countryName: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          countryCode: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          regionId: {
            type: Sequelize.DataTypes.INTEGER,
          },
          createdBy: {
            type: Sequelize.INTEGER,
          },
          updatedBy: {
            type: Sequelize.INTEGER,
          },
          createdAt: {
            allowNull: false,
            type: Sequelize.DataTypes.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
          updatedAt: {
            allowNull: false,
            type: Sequelize.DataTypes.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
        });
      })
      .then(() => {
        return queryInterface.createTable('states', {
          id: {
            allowNull: false,
            autoIncrement: true,
            primaryKey: true,
            type: Sequelize.DataTypes.INTEGER,
          },
          stateName: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          stateCode: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          countryId: {
            type: Sequelize.DataTypes.INTEGER,
          },
          createdBy: {
            type: Sequelize.INTEGER,
          },
          updatedBy: {
            type: Sequelize.INTEGER,
          },
          createdAt: {
            allowNull: false,
            type: Sequelize.DataTypes.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
          updatedAt: {
            allowNull: false,
            type: Sequelize.DataTypes.DATE,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
        });
      });
    await queryInterface.addIndex('regions', ['region', 'subRegion'], { name: 'region_subregion_unique_fields', unique: true });
    await queryInterface.addIndex('countries', ['countryName', 'countryCode'], { name: 'countryname_countrycode_unique_fields', unique: true });
    await queryInterface.addIndex('states', ['stateName', 'stateCode'], { name: 'statename_statecode_unique_fields', unique: true });
  },
  down: (queryInterface) => {
    return queryInterface.dropTable('countries')
      .then(() => {
        queryInterface.dropTable('states');
      })
      .then(() => {
        return queryInterface.dropTable('regions');
      });
  },
};
