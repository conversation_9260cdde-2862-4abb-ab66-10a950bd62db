module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('auditLogs', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      adminId: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      adminAccountId: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      userAccountId: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      feature: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      action: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      object: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      objectId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      childObject: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      childObjectId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      field: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      originalData: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      updatedData: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex('auditLogs', {
      fields: ['adminId'],
    });
    await queryInterface.addIndex('auditLogs', {
      fields: ['adminAccountId'],
    });
    await queryInterface.addIndex('auditLogs', {
      fields: ['userId'],
    });
    await queryInterface.addIndex('auditLogs', {
      fields: ['userAccountId'],
    });
  },

  down: (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.dropTable('auditLogs');
  },
};
