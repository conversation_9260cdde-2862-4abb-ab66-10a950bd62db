module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('workdayContentData', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.BIGINT,
      },
      accountId: {
        type: Sequelize.INTEGER,
      },
      activityId: {
        type: Sequelize.INTEGER,
      },
      contentType: {
        type: Sequelize.ENUM,
        values: ['program', 'lesson'],
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
      syncedAt: {
        type: Sequelize.DATE,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addConstraint('workdayContentData', {
      fields: ['accountId', 'activityId'],
      type: 'unique',
      name: 'unique_account_content',
    });
  },

  async down(queryInterface) {
    return queryInterface.dropTable('workdayContentData');
  },
};
