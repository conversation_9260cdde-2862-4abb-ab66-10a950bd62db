module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.addColumn(
      'protectedTraitsDefaults',
      'mapType',
      {
        type: Sequelize.ENUM('protected_traits', 'wage_and_hour'),
        defaultValue: 'protected_traits',
      },
    );
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.removeColumn('protectedTraitsDefaults', 'mapType');
  },
};
