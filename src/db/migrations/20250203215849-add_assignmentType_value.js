

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const addExternal = await queryInterface.changeColumn(
      'baseAssignments',
      'assignmentType',
      {
        type: Sequelize.ENUM('shared', 'scorm', 'external'),
      },
    );
    return addExternal;
  },

  async down(queryInterface, Sequelize) {
    const removeExternal = await queryInterface.changeColumn(
      'baseAssignments',
      'assignmentType',
      {
        type: Sequelize.ENUM('shared', 'scorm'),
      },
    );
    return removeExternal;
  },
};
