const csvToJson = require('csvtojson');
const db = require('../../db');
const path = require('path');

const Listings = db.listings;

const readFileIntoRows = (filename) => {
  const fullPathListingData = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathListingData);
};

const updateListingsPreviewId = async () => {
  const listingData = await readFileIntoRows('Listings-Preview-ContentId.csv');
  const promises = [];
  for await (const row of listingData) {
    if (row['Listing ID']) {
      promises.push(Listings.update({ microLessonPreviewId: parseInt(row['Microlesson Preview Content ID']) }, { where: { id: parseInt(row['Listing ID']) } }));
    }
  }
  return Promise.all(promises);
};

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('listings', 'microLessonPreviewId', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });
    await updateListingsPreviewId();
  },

  down: async (queryInterface) => {
    return queryInterface.removeColumn('listings', 'microLessonPreviewId');
  },
};
