'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableName = 'insightsData';
    const tableExists = await queryInterface
      .describeTable(tableName)
      .then(() => true)
      .catch(() => false);

    if (!tableExists) {
      await queryInterface.createTable(tableName, {
        id: {
          type: Sequelize.STRING(255),
          allowNull: false,
          primaryKey: true,
        },
        myOrgOrGlobal: {
          type: Sequelize.TEXT('long'),
          allowNull: true,
        },
        accountId: { type: Sequelize.INTEGER, allowNull: true },
        lessonCardId: { type: Sequelize.INTEGER, allowNull: true },
        cardType: { type: Sequelize.TEXT('long'), allowNull: true },
        lessonCardTitle: { type: Sequelize.TEXT('long'), allowNull: true },
        description: { type: Sequelize.TEXT('long'), allowNull: true },
        question1: { type: Sequelize.TEXT('long'), allowNull: true },
        periodCategory: { type: Sequelize.TEXT('long'), allowNull: true },
        period: { type: Sequelize.TEXT('long'), allowNull: true },
        periodId: { type: Sequelize.TEXT('long'), allowNull: true },
        periodSubCategory: { type: Sequelize.TEXT('long'), allowNull: true },
        val1: { type: Sequelize.INTEGER, allowNull: true },
        val1_pct: { type: Sequelize.DOUBLE, allowNull: true },
        val2: { type: Sequelize.INTEGER, allowNull: true },
        val2_pct: { type: Sequelize.DOUBLE, allowNull: true },
        val3: { type: Sequelize.INTEGER, allowNull: true },
        val3_pct: { type: Sequelize.DOUBLE, allowNull: true },
        val4: { type: Sequelize.INTEGER, allowNull: true },
        val4_pct: { type: Sequelize.DOUBLE, allowNull: true },
        val5: { type: Sequelize.INTEGER, allowNull: true },
        val5_pct: { type: Sequelize.DOUBLE, allowNull: true },
        val6: { type: Sequelize.INTEGER, allowNull: true },
        val6_pct: { type: Sequelize.DOUBLE, allowNull: true },
        val7: { type: Sequelize.INTEGER, allowNull: true },
        val7_pct: { type: Sequelize.DOUBLE, allowNull: true },
        val8: { type: Sequelize.INTEGER, allowNull: true },
        val8_pct: { type: Sequelize.DOUBLE, allowNull: true },
        val9: { type: Sequelize.INTEGER, allowNull: true },
        val9_pct: { type: Sequelize.DOUBLE, allowNull: true },
        val10: { type: Sequelize.INTEGER, allowNull: true },
        val10_pct: { type: Sequelize.DOUBLE, allowNull: true },
        val11: { type: Sequelize.INTEGER, allowNull: true },
        val11_pct: { type: Sequelize.DOUBLE, allowNull: true },
        total: { type: Sequelize.INTEGER, allowNull: true },
        wordCounts: { type: Sequelize.TEXT('long'), allowNull: true },
        createdAt: { type: Sequelize.DATE, allowNull: false },
        updatedAt: { type: Sequelize.DATE, allowNull: false },
        deletedAt: { type: Sequelize.DATE, allowNull: true },
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('insightsData');
  },
};