const { format } = require('date-fns');
const config = require('../../config/config');
const { setPassword } = config.encryption;
const db = require('../../db');

module.exports = {
  up: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const [roles, features, permissions] = await Promise.all([
      queryInterface.sequelize.query(
        "SELECT id FROM roles WHERE name = 'user'",
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      ),
      queryInterface.sequelize.query(
        "SELECT id FROM features WHERE name IN ('categories', 'concepts')",
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      ),
      queryInterface.sequelize.query(
        "SELECT id FROM permissions WHERE name = 'read'",
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      ),
    ]);

    const roleId = roles[0].id;
    const permissionId = permissions[0].id;

    const rows = features.map(f => ({
      roleId,
      featureId: f.id,
      permissionId,
      createdAt: new Date(),
      updatedAt: new Date(),
    }));

    await queryInterface.sequelize.transaction(async (t) => {
      await queryInterface.bulkInsert('roleFeaturePermissions', rows, { transaction: t });
    });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const roles = await queryInterface.sequelize.query(
      "SELECT id FROM roles WHERE name = 'user'",
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );
    const features = await queryInterface.sequelize.query(
      "SELECT id FROM features WHERE name IN ('categories', 'concepts')",
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );
    const permissions = await queryInterface.sequelize.query(
      "SELECT id FROM permissions WHERE name = 'read'",
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const roleId = roles[0] && roles[0].id;
    const permissionId = permissions[0] && permissions[0].id;
    const featureIds = (features && features.length) ? features.map(f => f.id) : [];

    if (roleId && permissionId && featureIds.length) {
      const inPlaceholders = featureIds.map(() => '?').join(', ');
      await queryInterface.sequelize.query(
        `DELETE FROM roleFeaturePermissions WHERE roleId = ? AND permissionId = ? AND featureId IN (${inPlaceholders})`,
        {
          replacements: [roleId, permissionId, ...featureIds],
        }
      );
    }
  },
};
