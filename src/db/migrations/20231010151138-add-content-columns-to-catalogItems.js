const { DataTypes } = require('sequelize');

module.exports = {
  up: (queryInterface, Sequelize) => {
    const migrations = [];
    migrations.push(queryInterface.addColumn(
      'catalogItems',
      'listingId',
      {
        type: DataTypes.INTEGER,
        references: {
          model: 'listings',
          key: 'id',
        },
      },
    ));
    migrations.push(queryInterface.changeColumn(
      'catalogItems',
      'title',
      {
        type: DataTypes.STRING,
        allowNull: true,
      },
    ));
    return Promise.all(migrations);
  },

  down: (queryInterface, Sequelize) => {
    const migrations = [];
    migrations.push(queryInterface.removeColumn(
      'catalogItems',
      'listingId',
    ));
    migrations.push(queryInterface.changeColumn(
      'catalogItems',
      'title',
      {
        type: DataTypes.STRING,
        allowNull: false,
      },
    ));
    return Promise.all(migrations);
  },
};
