module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.addColumn('answerCards', 'accountId', {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: false,
    })
      .then(() => {
        return queryInterface.addIndex('answerCards', {
          fields: ['accountId', 'lessonLessonCardId'],
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeIndex('answerCards', ['accountId', 'lessonLessonCardId'])
      .then(() => {
        return queryInterface.removeColumn('answerCards', 'accountId');
      });
  },
};
