module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('accountContentItems', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      resourceName: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      resourceId: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      resourceType: {
        type: Sequelize.ENUM('program', 'lesson', 'campaign'),
        allowNull: false,
      },
      accountId: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      isScorm: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      lifecycle: {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired', 'preview'),
        defaultValue: 'publish',
        allowNull: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex(
      'accountContentItems',
      ['resourceId', 'resourceType', 'accountId'],
      { name: 'account_content_items_unique_fields', unique: true },
    );
  },

  async down(queryInterface) {
    return queryInterface.dropTable('accountContentItems');
  },
};
