'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.changeColumn('userLessons', 'removalReason', {
      type: Sequelize.ENUM(
        'userDelete','groupDelete','groupRemovedFromGroup','userRemovedFromGroup','groupAssDelete','removedByLearner','withdrawnByAdmin'
      ),
    });
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.changeColumn('userLessons', 'removalReason', {
      type: Sequelize.ENUM(
        'userDelete','groupDelete','groupRemovedFromGroup','userRemovedFromGroup','groupAssDelete',
      ),
    });
  }
};
