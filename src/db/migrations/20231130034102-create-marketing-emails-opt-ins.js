/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    let promises = [];
    promises.push(queryInterface.createTable('marketingEmailsOptIns', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      optInStatus: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      prompted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
    }));
    await Promise.all(promises);
    promises = [];
    promises.push(queryInterface.addIndex('marketingEmailsOptIns', {
      fields: ['email', 'userId'], type: 'UNIQUE',
    }));
    promises.push(queryInterface.changeColumn('semaphores', 'type', {
      type: Sequelize.ENUM(
        'sweeper', 'hrsynch', 'scheduledimports', 'accountsynch', 'groupsynch',
        'campaigncompletion', 'unverifiedusers', 'expertemail', 'notificationsend',
        'texttospeech', 'machinetranslations', 'globalresults', 'videosynch',
        'licensesynch', 'sfsynch',
      ),
      allowNull: false,
    }));
    await Promise.all(promises);
    promises = [];
    // wait for  tables to be created before adding indexes and semaphore record
    promises.push(queryInterface.sequelize.query(
      'INSERT INTO semaphores (type, status, createdAt, updatedAt) VALUES(?, ?, now(), now())',
      {
        type: queryInterface.sequelize.QueryTypes.INSERT,
        replacements: ['sfsynch', 'unlocked'],
      },
    ));
    return Promise.all(promises);
  },
  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const promises = [];
    promises.push(queryInterface.dropTable('marketingEmailsOptIns'));
    promises.push(queryInterface.sequelize.query('DELETE FROM semaphores WHERE type = ?', {
      type: queryInterface.sequelize.QueryTypes.DELETE,
      replacements: ['sfsynch'],
    }));
    // wait for semaphores delete before changing column back
    await Promise.all(promises);
    return queryInterface.changeColumn('semaphores', 'type', {
      type: Sequelize.ENUM(
        'sweeper', 'hrsynch', 'scheduledimports', 'accountsynch', 'groupsynch',
        'campaigncompletion', 'unverifiedusers', 'expertemail', 'notificationsend',
        'texttospeech', 'machinetranslations', 'globalresults', 'videosynch',
        'licensesynch',
      ),
      allowNull: false,
    });
  },
};
