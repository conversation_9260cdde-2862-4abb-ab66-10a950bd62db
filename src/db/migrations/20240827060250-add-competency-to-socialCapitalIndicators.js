const csvToJson = require('csvtojson');
const path = require('path');
const db = require('..');
const logger = require('../../logger');

const SocialCapitalIndicators = db.socialCapitalIndicators;

const readFileIntoRows = (filename) => {
  const fullPathData = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathData);
};

const updateCompetency = async () => {
  const indicatorsData = await readFileIntoRows('SocialCapitalIndicator-Competency-Map.csv');
  for await (const row of indicatorsData) {
    const indicatorId = parseInt(row.indicatorId);
    const competency = row.competency;
    if (indicatorId) {
      await SocialCapitalIndicators.update({ competency }, { where: { id: indicatorId } });
    }
  }
};

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.addColumn(
        'socialCapitalIndicators',
        'competency',
        { type: Sequelize.STRING, allowNull: true },
      );
      await updateCompetency();
    } catch (err) {
      logger.error(err);
    }
  },
  // eslint-disable-next-line no-unused-vars
  down: async (queryInterface, Sequelize) => {
    return queryInterface.removeColumn('socialCapitalIndicators', 'competency');
  },
};
