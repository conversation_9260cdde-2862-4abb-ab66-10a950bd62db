module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('catalogItemIndicators', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      catalogItemId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
      },
      socialCapitalIndicatorId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex(
      'catalogItemIndicators',
      ['catalogItemId', 'socialCapitalIndicatorId'],
      { name: 'catalog_items_indicator_fields', unique: true },
    );
  },

  async down(queryInterface) {
    return queryInterface.dropTable('catalogItemIndicators');
  },
};
