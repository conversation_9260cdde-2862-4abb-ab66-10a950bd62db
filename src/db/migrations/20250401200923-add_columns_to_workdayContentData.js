module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('workdayContentData', 'updateRequired', {
      type: Sequelize.Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });
    await queryInterface.addColumn('workdayContentData', 'languagesChanged', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('workdayContentData', 'updateRequired');
    await queryInterface.removeColumn('workdayContentData', 'languagesChanged');
  },
};
