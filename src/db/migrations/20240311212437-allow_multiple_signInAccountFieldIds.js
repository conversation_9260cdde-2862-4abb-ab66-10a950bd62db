'use strict';
const db = require('../../db');

// /** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('accountFields', 'includeInSelfSignup', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: true,
    })
    await queryInterface.addColumn('accountFields', 'required', {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
      allowNull: true,
    })
    await queryInterface.addColumn('accountFields', 'isStandard', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    })
    await queryInterface.addColumn('accountFields', 'sortOrder', {
      type: Sequelize.INTEGER,
      defaultValue: null,
      allowNull: true,
    })
  },

  async down (queryInterface, Sequelize) {
      await queryInterface.removeColumn('accountFields', 'includeInSelfSignup')
      await queryInterface.removeColumn('accountFields', 'required')
      await queryInterface.removeColumn('accountFields', 'isStandard')
      await queryInterface.removeColumn('accountFields', 'sortOrder')
  }
};
