module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add a new boolean field "viewFullVideo" to the "account" table
    await queryInterface.addColumn('accounts', 'enforceFullVideoView', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });

    // Update the value of 'viewFullVideo' to true for a specific account ID
    const accountId = 1248;
    await queryInterface.bulkUpdate(
      'accounts',
      { enforceFullVideoView: true },
      { id: accountId },
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('accounts', 'enforceFullVideoView');
  },
};
