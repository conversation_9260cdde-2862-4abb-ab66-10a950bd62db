module.exports = {
  up: async (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.addColumn('userLessons', 'requiredSeconds', {
      type: Sequelize.DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: true,
    }));
    promises.push(queryInterface.addColumn('userLessons', 'assignedAt', {
      allowNull: true,
      type: Sequelize.DATE,
    }));
    promises.push(queryInterface.addColumn('userLessons', 'removalReason', {
      type: Sequelize.ENUM(
        'userDelete', 'groupDelete', 'groupRemovedFromGroup',
        'userRemovedFromGroup', 'groupAssDelete',
      ),
      allowNull: true,
    }));
    promises.push(queryInterface.addColumn('userLessons', 'removedAt', {
      allowNull: true,
      type: Sequelize.DATE,
    }));
    promises.push(queryInterface.changeColumn('userLessons', 'type', {
      type: Sequelize.ENUM('program', 'lesson', 'mediaAsset', 'questionAnswer', 'campaign'),
    }));
    await Promise.all(promises);
    return queryInterface.sequelize.query('UPDATE userLessons SET assignedAt = createdAt');
  },

  // eslint-disable-next-line no-unused-vars
  down: (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.removeColumn('userLessons', 'requiredSeconds'));
    promises.push(queryInterface.removeColumn('userLessons', 'assignedAt'));
    promises.push(queryInterface.removeColumn('userLessons', 'removalReason'));
    promises.push(queryInterface.removeColumn('userLessons', 'removedAt'));
    promises.push(queryInterface.changeColumn('userLessons', 'type', {
      type: Sequelize.ENUM('program', 'lesson', 'mediaAsset', 'questionAnswer'),
    }));
    return Promise.all(promises);
  },
};
