module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('catalogItemConcepts', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      catalogItemId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
      },
      socialCapitalConceptId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex(
      'catalogItemConcepts',
      ['catalogItemId', 'socialCapitalConceptId'],
      { name: 'catalog_items_concept_fields', unique: true },
    );
  },

  async down(queryInterface) {
    return queryInterface.dropTable('catalogItemConcepts');
  },
};
