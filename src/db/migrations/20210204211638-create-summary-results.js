module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('summaryLCResults', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      lessonCardId: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
        unique: true,
      },
      wordCounts: {
        type: Sequelize.DataTypes.JSON,
      },
      val1: {
        type: Sequelize.DataTypes.INTEGER,
      },
      val2: {
        type: Sequelize.DataTypes.INTEGER,
      },
      val3: {
        type: Sequelize.DataTypes.INTEGER,
      },
      val4: {
        type: Sequelize.DataTypes.INTEGER,
      },
      val5: {
        type: Sequelize.DataTypes.INTEGER,
      },
      val6: {
        type: Sequelize.DataTypes.INTEGER,
      },
      val7: {
        type: Sequelize.DataTypes.INTEGER,
      },
      val8: {
        type: Sequelize.DataTypes.INTEGER,
      },
      val9: {
        type: Sequelize.DataTypes.INTEGER,
      },
      val10: {
        type: Sequelize.DataTypes.INTEGER,
      },
      val11: {
        type: Sequelize.DataTypes.INTEGER,
      },
      total: {
        type: Sequelize.DataTypes.INTEGER,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      },
    });
    await queryInterface.changeColumn('semaphores', 'type', {
      type: Sequelize.ENUM(
        'sweeper', 'hrsynch', 'scheduledimports', 'accountsynch', 'groupsynch',
        'campaigncompletion', 'unverifiedusers', 'expertemail', 'notificationsend',
        'texttospeech', 'machinetranslations', 'globalresults',
      ),
      allowNull: false,
    });
    return queryInterface.sequelize.query(
      'INSERT INTO semaphores (type, status, createdAt, updatedAt) VALUES(?, ?, now(), now())',
      {
        type: queryInterface.sequelize.QueryTypes.INSERT,
        replacements: ['globalresults', 'unlocked'],
      },
    );
  },
  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    await queryInterface.dropTable('summaryLCResults');
    await queryInterface.sequelize.query('DELETE FROM semaphores WHERE type = ?', {
      type: queryInterface.sequelize.QueryTypes.DELETE,
      replacements: ['globalresults'],
    });
    return queryInterface.changeColumn('semaphores', 'type', {
      type: Sequelize.ENUM(
        'sweeper', 'hrsynch', 'scheduledimports', 'accountsynch', 'groupsynch',
        'campaigncompletion', 'unverifiedusers', 'expertemail', 'notificationsend',
        'texttospeech', 'machinetranslations',
      ),
      allowNull: false,
    });
  },
};
