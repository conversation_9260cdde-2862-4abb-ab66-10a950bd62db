/* eslint-disable max-len */
// in VSCode type Option Z to toggle line breaks
const protectedTraitsDefaults = [{ language: 'cs',
  abbr: 'AB',
  traits:
     '<p><PERSON><PERSON>, nábo<PERSON><PERSON>k<PERSON> přesvěd<PERSON>en<PERSON>, bar<PERSON>, poh<PERSON><PERSON>, genderová identita, genderová vyjádření, t<PERSON><PERSON><PERSON> postižení, ment<PERSON><PERSON><PERSON> post<PERSON>í, v<PERSON><PERSON>, p<PERSON><PERSON><PERSON>, místo p<PERSON>du, rod<PERSON><PERSON> stav, rod<PERSON><PERSON> stav a sexuální orientace.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'cs',
  abbr: 'BC',
  traits:
     '<p><PERSON><PERSON>, bar<PERSON>, pů<PERSON><PERSON>, místo původu, n<PERSON><PERSON><PERSON>tv<PERSON>, rod<PERSON><PERSON> stav, tělesná nebo duševní postižení, poh<PERSON><PERSON> (v<PERSON><PERSON><PERSON><PERSON> muž<PERSON>, <PERSON><PERSON><PERSON>, mezipo<PERSON>aví nebo transgender a zahrnuje také těhot<PERSON>tv<PERSON>, kojen<PERSON> a sexu<PERSON><PERSON><PERSON> obt<PERSON>), sex<PERSON><PERSON><PERSON><PERSON> orientaci (zahrnuje <PERSON>, gay, lesbi<PERSON><PERSON> nebo bisexuální), genderová identita, v<PERSON>raz pohlaví, věk (19 let a starší, nevztahuje se na nákup majetku), trestní odsouzení nebo souhrnný trestný čin nesouvisející se zaměstnáním a politickým přesvědčením.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'cs',
  abbr: 'MB',
  traits:
     '<p>rasa (včetně barvy a vnímané rasy), původ, etnický původ nebo původ, státní příslušnost nebo národní původ, náboženské vyznání nebo vyznání, náboženské sdružení nebo náboženská činnost, rodinný stav, rodinný stav (nevztahuje se na nákup majetku), fyzické nebo duševní postižení nebo související charakteristiky nebo okolnosti, včetně spoléhání se na služební zvíře, invalidní vozík nebo jakýkoli jiný nápravný prostředek nebo prostředek, pohlaví (včetně pohlavně určených znaků nebo okolností, jako je těhotenství, možnost těhotenství nebo okolnosti související s těhotenstvím), sexuální orientace, pohlaví identita, výraz pohlaví, věk (19 let a starší, nevztahuje se na nákup majetku), trestní odsouzení (platí pouze pro zaměstnání) a politická víra, politická asociace nebo politická činnost, zdroj příjmu, sociální znevýhodnění.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'cs',
  abbr: 'NB',
  traits:
     '<p>Věk, rodinný stav, rodinný stav, vyznání nebo náboženství, tělesná postižení, mentální postižení, rasa, barva, původ, místo původu, národní původ, sociální stav (který zahrnuje zdroj příjmu, úroveň vzdělání a povolání), politické přesvědčení nebo činnost, sexuální orientace genderové identity nebo výraz a pohlaví (včetně těhotenství).</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'cs',
  abbr: 'NL',
  traits:
     '<p>Rasa, barva, národnost, etnický původ, sociální původ, náboženské vyznání, náboženství, věk, postižení (včetně vnímaného postižení), znetvoření, pohlaví (včetně těhotenství), sexuální orientace, genderová identita, genderová vyjádření, rodinný stav, rodinný stav, zdroj příjmu a politický názor a trestní odsouzení (nesouvisející se zaměstnáním), osoba, jejíž mzdy jsou předmětem obloha.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'cs',
  abbr: 'NT',
  traits:
     '<p>Věk, postižení, rasa, barva, původ, místo původu, etnický původ, národnost, pohlaví, sexuální orientace, genderová identita, výraz pohlaví, rodinný stav, rodinná příslušnost, rodinný stav, rodinný stav, rodinný stav, společenský stav, náboženství, vyznání, politické přesvědčení, politické sdružení, odsouzení trestního činu a záznam suspenze.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'cs',
  abbr: 'NS',
  traits:
     '<p>Věk, rasa, barva, náboženství, vyznání, etnický, národní nebo domorodý původ, pohlaví (včetně těhotenství a odměňování), sexuální orientace, tělesné postižení, mentální postižení, rodinný stav, rodinný stav, zdroj příjmu, iracionální strach ze nákazy nebo nemoci, sdružování s chráněnými skupinami nebo jednotlivci, politická víra, příslušnost nebo činnost, genderová identita a genderové vyjádření.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'cs',
  abbr: 'NU',
  traits:
     '<p>Rasa, barva, původ, etnický původ, občanství, místo původu, vyznání, náboženství, věk, zdravotní postižení, pohlaví, sexuální orientace, rodinný stav, rodinný stav, těhotenství (včetně adopce dítěte mužem nebo ženou), genderová identita, genderová vyjádření, zákonný zdroj příjmu a odsouzení, za které Milost byla udělena.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'cs',
  abbr: 'ON',
  traits:
     '<p>Věk, původ, barva, rasa, občanství, etnický původ, místo původu, vyznání, zdravotní postižení, rodinný stav, rodinný stav (včetně jediného statusu), genderová identita, výraz pohlaví, příjem veřejné pomoci (pouze v bydlení), záznam trestných činů (pouze v zaměstnání), pohlaví (včetně těhotenství a kojení), a sexuální orientace.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'cs',
  abbr: 'PE',
  traits:
     '<p>Věk, sdružení (s jednotlivcem nebo skupinou jednotlivců, kteří jsou chráněni podle zákona), barva, rasa, vyznání nebo náboženské vyznání, trestní odsouzení, etnický nebo národní původ, rodinný stav nebo rodinný stav, tělesné nebo duševní postižení (včetně přidání a závislosti na alkoholu/drogové závislosti), politické přesvědčení, sexuální orientace, zdroj příjmu, pohlaví nebo pohlaví (včetně těhotenství a sexuálního obtěžování), genderová identita a genderová vyjádření.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'cs',
  abbr: 'QC',
  traits:
     '<p>Rasa, barva, pohlaví, genderová identita, genderová vyjádření, těhotenství, sexuální orientace, občanský stav, věk (s výjimkou zákonů), náboženství, politické přesvědčení, jazyk, etnický nebo národní původ, sociální stav a znevýhodnění nebo použití jakýchkoli prostředků k znevýhodnění.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'cs',
  abbr: 'SK',
  traits:
     '<p>Náboženství, vyznání, rodinný stav, rodinný stav (včetně vztahu mezi rodiči a dítětem), pohlaví (včetně těhotenství), sexuální orientace, tělesného nebo duševního postižení, věku (18 nebo více), barva, původ, státní příslušnost, místo původu, rasa nebo vnímaná rasa, příjem veřejné pomoci a genderová identita.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'cs',
  abbr: 'YT',
  traits:
     '<p>Předky (včetně barvy a rasy), národní původ, etnické nebo jazykové pozadí nebo původ, náboženské vyznání nebo vyznání, náboženská víra, náboženská sdružení, náboženská činnost, věk, pohlaví (včetně stavu těhotenství a těhotenství), sexuální orientace, genderová identita, genderová vyjádření, fyzická nebo fyzická duševní postižení, trestné obvinění nebo rejstřík trestů, politické přesvědčení, politické sdružení, politická činnost, rodinný stav, zdroj příjmu a skutečné nebo předpokládané spojení s jinými jednotlivci nebo skupinami, jejichž totožnost nebo členství je určeno některým z uvedených důvodů.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'AB',
  traits:
     '<p>Race, religiøs overbevisning, farve, køn, kønsidentitet, kønsudtryk, fysisk handicap, psykisk handicap, alder, herkomst, oprindelsessted, ægteskabelig status, indtægtskilde, familiestatus og seksuel orientering</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'BC',
  traits:
     '<p>Race, farve, herkomst, oprindelsessted, religion, civilstand, familiestatus, fysisk eller psykisk handicap, køn (herunder at være mand, kvinde, kønsbestemt eller transkøn, og omfatter også graviditet, amning og seksuel chikane), seksuel orientering (herunder at være heteroseksuel, homoseksuel, lesbisk eller biseksuel), kønsidentitet, kønsudtryk, alder (19 år og ældre, gælder ikke for køb af ejendom), strafferetlig overbevisning eller en sammenfattende lovovertrædelse, der ikke er relateret til beskæftigelse og politisk overbevisning.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'MB',
  traits:
     '<p>Race (herunder farve og opfattet race), herkomst, etnisk baggrund eller oprindelse, nationalitet eller national oprindelse, religion eller tro eller religiøs tro, religiøs forening eller religiøs aktivitet, civilstand, familiestatus (gælder ikke køb af ejendom), fysisk eller psykisk handicap eller beslægtet karakteristika eller omstændigheder, herunder afhængighed af et tjenestedyr, en kørestol eller ethvert andet afhjælpende apparat eller udstyr, køn (herunder kønsbestemte karakteristika eller omstændigheder, såsom graviditet, graviditet eller omstændigheder i forbindelse med graviditet), seksuel orientering, køn identitet, kønsudtryk, alder (19 år og ældre, gælder ikke for køb af ejendom), strafferetlig overbevisning (gælder kun for beskæftigelse) og politisk overbevisning, politisk forening eller politisk aktivitet, indtægtskilde, social ulempe.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'NB',
  traits:
     '<p>Alder, civilstand, familiestatus, tro eller religion, fysisk handicap, psykisk handicap, race, farve, herkomst, oprindelsessted, national oprindelse, social tilstand (herunder indtægtskilde, uddannelsesniveau og erhverv), politisk tro eller aktivitet, seksuel orientering kønsidentitet eller udtryk og køn (herunder graviditet).</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'NL',
  traits:
     '<p>Race, hudfarve, nationalitet, etnisk oprindelse, social oprindelse, religiøs overbevisning, religion, alder, handicap (herunder opfattet handicap), vansiring, køn (herunder graviditet), seksuel orientering, kønsidentitet, kønsudtryk, civilstand, familiestatus, indtægtskilde og politisk mening og strafferetlig overbevisning (ikke relateret til beskæftigelse), person, hvis løn er underlagt garnishment.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'NT',
  traits:
     '<p>Alder, handicap, race, farve, herkomst, oprindelsessted, etnisk oprindelse, nationalitet, køn, seksuel orientering, kønsidentitet, kønsudtryk, familiestatus, familiemæssig tilhørsforhold, civilstand, social tilstand, religion, tro, politisk overbevisning, politisk forening, benådet strafferetlig overbevisning og registrering suspension.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'NS',
  traits:
     '<p>Alder, race, farve, religion, tro, etnisk, national eller oprindelig oprindelse, køn (herunder graviditet og løn), seksuel orientering, fysisk handicap, psykisk handicap, familiestatus, ægteskabelig status, indtægtskilde, irrationel frygt for sygdom eller sygdom, tilknytning til beskyttede grupper eller enkeltpersoner, politisk tro, tilhørsforhold eller aktivitet, kønsidentitet og kønsudtryk.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'NU',
  traits:
     '<p>Race, farve, herkomst, etnisk oprindelse, statsborgerskab, oprindelsessted, trosbekendelse, religion, alder, handicap, køn, seksuel orientering, civilstand, familiestatus, graviditet (herunder en mands eller kvinders adoption af et barn), kønsidentitet, kønsudtryk, lovlig indtægtskilde og en overbevisning, for hvilken en benådning er blevet givet.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'ON',
  traits:
     '<p>Alder, herkomst, farve, race, statsborgerskab, etnisk oprindelse, oprindelsessted, trosbekendelse, handicap, familiestatus, civilstand (herunder enkeltstatus), kønsidentitet, kønsudtryk, modtagelse af offentlig bistand (kun på boligområdet), registrering af lovovertrædelser (kun i beskæftigelse), køn (herunder graviditet og amning), og seksuel orientering.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'PE',
  traits:
     '<p>Alder, tilknytning (med en person eller en gruppe af personer, der er beskyttet i henhold til loven), farve, race, tro eller religion, strafbar overbevisning, etnisk eller national oprindelse, familiestatus eller civilstand, fysisk eller psykisk handicap (herunder tilsætning og alkohol/narkotikamisbrug), politisk overbevisning, seksuel orientering, indtægtskilde, køn eller køn (herunder graviditet og seksuel chikane), kønsidentitet og kønsudtryk.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'QC',
  traits:
     '<p>Race, hudfarve, køn, kønsidentitet, kønsudtryk, graviditet, seksuel orientering, civilstand, alder (medmindre andet er fastsat ved lov), religion, politiske overbevisninger, sprog, etnisk eller national oprindelse, social tilstand og handicap eller anvendelse af midler til at afværge et handicap.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'SK',
  traits:
     '<p>Religion, trosbekendelse, civilstand, familiestatus (herunder forældre/barn-forhold), køn (herunder graviditet), seksuel orientering, fysisk eller psykisk handicap, alder (18 eller derover), farve, herkomst, nationalitet, oprindelsessted, race eller opfattet race, modtagelse af offentlig bistand og kønsidentitet.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'da',
  abbr: 'YT',
  traits:
     '<p>Stamme (herunder farve og race), national oprindelse, etnisk eller sproglig baggrund eller oprindelse, religion eller tro, religiøs overbevisning, religiøs forening, religiøs aktivitet, alder, køn (herunder graviditet og graviditetsrelateret tilstand), seksuel orientering, kønsidentitet, kønsudtryk, fysisk eller psykiske handicap, anklager eller straffeattest, politisk tro, politisk sammenslutning, politisk aktivitet, civilstand eller familiemæssig status, indtægtskilde og faktisk eller formodet tilknytning til andre personer eller grupper, hvis identitet eller medlemskab bestemmes af en af de anførte grunde.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'AB',
  traits:
     '<p>Rasa, náboženské presvedčenie, farba, pohlavie, rodová identita, výraz pohlavia, telesné postihnutie, mentálne postihnutie, vek, pôvod, miesto pôvodu, rodinný stav, rodinný stav a sexuálna orientácia.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'BC',
  traits:
     '<p>Rasa, farba, pôvod, miesto pôvodu, náboženstvo, manželský stav, rodinný stav, fyzické alebo duševné postihnutie, pohlavie (vrátane toho, že je muž, žena, medzipohlavné alebo transgender, a zahŕňa aj tehotenstvo, dojčenie a sexuálne obťažovanie), sexuálnu orientáciu (zahŕňa heterosexuálnu, homosexuálnu, lesbičku alebo bisexuálne), rodová identita, rodová expresia, vek (19 rokov a starší, nevzťahuje sa na nákup majetku), odsúdenie za trestné činy alebo súhrnný trestný čin, ktorý nesúvisí so zamestnaním a politickým presvedčením.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'MB',
  traits:
     '<p>Rasa (vrátane farby a vnímanej rasy), pôvod, etnické pozadie alebo pôvod, národnosť alebo národný pôvod, náboženské vyznanie alebo vyznanie, náboženské združenie alebo náboženská činnosť, rodinný stav, rodinný stav (nevzťahuje sa na nákup majetku), telesné alebo duševné postihnutie alebo súvisiace charakteristiky alebo okolnosti vrátane spoliehania sa na služobné zviera, invalidný vozík alebo akýkoľvek iný nápravný prostriedok alebo pomôcku, pohlavie (vrátane charakteristík alebo okolností určených na pohlavie, ako je tehotenstvo, možnosť gravidity alebo okolností súvisiacich s tehotenstvom), sexuálna orientácia, pohlavie identita, rodové vyjadrenie, vek (19 rokov a starší, nevzťahuje sa na nákup majetku), trestné odsúdenie (platí len pre zamestnanie) a politické presvedčenie, politické združenie alebo politická činnosť, zdroj príjmov, sociálne znevýhodnenie.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'NB',
  traits:
     '<p>Vek, rodinný stav, rodinný stav, vyznanie alebo náboženstvo, telesné postihnutie, mentálne postihnutie, rasa, farba, pôvod, miesto pôvodu, národný pôvod, sociálny stav (ktorý zahŕňa zdroj príjmu, úroveň vzdelania a povolania), politické presvedčenie alebo činnosť, sexuálna orientácia rodová identita alebo výraz a pohlavie (vrátane tehotenstva).</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'NL',
  traits:
     '<p>Rasa, farba, národnosť, etnický pôvod, sociálny pôvod, náboženské vyznanie, náboženstvo, vek, zdravotné postihnutie (vrátane vnímaného postihnutia), znetvorenie, pohlavie (vrátane tehotenstva), sexuálna orientácia, rodová identita, rodové vyjadrenie, manželský stav, rodinný stav, zdroj príjmu a politický názor a odsúdenie za trestné činy (nesúvisí so zamestnaním), osoba, ktorej mzdy podliehajú obloha.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'NT',
  traits:
     '<p>Vek, postihnutie, rasa, farba, pôvod, miesto pôvodu, etnický pôvod, národnosť, pohlavie, sexuálna orientácia, rodová identita, rodový výraz, rodinný stav, rodinný príslušnosť, rodinný stav, rodinný stav, rodinný stav, spoločenský stav, náboženstvo, vyznanie, politická viera, politické združenie, odpustené trestné odsúdenie a záznam suspenzie.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'NS',
  traits:
     '<p>Vek, rasa, farba, náboženstvo, vyznanie, etnický, národný alebo domorodý pôvod, pohlavie (vrátane tehotenstva a odmeňovania), sexuálna orientácia, telesné postihnutie, mentálne postihnutie, rodinný stav, rodinný stav, zdroj príjmu, iracionálny strach z nákazy choroby alebo choroby, spojenie s chránenými skupinami alebo jednotlivcov, politické presvedčenie, príslušnosť alebo činnosť, rodová identita a rodové vyjadrenie.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'NU',
  traits:
     '<p>Rasa, farba, pôvod, etnický pôvod, občianstvo, miesto pôvodu, vyznanie, náboženstvo, vek, zdravotné postihnutie, pohlavie, sexuálna orientácia, rodinný stav, rodinný stav, tehotenstvo (vrátane adopcie dieťaťa mužom alebo ženou), rodová identita, rodová identita, rodové vyjadrenie, zákonný zdroj príjmu a presvedčenie, za ktoré milosť bola udelená.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'ON',
  traits:
     '<p>Vek, pôvod, farba, rasa, občianstvo, etnický pôvod, miesto pôvodu, vyznanie, zdravotné postihnutie, rodinný stav, rodinný stav (vrátane jediného stavu), rodová identita, rodová identita, rodové vyjadrenie, prijímanie verejnej pomoci (len pri bývaní), záznam trestných činov (len v zamestnaní), pohlavie (vrátane tehotenstva a dojčenia) a sexuálnej orientácie.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'PE',
  traits:
     '<p>Vek, združenie (s jednotlivcom alebo skupinou jednotlivcov, ktorí sú chránení podľa zákona), farba, rasa, vyznanie alebo náboženstvo, odsúdenie za trestné činy, etnický alebo národný pôvod, rodinný stav alebo rodinný stav, telesné alebo duševné postihnutie (vrátane pridávania a závislosti na alkoholu/drogovej závislosti), politické presvedčenie, sexuálne orientácia, zdroj príjmu, pohlavie alebo pohlavie (vrátane tehotenstva a sexuálneho obťažovania), rodová identita a rodové vyjadrenie.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'QC',
  traits:
     '<p>Rasa, farba, pohlavie, rodová identita, rodová vyjadrenie, tehotenstvo, sexuálna orientácia, občiansky stav, vek (okrem prípadov stanovených zákonom), náboženstvo, politické presvedčenie, jazyk, etnický alebo národný pôvod, sociálny stav a znevýhodnenie alebo použitie akýchkoľvek prostriedkov na zmiernenie zdravotného postihnutia.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'SK',
  traits:
     '<p>Náboženstvo, vyznanie, rodinný stav, rodinný stav (vrátane vzťahu medzi rodičom a dieťaťom), pohlavie (vrátane tehotenstva), sexuálna orientácia, telesné alebo duševné postihnutie, vek (18 alebo viac), farba, pôvod, národnosť, miesto pôvodu, rasa alebo vnímaná rasa, prijatie verejnej pomoci a rodová identita.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sk',
  abbr: 'YT',
  traits:
     '<p>Predky (vrátane farby a rasy), národný pôvod, etnické alebo jazykové pozadie alebo pôvod, náboženstvo alebo vyznanie, náboženské viery, náboženské združenie, náboženská činnosť, vek, pohlavie (vrátane stavu súvisiaceho s tehotenstvom a tehotenstvom), sexuálna orientácia, rodová identita, rodové vyjadrenie, fyzické alebo mentálne postihnutie, obvinenia z trestov alebo registra trestov, politické presvedčenie, politické združenie, politická činnosť, manželský alebo rodinný stav, zdroj príjmu a skutočné alebo predpokladané spojenie s inými jednotlivcami alebo skupinami, ktorých identita alebo členstvo je určené niektorým z uvedených dôvodov.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'AB',
  traits:
     '<p>Ras, religiös övertygelse, hudfärg, kön, könsidentitet, könsuttryck, fysisk funktionsnedsättning, psykisk funktionsnedsättning, ålder, härkomst, ursprungsort, civilstånd, inkomstkälla, familjestatus och sexuell läggning.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'BC',
  traits:
     '<p>Ras, hudfärg, anor, ursprungsort, religion, civilstånd, familjestatus, fysisk eller psykisk funktionsnedsättning, kön (innefattar att vara man, kvinna, interkönen eller transpersonen, och omfattar även graviditet, amning och sexuella trakasserier), sexuell läggning (innefattar heterosexuell, homosexuell, lesbisk eller bisexuell), könsidentitet, genusuttryck, ålder (19 år och äldre, gäller inte köp av egendom), fällande domar eller ett sammanfattande brott som inte är relaterat till anställning och politisk övertygelse.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'MB',
  traits:
     '<p>Ras (inklusive färg och upplevd ras), härstamning, etnisk bakgrund eller ursprung, nationalitet eller nationellt ursprung, religion eller trosuppfattning eller religiös övertygelse, religiös förening eller religiös verksamhet, civilstånd, familjestatus (gäller inte köp av egendom), fysiskt eller psykiskt funktionshinder eller närstående egenskaper eller omständigheter, inklusive beroende av ett servicedjur, en rullstol eller någon annan avhjälpande apparat eller anordning, kön (inklusive könsbestämda egenskaper eller omständigheter, såsom graviditet, graviditetsrisk eller omständigheter i samband med graviditet), sexuell läggning, kön identitet, könsuttryck, ålder (19 år och äldre, gäller inte köp av egendom), fällande domar i brottmål (gäller endast anställning) och politisk övertygelse, politisk förening eller politisk verksamhet, inkomstkälla, social nackdel.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'NB',
  traits:
     '<p>Ålder, civilstånd, familjestatus, trosbekännelse eller religion, fysiskt handikapp, psykisk funktionsnedsättning, ras, hudfärg, härstamning, ursprungsort, nationellt ursprung, socialt tillstånd (som omfattar inkomstkälla, utbildningsnivå och yrke), politisk övertygelse eller aktivitet, sexuell läggning könsidentitet eller uttryck, och kön (inklusive graviditet).</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'NL',
  traits:
     '<p>Ras, hudfärg, nationalitet, etniskt ursprung, socialt ursprung, religiös tro, religion, ålder, funktionshinder (inklusive upplevd funktionsnedsättning), vanställning, kön (inklusive graviditet), sexuell läggning, könsidentitet, könsuttryck, civilstånd, familjestatus, inkomstkälla och politisk åsikt och brottmålsdom (utan anknytning till anställning), person vars löner är föremål för utmätning.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'NT',
  traits:
     '<p>Ålder, funktionshinder, ras, hudfärg, anor, ursprungsort, etniskt ursprung, nationalitet, kön, sexuell läggning, könsidentitet, könsuttryck, familjestatus, familjetillhörighet, civilstånd, socialt tillstånd, religion, trosbekännelse, politisk sammanslutning, benådad brottmålsdom och register fjädring.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'NS',
  traits:
     '<p>Ålder, ras, hudfärg, religion, trosbekännelse, etniskt, nationellt eller ursprungligt ursprung, kön (inklusive graviditet och lönekapital), sexuell läggning, fysisk funktionsnedsättning, psykisk funktionsnedsättning, familjestatus, civilstånd, inkomstkälla, irrationell rädsla för att drabbas av en sjukdom eller sjukdom, associering med skyddade grupper eller individer, politisk övertygelse, tillhörighet eller aktivitet, könsidentitet och genusuttryck.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'NU',
  traits:
     '<p>Ras, hudfärg, härstamning, etniskt ursprung, medborgarskap, ursprungsort, trosbekännelse, religion, ålder, funktionshinder, kön, sexuell läggning, civilstånd, familjestatus, graviditet (inklusive adoption av ett barn av en man eller kvinna), könsidentitet, genusuttryck, laglig inkomstkälla och fällande dom för vilka benådning har beviljats.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'ON',
  traits:
     '<p>Ålder, härkomst, hudfärg, ras, medborgarskap, etniskt ursprung, ursprungsort, trosbekännelse, funktionshinder, familjestatus, civilstånd (inklusive ensamstatus), könsidentitet, genusuttryck, mottagande av offentligt stöd (endast i bostäder), register över brott (endast i anställning), kön (inklusive graviditet och amning) och sexuell läggning.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'PE',
  traits:
     '<p>Ålder, förening (med en individ eller grupp av personer som skyddas enligt lagen), hudfärg, ras, tro eller religion, fällande domar i brottmål, etniskt eller nationellt ursprung, familjestatus eller civilstånd, fysisk eller psykisk funktionsnedsättning (inklusive tillägg och alkohol/narkotikamissbruk), politisk övertygelse, sexuell orientering, inkomstkälla, kön eller kön (inklusive graviditet och sexuella trakasserier), könsidentitet och könsuttryck.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'QC',
  traits:
     '<p>Ras, hudfärg, kön, könsidentitet, könsuttryck, graviditet, sexuell läggning, civilstånd, ålder (utom enligt lag), religion, politisk övertygelse, språk, etniskt eller nationellt ursprung, socialt tillstånd och handikapp eller användning av något sätt för att lindra ett handikapp.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'SK',
  traits:
     '<p>Religion, trosbekännelse, civilstånd, familjestatus (inklusive föräldra- och barnrelation), kön (inklusive graviditet), sexuell läggning, fysisk eller psykisk funktionsnedsättning, ålder (18 eller äldre), hudfärg, härkomst, nationalitet, ursprungsort, ras eller upplevd ras, mottagande av offentligt stöd och könsidentitet.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'sv',
  abbr: 'YT',
  traits:
     '<p>Anor (inklusive färg och ras), nationellt ursprung, etnisk eller språklig bakgrund eller ursprung, religion eller trosuppfattning, religiös övertygelse, religiös sammanslutning, religiös verksamhet, ålder, kön (inklusive graviditet och graviditetsrelaterat tillstånd), sexuell läggning, könsidentitet, könsuttryck, fysisk eller psykisk funktionsnedsättning, åtal eller kriminalregister, politisk övertygelse, politisk sammanslutning, politisk verksamhet, civilstånd eller familjestatus, inkomstkälla och faktisk eller förmodad koppling till andra personer eller grupper vars identitet eller medlemskap bestäms av någon av de grunder som anges.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'AB',
  traits:
     '<p>Irk, dini inançlar, renk, cinsiyet, cinsiyet kimliği, cinsiyet ifadesi, fiziksel engellilik, zihinsel engellilik, yaş, soy, köken yeri, medeni durum, gelir kaynağı, aile durumu ve cinsel yönelim.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'BC',
  traits:
     '<p>Irk, renk, soy, menşe yeri, din, medeni durum, aile durumu, fiziksel veya zihinsel engellilik, cinsiyet (erkek, kadın, cinsiyetler arası veya transseksüel olmayı içerir ve ayrıca gebelik, emzirme ve cinsel tacizi içerir), cinsel yönelim (heteroseksüel, eşcinsel, lezbiyen veya biseksüel), cinsiyet kimliği, cinsiyet ifadesi, yaş (19 ve üzeri, mülk satın almak için geçerli değildir), cezai mahkumiyet veya istihdam ve siyasi inanç ile ilgili olmayan bir özet suç.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'MB',
  traits:
     '<p>Irk (renk ve algılanan ırk dahil), soy, etnik arka plan veya köken, milliyet veya ulusal köken, din veya inanç, dini dernek veya dini aktivite, medeni durum, aile durumu (mülk satın almak için geçerli değildir), fiziksel veya zihinsel engellilik veya ilgili Hizmet hayvanına, tekerlekli sandalyeye veya başka bir iyileştirici cihaz veya cihaza, cinsiyete (hamilelik, gebelik olasılığı veya hamilelikle ilgili durumlar gibi cinsiyetle belirlenen özellikler veya durumlar dahil), cinsel yönelim, cinsiyet kimlik, cinsiyet ifadesi, yaş (19 ve üzeri, mülk satın almak için geçerli değildir), cezai mahkumiyet (sadece istihdam için geçerlidir) ve siyasi inanç, siyasi dernek veya siyasi faaliyet, gelir kaynağı, sosyal dezavantaj.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'NB',
  traits:
     '<p>Yaş, medeni durum, aile durumu, inanç veya din, fiziksel engellilik, zihinsel engellilik, ırk, renk, soy, menşe yeri, ulusal köken, sosyal durum (gelir kaynağı, eğitim ve meslek düzeyi dahil), siyasi inanç veya faaliyet, cinsel yönelim cinsiyet kimliği veya ifade ve cinsiyet (gebelik dahil).</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'NL',
  traits:
     '<p>Irk, renk, milliyet, etnik köken, sosyal köken, dini inanç, din, yaş, engellilik (algılanan engellilik dahil), şekil bozukluğu, cinsiyet (gebelik dahil), cinsel yönelim, cinsiyet kimliği, cinsiyet ifadesi, medeni durum, aile durumu, gelir kaynağı ve siyasi görüş ve cezai mahkumiyet (istihdam ile ilgisiz), ücretleri garnizasyona tabidir kişi.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'NT',
  traits:
     '<p>Yaş, engellilik, ırk, renk, soy, menşe yeri, etnik köken, milliyet, cinsiyet, cinsel yönelim, cinsiyet kimliği, cinsiyet ifadesi, aile durumu, aile ilişkisi, medeni durum, sosyal durum, din, inanç, siyasi inanç, siyasi ilişki, affedilmiş ceza ve kayıt süspansiyon.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'NS',
  traits:
     '<p>Yaş, ırk, renk, din, inanç, etnik, ulusal veya yerli köken, cinsiyet (gebelik ve ücret özkaynak dahil), cinsel yönelim, fiziksel engellilik, zihinsel engellilik, aile durumu, medeni durum, gelir kaynağı, hastalık veya hastalığa yakalanma konusunda irrasyonel korku, korunan gruplarla ilişki veya bireyler, siyasi inanç, bağlılık veya etkinlik, cinsiyet kimliği ve cinsiyet ifadesi.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'NU',
  traits:
     '<p>Irk, renk, soy, etnik köken, vatandaşlık, menşe yeri, inanç, din, yaş, engellilik, cinsiyet, cinsel yönelim, medeni durum, aile durumu, gebelik (bir erkek veya kadın tarafından bir çocuğun evlat edinilmesi dahil), cinsiyet kimliği, cinsiyet ifadesi, yasal gelir kaynağı ve mahkumiyet Affı kabul edildi.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'ON',
  traits:
     '<p>Yaş, soy, renk, ırk, vatandaşlık, etnik köken, menşe yeri, inanç, engellilik, aile durumu, medeni durum (tek statü dahil), cinsiyet kimliği, cinsiyet ifadesi, kamu yardımının alınması (sadece konut), suçların kaydı (sadece istihdamda), cinsiyet (gebelik ve emzirme) ve cinsel yönelim.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'PE',
  traits:
     '<p>Yaş, ilişki (Kanun kapsamında korunan bir birey veya grup bireyle), renk, ırk, inanç veya din, cezai mahkumiyet, etnik veya ulusal köken, aile durumu veya medeni durum, fiziksel veya zihinsel engellilik (toplama ve alkol/uyuşturucu bağımlılığı dahil), siyasi inanç, cinsel oryantasyon, gelir kaynağı, cinsiyet veya cinsiyet (gebelik ve cinsel taciz dahil), cinsiyet kimliği ve cinsiyet ifadesi.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'QC',
  traits:
     '<p>Irk, renk, cinsiyet, cinsiyet kimliği, cinsiyet ifadesi, gebelik, cinsel yönelim, medeni durum, yaş (yasaların öngördüğü durumlar hariç), din, siyasi inançlar, dil, etnik veya ulusal köken, sosyal durum ve engelli olmak için herhangi bir aracın kullanılması.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'SK',
  traits:
     '<p>Din, inanç, medeni durum, aile durumu (ebeveyn-çocuk ilişkisi dahil), cinsiyet (gebelik dahil), cinsel yönelim, fiziksel veya zihinsel engellilik, yaş (18 veya daha fazla), renk, soy, milliyet, menşe yeri, ırk veya algılanan ırk, kamu yardımının alınması ve cinsiyet kimliği.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'tr',
  abbr: 'YT',
  traits:
     '<p>Ata (renk ve ırk dahil), ulusal köken, etnik veya dilsel arka plan veya kökeni, din veya inanç, dini inanç, dini dernek, dini aktivite, yaş, cinsiyet (gebelik ve gebelikle ilgili durum dahil), cinsel yönelim, cinsiyet kimliği, cinsiyet ifadesi, fiziksel veya zihinsel engellilik, ceza suçlamaları veya sabıka kaydı, siyasi inanç, siyasi dernek, siyasi faaliyet, medeni veya aile durumu, gelir kaynağı ve kimliği veya üyeliği listelenen gerekçelerle herhangi biri tarafından belirlenir diğer kişi veya gruplarla fiili veya tahmin edilen dernek.</p>',
  country: 'CA',
  mapType: 'protected_traits' }];

module.exports = {
  up: (queryInterface) => {
    const inserts = [];
    protectedTraitsDefaults.forEach(({ traits, abbr, language }) => {
      const trait = {
        language,
        abbr,
        traits,
        country: 'CA',
        mapType: 'protected_traits',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      inserts.push(trait);
    });
    return queryInterface.bulkInsert('protectedTraitsDefaults', inserts);
  },

  down: async (queryInterface) => {
    // 18 langs
    const sql = 'DELETE FROM protectedTraitsDefaults WHERE mapType = "protected_traits" AND country = "CA" ' +
    ' AND language IN("cs","da","sk","sv","tr")';

    return queryInterface.sequelize.query(sql, {
      type: queryInterface.sequelize.QueryTypes.DELETE,
    });
  },
};
