const _ = require('lodash');

const permissionsTree = [
  {
    role: 'hrPersonnelRisk',
    allows: [
      {
        features: ['analytics'],
        permissions: ['read'],
      },
    ],
  },
  {
    role: 'businessComplianceRisk',
    allows: [
      {
        features: ['analytics'],
        permissions: ['read'],
      },
    ],
  },
];

const getRoleFeaturePermissionTuples = (roleIds, featureIds, permissionIds) => {
  const data = permissionsTree.map((roleSection) => {
    const roleId = roleIds[roleSection.role];
    const allows = roleSection.allows.map((allowSection) => {
      const features = allowSection.features.map((f) => {
        const permissions = allowSection.permissions.map((p) => {
          return {
            roleId,
            featureId: featureIds[f],
            permissionId: permissionIds[p],
          };
        });
        return permissions;
      });
      return _.flatten(features);
    });
    return _.flatten(allows);
  });
  return _.flatten(data);
};
const featureSelectQuery = 'SELECT id FROM roles WHERE name IN ("hrPersonnelRisk", "businessComplianceRisk")';

module.exports = {
  up: (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const promises = [];

    promises.push(queryInterface.sequelize.query(`
      INSERT INTO roles (name, createdAt, updatedAt) 
      VALUES 
      ('hrPersonnelRisk', now(), now()),
      ('businessComplianceRisk', now(), now())`)
      .then(async (result) => {
        const roleIds = { hrPersonnelRisk: result[0], businessComplianceRisk: result[1] };
        const featureResults = await queryInterface.sequelize.query('select id, name from features', {
          type: Sequelize.QueryTypes.SELECT,
        });
        const featureIds = featureResults.reduce((acc, feature) => {
          return { ...acc, [feature.name]: feature.id };
        }, {});

        const permResults = await queryInterface.sequelize.query('select id, name from permissions', {
          type: Sequelize.QueryTypes.SELECT,
        });
        const permissionIds = permResults.reduce((acc, perm) => {
          return { ...acc, [perm.name]: perm.id };
        }, {});

        const roleFeaturePermissionTuples = getRoleFeaturePermissionTuples(roleIds, featureIds, permissionIds);

        const roleFeaturePermissions = roleFeaturePermissionTuples.map(({ roleId, featureId, permissionId }) => {
          return {
            roleId,
            featureId,
            permissionId,
            createdAt: new Date(),
            updatedAt: new Date(),
          };
        });

        const insertResults = [
          await queryInterface.bulkInsert('roleFeaturePermissions', roleFeaturePermissions),
        ];
        return Promise.resolve(insertResults);
      }));

    return Promise.all(promises);
  },

  down: (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const promises = [];
    promises.push(queryInterface.sequelize.query(
      featureSelectQuery,
      { type: queryInterface.sequelize.QueryTypes.SELECT },
    ).then((result) => {
      const roleIds = result.map(role => role.id);
      return queryInterface.bulkDelete('roleFeaturePermissions', { featureId: { [Sequelize.Op.in]: roleIds } });
    }).then(() => {
      return queryInterface.sequelize.query('DELETE FROM roles WHERE name IN ("hrPersonnelRisk", "businessComplianceRisk")');
    }));

    return Promise.all(promises);
  },
};
