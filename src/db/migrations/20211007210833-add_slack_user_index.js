module.exports = {
  up: async (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.addIndex('users', {
      fields: ['slackTeamId'],
    }));
    promises.push(queryInterface.addIndex('users', {
      fields: ['email'],
    }));
    return Promise.all(promises);
  },
  down: async (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.removeIndex('users', ['slackTeamId']));
    promises.push(queryInterface.removeIndex('users', ['email']));
    return Promise.all(promises);
  },
};
