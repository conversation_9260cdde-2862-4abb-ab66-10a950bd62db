'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint('workdayContentData', 'unique_account_content');
    await queryInterface.addConstraint('workdayContentData', {
      fields: ['accountId', 'activityId', 'contentType'],
      type: 'unique',
      name: 'unique_account_content', 
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint('workdayContentData', 'unique_account_content');
    await queryInterface.addConstraint('workdayContentData', {
      fields: ['accountId', 'activityId'],
      type: 'unique',
      name: 'unique_account_content',
    });
  }
};
