/* eslint-disable object-curly-spacing */
/* eslint-disable key-spacing */
const languageTable = [
  {code:'ar', pName:'عربى', lName:'Arabic'},
  {code:'bn', pName:'বাংলা', lName:'Bengali'},
  {code:'de', pName:'Deutsch', lName:'German'},
  {code:'en', pName:'English', lName:'English'},
  {code:'en-CA', pName:'English Canadian', lName:'English (Canada)'},
  {code:'en-GB', pName:'British English', lName:'English (United Kingdom)'},
  {code:'es', pName:'Español', lName:'Spanish'},
  {code:'es-mx', pName:'Español (Mexico)', lName:'Mexican Spanish'},
  {code:'fr', pName:'Français', lName:'French'},
  {code:'fr-CA', pName:'Français du Canada', lName:'French Canadian'},
  {code:'he', pName:'עִברִית', lName:'Hebrew'},
  {code:'hi', pName:'हिन्दी, हिंदी', lName:'Hindi'},
  {code:'hu', pName:'Magyar', lName:'Hungarian'},
  {code:'id', pName:'Bahasa Indonesia', lName:'Indonesian'},
  {code:'it', pName:'Italiano', lName:'Italian'},
  {code:'ja', pName:'日本語', lName:'Japanese'},
  {code:'ko', pName:'한국어', lName:'Korean'},
  {code:'ms', pName:'Bahasa Melayu', lName:'Malay'},
  {code:'nl', pName:'Nederlands', lName:'Dutch'},
  {code:'pl', pName:'Polskie', lName:'Polish'},
  {code:'pt-br', pName:'Português (Brasil)', lName:'Portuguese'},
  {code:'ru', pName:'Русский язык', lName:'Russian'},
  {code:'sq', pName:'Shqiptare', lName:'Albanian'},
  {code:'th', pName:'ภาษาไทย', lName:'Thai'},
  {code:'tl', pName:'Wikang Tagalog', lName:'Tagalog'},
  {code:'vi', pName:'Tiếng Việt', lName:'Vietnamese'},
  {code:'zh-CN', pName:'汉语', lName:'Chinese Simplified'},
  {code:'zh-TW', pName:'繁體中文', lName:'Chinese Traditional'},
];

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // src: https://coderwall.com/p/gjyuwg/mysql-convert-encoding-to-utf8-without-garbled-data (#4)
    await queryInterface.sequelize.query(`
      UPDATE subtitles SET 
        languageProperName = @txt WHERE char_length(languageProperName) =
        LENGTH(@txt := CONVERT(BINARY CONVERT(languageProperName USING utf8) USING utf8mb4));
      ALTER TABLE subtitles MODIFY languageProperName TEXT CHARACTER SET utf8mb4;
    `);
    await queryInterface.addColumn(
      'subtitles',
      'languageName',
      Sequelize.STRING,
    );
    const promises = [];
    for (const lang of languageTable) {
      promises.push(queryInterface.sequelize.query(`
        UPDATE subtitles SET languageName = '${lang.lName}', languageProperName = '${lang.pName}'
        WHERE languageCode = '${lang.code}';
      `));
    }
    return Promise.all(promises);
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('subtitles', 'languageName');
    return queryInterface.sequelize.query('ALTER TABLE subtitles CONVERT TO CHARACTER SET utf8;');
  },
};
