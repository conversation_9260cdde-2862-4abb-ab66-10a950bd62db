module.exports = {
  up: async (queryInterface, Sequelize) => {
    const promises = [];
    await queryInterface.changeColumn(
      'events',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'active', 'retired', 'preview'),
        defaultValue: null,
        allowNull: true,
      },
    );
    await queryInterface.changeColumn(
      'lastViewedLessonCards',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired', 'preview'),
        defaultValue: 'publish',
        allowNull: false,
      },
    );
    await queryInterface.changeColumn(
      'userLessons',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired', 'preview'),
        defaultValue: 'publish',
        allowNull: false,
      },
    );
    await queryInterface.changeColumn(
      'answerCards',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired', 'preview'),
        defaultValue: 'publish',
        allowNull: false,
      },
    );
    await queryInterface.changeColumn(
      'viewLessonCardEvents',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired', 'preview'),
        defaultValue: null,
        allowNull: true,
      },
    );
    return Promise.all(promises);
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const promises = [];
    await queryInterface.changeColumn(
      'events',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'active', 'retired'),
        defaultValue: null,
        allowNull: true,
      },
    );
    await queryInterface.changeColumn(
      'lastViewedLessonCards',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired'),
        defaultValue: 'publish',
        allowNull: false,
      },
    );
    await queryInterface.changeColumn(
      'userLessons',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired'),
        defaultValue: 'publish',
        allowNull: false,
      },
    );
    await queryInterface.changeColumn(
      'answerCards',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired'),
        defaultValue: 'publish',
        allowNull: false,
      },
    );
    await queryInterface.changeColumn(
      'viewLessonCardEvents',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired'),
        defaultValue: null,
        allowNull: true,
      },
    );
    return Promise.all(promises);
  },
};
