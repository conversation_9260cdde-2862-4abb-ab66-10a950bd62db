'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      await queryInterface.sequelize.query(
        "UPDATE csvColumns SET `userTableColumn` = 'managerEmail' where `userTableColumn` = 'managerId'"
      );
    } catch (err) {
      logger.error(err.message)
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      "UPDATE csvColumns SET `userTableColumn` = 'managerId' where `userTableColumn` = 'managerEmail'"
    );
  }
};
