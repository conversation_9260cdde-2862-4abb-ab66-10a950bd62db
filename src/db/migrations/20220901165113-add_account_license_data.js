const updateQuery = 'UPDATE accounts SET analyticsId = id';
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('licenseInfo', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      accountId: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      salesforceId: {
        type: Sequelize.DataTypes.STRING,
        allowNull: false,
      },
      licenseConsumed: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      simpleLC: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      sfLicenseCount: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      sfLicensePeriodStart: {
        type: Sequelize.DataTypes.DATE,
        allowNull: true,
      },
      sfLicensePeriodEnd: {
        type: Sequelize.DataTypes.DATE,
        allowNull: true,
      },
      sfAccountType: {
        type: Sequelize.DataTypes.ENUM(
          'Original',
          'Subsidiary',
          'Migration',
        ),
        allowNull: false,
        defaultValue: 'Original',
      },
      sfPatchId: {
        type: Sequelize.DataTypes.STRING,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
    });
    // add index to support licensing queries
    await queryInterface.addIndex('lastViewedLessonCards', {
      fields: ['userId', 'updatedAt'],
    });
    // add semaphore for license data synch
    await queryInterface.changeColumn('semaphores', 'type', {
      type: Sequelize.ENUM(
        'sweeper', 'hrsynch', 'scheduledimports', 'accountsynch', 'groupsynch',
        'campaigncompletion', 'unverifiedusers', 'expertemail', 'notificationsend',
        'texttospeech', 'machinetranslations', 'globalresults', 'videosynch', 'licensesynch',
      ),
      allowNull: false,
    });
    await queryInterface.sequelize.query(
      'INSERT INTO semaphores (type, status, createdAt, updatedAt) VALUES(?, ?, now(), now())',
      {
        type: queryInterface.sequelize.QueryTypes.INSERT,
        replacements: ['licensesynch', 'unlocked'],
      },
    );
    await queryInterface.addColumn('accounts', 'analyticsId', Sequelize.INTEGER);
    return queryInterface.sequelize.query(updateQuery, {
      type: queryInterface.sequelize.QueryTypes.UPDATE,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('licenseInfo');

    await queryInterface.removeIndex('lastViewedLessonCards', ['userId', 'updatedAt']);

    await queryInterface.sequelize.query('DELETE FROM semaphores WHERE type = ?', {
      type: queryInterface.sequelize.QueryTypes.DELETE,
      replacements: ['licensesynch'],
    });
    await queryInterface.changeColumn('semaphores', 'type', {
      type: Sequelize.ENUM(
        'sweeper', 'hrsynch', 'scheduledimports', 'accountsynch', 'groupsynch',
        'campaigncompletion', 'unverifiedusers', 'expertemail', 'notificationsend',
        'texttospeech', 'machinetranslations', 'globalresults', 'videosynch',
      ),
      allowNull: false,
    });
    return queryInterface.removeColumn('accounts', 'analyticsId', Sequelize.INTEGER);
  },
};
