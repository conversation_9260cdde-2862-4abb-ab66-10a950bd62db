const csvToJson = require('csvtojson');
const path = require('path');
const db = require('../../db');
const logger = require('../../logger');

const Countries = db.countries;
const Listings = db.listings;

const readFileIntoRows = (filename) => {
  const fullPathListingData = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathListingData);
};

const getListingById = (listingId) => {
  return Listings.findOne({ where: { id: listingId } });
};

const getCountries = async () => {
  const countries = await Countries.findAll({ attributes: ['id', 'countryName', 'countryCode'] });
  return countries;
};

const findCountryByName = (countryName, countries) => {
  // eslint-disable-next-line no-param-reassign
  countryName = countryName === 'USA' ? 'United States of America' : countryName;
  return countries.find(country => country.countryName.toLowerCase() === countryName.toLowerCase());
};

const updateListingData = async () => {
  const listingData = await readFileIntoRows('Listings-Geography-Data.csv');
  const countryList = await getCountries();

  for await (const row of listingData) {
    if (row['Listing ID'] && row['Listing ID'] !== '') {
      const country = findCountryByName(row.Geography, countryList);
      const listing = {
        id: row['Listing ID'],
        countryId: country && country.id || null,
      };
      await Listings.update(listing, { where: { id: listing.id } });
    }
  }
};
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      await updateListingData();
    } catch (err) {
      logger.error(err);
    }
  },
  async down(queryInterface, Sequelize) {
    // no going back
  },
};

