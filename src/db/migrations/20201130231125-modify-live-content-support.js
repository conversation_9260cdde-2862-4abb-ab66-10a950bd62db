module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('lessonPrograms', 'dateAdded', {
      type: Sequelize.DATE,
    });
    await queryInterface.addColumn('lessonPrograms', 'dateRemoved', {
      type: Sequelize.DATE,
    });
    await queryInterface.addColumn('lessonLessonCards', 'dateAdded', {
      type: Sequelize.DATE,
    });
    await queryInterface.addColumn('lessonLessonCards', 'dateRemoved', {
      type: Sequelize.DATE,
    });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    await queryInterface.removeColumn('lessonPrograms', 'dateAdded');
    await queryInterface.removeColumn('lessonPrograms', 'dateRemoved');
    await queryInterface.removeColumn('lessonLessonCards', 'dateAdded');
    await queryInterface.removeColumn('lessonLessonCards', 'dateRemoved');
  },
};
