module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.changeColumn('catalogItems', 'recommendType', {
          type: Sequelize.ENUM,
          values: ['recommend', 'toprecommend', 'notrecommend'],
          defaultValue: 'notrecommend',
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.changeColumn('catalogItems', 'recommendType', {
      type: Sequelize.ENUM,
      values: ['recommend', 'toprecommend', 'both'],
      allowNull: true,
    });
  },
};
