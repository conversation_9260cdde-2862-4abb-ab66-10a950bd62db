module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('lessons', 'sourceId', {
      type: Sequelize.INTEGER,
      defaultValue: null,
    });
    return queryInterface.addColumn('programs', 'sourceId', {
      type: Sequelize.INTEGER,
      defaultValue: null,
    });
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('lessons', 'sourceId');
    return queryInterface.removeColumn('programs', 'sourceId');
  },
};
