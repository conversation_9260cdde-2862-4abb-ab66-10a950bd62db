module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('accountDemographicFields', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      accountId: {
        type: Sequelize.DataTypes.INTEGER,
      },
      dateOfBirth: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      gender: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      sexuality: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      raceEthnicity: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      disabilityStatus: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      veteranStatus: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      customMessage: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
      },
    }).then(() => {
      return queryInterface.addIndex('accountDemographicFields', {
        fields: ['accountId'],
      });
    }).then(() => {
      return queryInterface.addColumn(
        'accounts',
        'enableAccountDemographicsFeature',
        {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
      );
    });
  },

  down: (queryInterface) => {
    return queryInterface.removeIndex('accountDemographicFields', ['accountId'])
      .then(() => {
        return queryInterface.dropTable('accountDemographicFields');
      }).then(() => {
        return queryInterface.removeColumn('accounts', 'enableAccountDemographicsFeature');
      });
  },
};
