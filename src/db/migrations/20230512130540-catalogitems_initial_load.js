/* eslint-disable no-restricted-globals */
const csvToJson = require('csvtojson');
const path = require('path');
const db = require('../../db');

const Lessons = db.lessons;
const Programs = db.programs;
const Regions = db.regions;
const Countries = db.countries;
const States = db.states;
const CatalogItems = db.catalogItems;
const CatalogItemPillars = db.catalogItemPillars;
const CatalogItemIndicators = db.catalogItemIndicators;
const Op = db.Sequelize.Op;

const readFileIntoRows = (filename) => {
  const fullPathCatalogItems = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathCatalogItems);
};

const saveProgramsMetaData = async () => {
  const programData = await readFileIntoRows('Catalog-Program-Metadata.csv');
  for await (const row of programData) {
    const pData = {
      catalogId: parseInt(row.catalogId),
      edition: isNaN(parseInt(row.edition)) ? 0 : parseInt(row.edition),
      version: isNaN(parseInt(row.version)) ? 0 : parseInt(row.version),
      build: isNaN(parseInt(row.build)) ? 0 : parseInt(row.build),
      isBase: row.isBase === '1',
      isClone: row.isClone === '1',
      isCustomized: row.isCustomized === '1',
    };
    await Programs.update(pData, { where: { id: row.programId } });
  }
};

const saveLessonsMetaData = async () => {
  const programData = await readFileIntoRows('Catalog-Lesson-Metadata.csv');
  for await (const row of programData) {
    const pData = {
      catalogId: parseInt(row.catalogId),
      edition: isNaN(parseInt(row.edition)) ? 0 : parseInt(row.edition),
      version: isNaN(parseInt(row.version)) ? 0 : parseInt(row.version),
      build: isNaN(parseInt(row.build)) ? 0 : parseInt(row.build),
      isBase: row.isBase === '1',
      isClone: row.isClone === '1',
      isCustomized: row.isCustomized === '1',
    };
    await Lessons.update(pData, { where: { id: row.lessonId } });
  }
};

module.exports = {
  up: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    try {
      // truncate the catalogItems and update catalogId to null for all programs/lessons
      await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 0');
      await queryInterface.sequelize.query('TRUNCATE TABLE catalogItems');
      await queryInterface.sequelize.query('UPDATE programs SET `catalogId` = NULL');
      await queryInterface.sequelize.query('UPDATE lessons SET `catalogId` = NULL');
      await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 1');

      // programs meta data save
      await saveProgramsMetaData();
      // lessons meta data save
      await saveLessonsMetaData();
    } catch (err) {
      console.log(err);
    }
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    // no going back
  },
};
