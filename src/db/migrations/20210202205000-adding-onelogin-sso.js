module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('integrations', 'integrationType', {
      type: Sequelize.ENUM('okt sso', 'scorm', 'scorm20043rd', 'sftp', 'authentication', 'azure sso', 'google sso', 'onelogin sso'),
      allowNull: false,
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('integrations', 'integrationType', {
      type: Sequelize.ENUM('okt sso', 'scorm', 'scorm20043rd', 'sftp', 'authentication', 'azure sso', 'google sso'),
      allowNull: false,
    });
  },
};
