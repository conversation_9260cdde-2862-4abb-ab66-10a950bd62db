module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn(
      'catalogItems',
      'edition',
      {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
    );
    return queryInterface.sequelize.query(`
      UPDATE catalogItems SET edition = null WHERE edition = 0;
    `);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      UPDATE catalogItems SET edition = 0 where edition IS NULL;
    `);
    return queryInterface.changeColumn(
      'catalogItems',
      'edition',
      {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
    );
  },
};
