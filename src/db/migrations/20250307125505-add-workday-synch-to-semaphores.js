/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('semaphores', 'type', {
      type: Sequelize.ENUM(
        'sweeper', 'hrsynch', 'scheduledimports', 'accountsynch', 'groupsynch',
        'campaigncompletion', 'unverifiedusers', 'expertemail', 'notificationsend',
        'texttospeech', 'machinetranslations', 'globalresults', 'videosynch',
        'licensesynch', 'sfsynch', 'mclsynch', 'workdaysynch',
      ),
      allowNull: false,
    });
    return queryInterface.sequelize.query(
      'INSERT INTO semaphores (type, status, createdAt, updatedAt) VALUES(?, ?, now(), now())',
      {
        type: queryInterface.sequelize.QueryTypes.INSERT,
        replacements: ['workdaysynch', 'unlocked'],
      },
    );
  },
  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    await queryInterface.sequelize.query('DELETE FROM semaphores WHERE type = ?', {
      type: queryInterface.sequelize.QueryTypes.DELETE,
      replacements: ['workdaysynch'],
    });
    return queryInterface.changeColumn('semaphores', 'type', {
      type: Sequelize.ENUM(
        'sweeper', 'hrsynch', 'scheduledimports', 'accountsynch', 'groupsynch',
        'campaigncompletion', 'unverifiedusers', 'expertemail', 'notificationsend',
        'texttospeech', 'machinetranslations', 'globalresults', 'videosynch',
        'licensesynch', 'sfsynch', 'mclsynch',
      ),
      allowNull: false,
    });
  },
};
