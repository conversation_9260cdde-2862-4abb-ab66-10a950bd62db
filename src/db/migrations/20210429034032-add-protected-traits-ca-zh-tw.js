/* eslint-disable max-len */
/* eslint-disable quotes */

// in VSCode type Option Z to toggle line breaks
const ptds = [{ language: 'zh-tw',
  abbr: 'AB',
  traits:
     '<p>種族、宗教信仰、顏色、性別、性別認同、性別表達、身體殘疾、精神殘疾、年齡、祖先、原籍地、婚姻狀況、收入來源、家庭狀況及性取向。</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'zh-tw',
  abbr: 'BC',
  traits:
     '<p>種族、顏色、祖先、原籍地、宗教、婚姻狀況、家庭狀況（不適用於購買財產）、身體或精神殘疾、性別（包括男性、女性、交往性別或跨性別）。此外，還包括懷孕、哺乳和性騷擾）、性取向（包括異性戀、同性戀、同性戀或雙性戀）、性別認同、性別表達、年齡（19 歲以上，不適用於購買物業）、刑事定罪（只適用於就業）和政治信仰（只適用於就業）適用於就業）。</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'zh-tw',
  abbr: 'MB',
  traits:
     '<p>祖先（包括顏色和感知的種族），國籍或國籍，種族背景或來源，宗教或信仰，或宗教信仰，宗教協會或宗教活動，年齡，性別，包括性別確定的特徵或情況，如懷孕，懷孕的可能性，或有關懷孕、性別認同、性傾向、婚姻或家庭狀況、收入來源、政治信仰、政治協會或政治活動及身心障礙或相關特徵或情況，包括依賴服務動物、輪椅或任何其他補救設備或設備，社會上的缺點。</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'zh-tw',
  abbr: 'NB',
  traits:
     '<p>種族、膚色、國籍、原籍地、祖先、信仰或宗教、年齡、家庭狀況、婚姻狀況、性別（包括懷孕）、性取向、性別認同、性別表達、身體或精神殘疾、社會狀況（包括收入來源、教育水平和職業）和政治信仰或活動。</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'zh-tw',
  abbr: 'NL',
  traits:
     '<p>種族、顏色、國籍、族裔、社會來源、宗教信仰、宗教、年齡、殘疾（包括感知障礙）、損傷、性別（包括懷孕）、性取向、性別認同、性別認同、性別表達、婚姻狀況、家庭狀況、收入來源、政治意見及刑事定罪（無關的就業）。</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'zh-tw',
  abbr: 'NT',
  traits:
     '<p>年齡、殘疾、種族、顏色、祖先、原籍地、民族、國籍、性別、性取向、性別認同、家庭狀況、家庭關係、婚姻狀況、社會狀況、宗教、信仰、政治信仰、政治協會、赦免刑事定罪及暫停紀錄。</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'zh-tw',
  abbr: 'NS',
  traits:
     '<p>年齡、種族、膚色、宗教、信仰、族裔、國民或原住民、性別（包括懷孕和薪酬平等）、性取向、身體殘疾、精神殘疾、家庭狀況、婚姻狀況、收入來源、對患病或疾病的非理性恐懼、與受保護團體有關或個人、政治信仰、關係或活動、性別認同和性別表達。</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'zh-tw',
  abbr: 'NU',
  traits:
     '<p>種族、顏色、祖先、族裔、公民身份、原籍地、信仰、宗教、年齡、殘疾、性別、性取向、婚姻狀況、家庭狀況、懷孕（包括男女收養子女）、性別認同、性別表達、合法收入來源及定罪原諒已經被授予。</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'zh-tw',
  abbr: 'ON',
  traits:
     '<p>年齡、祖先、顏色、種族、公民身份、族裔、原籍地、信仰、殘疾、家庭狀況、婚姻狀況（包括單一身份）、性別認同、性別表達、接受公共援助（只限住房）、犯罪紀錄（僅限就業）、性別（包括懷孕和乳房）餵養）和性取向。</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'zh-tw',
  abbr: 'PE',
  traits:
     '<p>年齡、組織（與受法案保護的個人或團體）、顏色、種族、信仰或宗教、刑事定罪、族裔或國籍、家庭狀況或婚姻狀況、身體或精神殘疾（包括增加和酒精/藥物測試）、政治信仰、性別取向、收入來源、性別或性別（包括懷孕及性騷擾）、性別認同及性別表達。</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'zh-tw',
  abbr: 'QC',
  traits:
     '<p>種族、顏色、性別認同、性別認同、性別表達、懷孕、性取向、公民身份、年齡、法律規定、宗教、政治信仰、語言、民族或國籍、社會狀況和殘疾或使用任何方法來紓緩殘疾。</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'zh-tw',
  abbr: 'SK',
  traits:
     '<p>宗教、信仰、婚姻狀況、家庭狀況（包括親子關係及懷孕）、性別、性取向、身體或精神障礙、年齡（18 歲或以上）、顏色、祖先、國籍、原籍地、種族或感知種族、接受公共援助和性別認同。</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'zh-tw',
  abbr: 'YT',
  traits:
     '<p>祖先（包括顏色和種族）國籍、族裔或語言背景或來源、宗教或信仰、年齡、性別（包括懷孕）、性取向、性別認同、性別表達、身體或精神殘疾、刑事指控或刑事紀錄、政治信仰、聯盟或活動、婚姻或家庭狀況、收入來源，以及與其他個人或團體的身份或會員資格由列出的任何理由決定的實際或被推定的聯繫。</p>',
  country: 'CA',
  mapType: 'protected_traits' }];

module.exports = {
  up: (queryInterface) => {
    const inserts = [];
    ptds.forEach(({ language, abbr, traits, country, mapType }) => {
      const trait = {
        language,
        abbr,
        traits,
        country,
        mapType,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      inserts.push(trait);
    });
    return queryInterface.bulkInsert('protectedTraitsDefaults', inserts);
  },

  down: async (queryInterface) => {
    const sql = `DELETE FROM protectedTraitsDefaults 
    WHERE language = 'zh-tw'
    AND mapType = 'protected_traits'
    AND country = 'CA'`;

    return queryInterface.sequelize.query(sql, {
      type: queryInterface.sequelize.QueryTypes.DELETE,
    });
  },
};
