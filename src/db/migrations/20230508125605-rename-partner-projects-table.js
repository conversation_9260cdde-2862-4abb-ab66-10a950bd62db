/** @type {import('sequelize-cli').Migration} */
/**
 * Interim renaming required in order to account for OS level handling of case sensitivity.
 */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.renameTable('PartnerProjects', 'XPartnerProjects');
    await queryInterface.renameTable('XPartnerProjects', 'partnerProjects');
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.renameTable('partnerProjects', 'xpartnerProjects');
    await queryInterface.renameTable('xpartnerProjects', 'PartnerProjects');
  },
};
