module.exports = {
  up: async (queryInterface, Sequelize) => {
    const sql = `DELETE FROM accountFields 
    WHERE fieldType = 'SMS'
    AND accountId IS NULL`;

    await queryInterface.sequelize.query(sql, {
      type: queryInterface.sequelize.QueryTypes.DELETE,
    });

    return queryInterface.changeColumn(
      'accountFields',
      'fieldType',
      {
        type: Sequelize.ENUM('number', 'string', 'email', 'date'),
        defaultValue: 'email',
      },
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn(
      'accountFields',
      'fieldType',
      {
        type: Sequelize.ENUM('number', 'string', 'email', 'date', 'SMS'),
        defaultValue: 'email',
      },
    );
    const commonFields = [{
      fieldName: 'workSMS',
      fieldType: 'SMS',
      userEditable: true,
      encryptField: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    }, {
      fieldName: 'personalSMS',
      fieldType: 'SMS',
      userEditable: true,
      encryptField: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    }];

    return queryInterface.bulkInsert('accountFields', commonFields, {});
  },
};
