const csvToJson = require('csvtojson');
const path = require('path');
const db = require('../../db');
const logger = require('../../logger');

const Countries = db.countries;
const Listings = db.listings;
const ListingPillars = db.listingPillars;
const ListingIndicators = db.listingIndicators;
const ListingConcepts = db.listingConcepts;
const ListingExperts = db.listingExperts;
const readFileIntoRows = (filename) => {
  const fullPathListingData = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathListingData);
};

const getListingById = async (listingId) => {
  return await db.sequelize.query(`SELECT * FROM listings where id=${listingId} limit 1`, {
    type: db.sequelize.QueryTypes.SELECT,
  });
  return Listings.findOne({ where: { id: listingId } });
};

const saveListingPillars = async (listingId, pillarIds) => {
  if (pillarIds.length) {
    for await (const id of pillarIds) {
      if (id && id.trim() !== '') {
        await ListingPillars.upsert({ listingId, pillarId: id });
      }
    }
  }
};
const saveListingIndicators = async (listingId, indicatorIds) => {
  if (indicatorIds.length) {
    for await (const id of indicatorIds) {
      if (id && id.trim() !== '') {
        await ListingIndicators.upsert({ listingId, indicatorId: id });
      }
    }
  }
};
const saveListingConcepts = async (listingId, conceptIds) => {
  if (conceptIds.length) {
    for await (const id of conceptIds) {
      if (id && id.trim() !== '') {
        await ListingConcepts.upsert({ listingId, conceptId: id });
      }
    }
  }
};
const saveListingExperts = async (listingId, expertIds) => {
  if (expertIds.length) {
    for await (const id of expertIds) {
      if (id && id.trim() !== '') {
        await ListingExperts.upsert({ listingId, expertId: id });
      }
    }
  }
};

const getCountries = async () => {
  const countries = await Countries.findAll({ attributes: ['id', 'countryName', 'countryCode'], order: [['id', 'DESC']], });
  return countries;
};

const findCountryByName = async(countryName, countries) => {
  // eslint-disable-next-line no-param-reassign
  countryName = countryName === 'USA' ? 'United States of America' : countryName;
  countryName = countryName === 'Global' ? 'International' : countryName;
  const country =  countries.find(country => country.countryName.toLowerCase() === countryName.toLowerCase());
  return country;
};

const uploadListingData = async () => {
  const listingData = await readFileIntoRows('Listings-Data.csv');
  const countryList = await getCountries();
  for await (const row of listingData) {
    if (row['Listing ID'] && row['Listing ID'] !== '') {
      const country = await findCountryByName(row.Geography, countryList);
      const listing = {
        id: row['Listing ID'],
        rank: row['Listing ID'],
        lifecycle: 'draft',
        type: row['Listing Type'],
        title: row.Title.replace(/"/g, '""'),
        description: row.Descriptions.replace(/"/g, '""'),
        countryId: country && country.id || null,
        subtitle: row.Subtitle.replace(/"/g, '""'),
        marketingCopy: row['Marketing Copy'].replace(/"/g, '""'),
        featuredSnippet: row['Featured Snippet'].replace(/"/g, '""'),
        webUrl: row['Detail Page URL'],
      };
      
      const query = `
        INSERT INTO listings
        (id,type,\`rank\`,lifecycle,title,description,countryId,subtitle,marketingCopy,featuredSnippet,webUrl,createdAt, updatedAt)
        VALUES (${listing.id},"${listing.type}",${listing.rank},"${listing.lifecycle}","${listing.title}","${listing.description}",${listing.countryId},"${listing.subtitle}","${listing.marketingCopy}","${listing.featuredSnippet}","${listing.webUrl}",now(), now())
      `;
      await db.sequelize.query(query);
      const listingInfo = await getListingById(listing.id);
      if (listingInfo) {
        await saveListingPillars(listing.id, row.pillar.split(','));
        await saveListingIndicators(listing.id, row.Indicators.split(','));
        await saveListingConcepts(listing.id, row.Concepts.split(','));
        await saveListingExperts(listing.id, row['Topic Expert'].split(','));
      }
    }
  }
};
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      await uploadListingData();
    } catch (err) {
      logger.error(err);
    }
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 0');
    await queryInterface.sequelize.query('TRUNCATE TABLE listings');
    await queryInterface.sequelize.query('TRUNCATE TABLE listingConcepts');
    await queryInterface.sequelize.query('TRUNCATE TABLE listingExperts');
    await queryInterface.sequelize.query('TRUNCATE TABLE listingIndicators');
    await queryInterface.sequelize.query('TRUNCATE TABLE listingPillars');
    await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 1');
  },
};

