/** @type {import('sequelize-cli').Migration} */
const logger = require('../../logger');

module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      const promises = [];
      promises.push(
        queryInterface.addIndex('listings', ['lifecycle'], {
          name: 'listings_lifecycle',
        }),
        queryInterface.addIndex('catalogItems', ['stateId'], {
          name: 'catalogItems_stateId',
        }),
        queryInterface.addIndex('catalogItems', ['countryId'], {
          name: 'catalogItems_countryId',
        }),
        queryInterface.addIndex('catalogItems', ['isListingAssociate'], {
          name: 'catalogItems_isListingAssociate',
        }),
        queryInterface.addIndex('catalogItems', ['contentType'], {
          name: 'catalogItems_contentType',
        }),
        queryInterface.addIndex('catalogItems', ['fileId'], {
          name: 'catalogItems_fileId',
        }),
        queryInterface.addIndex('programs', ['catalogId'], {
          name: 'programs_catalogId',
        }),
        queryInterface.addIndex('programs', ['lifecycle'], {
          name: 'programs_lifecycle',
        }),
        queryInterface.addIndex('programs', ['isCustomized'], {
          name: 'programs_isCustomized',
        }),
        queryInterface.addIndex('lessons', ['catalogId'], {
          name: 'lessons_catalogId',
        }),
        queryInterface.addIndex('lessons', ['lifecycle'], {
          name: 'lessons_lifecycle',
        }),
        queryInterface.addIndex('lessons', ['isCustomized'], {
          name: 'lessons_isCustomized',
        }),
        queryInterface.addIndex('resourceBundles', ['deletedAt'], {
          name: 'resourceBundles_deletedAt',
        }),
        queryInterface.addIndex('accountPrograms', ['programId'], {
          name: 'accountPrograms_programId',
        }),
        queryInterface.addIndex('accountLessons', ['lessonId'], {
          name: 'accountLessons_lessonId',
        }),
        queryInterface.addIndex('listingIndicators', ['listingId'], {
          name: 'listingIndicators_listingId',
        }),
        queryInterface.addIndex('listingConcepts', ['listingId'], {
          name: 'listingConcepts_listingId',
        }),
        queryInterface.addIndex('listingPillars', ['listingId'], {
          name: 'listingPillars_listingId',
        }),
        queryInterface.addIndex('catalogItemPillars', ['catalogItemId'], {
          name: 'catalogItemPillars_catalogItemId',
        }),
        queryInterface.addIndex('catalogItemIndicators', ['catalogItemId'], {
          name: 'catalogItemIndicators_catalogItemId',
        }),
        queryInterface.addIndex('catalogItemConcepts', ['catalogItemId'], {
          name: 'catalogItemConcepts_catalogItemId',
        }),
      );
      await Promise.all(promises);
    } catch (err) {
      logger.error(err.message);
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      const promises = [];
      promises.push(
        queryInterface.removeIndex('listings', 'listings_lifecycle'),
        queryInterface.removeIndex('catalogItems', 'catalogItems_stateId'),
        queryInterface.removeIndex('catalogItems', 'catalogItems_countryId'),
        queryInterface.removeIndex('catalogItems', 'catalogItems_isListingAssociate'),
        queryInterface.removeIndex('catalogItems', 'catalogItems_contentType'),
        queryInterface.removeIndex('catalogItems', 'catalogItems_fileId'),
        queryInterface.removeIndex('programs', 'programs_catalogId'),
        queryInterface.removeIndex('programs', 'programs_lifecycle'),
        queryInterface.removeIndex('programs', 'programs_isCustomized'),
        queryInterface.removeIndex('lessons', 'lessons_catalogId'),
        queryInterface.removeIndex('lessons', 'lessons_lifecycle'),
        queryInterface.removeIndex('lessons', 'lessons_isCustomized'),
        queryInterface.removeIndex('resourceBundles', 'resourceBundles_deletedAt'),
        queryInterface.removeIndex('accountPrograms', 'accountPrograms_programId'),
        queryInterface.removeIndex('accountLessons', 'accountLessons_lessonId'),
        queryInterface.removeIndex('listingIndicators', 'listingIndicators_listingId'),
        queryInterface.removeIndex('listingConcepts', 'listingConcepts_listingId'),
        queryInterface.removeIndex('listingPillars', 'listingPillars_listingId'),
        queryInterface.removeIndex('catalogItemPillars', 'catalogItemPillars_catalogItemId'),
        queryInterface.removeIndex('catalogItemIndicators', 'catalogItemIndicators_catalogItemId'),
        queryInterface.removeIndex('catalogItemConcepts', 'catalogItemConcepts_catalogItemId'),
      );
      await Promise.all(promises);
    } catch (err) {
      logger.error(err.message);
    }
  },
};
