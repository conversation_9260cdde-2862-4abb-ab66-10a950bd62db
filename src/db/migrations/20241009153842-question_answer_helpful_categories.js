

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn(
      'questionAnswers',
      'helpful',
      { type: Sequelize.BOOLEAN, defaultValue: null, allowNull: true },
    );
    await queryInterface.addColumn(
      'questionAnswers',
      'answerHelpful',
      { type: Sequelize.BOOLEAN, defaultValue: null, allowNull: true },
    );
    await queryInterface.addColumn(
      'questionAnswers',
      'parentId',
      { type: Sequelize.DataTypes.INTEGER },
    );
    // Create join table for questions and categories
    await queryInterface.createTable('questionAnswerCategories', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      questionAnswerId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'questionAnswers',
          key: 'id',
        },
      },
      categoryId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'categories',
          key: 'id',
        },
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('questionAnswers', 'helpful');
    await queryInterface.removeColumn('questionAnswers', 'answerHelpful');
    await queryInterface.removeColumn('questionAnswers', 'parentId');
    await queryInterface.dropTable('questionAnswerCategories');
  },
};
