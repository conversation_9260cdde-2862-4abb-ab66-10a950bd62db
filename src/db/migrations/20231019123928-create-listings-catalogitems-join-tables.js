/** @type {import('sequelize-cli').Migration} */
module.exports = {
  // Create join table for listings and experts
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('listingExperts', {
      listingId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'listings',
          key: 'id',
        },

      },
      expertId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'experts',
          key: 'id',
        },
      },
    });

    // Create join table for listings and concepts
    await queryInterface.createTable('listingConcepts', {
      listingId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'listings',
          key: 'id',
        },

      },
      conceptId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'concepts',
          key: 'id',
        },
      },
    });

    // Create join table for listins and socialCapitalIndicators
    await queryInterface.createTable('listingIndicators', {
      listingId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'listings',
          key: 'id',
        },

      },
      indicatorId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'socialCapitalIndicators',
          key: 'id',
        },
      },
    });

    // Create join table for listings and socialCapitalPillars
    await queryInterface.createTable('listingPillars', {
      listingId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'listings',
          key: 'id',
        },

      },
      pillarId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'socialCapitalPillars',
          key: 'id',
        },
      },
    });

    // Rename topics and concepts for possible rollback
    await queryInterface.renameTable('catalogItemTopics', 'catalogItemTopicsBak');
    await queryInterface.renameTable('catalogItemConcepts', 'catalogItemConceptsBak');
    await queryInterface.renameTable('socialCapitalConcepts', 'socialCapitalConceptsBak');

    // Create join table for catalogItems and concepts
    await queryInterface.createTable('catalogItemConcepts', {
      catalogItemId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'catalogItems',
          key: 'id',
        },

      },
      conceptId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'concepts',
          key: 'id',
        },
      },
    });

    // Add soft delete to listings, concepts, experts
    await queryInterface.addColumn('listings', 'deletedAt', { type: Sequelize.DATE });
    await queryInterface.addColumn('concepts', 'deletedAt', { type: Sequelize.DATE });
    await queryInterface.addColumn('experts', 'deletedAt', { type: Sequelize.DATE });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('listingExperts');
    await queryInterface.dropTable('listingConcepts');
    await queryInterface.dropTable('listingIndicators');
    await queryInterface.dropTable('listingPillars');
    await queryInterface.dropTable('catalogItemConcepts');
    await queryInterface.renameTable('catalogItemTopicsBak', 'catalogItemTopics');
    await queryInterface.renameTable('catalogItemConceptsBak', 'catalogItemConcepts');
    await queryInterface.renameTable('socialCapitalConceptsBak', 'socialCapitalConcepts');
    await queryInterface.removeColumn('listings', 'deletedAt');
    await queryInterface.removeColumn('concepts', 'deletedAt');
    await queryInterface.removeColumn('experts', 'deletedAt');
  },
};
