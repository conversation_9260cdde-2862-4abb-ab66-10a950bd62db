module.exports = {
  up: (queryInterface, Sequelize) => {
    const migrations = [];
    migrations.push(queryInterface.changeColumn(
      'assessmentItems',
      'assessmentType',
      {
        type: Sequelize.ENUM('Benchmark', 'Knowledge', 'Survey', 'Climate'),
        allowNull: true,
      },
    ));
    migrations.push(queryInterface.addColumn(
      'assessmentItems', 'isSJT',
      { type: Sequelize.BOOLEAN, defaultValue: false, allowNull: true },
    ));
    migrations.push(queryInterface.addColumn(
      'assessmentItems', 'indicatorId',
      {
        type: Sequelize.INTEGER,
        references: {
          model: 'socialCapitalIndicators',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
    ));
    migrations.push(queryInterface.addColumn(
      'assessmentItems', 'noOfOptions',
      { type: Sequelize.INTEGER, allowNull: true },
    ));
    migrations.push(queryInterface.removeColumn('assessmentItems', 'itemExceptions'));
    migrations.push(queryInterface.removeColumn('assessmentItems', 'externalId'));
    migrations.push(queryInterface.removeColumn('assessmentItems', 'indicator'));
    migrations.push(queryInterface.removeColumn('assessmentItems', 'competencyEthics'));
    migrations.push(queryInterface.removeColumn('assessmentItems', 'competencyInclusion'));
    migrations.push(queryInterface.removeColumn('assessmentItems', 'competencyRespect'));

    return Promise.all(migrations);
  },

  down: (queryInterface, Sequelize) => {
    const migrations = [];
    migrations.push(queryInterface.changeColumn(
      'assessmentItems',
      'assessmentType',
      {
        type: Sequelize.ENUM('Benchmark', 'Knowledge', 'SJT', 'Survey', 'Climate'),
        allowNull: true,
      },
    ));
    migrations.push(queryInterface.addColumn('assessmentItems', 'itemExceptions', Sequelize.STRING));
    migrations.push(queryInterface.addColumn('assessmentItems', 'externalId', Sequelize.INTEGER));
    migrations.push(queryInterface.addColumn('assessmentItems', 'indicator', Sequelize.ENUM));
    migrations.push(queryInterface.addColumn('assessmentItems', 'competencyEthics', Sequelize.BOOLEAN));
    migrations.push(queryInterface.addColumn('assessmentItems', 'competencyInclusion', Sequelize.BOOLEAN));
    migrations.push(queryInterface.addColumn('assessmentItems', 'competencyRespect', Sequelize.BOOLEAN));
    migrations.push(queryInterface.removeColumn('assessmentItems', 'isSJT'));
    migrations.push(queryInterface.removeColumn('assessmentItems', 'indicatorId'));

    return Promise.all(migrations);
  },
};
