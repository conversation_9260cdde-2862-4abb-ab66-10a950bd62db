module.exports = {
  up: (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.changeColumn(
      'lessons',
      'lifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired'),
        allowNull: false,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'programs',
      'lifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired'),
        allowNull: false,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'events',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'active', 'retired'),
        defaultValue: null,
        allowNull: true,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'lastViewedLessonCards',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired'),
        defaultValue: 'publish',
        allowNull: false,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'userLessons',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired'),
        defaultValue: 'publish',
        allowNull: false,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'answerCards',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired'),
        defaultValue: 'publish',
        allowNull: false,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'viewLessonCardEvents',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'retired'),
        defaultValue: null,
        allowNull: true,
      },
    ));
    return Promise.all(promises);
  },

  down: (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const promises = [];
    promises.push(queryInterface.changeColumn(
      'lessons',
      'lifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close'),
        allowNull: false,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'programs',
      'lifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close'),
        allowNull: false,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'events',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close', 'active'),
        defaultValue: null,
        allowNull: true,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'lastViewedLessonCards',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close'),
        defaultValue: 'publish',
        allowNull: false,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'userLessons',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close'),
        defaultValue: 'publish',
        allowNull: false,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'answerCards',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close'),
        defaultValue: 'publish',
        allowNull: false,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'viewLessonCardEvents',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close'),
        defaultValue: null,
        allowNull: true,
      },
    ));
    return Promise.all(promises);
  },
};
