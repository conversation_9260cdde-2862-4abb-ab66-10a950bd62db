/* eslint-disable max-len */
const logger = require('../../logger');

module.exports = {
  async up(queryInterface) {
    try {
      const promise = [];
      promise.push(queryInterface.sequelize.query("UPDATE socialCapitalIndicators SET `name` = 'Connecting In- & Out-Groups' where `name` = 'Embracing Interculturalism'"));
      promise.push(queryInterface.sequelize.query("UPDATE socialCapitalIndicators SET `name` = 'Ensuring Equality' where `name` = 'Ensuring Equity'"));
      promise.push(queryInterface.sequelize.query("UPDATE resourceAssets SET `resourceType` = 'Emtrain Culture Skills Model' where `resourceType` = 'Emtrain Social Capital Model'"));
      await Promise.all(promise);
    } catch (err) {
      logger.error(err.message);
    }
  },

  async down(queryInterface) {
    try {
      const promise = [];
      promise.push(queryInterface.sequelize.query("UPDATE socialCapitalIndicators SET `name` = 'Embracing Interculturalism' where `name` = 'Connecting In- & Out-Groups'"));
      promise.push(queryInterface.sequelize.query("UPDATE socialCapitalIndicators SET `name` = 'Ensuring Equity' where `name` = 'Ensuring Equality'"));
      promise.push(queryInterface.sequelize.query("UPDATE resourceAssets SET `resourceType` = 'Emtrain Social Capital Model' where `resourceType` = 'Emtrain Culture Skills Model'"));
      await Promise.all(promise);
    } catch (err) {
      logger.error(err.message);
    }
  },
};
