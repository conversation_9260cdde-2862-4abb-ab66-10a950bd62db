module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn(
      'groups',
      'isArchived',
      { type: Sequelize.BOOLEAN, defaultValue: false, allowNull: false },
    );
    await queryInterface.addColumn(
      'groups',
      'archivedAt',
      { type: Sequelize.DATE, allowNull: true },
    );
  },
  // eslint-disable-next-line no-unused-vars
  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('groups', 'isArchived');
    await queryInterface.removeColumn('groups', 'archivedAt');
  },
};
