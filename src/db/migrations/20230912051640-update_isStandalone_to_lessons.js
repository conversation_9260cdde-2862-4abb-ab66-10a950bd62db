/* eslint-disable no-restricted-globals */
const csvToJson = require('csvtojson');
const path = require('path');
const { Op } = require('sequelize');
const db = require('../../db');
const logger = require('../../logger');

const Lessons = db.lessons;

const readFileIntoRows = (filename) => {
  const fullPathCatalogItems = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathCatalogItems);
};

const updateLessons = async () => {
  const lessonData = await readFileIntoRows('Lessons_Standalone_Update.csv');
  const enabledStandaloneLessons = [];
  const disabledStandaloneLessons = [];
  lessonData.forEach((element) => {
    if (parseInt(element.isStandalone) === 0) {
      disabledStandaloneLessons.push(element.lessonId);
    } else {
      enabledStandaloneLessons.push(element.lessonId);
    }
  });
  const promise = [];
  promise.push(Lessons.update({ isStandalone: 1 }, { where: { id: { [Op.in]: enabledStandaloneLessons } } }));
  promise.push(Lessons.update({ isStandalone: 0 }, { where: { id: { [Op.in]: disabledStandaloneLessons } } }));
  await Promise.all(promise);
};
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      await updateLessons();
    } catch (err) {
      logger.error(err);
    }
  },

  async down(queryInterface, Sequelize) {
    // no going back
  },
};
