module.exports = {
  up: (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.changeColumn('events', 'trackableType', {
      type: Sequelize.ENUM(
        'site', 'resource', 'mediaAssets', 'users', 'tags', 'topics',
        'sendLinks', 'groups', 'programs', 'campaign', 'video', 'csvImport', 'resourceAssets',
      ),
      allowNull: false,
    }));
    promises.push(queryInterface.changeColumn('userLessons', 'type', {
      type: Sequelize.ENUM('program', 'lesson', 'mediaAsset', 'questionAnswer', 'campaign', 'resourceAsset'),
    }));
    promises.push(queryInterface.changeColumn('campaignItem', 'itemType', {
      type: Sequelize.ENUM('program', 'lesson', 'mediaAsset', 'questionAnswer', 'resourceAsset'),
      allowNull: false,
    }));
    promises.push(queryInterface.changeColumn('contentStrings', 'model', {
      type: Sequelize.ENUM('program', 'lesson', 'lessonCard', 'mediaAsset', 'questionAnswer', 'resourceAsset'),
      allowNull: false,
    }));
    promises.push(queryInterface.changeColumn('licenseTracking', 'contentType', {
      type: Sequelize.ENUM('program', 'lesson', 'lessonCard', 'mediaAsset', 'questionAnswer', 'resourceAsset'),
      allowNull: false,
    }));
    promises.push(queryInterface.addColumn('resourceAssets', 'createdBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
    }));
    promises.push(queryInterface.addColumn('resourceAssets', 'updatedBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
    }));
    promises.push(queryInterface.addColumn('resourceAssets', 'deletedAt', {
      type: Sequelize.DATE,
      allowNull: true,
    }));
    return Promise.all(promises);
  },

  down: (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const promises = [];

    promises.push(queryInterface.changeColumn('events', 'trackableType', {
      type: Sequelize.ENUM(
        'site', 'resource', 'mediaAssets', 'users', 'tags', 'topics',
        'sendLinks', 'groups', 'programs', 'campaign', 'video', 'csvImport',
      ),
      allowNull: false,
    }));
    promises.push(queryInterface.changeColumn('userLessons', 'type', {
      type: Sequelize.ENUM('program', 'lesson', 'mediaAsset', 'questionAnswer', 'campaign'),
    }));
    promises.push(queryInterface.changeColumn('campaignItem', 'itemType', {
      type: Sequelize.ENUM('program', 'lesson', 'mediaAsset', 'questionAnswer'),
      allowNull: false,
    }));
    promises.push(queryInterface.changeColumn('contentStrings', 'model', {
      type: Sequelize.ENUM('program', 'lesson', 'lessonCard', 'mediaAsset', 'questionAnswer'),
      allowNull: false,
    }));
    promises.push(queryInterface.changeColumn('licenseTracking', 'contentType', {
      type: Sequelize.ENUM('program', 'lesson', 'lessonCard', 'mediaAsset', 'questionAnswer'),
      allowNull: false,
    }));
    promises.push(queryInterface.removeColumn('resourceAssets', 'createdBy'));
    promises.push(queryInterface.removeColumn('resourceAssets', 'updatedBy'));
    promises.push(queryInterface.removeColumn('resourceAssets', 'deletedAt'));
    return Promise.all(promises);
  },
};
