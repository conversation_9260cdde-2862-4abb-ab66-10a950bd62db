'use strict';
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('groupRules', 'operator', {
      type: Sequelize.ENUM('is', 'isNot', 'contains', 'doesNotContain', 'isBefore', 'isAfter', 'isMoreThen'),
      allowNull: false,
    });
    await queryInterface.addIndex('users', {
      fields: ['hireDate'],
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('groupRules', 'operator', {
      type: Sequelize.ENUM('is', 'isNot', 'contains', 'doesNotContain', 'isBefore', 'isAfter'),
      allowNull: false,
    });

    await queryInterface.removeIndex('users', {
      fields: ['hireDate'],
    });
  },
};