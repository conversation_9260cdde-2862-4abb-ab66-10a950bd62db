module.exports = {
  up: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.changeColumn('users', 'exempt', {
      type: Sequelize.BOOLEAN,
      defaultValue: null,
    });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.changeColumn('users', 'exempt', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    });
  },
};
