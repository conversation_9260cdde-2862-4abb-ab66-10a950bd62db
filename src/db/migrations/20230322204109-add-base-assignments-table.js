module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('baseAssignments', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      accountId: {
        allowNull: false,
        type: Sequelize.INTEGER,
        references: {
          model: 'accounts',
          key: 'id',
        },
      },
      assignmentType: {
        type: Sequelize.ENUM,
        values: ['shared', 'scorm'],
      },
      status: {
        type: Sequelize.ENUM,
        values: ['open', 'closed'],
      },
      startDate: {
        allowNull: true,
        type: Sequelize.DATE,
      },
      endDate: {
        allowNull: true,
        type: Sequelize.DATE,
      },
      dueDate: {
        allowNull: true,
        type: Sequelize.DATE,
      },
      expireDate: {
        allowNull: true,
        type: Sequelize.DATE,
      },
      groupUpdatesProcessedAt: {
        allowNull: true,
        type: Sequelize.DATE,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
    });
    await queryInterface.createTable('baseAssignmentPrograms', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      baseAssignmentId: {
        allowNull: false,
        type: Sequelize.INTEGER,
        references: {
          model: 'baseAssignments',
          key: 'id',
        },
      },
      programId: {
        allowNull: false,
        type: Sequelize.INTEGER,
        references: {
          model: 'programs',
          key: 'id',
        },
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.createTable('baseAssignmentLessons', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      baseAssignmentId: {
        allowNull: false,
        type: Sequelize.INTEGER,
        references: {
          model: 'baseAssignments',
          key: 'id',
        },
      },
      lessonId: {
        allowNull: false,
        type: Sequelize.INTEGER,
        references: {
          model: 'lessons',
          key: 'id',
        },
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.createTable('baseAssignmentGroups', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      baseAssignmentId: {
        allowNull: false,
        type: Sequelize.INTEGER,
        references: {
          model: 'baseAssignments',
          key: 'id',
        },
      },
      groupId: {
        allowNull: false,
        type: Sequelize.INTEGER,
        references: {
          model: 'groups',
          key: 'id',
        },
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.createTable('baseAssignmentUsers', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      baseAssignmentId: {
        allowNull: false,
        type: Sequelize.INTEGER,
        references: {
          model: 'baseAssignments',
          key: 'id',
        },
      },
      userId: {
        allowNull: false,
        type: Sequelize.INTEGER,
        references: {
          model: 'users',
          key: 'id',
        },
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addColumn('userLessons', 'baseAssignmentId', {
      type: Sequelize.INTEGER,
    });
    await queryInterface.addColumn('userLessons', 'baseAssignmentParentProgramId', {
      type: Sequelize.INTEGER,
    });
    await queryInterface.addIndex('userLessons', ['baseAssignmentId']);
    await queryInterface.addIndex('baseAssignments', { fields: ['assignmentType', 'status'] });
  },

  async down(queryInterface) {
    await queryInterface.removeIndex('baseAssignments', { fields: ['assignmentType', 'status'] });
    await queryInterface.removeIndex('userLessons', ['baseAssignmentId']);
    await queryInterface.removeColumn('userLessons', 'baseAssignmentId');
    await queryInterface.removeColumn('userLessons', 'baseAssignmentParentProgramId');
    await queryInterface.dropTable('baseAssignmentUsers');
    await queryInterface.dropTable('baseAssignmentGroups');
    await queryInterface.dropTable('baseAssignmentLessons');
    await queryInterface.dropTable('baseAssignmentPrograms');
    await queryInterface.dropTable('baseAssignments');
  },
};

