const db = require('../../db');

const Op = db.Sequelize.Op;

module.exports = {
  up: (queryInterface, Sequelize) => {
    const migrations = [];
    migrations.push(queryInterface.changeColumn('accounts', 'hasAnalytics', {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    }));
    migrations.push(db.accounts.update(
      { hasAnalytics: true },
      {
        where: {
          deletedAt: {
            [Op.eq]: null,
          },
        },
      },
    ));
    return Promise.all(migrations);
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('accounts', 'hasAnalytics', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    }); // no going back on data change
  },
};
