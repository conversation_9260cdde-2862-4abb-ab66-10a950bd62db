module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('userLessons', 'requiredSecondsTaken', {
      type: Sequelize.DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: true,
    });
    return queryInterface.sequelize.query('UPDATE userLessons SET requiredSecondsTaken = elapsedTimeInSeconds WHERE type = \'program\'');
  },

  // eslint-disable-next-line no-unused-vars
  async down(queryInterface, Sequelize) {
    return queryInterface.removeColumn('userLessons', 'requiredSecondsTaken');
  },
};
