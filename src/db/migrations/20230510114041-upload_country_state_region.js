const path = require('path');
const csvToJson = require('csvtojson');
const db = require('..');

const readFileIntoRows = (filename) => {
  const fullFilePath = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullFilePath);
};

// eslint-disable-next-line consistent-return
const saveRegions = async () => {
  const regionCountryData = await readFileIntoRows('region-country.csv');
  const regionData = [];
  for await (const row of regionCountryData) {
    const regionName = row.region;
    const subRegion = row['sub-region'];
    const obj = {
      region: regionName,
      subRegion,
      createdBy: 1,
    };
    if (regionData.filter(e => e.region === regionName && e.subRegion === subRegion).length <= 0
      && regionName !== '""' && !regionName.startsWith('ISO ') && regionName !== '') {
      regionData.push(obj);
      await db.regions.upsert(obj);
    }
  }
};
const getRegions = async () => {
  const regions = await db.sequelize.query('SELECT * FROM regions', {
    type: db.sequelize.QueryTypes.SELECT,
  });
  return regions;
};
const findRegionId = (regionName, subRegionName, regions) => {
  return regions.find(region => region.region === regionName && region.subRegion === subRegionName);
};
const saveCountries = async () => {
  const regionCountryData = await readFileIntoRows('region-country.csv');
  const regionsList = await getRegions();
  for await (const row of regionCountryData) {
    let regionId = await findRegionId(row.region, row['sub-region'], regionsList);
    regionId = regionId ? regionId.id : null;
    const countryData = {
      countryName: row.name,
      countryCode: row['alpha-3'],
      regionId,
      createdBy: 1,
    };
    await db.countries.upsert(countryData);
  }
};
const getCountries = async () => {
  const countries = await db.sequelize
    .query('SELECT id, countryName FROM countries where countryName IN ("Canada", "United States of America")', {
      type: db.sequelize.QueryTypes.SELECT,
    });
  return countries;
};
const saveUsaStates = async (usId) => {
  const usaData = await readFileIntoRows('usaStates.csv');
  for await (const row of usaData) {
    const stateObj = {
      stateCode: row.stateCode,
      stateName: row.stateName,
      countryId: usId,
      createdBy: 1,
    };
    await db.states.upsert(stateObj);
  }
};
const saveCanadaStates = async (canadaId) => {
  const canadaData = await readFileIntoRows('canadaStates.csv');
  for await (const row of canadaData) {
    const stateObj = {
      stateCode: row.stateCode,
      stateName: row.stateName,
      countryId: canadaId,
      createdBy: 1,
    };
    await db.states.upsert(stateObj);
  }
};

module.exports = {
  up: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    // truncate the tables first
    await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 0');
    await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 1');
    // regions seed
    await saveRegions();
    // countries seed
    await saveCountries();

    // global region, all country
    const regionGlobal = {
      id: 18,
      region: 'ALL',
      subRegion: 'ALL',
      createdBy: 1,
    };
    const international = {
      id: 250,
      countryName: 'International',
      countryCode: 'intl',
      regionId: 18,
    };
    await db.regions.upsert(regionGlobal, { returning: true });
    await db.countries.upsert(international);

    // states seed
    const countryIds = await getCountries();
    const canadaId = countryIds[0].id;
    const usId = countryIds[1].id;
    await saveUsaStates(usId);
    await saveCanadaStates(canadaId);
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.sequelize.query('TRUNCATE TABLE regions')
      .then(() => {
        queryInterface.sequelize.query('TRUNCATE TABLE  countries');
      })
      .then(() => {
        queryInterface.sequelize.query('TRUNCATE TABLE states');
      });
  },
};
