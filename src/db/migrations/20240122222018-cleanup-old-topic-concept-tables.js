'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.dropTable('topicConcepts');
    await queryInterface.dropTable('catalogItemConceptsBak');
    await queryInterface.dropTable('catalogItemTopicsBak');
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.createTable('topicConcepts', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      topicId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        allowNull: false,
      },
      socialCapitalConceptId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex(
      'topicConcepts',
      ['topicId', 'socialCapitalConceptId'],
      { unique: true },
    );

    await queryInterface.createTable('catalogItemConceptsBak', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      catalogItemId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        allowNull: false,
      },
      socialCapitalConceptId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
    });
    await queryInterface.addIndex(
      'catalogItemConceptsBak',
      ['catalogItemId', 'socialCapitalConceptId'],
      { name: 'catalog_items_concept_fields', unique: true },
    );

    await queryInterface.createTable('catalogItemTopicsBak', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      catalogItemId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        allowNull: false,
      },
      topicId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        allowNull: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex(
      'catalogItemTopicsBak',
      ['catalogItemId', 'topicId'],
      { name: 'catalog_items_topic_fields', unique: true },
    );
  }
};
