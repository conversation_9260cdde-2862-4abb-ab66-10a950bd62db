const csvToJson = require('csvtojson');
const path = require('path');
const db = require('..');
const logger = require('../../logger');

const Listing = db.listings;

const readFileIntoRows = (filename) => {
  const fullPathListingsData = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathListingsData);
};

const uploadMaketingCopy = async () => {
  const listingData = await readFileIntoRows('Listing-Marketing-Copy.csv');
  for await (const row of listingData) {
    const listingId = parseInt(row.ListingID);
    const marketingCopy = row.MarketingCopy;
    if (listingId) {
      await Listing.update({ marketingCopy }, { where: { id: listingId } });
    }
  }
};

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up() {
    try {
      await uploadMaketingCopy();
    } catch (err) {
      logger.error(err);
    }
  },
  async down() {
    // no going back
  },
};
