module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('contentLibrary', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      mclContentId: {
        type: Sequelize.INTEGER,
      },
      accountId: {
        type: Sequelize.INTEGER,
      },
      type: {
        type: Sequelize.STRING,
      },
      title: {
        type: Sequelize.STRING,
      },
      description: {
        type: Sequelize.TEXT,
      },
      listingRank: {
        type: Sequelize.INTEGER,
      },
      edition: {
        type: Sequelize.INTEGER,
      },
      audience: {
        type: Sequelize.ENUM,
        values: ['employee', 'manager', 'all'],
      },
      duration: {
        type: Sequelize.INTEGER,
      },
      part: {
        type: Sequelize.ENUM,
        values: ['a', 'b', 'ab'],
      },
      isClientSpecific: {
        type: Sequelize.BOOLEAN,
      },
      instructionalType: {
        type: Sequelize.ENUM,
        values: ['course', 'lesson', 'microlesson', 'diagnostic'],
      },
      countryId: {
        type: Sequelize.INTEGER,
      },
      countryName: {
        type: Sequelize.STRING,
      },
      listType: {
        type: Sequelize.ENUM,
        values: ['listing', 'catalog'],
        allowNull: false,
      },
      fileId: {
        type: Sequelize.INTEGER,
      },
      filePath: {
        type: Sequelize.STRING,
      },
      stateName: {
        type: Sequelize.STRING,
      },
      stateCode: {
        type: Sequelize.STRING,
      },
      pillars: {
        type: Sequelize.JSON,
      },
      indicators: {
        type: Sequelize.JSON,
      },
      concepts: {
        type: Sequelize.JSON,
      },
      programs: {
        type: Sequelize.JSON,
      },
      lessons: {
        type: Sequelize.JSON,
      },
      catalogItems: {
        type: Sequelize.JSON,
      },
      lastMappedAt: {
        type: Sequelize.DATE,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex(
      'contentLibrary',
      ['mclContentId', 'accountId', 'listType'],
      { unique: true },
    );
  },

  async down(queryInterface) {
    return queryInterface.dropTable('contentLibrary');
  },
};
