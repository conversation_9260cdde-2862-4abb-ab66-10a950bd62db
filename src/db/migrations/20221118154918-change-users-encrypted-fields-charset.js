/** @type {import('sequelize-cli').Migration} */
module.exports = {
  // Since the encrypted values do not contain any characters outside of latin1 there is no
  // need to convert to binary before changing the character set as described here:
  // https://coderwall.com/p/gjyuwg/mysql-convert-encoding-to-utf8-without-garbled-data
  // Also, the default collation is utf8mb4_general_ci, so no need to specify.
  async up(queryInterface, Sequelize) {
    return queryInterface.sequelize.query(`
      ALTER TABLE users MODIFY firstName VARCHAR(255) CHARACTER SET utf8mb4;
      ALTER TABLE users MODIFY lastName VARCHAR(255) CHARACTER SET utf8mb4;
      ALTER TABLE users MODIFY email VARCHAR(255) CHARACTER SET utf8mb4;
      ALTER TABLE users MODIFY scormId VARCHAR(255) CHARACTER SET utf8mb4;
      ALTER TABLE users MODIFY employeeId VARCHAR(255) CHARACTER SET utf8mb4;
      ALTER TABLE users MODIFY notificationEmail VARCHAR(255) CHARACTER SET utf8mb4;
    `);
  },
  // If this is performed after values are decrypted and they contain characters outside the
  // latin1 character set the rollback will fail starting with that column.
  async down(queryInterface, Sequelize) {
    return queryInterface.sequelize.query(`
      ALTER TABLE users MODIFY firstName VARCHAR(255) CHARACTER SET latin1;
      ALTER TABLE users MODIFY lastName VARCHAR(255) CHARACTER SET latin1;
      ALTER TABLE users MODIFY email VARCHAR(255) CHARACTER SET latin1;
      ALTER TABLE users MODIFY scormId VARCHAR(255) CHARACTER SET latin1;
      ALTER TABLE users MODIFY employeeId VARCHAR(255) CHARACTER SET latin1;
      ALTER TABLE users MODIFY notificationEmail VARCHAR(255) CHARACTER SET latin1;
    `);
  },
};
