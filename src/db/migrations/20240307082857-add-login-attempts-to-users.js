module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.addColumn('users', 'failedLoginAttempts', {
          type: Sequelize.DataTypes.INTEGER,
          defaultValue: 0,
        });
      })
      .then(() => {
        return queryInterface.addColumn('users', 'failedLoginAt', {
          type: Sequelize.DataTypes.DATE,
        });
      })
      .then(() => {
        return queryInterface.addColumn('users', 'accountLocked', {
          type: Sequelize.DataTypes.BOOLEAN,
          defaultValue: false,
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeColumn('users', 'failedLoginAttempts')
      .then(() => {
        return queryInterface.removeColumn('users', 'failedLoginAt');
      })
      .then(() => {
        return queryInterface.removeColumn('users', 'accountLocked');
      });
  },
};
