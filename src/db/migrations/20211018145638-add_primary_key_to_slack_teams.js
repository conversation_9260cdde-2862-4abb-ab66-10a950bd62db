module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query('ALTER TABLE slackTeams DROP PRIMARY KEY');
    await queryInterface.renameColumn('slackTeams', 'id', 'slackTeamId');
    await queryInterface.addColumn('slackTeams', 'id', {
      type: Sequelize.INTEGER,
      allowNull: false,
      autoIncrement: true,
      primaryKey: true,
    });
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('slackTeams', 'id');
    await queryInterface.renameColumn('slackTeams', 'slackTeamId', 'id');
    await queryInterface.changeColumn('slackTeams', 'id', {
      type: Sequelize.STRING,
      primaryKey: true,
    });
  },
};
