module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.addColumn('accounts', 'hasAnalytics', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        });
      })
      .then(() => {
        return queryInterface.addColumn('accounts', 'licenceLevel', {
          type: Sequelize.ENUM,
          values: ['tier1', 'tier2', 'tier3'],
          defaultValue: 'tier1',
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeColumn('accounts', 'hasAnalytics')
      .then(() => {
        return queryInterface.removeColumn('accounts', 'licenceLevel');
      });
  },
};
