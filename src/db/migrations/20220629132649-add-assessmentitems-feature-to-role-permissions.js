const _ = require('lodash');

const permissionsTree = [
  {
    role: 'guest',
    allows: [
      {
        features: ['assessmentItems'],
        permissions: ['read'],
      },
    ],
  },
  {
    role: 'user', // user is also a guest
    allows: [
      {
        features: ['assessmentItems'],
        permissions: ['read'],
      },
    ],
  },
  {
    role: 'expert', // expert is also a user
    allows: [
      {
        features: ['assessmentItems'],
        permissions: ['create', 'read', 'update', 'delete'],
      },
    ],
  },
  {
    role: 'accountAdmin', // accountAdmin is also a user
    allows: [
      {
        features: ['assessmentItems'],
        permissions: ['create', 'read', 'update', 'delete'],
      },
    ],
  },
  {
    role: 'systemAdmin', // systemAd<PERSON> is also an expert
    allows: [
      {
        features: ['assessmentItems'],
        permissions: ['create', 'read', 'update', 'delete'],
      },
    ],
  },
];

const getRoleFeaturePermissionTuples = (roleIds, featureIds, permissionIds) => {
  const data = permissionsTree.map((roleSection) => {
    const roleId = roleIds[roleSection.role];
    const allows = roleSection.allows.map((allowSection) => {
      const features = allowSection.features.map((f) => {
        const permissions = allowSection.permissions.map((p) => {
          return {
            roleId,
            featureId: featureIds[f],
            permissionId: permissionIds[p],
          };
        });
        return permissions;
      });
      return _.flatten(features);
    });
    return _.flatten(allows);
  });
  return _.flatten(data);
};

module.exports = {
  up: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const roleResults = await queryInterface.sequelize.query('select id, name from roles', {
      type: Sequelize.QueryTypes.SELECT,
    });
    const roleIds = roleResults.reduce((acc, role) => {
      return { ...acc, [role.name]: role.id };
    }, {});

    const featureResults = await queryInterface.sequelize.query('select id, name from features where name="assessmentItems" limit 1', {
      type: Sequelize.QueryTypes.SELECT,
    });

    const featureIds = featureResults.reduce((acc, feature) => {
      return { ...acc, [feature.name]: feature.id };
    }, {});

    const permResults = await queryInterface.sequelize.query('select id, name from permissions', {
      type: Sequelize.QueryTypes.SELECT,
    });
    const permissionIds = permResults.reduce((acc, perm) => {
      return { ...acc, [perm.name]: perm.id };
    }, {});
    const roleFeaturePermissionTuples = getRoleFeaturePermissionTuples(roleIds, featureIds, permissionIds);

    const roleFeaturePermissions = roleFeaturePermissionTuples.map(({ roleId, featureId, permissionId }) => {
      return {
        roleId,
        featureId,
        permissionId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    });

    // For super admins, just take the cartesian product of features and permissions
    const superAdminDataData = featureResults.map(f => permResults.map((p) => {
      return {
        roleId: roleIds.superAdmin,
        featureId: f.id,
        permissionId: p.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }));
    const superAdminData = _.flatten(superAdminDataData);
    const result = [
      await queryInterface.bulkInsert('roleFeaturePermissions', roleFeaturePermissions),
      await queryInterface.bulkInsert('roleFeaturePermissions', superAdminData),
    ];
    return Promise.resolve(result);
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query('select id, name from features where name="assessmentItems" limit 1', {
      type: Sequelize.QueryTypes.SELECT,
    }).then((features) => {
      queryInterface.bulkDelete('roleFeaturePermissions', { featureId: features[0].id });
    });
  },
};
