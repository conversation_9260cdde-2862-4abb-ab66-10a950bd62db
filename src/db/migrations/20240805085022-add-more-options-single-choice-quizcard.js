'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {

    await queryInterface.sequelize.transaction(async (transaction) => {

      // Update columns in lessonCards
      const promise = [];
      promise.push(queryInterface.addColumn('lessonCards', 'choice5', {
        type: Sequelize.BOOLEAN,
      }, { transaction }));
      promise.push(queryInterface.addColumn('lessonCards', 'choice6', {
        type: Sequelize.BOOLEAN,
      }, { transaction }));
      promise.push(queryInterface.addColumn('lessonCards', 'choice7', {
        type: Sequelize.BOOLEAN,
      }, { transaction }));
      promise.push(queryInterface.addColumn('lessonCards', 'choice8', {
        type: Sequelize.BOOLEAN,
      }, { transaction }));
      promise.push(queryInterface.addColumn('lessonCards', 'choice9', {
        type: Sequelize.BOOLEAN,
      }, { transaction }));
      promise.push(queryInterface.addColumn('lessonCards', 'choice10', {
        type: Sequelize.BOOLEAN,
      }, { transaction }));
      await Promise.all(promise);

      // Update columns in answerCards
      const promise2 = []
      promise2.push(queryInterface.addColumn('answerCards', 'answer6', {
        type: Sequelize.INTEGER,
      }, { transaction }));
      promise2.push(queryInterface.addColumn('answerCards', 'answer7', {
        type: Sequelize.INTEGER,
      }, { transaction }));
      promise2.push(queryInterface.addColumn('answerCards', 'answer8', {
        type: Sequelize.INTEGER,
      }, { transaction }));
      promise2.push(queryInterface.addColumn('answerCards', 'answer9', {
        type: Sequelize.INTEGER,
      }, { transaction }));
      promise2.push(queryInterface.addColumn('answerCards', 'answer10', {
        type: Sequelize.INTEGER,
      }, { transaction }));
      await Promise.all(promise2);


    });
  },

  down: async (queryInterface, Sequelize) => {

    // Revert changes
    await queryInterface.sequelize.transaction(async (transaction) => {
      const promise = []
      promise.push(queryInterface.removeColumn('lessonCards', 'choice5', { transaction }));
      promise.push(queryInterface.removeColumn('lessonCards', 'choice6', { transaction }));
      promise.push(queryInterface.removeColumn('lessonCards', 'choice7', { transaction }));
      promise.push(queryInterface.removeColumn('lessonCards', 'choice8', { transaction }));
      promise.push(queryInterface.removeColumn('lessonCards', 'choice9', { transaction }));
      promise.push(queryInterface.removeColumn('lessonCards', 'choice10', { transaction }));
      promise.push(queryInterface.removeColumn('answerCards', 'answer6', { transaction }));
      promise.push(queryInterface.removeColumn('answerCards', 'answer7', { transaction }));
      promise.push(queryInterface.removeColumn('answerCards', 'answer8', { transaction }));
      promise.push(queryInterface.removeColumn('answerCards', 'answer9', { transaction }));
      promise.push(queryInterface.removeColumn('answerCards', 'answer10', { transaction }));
      await Promise.all(promise);
    });
  },
};
