'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('listings', 'isSpecialPwhUs', {
      type: Sequelize.Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });
    await queryInterface.addColumn('catalogItems', 'pwhUsOrder', {
      type: Sequelize.Sequelize.INTEGER,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('listings', 'isSpecialPwhUs');
    await queryInterface.removeColumn('catalogItems', 'pwhUsOrder');
  }
};
