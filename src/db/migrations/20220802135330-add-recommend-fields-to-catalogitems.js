module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.addColumn('catalogItems', 'recommendType', {
          type: Sequelize.ENUM,
          values: ['recommend', 'toprecommend', 'both'],
          allowNull: true,
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeColumn('catalogItems', 'recommendType');
  },
};
