const db = require('../../db');
const logger = require('../../logger');

const Concepts = db.concepts;
const Categories = db.categories;

// create categories data
const createCategories = async (queryInterface) => {
  try {
    // get unique concepts data based on category
    const conceptsData = await queryInterface.sequelize.query(
      'SELECT DISTINCT `priority`, `category` FROM `concepts` AS `concepts` WHERE (`concepts`.`deletedAt` IS NULL)',
      {
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );
    for await (const data of conceptsData) {
      const categoryObj = {
        priority: data.priority,
        name: data.category,
      };
      // create category record
      const categoryData = await Categories.create(categoryObj);
      // update the categoryId in concepts
      await Concepts.update({ categoryId: categoryData.id }, {
        where: {
          category: data.category,
        },
      });
    }
  } catch (err) {
    logger.error(err);
  }
};

module.exports = {
  async up(queryInterface, Sequelize) {
    // create new categories table
    await queryInterface.createTable('categories', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      priority: {
        allowNull: false,
        type: Sequelize.INTEGER,
      },
      name: {
        allowNull: false,
        type: Sequelize.STRING,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
    });
    // add categoryId in concepts
    await queryInterface.addColumn('concepts', 'categoryId', {
      type: Sequelize.INTEGER,
      references: {
        model: 'categories',
        key: 'id',
      },
    });
    // create data in categories table
    await createCategories(queryInterface);
    // remove priority & category from concepts
    await queryInterface.removeColumn('concepts', 'priority');
    await queryInterface.removeColumn('concepts', 'category');
  },

  async down(queryInterface, Sequelize) {
    // no going back
  },
};
