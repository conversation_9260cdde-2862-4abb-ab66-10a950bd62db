module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.addColumn('users', 'jobGroup', {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        });
      })
      .then(() => {
        return queryInterface.addColumn('users', 'birthDate', {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        });
      })
      .then(() => {
        return queryInterface.addColumn('users', 'department', {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        });
      })
      .then(() => {
        return queryInterface.addColumn('users', 'orgLevel', {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        });
      })
      .then(() => {
        return queryInterface.addColumn('users', 'sexualOrientation', {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        });
      })
      .then(() => {
        return queryInterface.addColumn('users', 'attiritionDate', {
          type: Sequelize.DataTypes.DATEONLY,
        });
      })
      .then(() => {
        return queryInterface.addColumn('users', 'voluntaryAttrition', {
          type: Sequelize.DataTypes.BOOLEAN,
          allowNull: true,
        });
      })
      .then(() => {
        return queryInterface.addColumn('users', 'remote', {
          type: Sequelize.DataTypes.BOOLEAN,
          allowNull: true,
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeColumn('users', 'jobGroup')
      .then(() => {
        return queryInterface.removeColumn('users', 'birthDate');
      })
      .then(() => {
        return queryInterface.removeColumn('users', 'department');
      })
      .then(() => {
        return queryInterface.removeColumn('users', 'orgLevel');
      })
      .then(() => {
        return queryInterface.removeColumn('users', 'sexualOrientation');
      })
      .then(() => {
        return queryInterface.removeColumn('users', 'attiritionDate');
      })
      .then(() => {
        return queryInterface.removeColumn('users', 'voluntaryAttrition');
      })
      .then(() => {
        return queryInterface.removeColumn('users', 'remote');
      });
  },
};
