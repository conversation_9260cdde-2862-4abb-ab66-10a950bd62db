module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn(
      'users', 'demographicDataRequested',
      { type: Sequelize.BOOLEAN, defaultValue: false },
    );
    await queryInterface.addColumn(
      'users', 'dateOfBirth',
      { type: Sequelize.DATE, defaultValue: null, allowNull: true },
    );
    await queryInterface.addColumn(
      'users', 'gender',
      { type: Sequelize.ENUM('male', 'female', 'non-binary', 'prefer-not-say'),
        allowNull: true },
    );
    await queryInterface.addColumn(
      'users', 'transgender',
      { type: Sequelize.ENUM('yes', 'no', 'prefer-not-say'),
        allowNull: true },
    );
    await queryInterface.addColumn(
      'users', 'heterosexual',
      { type: Sequelize.ENUM('yes', 'no', 'prefer-not-say'),
        allowNull: true },
    );
    await queryInterface.addColumn(
      'users', 'raceEthnicity',
      // eslint-disable-next-line max-len
      { type: Sequelize.ENUM('american-indian-alaskan', 'asian', 'hispanic', 'hawaiian-pacific', 'african', 'white', 'multiple', 'prefer-not-say'),
        allowNull: true },
    );
    await queryInterface.addColumn(
      'users', 'disabilityStatus',
      { type: Sequelize.ENUM('yes', 'no', 'prefer-not-say'),
        allowNull: true },
    );
    await queryInterface.addColumn(
      'users', 'veteranStatus',
      { type: Sequelize.ENUM('yes', 'no', 'prefer-not-say'),
        allowNull: true },
    );
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('users', 'demographicDataRequested');
    await queryInterface.removeColumn('users', 'dateOfBirth');
    await queryInterface.removeColumn('users', 'gender');
    await queryInterface.removeColumn('users', 'transgender');
    await queryInterface.removeColumn('users', 'heterosexual');
    await queryInterface.removeColumn('users', 'raceEthnicity');
    await queryInterface.removeColumn('users', 'disabilityStatus');
    await queryInterface.removeColumn('users', 'veteranStatus');
  },
};

