module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('catalogItemTopics', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      catalogItemId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
      },
      topicId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex(
      'catalogItemTopics',
      ['catalogItemId', 'topicId'],
      { name: 'catalog_items_topic_fields', unique: true },
    );
  },

  async down(queryInterface) {
    return queryInterface.dropTable('catalogItemTopics');
  },
};
