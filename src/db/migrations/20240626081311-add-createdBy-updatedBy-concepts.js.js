module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.addColumn('concepts', 'createdBy', {
          type: Sequelize.DataTypes.INTEGER,
        });
      })
      .then(() => {
        return queryInterface.addColumn('concepts', 'updatedBy', {
          type: Sequelize.DataTypes.INTEGER,
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeColumn('concepts', 'createdBy')
      .then(() => {
        return queryInterface.removeColumn('concepts', 'updatedBy');
      });
  },
};
