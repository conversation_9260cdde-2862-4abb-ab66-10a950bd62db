module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn(
      'lessons',
      'isBase',
      {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    );
    await queryInterface.changeColumn(
      'lessons',
      'isClone',
      {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    );
    await queryInterface.changeColumn(
      'lessons',
      'isCustomized',
      {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    );
    await queryInterface.changeColumn(
      'programs',
      'isBase',
      {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    );
    await queryInterface.changeColumn(
      'programs',
      'isClone',
      {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    );
    await queryInterface.changeColumn(
      'programs',
      'isCustomized',
      {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    );
    return queryInterface.sequelize.query(`
      UPDATE lessons SET isBase = 0 WHERE isBase IS NULL;
      UPDATE lessons SET isClone = 0 WHERE isClone IS NULL;
      UPDATE lessons SET isCustomized = 0 WHERE isCustomized IS NULL;
      UPDATE programs SET isBase = 0 WHERE isBase IS NULL;
      UPDATE programs SET isClone = 0 WHERE isClone IS NULL;
      UPDATE programs SET isCustomized = 0 WHERE isCustomized IS NULL;
    `);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn(
      'lessons',
      'isBase',
      {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    );
    await queryInterface.changeColumn(
      'lessons',
      'isClone',
      {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
    );
    await queryInterface.changeColumn(
      'lessons',
      'isCustomized',
      {
        type: Sequelize.BOOLEAN,
      },
    );
    await queryInterface.changeColumn(
      'programs',
      'isBase',
      {
        type: Sequelize.BOOLEAN,
      },
    );
    await queryInterface.changeColumn(
      'programs',
      'isClone',
      {
        type: Sequelize.BOOLEAN,
      },
    );
    await queryInterface.changeColumn(
      'programs',
      'isCustomized',
      {
        type: Sequelize.BOOLEAN,
      },
    );
    return queryInterface.sequelize.query(`
      UPDATE lessons SET isBase = null WHERE isBase = '0';
      UPDATE lessons SET isClone = null WHERE isClone = '0';
      UPDATE lessons SET isCustomized = null WHERE isCustomized = '0';
      UPDATE programs SET isBase = null WHERE isBase IS NULL;
      UPDATE programs SET isClone = null WHERE isClone = '0';
      UPDATE programs SET isCustomized = null WHERE isCustomized = '0';
    `);
  },
};
