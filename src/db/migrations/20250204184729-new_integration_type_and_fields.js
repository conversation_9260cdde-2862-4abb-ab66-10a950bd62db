

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('integrations', 'clientId', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('integrations', 'tenantAlias', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.changeColumn(
      'integrations',
      'integrationType',
      {
        type: Sequelize.ENUM('okt sso', 'scorm', 'scorm20043rd', 'scorm20044th', 'sftp', 'authentication', 'azure sso', 'google sso', 'onelogin sso', 'centrify sso', 'aragorn', 'workdayCCL'),
      },
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('integrations', 'clientId');
    await queryInterface.removeColumn('integrations', 'tenantAlias');
    await queryInterface.changeColumn(
      'integrations',
      'integrationType',
      {
        type: Sequelize.ENUM('okt sso', 'scorm', 'scorm20043rd', 'scorm20044th', 'sftp', 'authentication', 'azure sso', 'google sso', 'onelogin sso', 'centrify sso', 'aragorn'),
      },
    );
  },
};
