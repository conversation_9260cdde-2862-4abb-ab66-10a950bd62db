module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.addColumn(
      'users',
      'adminAccountId',
      { type: Sequelize.INTEGER, defaultValue: null, allowNull: true, after: 'accountSSOToken' },
    );
  },
  // eslint-disable-next-line no-unused-vars
  down: async (queryInterface, Sequelize) => {
    return queryInterface.removeColumn('users', 'adminAccountId');
  },
};
