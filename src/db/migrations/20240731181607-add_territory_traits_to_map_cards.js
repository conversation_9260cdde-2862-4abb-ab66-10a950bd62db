/* eslint-disable max-len */
module.exports = {
  up: async (queryInterface) => {
    // eslint-disable-next-line max-len
    const territoryDefaultsTraitsSql = 'SELECT mapType, language, traits, abbr FROM protectedTraitsDefaults WHERE abbr IN (\'GU\' , \'VI\', \'PR\', \'AS\', \'MP\')';
    const territoryDefaultsTraits = await queryInterface.sequelize.query(territoryDefaultsTraitsSql, {
      type: queryInterface.sequelize.QueryTypes.SELECT,
    });

    const mapCardsSql = `SELECT llc.lessonId, llc.lessonCardId, sl.language, lc.cardType, lc.list2 mapType 
    FROM lessonCards lc, lessonLessonCards llc, supportedLanguages sl
    WHERE lc.cardType = 'statesMap'
    AND lc.list1 = 'US'
    AND llc.lessonCardId = lc.id
    AND sl.langSupportable = 'lesson'
    AND sl.langSupportableId = llc.lessonId
    AND llc.dateRemoved IS NULL
    AND lc.deletedAt IS NULL`;

    const USmapCards = await queryInterface.sequelize.query(mapCardsSql, {
      type: queryInterface.sequelize.QueryTypes.SELECT,
    });

    // one record for each statesMap card for each lesson language
    USmapCards.forEach(async (card) => {
      const inserts = [];
      const cardTraits = territoryDefaultsTraits.filter(t => t.mapType === card.mapType && t.language === card.language);
      cardTraits.forEach((trait) => {
        inserts.push({
          abbr: trait.abbr,
          lessonCardId: card.lessonCardId,
          traits: trait.traits,
          language: card.language,
          mapType: card.mapType,
          createdAt: new Date(),
        });
      });
      if (inserts.length > 0) {
        await queryInterface.bulkInsert('protectedTraits', inserts);
      }
    });
  },
  down: async (queryInterface) => {
    const sql = `DELETE FROM protectedTraits
    WHERE abbr IN ('GU' , 'VI', 'PR', 'AS', 'MP')`;

    return queryInterface.sequelize.query(sql, {
      type: queryInterface.sequelize.QueryTypes.DELETE,
    });
  },
};
