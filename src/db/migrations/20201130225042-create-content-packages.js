const _ = require('lodash');

const permissionsTree = [
  {
    role: 'superAdmin',
    allows: [
      {
        features: ['contentPackages'],
        permissions: ['create', 'read', 'update', 'delete'],
      },
    ],
  },
  {
    role: 'systemAdmin',
    allows: [
      {
        features: ['contentPackages'],
        permissions: ['create', 'read', 'update', 'delete'],
      },
    ],
  },
];

const getRoleFeaturePermissionTuples = (roleIds, featureIds, permissionIds) => {
  const data = permissionsTree.map((roleSection) => {
    const roleId = roleIds[roleSection.role];
    const allows = roleSection.allows.map((allowSection) => {
      const features = allowSection.features.map((f) => {
        const permissions = allowSection.permissions.map((p) => {
          return {
            roleId,
            featureId: featureIds[f],
            permissionId: permissionIds[p],
          };
        });
        return permissions;
      });
      return _.flatten(features);
    });
    return _.flatten(allows);
  });
  return _.flatten(data);
};


module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('contentPackages', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      name: {
        type: Sequelize.DataTypes.STRING,
      },
      description: {
        type: Sequelize.DataTypes.STRING,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      },
    });
    await queryInterface.createTable('contentPackageResources', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      contentPackageId: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      resourceId: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      },
    });
    await queryInterface.createTable('accountContentPackages', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      contentPackageId: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      accountId: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      },
    });

    const result = await queryInterface.sequelize.query('INSERT INTO features ' +
      '(name, createdAt, updatedAt) VALUES (\'contentPackages\',now(),now())');
    const featureIds = { contentPackages: result[0] };
    const roleResults = await queryInterface.sequelize.query('select id, name from roles', {
      type: Sequelize.QueryTypes.SELECT,
    });
    const roleIds = roleResults.reduce((acc, role) => {
      return { ...acc, [role.name]: role.id };
    }, {});

    const permResults = await queryInterface.sequelize.query('select id, name from permissions', {
      type: Sequelize.QueryTypes.SELECT,
    });
    const permissionIds = permResults.reduce((acc, perm) => {
      return { ...acc, [perm.name]: perm.id };
    }, {});
    const roleFeaturePermissionTuples = getRoleFeaturePermissionTuples(roleIds, featureIds, permissionIds);

    const roleFeaturePermissions = roleFeaturePermissionTuples.map(({ roleId, featureId, permissionId }) => {
      return {
        roleId,
        featureId,
        permissionId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    });

    await queryInterface.bulkInsert('roleFeaturePermissions', roleFeaturePermissions);
  },
  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    await queryInterface.dropTable('contentPackages');
    await queryInterface.dropTable('contentPackageResources');
    await queryInterface.dropTable('accountContentPackages');
    const result = await queryInterface.sequelize.query(
      'SELECT id FROM features where name=\'contentPackages\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT },
    );
    await queryInterface.sequelize.query('DELETE FROM roleFeaturePermissions WHERE featureId=?', {
      type: queryInterface.sequelize.QueryTypes.DELETE,
      replacements: [
        result[0].id,
      ],
    });
    await queryInterface.sequelize.query('DELETE FROM features WHERE name=?', {
      type: queryInterface.sequelize.QueryTypes.DELETE,
      replacements: ['contentPackages'],
    });
  },
};
