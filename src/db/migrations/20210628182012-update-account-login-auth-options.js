module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn(
      'accounts',
      'authField',
      {
        type: Sequelize.ENUM(
          'email',
          'employeeId',
          'emailOrEmployeeId',
        ),
        defaultValue: 'email',
      },
    );
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn(
      'accounts',
      'authField',
      {
        type: Sequelize.ENUM(
          'email',
          'employeeId',
        ),
        defaultValue: 'email',
      },
    );
  },
};
