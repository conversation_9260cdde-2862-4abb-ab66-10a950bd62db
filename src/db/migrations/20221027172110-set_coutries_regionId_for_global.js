module.exports = {
  up: async (queryInterface) => {
    await queryInterface.sequelize.query("UPDATE regions SET region = 'ALL', subRegion = 'ALL' WHERE region = 'All';");
    return queryInterface.sequelize.query("UPDATE countries SET regionId = (SELECT id FROM regions WHERE region = 'ALL') WHERE countryName = 'Global';");
  },

  down: async (queryInterface) => {
    await queryInterface.sequelize.query("UPDATE regions SET region = 'All', subRegion = 'all' WHERE region = 'ALL';");
    return queryInterface.sequelize.query("UPDATE countries SET regionId = null WHERE countryName = 'Global';");
  },
};
