module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn(
      'listings',
      'lastMappedAt',
      { type: Sequelize.DATE, allowNull: true },
    );
    await queryInterface.addColumn(
      'catalogItems',
      'lastMappedAt',
      { type: Sequelize.DATE, allowNull: true },
    );
    await queryInterface.sequelize.query(`UPDATE listings SET lastMappedAt = updatedAt`);
    await queryInterface.sequelize.query(`UPDATE catalogItems SET lastMappedAt = updatedAt`);
  },
  // eslint-disable-next-line no-unused-vars
  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('listings', 'lastMappedAt');
    await queryInterface.removeColumn('catalogItems', 'lastMappedAt');
  },
};
