const _ = require('lodash');

const permissionsTree = [
  {
    role: 'accountAdmin',
    allows: [
      {
        features: ['roles'],
        permissions: ['createAccount', 'readAccount', 'updateAccount', 'deleteAccount'],
      },
    ],
  },
];

const getRoleFeaturePermissionTuples = (roleIds, featureIds, permissionIds) => {
  const data = permissionsTree.map((roleSection) => {
    const roleId = roleIds[roleSection.role];
    const allows = roleSection.allows.map((allowSection) => {
      const features = allowSection.features.map((f) => {
        const permissions = allowSection.permissions.map((p) => {
          return {
            roleId,
            featureId: featureIds[f],
            permissionId: permissionIds[p],
          };
        });
        return permissions;
      });
      return _.flatten(features);
    });
    return _.flatten(allows);
  });
  return _.flatten(data);
};

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('roles', 'accountId', {
      type: Sequelize.DataTypes.INTEGER,
    });
    const result = await queryInterface.sequelize.query(
      'SELECT id FROM features where name=\'roles\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT },
    );
    const featureIds = { roles: result[0].id };
    const roleResults = await queryInterface.sequelize.query('select id, name from roles', {
      type: Sequelize.QueryTypes.SELECT,
    });
    const roleIds = roleResults.reduce((acc, role) => {
      return { ...acc, [role.name]: role.id };
    }, {});

    const permResults = await queryInterface.sequelize.query('select id, name from permissions', {
      type: Sequelize.QueryTypes.SELECT,
    });
    const permissionIds = permResults.reduce((acc, perm) => {
      return { ...acc, [perm.name]: perm.id };
    }, {});
    const roleFeaturePermissionTuples = getRoleFeaturePermissionTuples(roleIds, featureIds, permissionIds);

    const roleFeaturePermissions = roleFeaturePermissionTuples.map(({ roleId, featureId, permissionId }) => {
      return {
        roleId,
        featureId,
        permissionId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    });

    await queryInterface.bulkInsert('roleFeaturePermissions', roleFeaturePermissions);
  },

  down: async (queryInterface) => {
    await queryInterface.removeColumn('roles', 'accountId');
    const resultFeature = await queryInterface.sequelize.query(
      'SELECT id FROM features where name=\'roles\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT },
    );
    const resultRole = await queryInterface.sequelize.query(
      'SELECT id FROM roles where name=\'accountAdmin\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT },
    );

    await queryInterface.sequelize.query('DELETE FROM roleFeaturePermissions WHERE featureId=? and roleId=?', {
      type: queryInterface.sequelize.QueryTypes.DELETE,
      replacements: [
        resultFeature[0].id,
        resultRole[0].id,
      ],
    });
  },
};
