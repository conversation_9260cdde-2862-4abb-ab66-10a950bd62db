const featureName = ['assessmentItems'];

module.exports = {
  up: (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const features = [{
      name: featureName[0],
      createdAt: new Date(),
      updatedAt: new Date(),
    }];
    return queryInterface.bulkInsert('features', features);
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.bulkDelete('features', { name: featureName[0] });
  },
};
