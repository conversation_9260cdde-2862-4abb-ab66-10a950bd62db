module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.addColumn('listings', 'createdBy', {
          type: Sequelize.DataTypes.INTEGER,
        });
      })
      .then(() => {
        return queryInterface.addColumn('listings', 'updatedBy', {
          type: Sequelize.DataTypes.INTEGER,
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeColumn('listings', 'createdBy')
      .then(() => {
        return queryInterface.removeColumn('listings', 'updatedBy');
      });
  },
};
