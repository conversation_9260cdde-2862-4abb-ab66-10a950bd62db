module.exports = {
  up: async (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.addColumn('listingLessonListItems', 'title', {
      type: Sequelize.STRING,
      allowNull: false,
    }));
    promises.push(queryInterface.addColumn('listingLessonListItems', 'description', {
      type: Sequelize.STRING,
      allowNull: true,
    }));
    promises.push(queryInterface.addIndex(
      'listingLessonListItems',
      ['listingId', 'lessonId'],
      { unique: true },
    ));
    return Promise.all(promises);
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const promises = [];
    promises.push(queryInterface.removeColumn('listingLessonListItems', 'title'));
    promises.push(queryInterface.removeColumn('listingLessonListItems', 'description'));
    promises.push(queryInterface.removeIndex('listingLessonListItems', 'listing_lesson_list_items_listing_id_lesson_id'));
    return Promise.all(promises);
  },
};
