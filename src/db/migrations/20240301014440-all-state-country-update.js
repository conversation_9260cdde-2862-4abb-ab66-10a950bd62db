
const db = require('../../db');

const getAllCountry = async () => {
  const country = await db.sequelize
    .query('SELECT id FROM countries where countryName = "All Countries" limit 1', {
      type: db.sequelize.QueryTypes.SELECT,
    });
  return country.length ? country[0].id : 251;
};

module.exports = {
  async up(queryInterface, Sequelize) {
    const countryId = await getAllCountry();
    await queryInterface.sequelize.query(`
      UPDATE states SET countryId = ${countryId} WHERE stateName = "All";
    `);
  },

  async down(queryInterface, Sequelize) {
    // no going back
  }
};
