module.exports = {
  up: (queryInterface, Sequelize) => {
    const modifyScreenshotColumn = queryInterface.changeColumn(
      'videos',
      'ssFilename',
      {
        allowNull: true,
        type: Sequelize.DataTypes.STRING,
      },
    );
    return Promise.all([modifyScreenshotColumn]);
  },

  down: (queryInterface, Sequelize) => {
    const modifyScreenshotColumn = queryInterface.changeColumn(
      'videos',
      'ssFilename',
      {
        allowNull: false,
        type: Sequelize.DataTypes.STRING,
      },
    );
    return Promise.all([modifyScreenshotColumn]);
  },
};