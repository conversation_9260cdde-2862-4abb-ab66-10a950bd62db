module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
    # address 2 issues: remove bad hireDate data values, change hireDate column type from DATETIME to DATE

    # we are prevented from making any changes to the hireDate col
    # because there are invalid 0000-00-00 00:00:00 values
    
    # 1. fix 0000-00-00 00:00:00 hire dates
    # This will modify several hundred values out of A total of over 200k non-null values

    # save current setting of sql_mode
    SET @old_sql_mode := @@sql_mode ;
    
    # modify sql_mode, remove NO_ZERO_DATE and NO_ZERO_IN_DATE
    SET @new_sql_mode := @old_sql_mode ;
    SET @new_sql_mode := TRIM(BOTH ',' FROM REPLACE(CONCAT(',',@new_sql_mode,','),',NO_ZERO_DATE,'  ,','));
    SET @new_sql_mode := TRIM(BOTH ',' FROM REPLACE(CONCAT(',',@new_sql_mode,','),',NO_ZERO_IN_DATE,',','));
    SET @@sql_mode := @new_sql_mode ;
    
    # set "all zeros" hireDate values to null
    UPDATE users set hireDate = NULL where hireDate = '0000-00-00 00:00:00';
    
    # revert sql_mode to the original sql_mode setting
    SET @@sql_mode := @old_sql_mode ;

    # 2. fix 1970-01-01 00:00:00 hire dates (about 200 values?)
    UPDATE users set hireDate = NULL where hireDate = '1970-01-01 00:00:00';
    
    # 3. create backup of hireDate col 
    ALTER TABLE users ADD hireDateBkp DATETIME DEFAULT NULL;  
    UPDATE users SET hireDateBkp = hireDate;
    
    # 4. convert hireDate to DATE
    ALTER TABLE users MODIFY COLUMN hireDate DATE DEFAULT NULL;
    `);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('users', 'hireDate');
    return queryInterface.renameColumn('users', 'hireDateBkp', 'hireDate');
  },
};
