const csvToJson = require('csvtojson');
const path = require('path');
const db = require('../../db');
const logger = require('../../logger');

const Experts = db.experts;
const readFileIntoRows = (filename) => {
  const fullPathExpertsData = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathExpertsData);
};
const uploadExpertsData = async () => {
  const expertData = await readFileIntoRows('Experts-Data.csv');
  for await (const row of expertData) {
    const expert = {
      id: row['Expert ID'],
      firstName: row['First Name'],
      lastName: row['Last Name'],
      title: row.Title,
      bio: row['Bio URL'],
    };
    await Experts.upsert(expert, { where: { id: expert.id } });
  }
};
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      await uploadExpertsData();
    } catch (err) {
      logger.error(err);
    }
  },
  async down(queryInterface, Sequelize) {
    // no going back
  },
};
