module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('accounts', 'licenseLevel', {
      type: Sequelize.TEXT,
    });
    await queryInterface.sequelize.query(`
      UPDATE accounts SET licenseLevel = "level1" WHERE licenseLevel = "tier1";
      UPDATE accounts SET licenseLevel = "level2" WHERE licenseLevel = "tier2";
      UPDATE accounts SET licenseLevel = "level3" WHERE licenseLevel = "tier3";
    `);
    return queryInterface.changeColumn('accounts', 'licenseLevel', {
      type: Sequelize.ENUM('level1', 'level2', 'level3'),
      defaultValue: 'level1',
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('accounts', 'licenseLevel', {
      type: Sequelize.TEXT,
    });
    await queryInterface.sequelize.query(`
      UPDATE accounts SET licenseLevel = "tier1" WHERE licenseLevel = "level1";
      UPDATE accounts SET licenseLevel = "tier2" WHERE licenseLevel = "level2";
      UPDATE accounts SET licenseLevel = "tier3" WHERE licenseLevel = "level3";
    `);
    return queryInterface.changeColumn('accounts', 'licenseLevel', {
      type: Sequelize.ENUM('tier1', 'tier2', 'tier3'),
      defaultValue: 'tier1',
    });
  },
};
