const _ = require('lodash');

const permissionsTree = [
  {
    role: 'superAdmin',
    allows: [
      {
        features: ['reports'],
        permissions: ['readAccount'],
      },
    ],
  },
  {
    role: 'systemAdmin',
    allows: [
      {
        features: ['reports'],
        permissions: ['readAccount'],
      },
    ],
  },
];

const getRoleFeaturePermissionTuples = (roleIds, featureIds, permissionIds) => {
  const data = permissionsTree.map((roleSection) => {
    const roleId = roleIds[roleSection.role];
    const allows = roleSection.allows.map((allowSection) => {
      const features = allowSection.features.map((f) => {
        const permissions = allowSection.permissions.map((p) => {
          return {
            roleId,
            featureId: featureIds[f],
            permissionId: permissionIds[p],
          };
        });
        return permissions;
      });
      return _.flatten(features);
    });
    return _.flatten(allows);
  });
  return _.flatten(data);
};

module.exports = {
  up: (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const promises = [];

    promises.push(queryInterface.sequelize.query(
      'SELECT id FROM features where name=\'reports\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT },
    )
      .then(async (result) => {
        const featureIds = { reports: result[0].id };
        const roleResults = await queryInterface.sequelize.query('select id, name from roles', {
          type: Sequelize.QueryTypes.SELECT,
        });
        const roleIds = roleResults.reduce((acc, role) => {
          return { ...acc, [role.name]: role.id };
        }, {});

        const permResults = await queryInterface.sequelize.query('select id, name from permissions', {
          type: Sequelize.QueryTypes.SELECT,
        });
        const permissionIds = permResults.reduce((acc, perm) => {
          return { ...acc, [perm.name]: perm.id };
        }, {});

        // We keep the master permissions per role/feature in the regular source... go get them.
        // If this changes, a later migration should do this exact thing, but clear the roleFeaturePermissions
        // table first.
        const roleFeaturePermissionTuples = getRoleFeaturePermissionTuples(roleIds, featureIds, permissionIds);

        const roleFeaturePermissions = roleFeaturePermissionTuples.map(({ roleId, featureId, permissionId }) => {
          return {
            roleId,
            featureId,
            permissionId,
            createdAt: new Date(),
            updatedAt: new Date(),
          };
        });

        const insertResults = [
          await queryInterface.bulkInsert('roleFeaturePermissions', roleFeaturePermissions),
        ];
        return Promise.resolve(insertResults);
      }));

    return Promise.all(promises);
  },

  down: (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const promises = [];

    promises.push(queryInterface.sequelize.query(
      'SELECT id FROM features where name=\'reports\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT },
    )
      .then(async (result) => {
        const featureIds = { reports: result[0].id };
        const roleResults = await queryInterface.sequelize.query('select id, name from roles', {
          type: Sequelize.QueryTypes.SELECT,
        });
        const roleIds = roleResults.reduce((acc, role) => {
          return { ...acc, [role.name]: role.id };
        }, {});

        const permResults = await queryInterface.sequelize.query('select id, name from permissions', {
          type: Sequelize.QueryTypes.SELECT,
        });
        const permissionIds = permResults.reduce((acc, perm) => {
          return { ...acc, [perm.name]: perm.id };
        }, {});

        const roleFeaturePermissionTuples = getRoleFeaturePermissionTuples(roleIds, featureIds, permissionIds);

        roleFeaturePermissionTuples.forEach(({ roleId, featureId, permissionId }) => {
          promises.push(queryInterface.sequelize.query(
            'DELETE FROM roleFeaturePermissions WHERE roleId=? AND featureId = ? AND permissionId = ?',
            {
              type: queryInterface.sequelize.QueryTypes.DELETE,
              replacements: [roleId, featureId, permissionId],
            },
          ));
        });
      }));

    return Promise.all(promises);
  },
};
