module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.addColumn('assessmentItems', 'createdBy', {
          type: Sequelize.DataTypes.INTEGER,
        });
      })
      .then(() => {
        return queryInterface.addColumn('assessmentItems', 'updatedBy', {
          type: Sequelize.DataTypes.INTEGER,
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeColumn('assessmentItems', 'createdBy')
      .then(() => {
        return queryInterface.removeColumn('assessmentItems', 'updatedBy');
      });
  },
};
