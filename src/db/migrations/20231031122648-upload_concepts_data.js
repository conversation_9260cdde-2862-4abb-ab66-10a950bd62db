/* eslint-disable no-restricted-globals */
const csvToJson = require('csvtojson');
const path = require('path');
const db = require('../../db');
const logger = require('../../logger');

const Concepts = db.concepts;

const readFileIntoRows = (filename) => {
  const fullPathConceptsData = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathConceptsData);
};

const updateConcepts = async () => {
  const conceptData = await readFileIntoRows('Categorized-Concepts-data.csv');
  for await (const row of conceptData) {
    const query = `
        INSERT INTO concepts (id, priority, catagory, concept, createdAt, updatedAt)
        VALUES (${row['Concept ID']},${row.Priority},"${row.Category}","${row.Concept}", now(), now())
      `;
    await db.sequelize.query(query);
  }
};
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      await updateConcepts();
    } catch (err) {
      logger.error(err);
    }
  },

  async down(queryInterface, Sequelize) {
    // no going back
  },
};
