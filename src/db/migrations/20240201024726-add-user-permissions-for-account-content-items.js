module.exports = {
  up: (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return Promise.all([
      queryInterface.sequelize.query(
        "SELECT id FROM roles WHERE roles.name = 'user'",
        { type: queryInterface.sequelize.QueryTypes.SELECT },
      ),
      queryInterface.sequelize.query(
        "SELECT id FROM features WHERE features.name = 'accountContentItems'",
        { type: queryInterface.sequelize.QueryTypes.SELECT },
      ),
      queryInterface.sequelize.query(
        "SELECT id FROM permissions WHERE permissions.name = 'read'",
        { type: queryInterface.sequelize.QueryTypes.SELECT },
      ),
    ])
      .then((result) => {
        return queryInterface.sequelize.query('INSERT INTO roleFeaturePermissions ' +
        '(roleId, featureId, permissionId, createdAt, updatedAt) VALUES (?, ?, ? ,now(),now())', {
          type: queryInterface.sequelize.QueryTypes.INSERT,
          replacements: [result[0][0].id, result[1][0].id, result[2][0].id],
        });
      });
  },

  down: (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return Promise.all([
      queryInterface.sequelize.query(
        "SELECT id FROM roles WHERE roles.name = 'user'",
        { type: queryInterface.sequelize.QueryTypes.SELECT },
      ),
      queryInterface.sequelize.query(
        "SELECT id FROM features WHERE features.name = 'accountContentItems'",
        { type: queryInterface.sequelize.QueryTypes.SELECT },
      ),
      queryInterface.sequelize.query(
        "SELECT id FROM permissions WHERE permissions.name = 'read'",
        { type: queryInterface.sequelize.QueryTypes.SELECT },
      ),
    ])
      .then((result) => {
        return queryInterface.sequelize.query('DELETE FROM roleFeaturePermissions WHERE ' +
        'roleId = ? AND featureId = ? AND permissionId = ?', {
          type: queryInterface.sequelize.QueryTypes.DELETE,
          replacements: [result[0][0].id, result[1][0].id, result[2][0].id],
        });
      });
  },
};
