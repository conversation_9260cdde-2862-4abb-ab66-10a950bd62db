module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('answerCards', 'accountId', {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: false,
    });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.changeColumn('answerCards', 'accountId', {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: true,
    });
  },
};

