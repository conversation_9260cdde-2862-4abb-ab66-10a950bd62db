const path = require('path');
const db = require('../../db');
const csvToJson = require('csvtojson');


const readFileIntoRows = async (filename) => {
  const fullPathSocialCapital = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathSocialCapital);
};

const savePillars = async () => {
  const pillarsData = await readFileIntoRows('SocialCapitalTerms-Pillars.csv');
  for await (const row of pillarsData) {
    if (row.name) {
      row.createdBy = 1;
      await db.socialCapitalPillars.upsert(row);
    }
  }
};
const saveIndicators = async () => {
  // eslint-disable-next-line consistent-return
  const indicatorsData = await readFileIntoRows('SocialCapitalTerms-Indicators.csv');
  for await (const row of indicatorsData) {
    if (row.name) {
      row.createdBy = 1;
      await db.socialCapitalIndicators.upsert(row);
    }
  }
};

module.exports = {
  // eslint-disable-next-line no-unused-vars
  up: async (queryInterface, Sequelize) => {
    // Pillars seed
    await savePillars();

    // indicators seed
    await saveIndicators();
  },

  // eslint-disable-next-line no-unused-vars
  down: (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query('DELETE from socialCapitalPillars')
      .then(() => {
        queryInterface.sequelize.query('DELETE from socialCapitalIndicators');
      });
  },
};
