module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('campaigns', 'deployedAt', {
      type: Sequelize.DATE,
    });
    await queryInterface.addColumn('campaigns', 'deployedById', {
      type: Sequelize.INTEGER,
    });
    await queryInterface.addColumn('campaigns', 'statusChangedAt', {
      type: Sequelize.DATE,
    });
    await queryInterface.addColumn('campaigns', 'statusChangedById', {
      type: Sequelize.INTEGER,
    });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    await queryInterface.removeColumn('campaigns', 'deployedAt');
    await queryInterface.removeColumn('campaigns', 'deployedById');
    await queryInterface.removeColumn('campaigns', 'statusChangedAt');
    await queryInterface.removeColumn('campaigns', 'statusChangedById');
  },
};
