module.exports = {
  up: (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.addColumn(
      'csvImports',
      'fileSize',
      {
        type: Sequelize.INTEGER,
        defaultValue: 0,
        allowNull: false,
      },
    ));
    promises.push(queryInterface.addColumn(
      'csvImports',
      'priority',
      {
        type: Sequelize.INTEGER,
        defaultValue: 1,
        allowNull: false,
      },
    ));
    promises.push(queryInterface.addColumn(
      'csvImports',
      'failReason',
      {
        type: Sequelize.STRING,
        allowNull: true,
      },
    ));
    promises.push(queryInterface.changeColumn(
      'events',
      'trackableType',
      {
        type: Sequelize.ENUM(
          'site', 'resource', 'mediaAssets', 'users', 'tags', 'topics',
          'sendLinks', 'groups', 'programs', 'campaign', 'video', 'csvImport',
        ),
        allowNull: false,
      },
    ));
    return Promise.all(promises);
  },

  // eslint-disable-next-line no-unused-vars
  down: (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.removeColumn('csvImports', 'fileSize'));
    promises.push(queryInterface.removeColumn('csvImports', 'priority'));
    promises.push(queryInterface.removeColumn('csvImports', 'failReason'));
    promises.push(queryInterface.changeColumn(
      'events',
      'trackableType',
      {
        type: Sequelize.ENUM(
          'site', 'resource', 'mediaAssets', 'users', 'tags', 'topics',
          'sendLinks', 'groups', 'programs', 'campaign', 'video',
        ),
        allowNull: false,
      },
    ));
    return Promise.all(promises);
  },
};
