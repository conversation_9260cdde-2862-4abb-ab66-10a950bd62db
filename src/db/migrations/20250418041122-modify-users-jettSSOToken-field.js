module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('users', 'jettSSOToken', {
      type: Sequelize.STRING(800)
    });
    await queryInterface.changeColumn('users', 'accountSSOToken', {
      type: Sequelize.STRING(800)
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('users', 'jettSSOToken', {
      type: Sequelize.STRING(255)
    });
    await queryInterface.changeColumn('users', 'accountSSOToken', {
      type: Sequelize.STRING(255)
    });
  }
};