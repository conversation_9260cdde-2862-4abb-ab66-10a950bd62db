module.exports = {
  up: (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.addColumn(
      'userLessons',
      'enrollmentId',
      {
        type: Sequelize.INTEGER,
        defaultValue: 1,
        allowNull: true,
      },
    ));
    promises.push(queryInterface.addColumn(
      'viewLessonCardEvents',
      'enrollmentId',
      {
        type: Sequelize.INTEGER,
        defaultValue: 1,
        allowNull: false,
      },
    ));
    promises.push(queryInterface.addColumn(
      'answerCards',
      'enrollmentId',
      {
        type: Sequelize.INTEGER,
        defaultValue: 1,
        allowNull: false,
      },
    ));
    promises.push(queryInterface.addColumn(
      'lastViewedLessonCards',
      'enrollmentId',
      {
        type: Sequelize.INTEGER,
        defaultValue: 1,
        allowNull: false,
      },
    ));
    promises.push(queryInterface.addColumn(
      'lastViewedLessonCards',
      'sourceLifecycle',
      {
        type: Sequelize.ENUM('draft', 'review', 'publish', 'close'),
        defaultValue: 'publish',
        allowNull: false,
      },
    ));
    return Promise.all(promises);
  },

  // eslint-disable-next-line no-unused-vars
  down: (queryInterface, Sequelize) => {
    const promises = [];
    promises.push(queryInterface.removeColumn('userLessons', 'enrollmentId'));
    promises.push(queryInterface.removeColumn('viewLessonCardEvents', 'enrollmentId'));
    promises.push(queryInterface.removeColumn('answerCards', 'enrollmentId'));
    promises.push(queryInterface.removeColumn('lastViewedLessonCards', 'sourceLifecycle'));
    promises.push(queryInterface.removeColumn('lastViewedLessonCards', 'enrollmentId'));
    return Promise.all(promises);
  },
};
