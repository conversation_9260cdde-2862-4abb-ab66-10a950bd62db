module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('resourceAssets', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      title: {
        type: Sequelize.STRING,
      },
      description: {
        type: Sequelize.TEXT,
      },
      type: {
        type: Sequelize.ENUM('file', 'link'),
        allowNull: false,
      },
      link: {
        type: Sequelize.TEXT,
      },
      fileId: {
        type: Sequelize.INTEGER,
      },
      resourceId: {
        type: Sequelize.INTEGER,
      },
      resourceType: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      publishToManageLibrary: {
        type: Sequelize.BOOLEAN,
      },
      manageLibraryTitle: {
        type: Sequelize.TEXT,
      },
      audience: {
        type: Sequelize.ENUM('learners', 'admins'),
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    await queryInterface.createTable('resourceAssetPillars', {
      resourceAssetId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'resourceAssets',
          key: 'id',
        },

      },
      pillarId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        references: {
          model: 'socialCapitalPillars',
          key: 'id',
        },
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('resourceAssets');
    await queryInterface.dropTable('resourceAssetPillars');
  },
};
