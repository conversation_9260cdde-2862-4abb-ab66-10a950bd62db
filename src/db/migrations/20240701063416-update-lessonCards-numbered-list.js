'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Update columns in your table
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn('lessonCards', 'list6', {
        type: Sequelize.TEXT,
        allowNull: true,
      }, { transaction });

      await queryInterface.addColumn('lessonCards', 'list7', {
        type: Sequelize.TEXT,
        allowNull: true,
      }, { transaction });

      await queryInterface.addColumn('lessonCards', 'list8', {
        type: Sequelize.TEXT,
        allowNull: true,
      }, { transaction });

      await queryInterface.addColumn('lessonCards', 'list9', {
        type: Sequelize.TEXT,
        allowNull: true,
      }, { transaction });


      await queryInterface.addColumn('lessonCards', 'list10', {
        type: Sequelize.TEXT,
        allowNull: true,
      }, { transaction });

      const newEnumValues = ['name', 'title', 'description', 'list1', 'list2',
        'list3', 'list4', 'list5', 'list1Detail', 'list2Detail', 'list3Detail',
        'list4Detail', 'question1', 'question2', 'answer', 'choiceFeedback', 'choiceError',
        'question', 'questionEdited', 'certificateText', 'downloadInstructions', 'completedMessage',
        'list6', 'list7', 'list8', 'list9', 'list10'];

      await queryInterface.changeColumn('contentStrings', 'field', {
        type: Sequelize.ENUM(...newEnumValues),
        allowNull: false,
      }, { transaction });
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Revert changes if needed
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('lessonCards', 'list6', { transaction });
      await queryInterface.removeColumn('lessonCards', 'list7', { transaction });
      await queryInterface.removeColumn('lessonCards', 'list8', { transaction });
      await queryInterface.removeColumn('lessonCards', 'list9', { transaction });
      await queryInterface.removeColumn('lessonCards', 'list10', { transaction });

      const oldEnumValues = ['name', 'title', 'description', 'list1', 'list2',
        'list3', 'list4', 'list5', 'list1Detail', 'list2Detail', 'list3Detail',
        'list4Detail', 'question1', 'question2', 'answer', 'choiceFeedback', 'choiceError',
        'question', 'questionEdited', 'certificateText', 'downloadInstructions', 'completedMessage'];

      await queryInterface.changeColumn('contentStrings', 'field', {
        type: Sequelize.ENUM(...oldEnumValues),
        allowNull: false,
      }, { transaction });
    });
  },
};
