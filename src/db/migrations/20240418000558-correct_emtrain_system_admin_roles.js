module.exports = {
  up: async (queryInterface) => {
    return queryInterface.sequelize.query(`
    UPDATE  users u, accountUsers au, userRoles ur, roles r
    SET au.roleId = 5
    WHERE au.accountId = 1
    AND au.userId = u.id
    AND ur.userId = u.id
    AND u.email LIKE ("%@emtrain.com")
    AND u.deletedAt IS NULL
    AND r.id = ur.roleId
    AND (ur.roleId IN (5,6) OR au.roleId IN (5,6))
    AND au.roleId < ur.roleId;
    `);
  },

  down: async (queryInterface) => {
    // not reversible
  },
};
