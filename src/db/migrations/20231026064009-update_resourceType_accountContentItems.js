const db = require('../../db');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const migrations = [];
    migrations.push(db.accountContentItems.destroy({ where: { resourceType: 'campaign' }, force: true }));
    migrations.push(queryInterface.changeColumn('accountContentItems', 'resourceType', {
      type: Sequelize.ENUM('program', 'lesson'),
    }));
    return Promise.all(migrations);
  },

  async down(queryInterface, Sequelize) {
    // no going back
  },
};
