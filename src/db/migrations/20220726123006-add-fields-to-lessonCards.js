module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.addColumn('lessonCards', 'createdBy', {
          type: Sequelize.DataTypes.INTEGER,
        });
      })
      .then(() => {
        return queryInterface.addColumn('lessonCards', 'updatedBy', {
          type: Sequelize.DataTypes.INTEGER,
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeColumn('lessonCards', 'createdBy')
      .then(() => {
        return queryInterface.removeColumn('lessonCards', 'updatedBy');
      });
  },
};
