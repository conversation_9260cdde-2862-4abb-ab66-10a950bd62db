module.exports = {
  async up(queryInterface, Sequelize) {
    const promises = [];
    promises.push(queryInterface.addColumn('programs', 'isTimed', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    }));
    promises.push(queryInterface.addColumn('programs', 'duration', {
      type: Sequelize.INTEGER,
      allowNull: true,
    }));
    return Promise.all(promises);
  },

  async down(queryInterface, Sequelize) {
    const promises = [];
    promises.push(queryInterface.removeColumn('programs', 'isTimed'));
    promises.push(queryInterface.removeColumn('programs', 'duration'));
    return Promise.all(promises);
  }
};
