module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('groups', 'groupType', {
      type: Sequelize.ENUM('static', 'dynamic', 'roster'),
      allowNull: false,
      defaultValue: 'static',
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('groups', 'groupType', {
      type: Sequelize.ENUM('static', 'dynamic'),
      allowNull: false,
      defaultValue: 'static',
    });
  },
};
