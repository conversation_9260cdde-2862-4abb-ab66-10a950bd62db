module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('campaigns', 'status', {
      type: Sequelize.ENUM('scheduled', 'inProgress', 'closed', 'withdrawn', 'failed'),
      defaultValue: 'scheduled',
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('campaigns', 'status', {
      type: Sequelize.ENUM('scheduled', 'inProgress', 'closed', 'withdrawn'),
      defaultValue: 'scheduled',
    });
  },
};
