const { DataTypes } = require('sequelize');

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('listings', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      rank: {
        allowNull: false,
        type: DataTypes.INTEGER,
      },
      type: {
        allowNull: false,
        type: DataTypes.ENUM('course', 'microlesson', 'diagnostic'),
      },
      lifecycle: {
        allowNull: false,
        type: DataTypes.ENUM('draft', 'published', 'retired'),
      },
      title: {
        allowNull: false,
        type: DataTypes.STRING,
      },
      description: {
        type: DataTypes.TEXT,
      },
      countryId: {
        allowNull: false,
        type: DataTypes.INTEGER,
        references: {
          model: 'countries',
          key: 'id',
        },
      },
      subtitle: {
        type: DataTypes.STRING,
      },
      marketingCopy: {
        type: DataTypes.TEXT,
      },
      featuredSnippet: {
        type: DataTypes.TEXT,
      },
      thumbnailId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'files',
          key: 'id',
        },
      },
      createdAt: {
        allowNull: false,
        type: DataTypes.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: DataTypes.DATE,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('listings');
  },
};
