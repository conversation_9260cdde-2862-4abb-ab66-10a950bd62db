'use strict';

module.exports = {
  up: (queryInterface, Sequelize) => {
    const promises = [];

    promises.push(queryInterface.changeColumn('accounts', 'csvImportSyncField', {
      type: Sequelize.ENUM(
        'email',
        'employeeId',
        'scormId',
      ),
      defaultValue: 'email',
    }));
    return Promise.all(promises);
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('accounts', 'csvImportSyncField', {
      type: Sequelize.ENUM('email', 'employeeId'),
      defaultValue: 'email',
    });
  },
};
