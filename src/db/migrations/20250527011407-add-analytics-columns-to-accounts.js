module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('accounts', 'hasCultureSkills', {
      type: Sequelize.Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    });
    await queryInterface.addColumn('accounts', 'hasHrPeopleRisk', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });
    await queryInterface.addColumn('accounts', 'hasBusinessComplianceRisk', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });
    await queryInterface.addColumn('accounts', 'reportingAccess', {
      type: Sequelize.ENUM('SummaryOnly', 'Questions+Summary', 'Segmentation+Questions+Summary'),
      allowNull: true,
      defaultValue: null 
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('accounts', 'hasCultureSkills');
    await queryInterface.removeColumn('accounts', 'hasHrPeopleRisk');
    await queryInterface.removeColumn('accounts', 'hasBusinessComplianceRisk');
    await queryInterface.removeColumn('accounts', 'reportingAccess');
  },
};
