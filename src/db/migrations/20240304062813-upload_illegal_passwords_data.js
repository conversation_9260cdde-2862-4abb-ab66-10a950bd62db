const csvToJson = require('csvtojson');
const path = require('path');
const db = require('..');
const logger = require('../../logger');

const IllegalPasswords = db.illegalPasswords;

const readFileIntoRows = (filename) => {
  const fullPathIllegalPwdData = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathIllegalPwdData);
};

const insertIllegalPasswordData = async () => {
  const illegalPwdData = await readFileIntoRows('illegal-pwds.csv');
  let finalData = [];
  for await (const row of illegalPwdData) {
    const createObj = {};
    createObj.passwordText = row.password;
    finalData.push(createObj);
  }
  finalData = finalData.flat();

  await IllegalPasswords.bulkCreate(finalData, {
    fields: ['passwordText'],
    updateOnDuplicate: ['passwordText'],
  });
};

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up() {
    try {
      await insertIllegalPasswordData();
    } catch (err) {
      logger.error(err);
    }
  },
  async down() {
    // no going back
  },
};
