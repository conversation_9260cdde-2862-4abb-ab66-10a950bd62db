const csvToJson = require('csvtojson');
const path = require('path');
const db = require('..');
const logger = require('../../logger');

const Lessons = db.lessons;
const Programs = db.programs;

const readFileIntoRows = (filename) => {
  const fullPathcatalogItemsData = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathcatalogItemsData);
};

const uploadCatalogPrograms = async () => {
  const catalogProgramData = await readFileIntoRows('Catalog-Program-Data.csv');
  for await (const row of catalogProgramData) {
    const catalogId = parseInt(row.catalogId);
    const programId = parseInt(row.programId);
    if (catalogId) {
      await Programs.update({ catalogId }, { where: { id: programId } });
    }
  }
};
const uploadCatalogLessons = async () => {
  const catalogLessonData = await readFileIntoRows('Catalog-Lesson-Data.csv');
  for await (const row of catalogLessonData) {
    const catalogId = parseInt(row.catalogId);
    const lessonId = parseInt(row.lessonId);
    if (catalogId) {
      await Lessons.update({ catalogId }, { where: { id: lessonId } });
    }
  }
};

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up() {
    try {
      const promise = [];
      promise.push(uploadCatalogPrograms());
      promise.push(uploadCatalogLessons());
      await Promise.all(promise);
    } catch (err) {
      logger.error(err);
    }
  },
  async down() {
    // no going back
  },
};
