const { format } = require('date-fns');
const config = require('../../config/config');
const { setPassword } = config.encryption;
const db = require('../../db');

const createUser = async () => {
  const timestamp = format(new Date(), 'YYYY-MM-DD HH:mm:ss');
  const encryptedPassword = await setPassword('Emtr@in@123$');
  const user = await db.sequelize.query('INSERT ' +
    'INTO users (email, firstName, lastName, password, supervisor, employeeId,' +
    'isVerified, canAskExpert, createdAt, updatedAt) ' +
    'VALUES(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', {
    replacements: [
      '<EMAIL>',
      'Center',
      'Stage',
      encryptedPassword,
      'Yes',
      '123456',
      1,
      1,
      timestamp,
      timestamp,
    ],
    type: db.sequelize.QueryTypes.INSERT,
  });
  await db.sequelize.query('INSERT ' +
    'INTO userRoles (userId, roleId, createdAt, updatedAt) ' +
    'VALUES(?, ?, ?, ?)', {
    replacements: [user[0], 2, timestamp, timestamp],
    type: db.sequelize.QueryTypes.INSERT,
  });

  await db.sequelize.query('INSERT ' +
    'INTO accountUsers (userId, roleId, accountId, createdAt, updatedAt) ' +
    'VALUES(?, ?, ?, ?, ?)', {
    replacements: [user[0], 2, 1, timestamp, timestamp],
    type: db.sequelize.QueryTypes.INSERT,
  });
}

const getUserId = async () => {
  const user = await db.sequelize
    .query('SELECT id FROM users where email = "<EMAIL>" limit 1', {
      type: db.sequelize.QueryTypes.SELECT,
    });
  return user.length ? user[0].id : null;
};

module.exports = {
  up: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    await createUser();
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const userId = await getUserId();
    if (userId) {
      await queryInterface.sequelize.query(`DELETE FROM users WHERE id = ${userId}`);
      await queryInterface.sequelize.query(`DELETE FROM userRoles WHERE userId = ${userId}`);
      await queryInterface.sequelize.query(`DELETE FROM accountUsers WHERE userId = ${userId}`);
    }
  },
};
