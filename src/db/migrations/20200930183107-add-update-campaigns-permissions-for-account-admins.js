const { format } = require('date-fns');

const timestamp = format(new Date(), 'YYYY-MM-DD HH:mm:ss');
const roleNames = ['\'accountAdmin\''];

async function lookupIds(queryInterface) {
  return Promise.all([
    queryInterface.sequelize.query(
      `SELECT id FROM roles WHERE name IN(${roleNames})`,
      { type: queryInterface.sequelize.QueryTypes.SELECT },
    ),
    queryInterface.sequelize.query(
      'SELECT id FROM permissions WHERE name=\'update\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT },
    ),
    queryInterface.sequelize.query(
      'SELECT id FROM features WHERE name=\'campaigns\'',
      { type: queryInterface.sequelize.QueryTypes.SELECT },
    ),
  ]);
}

module.exports = {
  up: (queryInterface) => {
    return lookupIds(queryInterface)
      .then(([roleIds, updatePermission, campaignFeature]) => {
        const promises = roleIds.map((role) => {
          const existingQuery = queryInterface.sequelize.query(
            'SELECT * from roleFeaturePermissions WHERE roleId = ? AND featureId = ? AND permissionId = ? ',
            { type: queryInterface.sequelize.QueryTypes.SELECT,
              replacements: [
                role.id,
                campaignFeature[0].id,
                updatePermission[0].id,
              ] },
          );
          Promise.resolve(existingQuery).then((existing) => {
            if (existing && existing[0] && existing[0].id) {
              return null;
            }
            return queryInterface.sequelize.query(
              'INSERT INTO roleFeaturePermissions (roleId, featureId, permissionId, createdAt, updatedAt) ' +
              'VALUES(?, ?, ?, ?, ?)',
              {
                type: queryInterface.sequelize.QueryTypes.INSERT,
                replacements: [
                  role.id,
                  campaignFeature[0].id,
                  updatePermission[0].id,
                  timestamp,
                  timestamp,
                ],
              },
            );
          });
        });
        return Promise.all(promises);
      });
  },

  down: (queryInterface) => {
    return lookupIds(queryInterface)
      .then(([roleId, updatePermission, campaignFeature]) => {
        Promise.resolve(queryInterface.sequelize.query(
          'DELETE FROM roleFeaturePermissions WHERE roleId = ? AND featureId = ? AND permissionId = ?',
          {
            type: queryInterface.sequelize.QueryTypes.DELETE,
            replacements: [
              roleId[0].id,
              campaignFeature[0].id,
              updatePermission[0].id,
            ],
          },
        ));
      });
  },
};
