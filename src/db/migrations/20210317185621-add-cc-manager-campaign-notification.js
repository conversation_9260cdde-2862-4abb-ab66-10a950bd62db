module.exports = {
  up: (queryInterface, Sequelize) => {
    const addCCManager = queryInterface.addColumn(
      'campaignNotifications',
      'notificationCCManager', {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
      },
    );
    return Promise.all([addCCManager]);
  },

  down: (queryInterface, Sequelize) => {
    const removeCCManager = queryInterface.removeColumn('campaignNotifications', 'notificationCCManager', Sequelize.BOOLEAN);
    return Promise.all([removeCCManager]);
  },
};
