module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('listingLessonListItems', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      listingId: {
        allowNull: false,
        type: Sequelize.INTEGER,
      },
      lessonId: {
        allowNull: false,
        type: Sequelize.INTEGER,
      },
      rank: {
        allowNull: false,
        type: Sequelize.INTEGER,
      },
      catalogItemIds: {
        allowNull: true,
        type: Sequelize.STRING,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('listingLessonListItems');
  },
};
