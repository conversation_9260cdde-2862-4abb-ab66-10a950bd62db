/* eslint-disable max-len */
/* eslint-disable quotes */

// in VSCode type Option Z to toggle line breaks
const ptds = [{ language: 'ms',
  abbr: 'AB',
  traits:
   '<p><PERSON><PERSON><PERSON><PERSON>, keper<PERSON><PERSON>an agama, warna, jantina, identiti jantina, ung<PERSON>pan jantina, kecacatan fizikal, kecacatan mental, umur, nenek moyang, tempat asal, status perkahwinan, sumber pendapatan, status keluarga dan orientasi seksual.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'ms',
  abbr: 'BC',
  traits:
   '<p><PERSON><PERSON><PERSON><PERSON>, warna, keturunan, tempat asal, agama, status perkahwinan, status keluarga (tidak terpakai untuk membeli harta benda), kecacatan fizikal atau mental, seks (termasuk lelaki, wanita, antara seks atau transgender. Ia juga termasuk mengandung, <PERSON><PERSON><PERSON>, dan gangguan seksual), orientasi seksual (termasuk menjadi heteroseksual, gay, lesbian atau biseksual), identiti jantina, ung<PERSON><PERSON> jantina, umur (19 ke atas, tidak terpakai untuk membeli harta), sabitan jenayah (hanya terpakai kepada pekerjaan) dan kepercayaan politik (hanya terpakai kepada pekerjaan).</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'ms',
  abbr: 'MB',
  traits:
   '<p>keturunan (termasuk warna dan bangsa yang dirasakan), kewarganegaraan atau asal negara, latar belakang etnik atau asal, agama atau kepercayaan, atau kepercayaan agama, persatuan agama atau aktiviti agama, umur, jantina, termasuk ciri-ciri atau keadaan yang ditentukan seks, seperti kehamilan, kemungkinan kehamilan, atau keadaan yang berkaitan dengan kehamilan, identiti jantina, orientasi seksual, status perkahwinan atau keluarga, sumber pendapatan, kepercayaan politik, persatuan politik atau aktiviti politik dan hilang upaya fizikal atau mental atau ciri-ciri atau keadaan yang berkaitan, termasuk pergantungan kepada haiwan perkhidmatan, kerusi roda, atau sebarang perkakas atau peranti pemulihan lain, kelemahan sosial.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'ms',
  abbr: 'NB',
  traits:
   '<p>Perlumbaan, warna, asal negara, tempat asal, keturunan, kepercayaan atau agama, umur, status keluarga, status perkahwinan, jantina (termasuk kehamilan), orientasi seksual, identiti jantina, ungkapan jantina, kecacatan fizikal atau mental, keadaan sosial (termasuk sumber pendapatan, tahap pendidikan dan pekerjaan) dan kepercayaan politik atau aktiviti.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'ms',
  abbr: 'NL',
  traits:
   '<p>Ras, warna, kewarganegaraan, asal etnis, asal sosial, kepercayaan agama, agama, usia, kecacatan (termasuk kecacatan yang dirasakan), cacat, jenis kelamin (termasuk kehamilan), orientasi seksual, identitas gender, ekspresi gender, status perkawinan, status keluarga, sumber pendapatan, dan pendapat politik dan sabitan jenayah (tidak berkaitan dengan pekerjaan).</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'ms',
  abbr: 'NT',
  traits:
   '<p>Umur, kecacatan, bangsa, warna, nenek moyang, tempat asal, asal etnik, kewarganegaraan, jantina, orientasi seksual, identiti jantina, status keluarga, gabungan keluarga, status perkahwinan, keadaan sosial, agama, kepercayaan, kepercayaan politik, persatuan politik, keyakinan politik, keyakinan jenayah diampuni dan penggantungan rekod.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'ms',
  abbr: 'NS',
  traits:
   '<p>Umur, bangsa, warna, agama, kepercayaan, etnik, negara atau asal asli, seks (termasuk kehamilan dan ekuiti gaji), orientasi seksual, kecacatan fizikal, kecacatan mental, status keluarga, status perkahwinan, sumber pendapatan, ketakutan tidak rasional tertular penyakit atau penyakit, persatuan dengan kumpulan yang dilindungi atau individu, kepercayaan politik, gabungan atau aktiviti, identiti jantina dan ungkapan jantina.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'ms',
  abbr: 'NU',
  traits:
   '<p>Ras, warna, keturunan, asal etnis, kewarganegaraan, tempat asal, kepercayaan, agama, usia, kecacatan, jenis kelamin, orientasi seksual, status perkawinan, status keluarga, kehamilan (termasuk adopsi anak oleh seorang pria atau wanita), identitas gender, ekspresi gender, sumber pendapatan yang sah dan keyakinan yang mana pengampunan telah diberikan.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'ms',
  abbr: 'ON',
  traits:
   '<p>Umur, keturunan, warna, bangsa, kewarganegaraan, asal etnik, tempat asal, kepercayaan, kecacatan, status keluarga, status perkahwinan (termasuk status tunggal), identiti jantina, ungkapan jantina, penerimaan bantuan awam (dalam perumahan sahaja), rekod kesalahan (dalam pekerjaan sahaja), jantina (termasuk kehamilan dan payudara memberi makan), dan orientasi seksual.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'ms',
  abbr: 'PE',
  traits:
   '<p>Umur, persatuan (dengan individu atau kumpulan individu yang dilindungi di bawah Akta), warna, bangsa, kepercayaan atau agama, sabitan jenayah, etnik atau negara asal, status keluarga atau status perkahwinan, kecacatan fizikal atau mental (termasuk ujian tambahan dan alkohol/dadah), kepercayaan politik, seksual orientasi, sumber pendapatan, jantina atau jantina (termasuk kehamilan dan gangguan seksual), identiti jantina dan ungkapan jantina.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'ms',
  abbr: 'QC',
  traits:
   '<p>Ras, warna, jantina, identiti jantina, ungkapan jantina, kehamilan, orientasi seksual, status sivil, umur kecuali yang diperuntukkan oleh undang-undang, agama, keyakinan politik, bahasa, etnik atau negara asal, keadaan sosial dan kecacatan atau penggunaan apa-apa cara untuk melenyapkan kecacatan.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'ms',
  abbr: 'SK',
  traits:
   '<p>Agama, kepercayaan, status perkahwinan, status keluarga (termasuk hubungan ibu bapa-anak dan kehamilan), jantina, orientasi seksual, kecacatan fizikal atau mental, umur (18 atau lebih), warna, keturunan, kewarganegaraan, tempat asal, bangsa atau bangsa yang dirasakan, penerimaan bantuan awam dan identiti jantina.</p>',
  country: 'CA',
  mapType: 'protected_traits' },
{ language: 'ms',
  abbr: 'YT',
  traits:
   '<p>Keturunan (termasuk warna dan bangsa) asal negara, latar belakang etnik atau bahasa atau asal, agama atau kepercayaan, umur, jantina (termasuk kehamilan), orientasi seksual, identiti jantina, ungkapan jantina, kecacatan fizikal atau mental, tuduhan jenayah atau rekod jenayah, kepercayaan politik, persatuan, atau aktiviti , status perkahwinan atau keluarga, sumber pendapatan dan persatuan sebenar atau dianggap dengan individu atau kumpulan lain yang identiti atau keahlian ditentukan oleh mana-mana alasan yang disenaraikan.</p>',
  country: 'CA',
  mapType: 'protected_traits' }];

module.exports = {
  up: (queryInterface) => {
    const inserts = [];
    ptds.forEach(({ language, abbr, traits, country, mapType }) => {
      const trait = {
        language,
        abbr,
        traits,
        country,
        mapType,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      inserts.push(trait);
    });
    return queryInterface.bulkInsert('protectedTraitsDefaults', inserts);
  },

  down: async (queryInterface) => {
    const sql = `DELETE FROM protectedTraitsDefaults 
    WHERE language = 'ms'
    AND mapType = 'protected_traits'
    AND country = 'CA'`;

    return queryInterface.sequelize.query(sql, {
      type: queryInterface.sequelize.QueryTypes.DELETE,
    });
  },
};
