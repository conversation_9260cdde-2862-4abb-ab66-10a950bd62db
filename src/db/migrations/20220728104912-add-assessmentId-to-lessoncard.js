module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.addColumn('lessonCards', 'assessmentItemId', {
          type: Sequelize.INTEGER,
          defaultValue: false,
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeColumn('lessonCards', 'assessmentItemId');
  },
};
