module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.createTable('licenseTracking', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      accountId: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      userId: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      contentId: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      contentType: {
        type: Sequelize.DataTypes.ENUM(
          'program',
          'lesson',
          'mediaAsset',
          'questionAnswer',
        ),
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
    });
  },

  // eslint-disable-next-line no-unused-vars
  down: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('licenseTracking');
  },
};
