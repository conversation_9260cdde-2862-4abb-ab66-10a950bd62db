module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.addColumn('answerCards', 'programId', {
      type: Sequelize.DataTypes.INTEGER,
      allowNull: true,
    })
      .then(() => {
        return queryInterface.addIndex('answerCards', {
          fields: ['programId'],
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeIndex('answerCards', ['programId'])
      .then(() => {
        return queryInterface.removeColumn('answerCards', 'programId');
      });
  },
};

