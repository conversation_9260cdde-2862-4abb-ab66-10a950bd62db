/* eslint-disable max-len */

// in VSCode type Option Z to toggle line breaks
const ptds = [
  {
    language: 'bn',
    abbr: 'AB',
    traits: '<p>জাতি, ধর্মীয় বিশ্বাস, রঙ, লিঙ্গ, লিঙ্গ পরিচয়, লিঙ্গ অভিব্যক্তি, শারীরিক অক্ষমতা, মানসিক অক্ষমতা, বয়স, পূর্বপুরুষ, উৎপত্তি স্থান, বৈবাহিক অবস্থা, আয়ের উৎস, পারিবারিক অবস্থা এবং যৌন অভিমুখিতা।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'bn',
    abbr: 'BC',
    traits: '<p>জাতি, রঙ, পূর্বপুরুষ, উৎপত্তি স্থান, ধর্ম, বৈবাহিক অবস্থা, পারিবারিক অবস্থা, শারীরিক বা মানসিক অক্ষমতা, লিঙ্গ (পুরুষ, নারী, আন্তঃলিঙ্গ বা ট্রান্সজেন্ডার হওয়া অন্তর্ভুক্ত, এবং গর্ভাবস্থা, স্তন্যপান করানো, এবং যৌন হয়রানিও অন্তর্ভুক্ত), যৌন অভিমুখিতা (বিষমকামী, সমকামী, লেসবিয়ান বা উভকামী হওয়া অন্তর্ভুক্ত), লিঙ্গ পরিচয়, লিঙ্গ অভিব্যক্তি, বয়স (১৯ বা তার বেশি, সম্পত্তি কেনার ক্ষেত্রে প্রযোজ্য নয়), অপরাধমূলক প্রত্যয় বা একটি সারসংক্ষেপ কর্মসংস্থান এবং রাজনৈতিক বিশ্বাস।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'bn',
    abbr: 'MB',
    traits: '<p>জাতি (রঙ এবং অনুভূত জাতি সহ), পূর্বপুরুষ, জাতিগত পটভূমি বা উত্স, জাতীয়তা বা জাতীয় উত্স, ধর্ম বা ধর্মমত, বা ধর্মীয় বিশ্বাস, ধর্মীয় সংগঠন বা ধর্মীয় কার্যকলাপ, বৈবাহিক অবস্থা, পারিবারিক অবস্থা (সম্পত্তি কেনার ক্ষেত্রে প্রযোজ্য নয়), শারীরিক বা মানসিক অক্ষমতা বা সম্পর্কিত বৈশিষ্ট্য বা পরিস্থিতিতে, একটি সেবা পশু, একটি হুইলচেয়ার, বা অন্য কোন প্রতিকারমূলক যন্ত্র বা ডিভাইস, লিঙ্গ (লিঙ্গ নির্ধারিত বৈশিষ্ট্য বা পরিস্থিতিতে সহ, যেমন গর্ভাবস্থা, গর্ভাবস্থার সম্ভাবনা, বা পরিস্থিতিতে গর্ভাবস্থা সম্পর্কিত), যৌন অভিমুখিতা, লিঙ্গ পরিচয়, লিঙ্গ অভিব্যক্তি, বয়স (১৯ এবং তার বেশি, সম্পত্তি কেনার ক্ষেত্রে প্রযোজ্য নয়), অপরাধমূলক প্রত্যয় (শুধুমাত্র কর্মসংস্থানের ক্ষেত্রে প্রযোজ্য) এবং রাজনৈতিক বিশ্বাস, রাজনৈতিক সমিতি, বা রাজনৈতিক কার্যকলাপ, আয়ের উৎস, সামাজিক অসুবিধা।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'bn',
    abbr: 'NB',
    traits: '<p>বয়স, বৈবাহিক অবস্থা, পারিবারিক অবস্থা, ধর্মবিশ্বাস বা ধর্ম, শারীরিক অক্ষমতা, মানসিক অক্ষমতা, জাতি, রঙ, পূর্বপুরুষ, উৎপত্তির স্থান, জাতীয় উৎপত্তি, সামাজিক অবস্থা (যার মধ্যে আয়ের উৎস, শিক্ষার স্তর এবং পেশা অন্তর্ভুক্ত), রাজনৈতিক বিশ্বাস বা কার্যকলাপ, যৌন অভিমুখিতা, লিঙ্গ পরিচয় বা অভিব্যক্তি, এবং লিঙ্গ (গর্ভাবস্থা সহ)।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'bn',
    abbr: 'NL',
    traits: '<p>জাতি, রঙ, জাতীয়তা, জাতিগত উত্স, সামাজিক উত্স, ধর্মীয় ধর্মমত, ধর্ম, বয়স, অক্ষমতা (অনুভূত অক্ষমতা সহ), বিকৃতি, লিঙ্গ (গর্ভাবস্থা সহ), যৌন অভিমুখিতা, লিঙ্গ পরিচয়, লিঙ্গ অভিব্যক্তি, বৈবাহিক অবস্থা, পারিবারিক অবস্থা, আয়ের উৎস, এবং রাজনৈতিক মতামত এবং অপরাধমূলক প্রত্যয় (কর্মসংস্থানের সাথে সম্পর্কহীন), যার মজুরি সাপেক্ষে।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'bn',
    abbr: 'NT',
    traits: '<p>বয়স, অক্ষমতা, জাতি, রঙ, পূর্বপুরুষ, উৎপত্তি স্থান, জাতিগত উত্স, জাতীয়তা, লিঙ্গ, যৌন অভিমুখিতা, লিঙ্গ পরিচয়, লিঙ্গ অভিব্যক্তি, পারিবারিক অবস্থা, পারিবারিক অন্তর্ভুক্তি, বৈবাহিক অবস্থা, সামাজিক অবস্থা, ধর্ম, ধর্মবিশ্বাস, রাজনৈতিক বিশ্বাস, রাজনৈতিক সংঘ, ক্ষমা অপরাধমূলক প্রত্যয় এবং রেকর্ড স্থগিতাদেশ।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'bn',
    abbr: 'NS',
    traits: '<p>বয়স, জাতি, রঙ, ধর্ম, ধর্ম, জাতিগত, জাতীয় বা আদিবাসী মূল, লিঙ্গ (গর্ভাবস্থা এবং বেতন ইকুইটি সহ), যৌন অভিমুখিতা, শারীরিক অক্ষমতা, মানসিক অক্ষমতা, পারিবারিক অবস্থা, বৈবাহিক অবস্থা, আয়ের উৎস, অসুস্থতা বা রোগ চুক্তির অযৌক্তিক ভয়, সুরক্ষিত গোষ্ঠী বা ব্যক্তির সাথে অ্যাসোসিয়েশন, রাজনৈতিক বিশ্বাস, সংযুক্তি বা কার্যকলাপ, লিঙ্গ পরিচয় এবং লিঙ্গ অভিব্যক্তি।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'bn',
    abbr: 'NU',
    traits: '<p>জাতি, রঙ, পূর্বপুরুষ, জাতিগত উত্স, নাগরিকত্ব, উৎপত্তি স্থান, ধর্ম, বয়স, অক্ষমতা, লিঙ্গ, যৌন অভিমুখিতা, বৈবাহিক অবস্থা, পারিবারিক অবস্থা, গর্ভাবস্থা (পুরুষ বা মহিলার দ্বারা সন্তান গ্রহণ সহ), লিঙ্গ পরিচয়, লিঙ্গ অভিব্যক্তি, আয়ের বৈধ উৎস এবং একটি দৃঢ় বিশ্বাস যার জন্য একটি ক্ষমা মঞ্জুর করা হয়েছে।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'bn',
    abbr: 'ON',
    traits: '<p>বয়স, পূর্বপুরুষ, রঙ, জাতি, নাগরিকত্ব, জাতিগত উত্স, মূল স্থান, ধর্ম, অক্ষমতা, পারিবারিক অবস্থা, বৈবাহিক অবস্থা (একক অবস্থা সহ), লিঙ্গ পরিচয়, লিঙ্গ অভিব্যক্তি, জনসাধারণের সহায়তা প্রাপ্তি (শুধুমাত্র গৃহে), অপরাধের রেকর্ড (শুধুমাত্র কর্মসংস্থানে), লিঙ্গ (গর্ভাবস্থা এবং স্তন্যপান করানো সহ), এবং যৌন অভিযোজন।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'bn',
    abbr: 'PE',
    traits: '<p>বয়স, সংঘ (আইনের অধীনে সুরক্ষিত কোন ব্যক্তি বা গোষ্ঠীর সাথে), রঙ, জাতি, ধর্মবিশ্বাস বা ধর্ম, অপরাধমূলক প্রত্যয়, জাতিগত বা জাতীয় উত্স, পারিবারিক অবস্থা বা বৈবাহিক অবস্থা, শারীরিক বা মানসিক অক্ষমতা (সংযোজন এবং অ্যালকোহল/মাদকাসক্তি সহ), রাজনৈতিক বিশ্বাস, যৌন অভিমুখিতা, আয়ের উৎস, লিঙ্গ বা লিঙ্গ (গর্ভাবস্থা এবং যৌন হয়রানি সহ), লিঙ্গ পরিচয় এবং লিঙ্গ অভিব্যক্তি।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'bn',
    abbr: 'QC',
    traits: '<p>জাতি, রঙ, লিঙ্গ, লিঙ্গ পরিচয়, লিঙ্গ অভিব্যক্তি, গর্ভাবস্থা, যৌন অভিমুখিতা, নাগরিক অবস্থা, বয়স (আইন দ্বারা প্রদত্ত ব্যতীত), ধর্ম, রাজনৈতিক দৃঢ় বিশ্বাস, ভাষা, জাতিগত বা জাতীয় উত্স, সামাজিক অবস্থা এবং হস্তশিল্প বা কোন হস্তচালনা উপশম করার জন্য কোন উপায় ব্যবহার।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'bn',
    abbr: 'SK',
    traits: '<p>ধর্ম, ধর্মবিশ্বাস, বৈবাহিক অবস্থা, পারিবারিক অবস্থা (পিতা-মাতার সম্পর্ক সহ), লিঙ্গ (গর্ভাবস্থা অন্তর্ভুক্ত), যৌন অভিমুখিতা, শারীরিক বা মানসিক অক্ষমতা, বয়স (১৮ বা তার বেশি), রঙ, পূর্বপুরুষ, জাতীয়তা, উৎপত্তির স্থান, জাতি বা অনুভূত জাতি, জনসাধারণের সহায়তা এবং লিঙ্গ পরিচয় প্রাপ্তি।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'bn',
    abbr: 'YT',
    traits: '<p>পূর্বপুরুষ (রঙ এবং জাতি সহ), জাতীয় উত্স, জাতিগত বা ভাষাগত পটভূমি বা উত্স, ধর্ম বা ধর্মমত, ধর্মীয় বিশ্বাস, ধর্মীয় সংগঠন, ধর্মীয় কার্যকলাপ, বয়স, লিঙ্গ (গর্ভাবস্থা এবং গর্ভাবস্থা সংক্রান্ত শর্ত সহ), যৌন অভিমুখিতা, লিঙ্গ পরিচয়, লিঙ্গ অভিব্যক্তি, শারীরিক বা মানসিক অক্ষমতা, অপরাধমূলক অভিযোগ বা অপরাধমূলক রেকর্ড, রাজনৈতিক বিশ্বাস, রাজনৈতিক সংগঠন, রাজনৈতিক কার্যকলাপ, বৈবাহিক বা পারিবারিক অবস্থা, আয়ের উৎস এবং অন্যান্য ব্যক্তি বা গোষ্ঠীর সাথে প্রকৃত বা সম্ভাব্য সমিতি যার পরিচয় বা সদস্যপদ হল তালিকাভুক্ত কোন স্থল দ্বারা নির্ধারিত।</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'AB',
    traits: '<p>Φυλή, θρησκευτικές πεποιθήσεις, χρώμα, γένος, ταυτότητα φύλου, έκφραση φύλου, σωματική αναπηρία, διανοητική αναπηρία, ηλικία, καταγωγή, τόπος καταγωγής, οικογενειακή κατάσταση, πηγή εισοδήματος, οικογενειακή κατάσταση και σεξουαλικός προσανατολισμός.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'BC',
    traits: '<p>Φυλή, χρώμα, καταγωγή, τόπος καταγωγής, θρησκεία, οικογενειακή κατάσταση, οικογενειακή κατάσταση, σωματική ή διανοητική αναπηρία, φύλο (περιλαμβάνει άνδρες, γυναίκες, διαφυλικούς ή τρανσέξουαλ, και περιλαμβάνει επίσης εγκυμοσύνη, θηλασμό και σεξουαλική παρενόχληση), σεξουαλικός προσανατολισμός (περιλαμβάνει ετεροφυλόφιλους, ομοφυλόφιλους, λεσβίες ή αμφιφυλόφιλους), ταυτότητα φύλου, έκφραση φύλου, ηλικία (19 ετών και άνω, δεν ισχύει για την αγορά ακινήτου), ποινική καταδίκη ή συνοπτικό αδίκημα που δεν σχετίζεται με την απασχόληση και πολιτικών πεποιθήσεων.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'MB',
    traits: '<p>Φυλή (συμπεριλαμβανομένου του χρώματος και της αντιληπτής φυλής), καταγωγή, εθνοτική καταγωγή ή καταγωγή, εθνικότητα ή εθνική καταγωγή, θρησκεία ή θρήσκευμα ή θρησκευτικές πεποιθήσεις, θρησκευτική ένωση ή θρησκευτική δραστηριότητα, οικογενειακή κατάσταση, οικογενειακή κατάσταση (δεν ισχύει για την αγορά ακινήτου), σωματική ή διανοητική αναπηρία ή συναφή χαρακτηριστικά ή περιστάσεις, συμπεριλαμβανομένης της εξάρτησης από ζώο υπηρεσίας, αναπηρικό καροτσάκι ή οποιαδήποτε άλλη διορθωτική συσκευή ή συσκευή, φύλο (συμπεριλαμβανομένων χαρακτηριστικών ή περιστάσεων που καθορίζονται από το φύλο, όπως η εγκυμοσύνη, πιθανότητα εγκυμοσύνης ή περιστάσεων που σχετίζονται με την εγκυμοσύνη), τον σεξουαλικό προσανατολισμό, την ταυτότητα φύλου, την έκφραση φύλου, την ηλικία (19 ετών και άνω, δεν ισχύει για την αγορά ακινήτου), ποινική καταδίκη (ισχύει μόνο για την απασχόληση) και πολιτικές πεποιθήσεις, πολιτική ένωση ή πολιτική δραστηριότητα, πηγή εισοδήματος, κοινωνικό μειονέκτημα.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'NB',
    traits: '<p>Ηλικία, οικογενειακή κατάσταση, οικογενειακή κατάσταση, δόγμα ή θρησκεία, σωματική αναπηρία, διανοητική αναπηρία, φυλή, χρώμα, καταγωγή, τόπος καταγωγής, εθνική καταγωγή, κοινωνική κατάσταση (η οποία περιλαμβάνει πηγή εισοδήματος, επίπεδο εκπαίδευσης και επάγγελμα), πολιτικές πεποιθήσεις ή δραστηριότητα, ταυτότητα ή έκφραση φύλου σεξουαλικού προσανατολισμού και φύλο (συμπεριλαμβανομένης της εγκυμοσύνης).</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'NL',
    traits: '<p>Φυλή, χρώμα, εθνικότητα, εθνική καταγωγή, κοινωνική καταγωγή, θρησκευτικό δόγμα, θρησκεία, ηλικία, αναπηρία (συμπεριλαμβανομένης της αντιληπτής αναπηρίας), παραμόρφωση, φύλο (συμπεριλαμβανομένης της εγκυμοσύνης), σεξουαλικός προσανατολισμός, ταυτότητα φύλου, έκφραση φύλου, οικογενειακή κατάσταση, οικογενειακή κατάσταση, πηγή εισοδήματος και πολιτική γνώμη και ποινική καταδίκη (άσχετη με την απασχόληση), πρόσωπο του οποίου οι μισθοί υπόκεινται σε κατάσχεση.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'NT',
    traits: '<p>Ηλικία, αναπηρία, φυλή, χρώμα, καταγωγή, τόπος καταγωγής, εθνική καταγωγή, εθνικότητα, φύλο, σεξουαλικός προσανατολισμός, ταυτότητα φύλου, έκφραση φύλου, οικογενειακή κατάσταση, οικογενειακή σχέση, οικογενειακή κατάσταση, κοινωνική κατάσταση, θρησκεία, θρήσκευμα, πολιτική πεποίθηση, πολιτική ένωση, συγχώρεση ποινικής καταδίκης και αναστολή αρχείου.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'NS',
    traits: '<p>Ηλικία, φυλή, χρώμα, θρησκεία, θρήσκευμα, εθνοτική, εθνική ή αυτόχθονη καταγωγή, φύλο (συμπεριλαμβανομένης της εγκυμοσύνης και της ισότητας των αμοιβών), σεξουαλικός προσανατολισμός, σωματική αναπηρία, διανοητική αναπηρία, οικογενειακή κατάσταση, οικογενειακή κατάσταση, πηγή εισοδήματος, παράλογος φόβος προσβολής ασθένειας ή ασθένειας, συσχέτιση με προστατευόμενες ομάδες ή άτομα, πολιτική πεποίθηση, σχέση ή δραστηριότητα, ταυτότητα φύλου και έκφραση φύλου.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'NU',
    traits: '<p>Φυλή, χρώμα, καταγωγή, εθνική καταγωγή, ιθαγένεια, τόπος καταγωγής, θρήσκευμα, θρησκεία, ηλικία, αναπηρία, φύλο, σεξουαλικός προσανατολισμός, οικογενειακή κατάσταση, οικογενειακή κατάσταση, εγκυμοσύνη (συμπεριλαμβανομένης της υιοθεσίας παιδιού από άνδρα ή γυναίκα), ταυτότητα φύλου, έκφραση φύλου, νόμιμη πηγή εισοδήματος και καταδίκη για την οποία έχει χορηγηθεί χάρη.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'ON',
    traits: '<p>Ηλικία, καταγωγή, χρώμα, φυλή, ιθαγένεια, εθνοτική καταγωγή, τόπος καταγωγής, θρήσκευμα, αναπηρία, οικογενειακή κατάσταση, οικογενειακή κατάσταση (συμπεριλαμβανομένης της ενιαίας κατάστασης), ταυτότητα φύλου, έκφραση φύλου, λήψη δημόσιας βοήθειας (μόνο στη στέγαση), καταγραφή αδικημάτων (μόνο στην απασχόληση), φύλο (συμπεριλαμβανομένης της εγκυμοσύνης και του θηλασμού) και σεξουαλικός προσανατολισμός.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'PE',
    traits: '<p>Ηλικία, συσχέτιση (με άτομο ή ομάδα ατόμων που προστατεύονται βάσει του νόμου), χρώμα, φυλή, δόγμα ή θρησκεία, ποινική καταδίκη, εθνοτική ή εθνική καταγωγή, οικογενειακή κατάσταση ή οικογενειακή κατάσταση, σωματική ή διανοητική αναπηρία (συμπεριλαμβανομένης της προσθήκης και αλκοολής/τοξικομανίας), πολιτικές πεποιθήσεις, σεξουαλικός προσανατολισμός, πηγή εισοδήματος, φύλο ή φύλο (συμπεριλαμβανομένης της εγκυμοσύνης και της σεξουαλικής παρενόχλησης), ταυτότητα φύλου και έκφραση φύλου.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'QC',
    traits: '<p>Φυλή, χρώμα, φύλο, ταυτότητα φύλου, έκφραση φύλου, εγκυμοσύνη, γενετήσιος προσανατολισμός, προσωπική κατάσταση, ηλικία (εκτός εάν προβλέπεται από το νόμο), θρησκεία, πολιτικές πεποιθήσεις, γλώσσα, εθνική ή εθνική καταγωγή, κοινωνική κατάσταση και αναπηρία ή χρήση οποιουδήποτε μέσου για την αντιμετώπιση ενός μειονεκτήματος.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'SK',
    traits: '<p>Θρησκεία, θρήσκευμα, οικογενειακή κατάσταση, οικογενειακή κατάσταση (συμπεριλαμβανομένης της σχέσης γονέα-παιδιού), φύλο (συμπεριλαμβανομένης της εγκυμοσύνης), σεξουαλικός προσανατολισμός, σωματική ή διανοητική αναπηρία, ηλικία (18 ή περισσότερο), χρώμα, καταγωγή, εθνικότητα, τόπος καταγωγής, φυλή ή αντιληπτή φυλή, λήψη δημόσιας βοήθειας και ταυτότητα φύλου.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'el',
    abbr: 'YT',
    traits: '<p>Καταγωγή (συμπεριλαμβανομένου του χρώματος και της φυλής), εθνική καταγωγή, εθνικό ή γλωσσικό υπόβαθρο ή καταγωγή, θρησκεία ή δόγμα, θρησκευτική πίστη, θρησκευτική ένωση, θρησκευτική δραστηριότητα, ηλικία, φύλο (συμπεριλαμβανομένης της εγκυμοσύνης και της κατάστασης που σχετίζεται με την εγκυμοσύνη και την εγκυμοσύνη), σεξουαλικός προσανατολισμός, ταυτότητα φύλου, έκφραση φύλου, σωματική ή διανοητική αναπηρία, ποινικές κατηγορίες ή ποινικό μητρώο, πολιτικές πεποιθήσεις, πολιτική ένωση, πολιτική δραστηριότητα, οικογενειακή ή οικογενειακή κατάσταση, πηγή εισοδήματος και πραγματική ή υποτιθέμενη σχέση με άλλα άτομα ή ομάδες Η ταυτότητα ή η ιδιότητα μέλους είναι καθορίζεται από οποιονδήποτε από τους λόγους που αναφέρονται.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'AB',
    traits: '<p>نسل، مذہبی عقائد، رنگ، صنف، صنفی شناخت، صنفی اظہار، جسمانی معذوری، ذہنی معذوری، عمر، آبائی، اصل جگہ، ازدواجی حیثیت، آمدنی کا ذریعہ، خاندان کی حیثیت اور جنسی رجحان.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'BC',
    traits: '<p>نسل، رنگ، نسب، اصل جگہ، مذہب، ازدواجی حیثیت، خاندان کی حیثیت، جسمانی یا ذہنی معذوری، جنس (بشمول مرد، عورت، بین جنس یا مخنث ہونے کے ساتھ ساتھ حمل، دودھ پلانے اور جنسی ہراساں کرنا بھی شامل ہے)، جنسی رجحان (بشمول ہیٹرزجنسی، ہم جنس پرست، ہم جنس پرست یا جنسی)، صنفی شناخت، صنفی اظہار، عمر (19 اور بڑی عمر، جائیداد خریدنے پر لاگو نہیں ہوتا)، مجرمانہ سزا یا ایک خلاصہ جرم روزگار اور سیاسی عقیدے سے متعلق نہیں.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'MB',
    traits: '<p>نسل (بشمول رنگ اور سمجھی نسل)، نسب، تہذیبی پس منظر یا اصل، قومیت یا قومی اصل، مذہب یا عقیدہ، مذہبی ایسوسی ایشن یا مذہبی سرگرمی، ازدواجی حیثیت، خاندانی حیثیت (جائیداد خریدنے پر لاگو نہیں ہوتا)، جسمانی یا ذہنی معذوری یا متعلقہ خصوصیات یا حالات، بشمول خدمت جانور، ایک وہیل چیئر، یا کسی دوسرے علاج کے آلات یا آلہ، جنس (بشمول جنس کے تعین کی خصوصیات یا حالات، جیسے حمل، حمل، یا حالات کا امکان حمل سے متعلق)، جنسی رجحان، صنفی شناخت، صنفی اظہار، عمر (19 اور بڑی عمر، جائیداد خریدنے پر لاگو نہیں ہوتا)، مجرمانہ سزا (صرف روزگار پر لاگو ہوتا ہے) اور سیاسی عقیدہ، سیاسی ایسوسی ایشن، یا سیاسی سرگرمی، ذریعہ آمدنی، سماجی نقصان.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'NB',
    traits: '<p>عمر، ازدواجی حیثیت، خاندان کی حیثیت، عقیدہ یا مذہب، جسمانی معذوری، ذہنی معذوری، نسل، رنگ، نسب، اصل کی جگہ، قومی اصل، سماجی حالت (جس میں آمدنی کا ذریعہ، تعلیم اور قبضے کی سطح شامل ہے)، سیاسی عقیدہ یا سرگرمی، جنسی رجحان صنفی شناخت یا اظہار، اور جنسی (حمل سمیت).</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'NL',
    traits: '<p>نسل، رنگ، قومیت، تہذیبی اصل، سماجی نژاد، مذہبی عقیدہ، مذہب، عمر، معذوری (قابل معذوری سمیت)، بدعنوانی، جنس (حمل سمیت)، جنسی رجحان، صنفی شناخت، صنفی اظہار، ازدواجی حیثیت، خاندان کی حیثیت، ذریعہ آمدنی، اور سیاسی رائے اور مجرمانہ سزا (روزگار سے متعلق نہیں)، وہ شخص جس کی اجرت آرائش کے تابع ہے.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'NT',
    traits: '<p>عمر، معذوری، نسل، رنگ، نسب، اصل کی جگہ، تہذیبی اصل، قومیت، جنس، جنسی رجحان، صنفی شناخت، صنفی اظہار، خاندان کی حیثیت، خاندان کے وابستگی، ازدواجی حیثیت، سماجی حالت، مذہب، عقیدہ، سیاسی عقیدہ، سیاسی ایسوسی ایشن، معاف مجرمانہ سزا اور ریکارڈ معطلی.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'NS',
    traits: '<p>عمر، نسل، رنگ، مذہب، عقیدہ، تہذیبی، قومی یا غیر قانونی نژاد، جنس (حمل اور تنخواہ کے مساوی سمیت)، جنسی رجحان، جسمانی معذوری، ذہنی معذوری، خاندان کی حیثیت، ازدواجی حیثیت، آمدنی کا ذریعہ، کسی بیماری یا بیماری کا معاہدہ کرنے کا غیر معقول خوف، محفوظ گروپوں یا افراد کے ساتھ ایسوسی ایشن، سیاسی عقیدہ، وابستگی یا سرگرمی، صنفی شناخت اور صنفی اظہار.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'NU',
    traits: '<p>نسل، رنگ، نسب، تہذیبی نژاد، شہریت، اصل کی جگہ، عقیدہ، مذہب، عمر، معذوری، جنسی رجحان، ازدواجی حیثیت، خاندان کی حیثیت، حمل (بشمول مرد یا عورت کی طرف سے بچے کو اپنانے سمیت)، صنفی شناخت، صنفی اظہار، آمدنی کا جائز ذریعہ اور ایک سزا جس کے لئے معافی دی گئی ہے.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'ON',
    traits: '<p>عمر، نسب، رنگ، نسل، شہریت، تہذیبی نژاد، اصل جگہ، عقیدہ، معذوری، خاندان کی حیثیت، ازدواجی حیثیت (واحد حیثیت سمیت)، صنفی شناخت، صنفی اظہار، عوامی مدد کی وصولی (صرف رہائش گاہ میں)، ریکارڈ جرائم (صرف روزگار میں)، جنسی (حمل اور دودھ پلانے سمیت)، اور جنسی واقفیت.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'PE',
    traits: '<p>عمر، انجمن (اس قانون کے تحت محفوظ افراد کے کسی فرد یا گروہ کے ساتھ)، رنگ، نسل، عقیدہ یا مذہب، مجرمانہ سزا، تہذیبی یا قومی اصل، خاندانی حیثیت یا ازدواجی حیثیت، جسمانی یا ذہنی معذوری (بشمول اضافہ اور شراب/منشیات کی لت)، سیاسی عقیدہ، جنسی رجحان، آمدنی کا ذریعہ، جنس یا جنس (بشمول حمل اور جنسی ہراسگی)، صنفی شناخت اور صنفی اظہار.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'QC',
    traits: '<p>نسل، رنگ، جنس، صنفی شناخت، صنفی اظہار، حمل، جنسی رجحان، سول حیثیت، عمر (سوائے قانون کے فراہم کردہ کے)، مذہب، سیاسی سزایابی، زبان، تہذیبی یا قومی اصل، سماجی حالت اور معذور یا کسی معذور کو حل کرنے کے کسی بھی ذرائع کا استعمال.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'SK',
    traits: '<p>مذہب، عقیدہ، ازدواجی حیثیت، خاندان کی حیثیت (والدین کے بچے کے تعلقات سمیت)، جنس (حمل بھی شامل ہے)، جنسی رجحان، جسمانی یا ذہنی معذوری، عمر (18 یا اس سے زیادہ)، رنگ، اولاد، قومیت، اصل جگہ، نسل یا سمجھا نسل، عوامی مدد اور صنفی شناخت کی وصولی.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
  {
    language: 'ur',
    abbr: 'YT',
    traits: '<p>نسب (بشمول رنگ اور نسل)، قومی اصل، تہذیبی یا لسانی پس منظر یا اصل، مذہب یا عقیدہ، مذہبی عقیدہ، مذہبی ایسوسی ایشن، مذہبی سرگرمی، عمر، جنس (حمل اور حمل سے متعلقہ حالت سمیت)، جنسی رجحان، صنفی شناخت، صنفی اظہار، جسمانی یا ذہنی معذوری، مجرمانہ الزامات یا مجرمانہ ریکارڈ، سیاسی ایسوسی ایشن، سیاسی سرگرمی، ازدواجی یا خاندانی حیثیت، آمدنی کا ذریعہ اور دیگر افراد کے ساتھ اصل یا متوقع ایسوسی ایشن گروپس جن کی شناخت یا رکنیت درج کردہ بنیادوں میں سے کسی کی طرف سے مقرر کیا گیا ہے.</p>',
    country: 'CA',
    mapType: 'protected_traits',
  },
];


module.exports = {
  up: (queryInterface) => {
    const inserts = [];
    ptds.forEach(({ language, abbr, traits, country, mapType }) => {
      const trait = {
        language,
        abbr,
        traits,
        country,
        mapType,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      inserts.push(trait);
    });
    return queryInterface.bulkInsert('protectedTraitsDefaults', inserts);
  },

  down: async (queryInterface) => {
    const sql = `DELETE FROM protectedTraitsDefaults 
    WHERE language IN('bn', 'el', 'ur')
    AND mapType = 'protected_traits'
    AND country = 'CA'`;

    return queryInterface.sequelize.query(sql, {
      type: queryInterface.sequelize.QueryTypes.DELETE,
    });
  },
};
