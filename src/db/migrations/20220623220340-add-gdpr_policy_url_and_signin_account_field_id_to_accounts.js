module.exports = {
  up: (queryInterface, Sequelize) => {
    queryInterface.addColumn('accounts', 'signinAccountFieldId', Sequelize.INTEGER);
    return queryInterface.addColumn('accounts', 'gdprPolicyUrl', Sequelize.STRING);
  },

  down: (queryInterface, Sequelize) => {
    queryInterface.removeColumn('accounts', 'signinAccountFieldId', Sequelize.INTEGER);
    return queryInterface.removeColumn('accounts', 'gdprPolicyUrl', Sequelize.STRING);
  },
};
