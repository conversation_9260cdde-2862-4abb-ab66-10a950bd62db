module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.changeColumn('users', 'gender', {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        });
      })
      .then(() => {
        return queryInterface.changeColumn('users', 'raceEthnicity', {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        });
      })
      .then(() => {
        return queryInterface.changeColumn('users', 'disabilityStatus', {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        });
      })
      .then(() => {
        return queryInterface.changeColumn('users', 'veteranStatus', {
          type: Sequelize.DataTypes.STRING,
          allowNull: true,
        });
      });
  },

  down: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.changeColumn('users', 'gender', { type: Sequelize.ENUM('male', 'female', 'non-binary', 'prefer-not-say'),
          allowNull: true });
      })
      .then(() => {
        return queryInterface.changeColumn('users', 'raceEthnicity', { type: Sequelize.ENUM('american-indian-alaskan', 'asian', 'hispanic', 'hawaiian-pacific', 'african', 'white', 'multiple', 'prefer-not-say'),
          allowNull: true });
      })
      .then(() => {
        return queryInterface.changeColumn('users', 'disabilityStatus', { type: Sequelize.ENUM('yes', 'no', 'prefer-not-say'),
          allowNull: true });
      })
      .then(() => {
        return queryInterface.changeColumn('users', 'veteranStatus', { type: Sequelize.ENUM('yes', 'no', 'prefer-not-say'),
          allowNull: true });
      });
  },
};
