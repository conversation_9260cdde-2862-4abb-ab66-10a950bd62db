/* eslint-disable max-len */
const logger = require('../../logger');

module.exports = {
  async up(queryInterface) {
    try {
      await queryInterface.sequelize.query("UPDATE lessonCards SET `list1` = 'trueFalse' where `id` = 12839");
    } catch (err) {
      logger.error(err.message);
    }
  },

  async down(queryInterface) {
    try {
      await queryInterface.sequelize.query("UPDATE lessonCards SET `list1` = 'yesNo' where `id` = 12839");
    } catch (err) {
      logger.error(err.message);
    }
  },
};
