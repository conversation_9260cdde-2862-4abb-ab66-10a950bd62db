module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('workdayContentData', 'resourceId', {
      type: Sequelize.Sequelize.INTEGER,
    });
    await queryInterface.addColumn('workdayContentData', 'queuedForRetirement', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('workdayContentData', 'resourceId');
    await queryInterface.removeColumn('workdayContentData', 'queuedForRetirement');
  },
};
