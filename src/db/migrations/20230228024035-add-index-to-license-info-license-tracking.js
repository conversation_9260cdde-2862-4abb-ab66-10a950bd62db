/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addIndex('licenseInfo', ['accountId'], {
      name: 'license_info_account_id',
    });
    await queryInterface.addIndex('licenseTracking', ['accountId', 'userId'], {
      name: 'license_tracking_account_id_user_id',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeIndex('licenseInfo', 'license_info_account_id');
    await queryInterface.removeIndex('licenseTracking', 'license_tracking_account_id_user_id');
  },
};
