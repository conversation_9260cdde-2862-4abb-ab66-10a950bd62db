module.exports = {
  up: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeIndex('answerCards', ['accountId', 'lessonLessonCardId'])
      .then(() => {
        return queryInterface.removeColumn('answerCards', 'accountId');
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    // there is no undo for this script.
    return null;
  },
};
