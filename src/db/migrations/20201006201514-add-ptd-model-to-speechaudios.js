module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('speechAudios', 'model', {
      type: Sequelize.ENUM('lessonCard', 'protectedTraitsDefault'),
      allowNull: false,
    });
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('speechAudios', 'model', {
      type: Sequelize.ENUM('lessonCard'),
      allowNull: false,
    });
  },
};
