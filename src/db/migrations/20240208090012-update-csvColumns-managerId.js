/* eslint-disable no-unused-vars */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      UPDATE csvColumns SET userTableColumn = "managerEmail" WHERE userTableColumn = "managerId";
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      UPDATE csvColumns SET userTableColumn = "managerId" WHERE userTableColumn = "managerEmail";
    `);
  },
};
