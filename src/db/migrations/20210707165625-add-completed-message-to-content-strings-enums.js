module.exports = {
  up: (queryInterface, Sequelize) => {
    const addTypes = queryInterface.changeColumn(
      'contentStrings',
      'field',
      {
        type: Sequelize.ENUM(
          'name', 'title', 'description', 'list1', 'list2', 'list3', 'list4', 'list5', 'list1Detail',
          'list2Detail', 'list3Detail', 'list4Detail', 'question1', 'question2', 'answer',
          'choiceFeedback', 'choiceError', 'question', 'questionEdited', 'certificateText',
          'downloadInstructions', 'completedMessage',
        ),
        allowNull: false,
      },
    );
    return Promise.all([addTypes]);
  },

  down: (queryInterface, Sequelize) => {
    const removeTypes = queryInterface.changeColumn(
      'contentStrings',
      'field',
      {
        type: Sequelize.ENUM(
          'name', 'title', 'description', 'list1', 'list2', 'list3', 'list4', 'list5', 'list1Detail',
          'list2Detail', 'list3Detail', 'list4Detail', 'question1', 'question2', 'answer',
          'choiceFeedback', 'choiceError', 'question', 'questionEdited', 'certificateText',
          'downloadInstructions',
        ),
        allowNull: false,
      },
    );
    return Promise.all([removeTypes]);
  },
};
