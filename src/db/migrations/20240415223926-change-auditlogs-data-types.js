module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('auditLogs', 'originalData', {
      type: Sequelize.JSON,
    });
    return queryInterface.changeColumn('auditLogs', 'updatedData', {
      type: Sequelize.JSON,
    });
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('auditLogs', 'originalData', {
      type: Sequelize.TEXT,
    });
    return queryInterface.changeColumn('auditLogs', 'updatedData', {
      type: Sequelize.TEXT,
    });
  },
};
