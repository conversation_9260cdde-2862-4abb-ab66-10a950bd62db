/* eslint-disable no-unused-vars */
// BST-3078
module.exports = {
  up: (queryInterface, _Sequelize) => {
    const promises = [];
    promises.push(queryInterface.addIndex('roleGroups', {
      fields: ['roleId'],
    }));
    promises.push(queryInterface.addIndex('roleGroups', {
      fields: ['groupId'],
    }));
    promises.push(queryInterface.addIndex('roleGroups', {
      fields: ['roleId', 'groupId'],
    }));
    promises.push(queryInterface.addIndex('accountUsers', {
      fields: ['roleId'],
    }));
    return Promise.all(promises);
  },

  down: (queryInterface, _Sequelize) => {
    const promises = [];
    promises.push(queryInterface.removeIndex('roleGroups', ['roleId']));
    promises.push(queryInterface.removeIndex('roleGroups', ['groupId']));
    promises.push(queryInterface.removeIndex('roleGroups', ['roleId', 'groupId']));
    promises.push(queryInterface.removeIndex('accountUsers', ['roleId']));
    return Promise.all(promises);
  },
};
