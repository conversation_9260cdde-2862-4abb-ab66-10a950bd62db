const csvToJson = require('csvtojson');
const path = require('path');
const db = require('..');
const logger = require('../../logger');

const CatalogItemConcepts = db.catalogItemConcepts;

const readFileIntoRows = (filename) => {
  const fullPathcatalogItemsData = path.join(`${__dirname}/data`, filename);
  return csvToJson().fromFile(fullPathcatalogItemsData);
};

const formatData = (id, data) => {
  data = data.replace(/\s/g, '').split(',');
  const finalArray = data.map((n) => {
    const createObj = {};
    createObj.catalogItemId = id;
    createObj.conceptId = parseInt(n);
    return createObj;
  });
  return finalArray;
};

const mapCatalogConceptsData = async () => {
  const catalogConceptData = await readFileIntoRows('Catalog-Concept-Data.csv');
  let finalData = [];
  for await (const row of catalogConceptData) {
    const catalogId = parseInt(row.catalogId);
    const conceptIds = row.newId;
    if (catalogId && conceptIds) {
      finalData.push(formatData(catalogId, conceptIds));
    }
  }
  finalData = finalData.flat();

  await CatalogItemConcepts.bulkCreate(finalData, {
    fields: ["catalogItemId", "conceptId"],
    updateOnDuplicate: ["conceptId"]
  });
};

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up() {
    try {
      await mapCatalogConceptsData()
    } catch (err) {
      logger.error(err);
    }
  },
  async down() {
    // no going back
  },
};
