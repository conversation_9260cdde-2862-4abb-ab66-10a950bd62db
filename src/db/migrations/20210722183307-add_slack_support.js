'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('users', 'slackId', { type: Sequelize.STRING, defaultValue: null, allowNull: true });
    await queryInterface.addColumn('users', 'slackTeamId', { type: Sequelize.STRING, defaultValue: null, allowNull: true });
    await queryInterface.addColumn('notificationLog', 'slackMessage', { type: Sequelize.BOOLEAN, defaultValue: false });

    await queryInterface.createTable('slackTeams', {
      id: {
        type: Sequelize.STRING,
        allowNull: false,
        primaryKey: true,
      },
      name: Sequelize.STRING,
      accessToken: Sequelize.STRING,
      accountId: Sequelize.INTEGER,
      createdAt: { type: Sequelize.DATE, defaultValue: Sequelize.fn('NOW') },
      updatedAt: { type: Sequelize.DATE, defaultValue: Sequelize.literal('CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP()') }
    });
  },
  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('users', 'slackId');
    await queryInterface.removeColumn('users', 'slackTeamId');
    await queryInterface.removeColumn('notificationLog', 'slackMessage');
    await queryInterface.dropTable('slackTeams');
  },
};
