const db = require('../../db');
const logger = require('../../logger');
const moment = require('moment');

const Users = db.users;

const usersDateOfBirth = async (queryInterface) => {
  try {
    const usersDateOfBirthData = await queryInterface.sequelize.query(
      'SELECT `id`, `dateOfBirth` FROM `users` AS `users` WHERE (`users`.`dateOfBirth` IS NOT NULL)',
      {
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );
    const promises = [];
    for (const data of usersDateOfBirthData) {
      promises.push(Users.update({ birthDate: moment(data.dateOfBirth).format('YYYY-MM-DD') }, {
        where: {
          id: data.id,
        },
      }));
    }
    await Promise.all(promises);
  } catch (err) {
    logger.error(err);
  }
};

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    await usersDateOfBirth(queryInterface);
  },

  async down() {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
  },
};
