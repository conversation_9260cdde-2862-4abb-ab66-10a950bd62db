module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('catalogItems', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      code: {
        type: Sequelize.STRING,
        unique: true,
      },
      internalName: {
        type: Sequelize.STRING,
      },
      contentType: {
        type: Sequelize.ENUM,
        values: ['program', 'lesson'],
        allowNull: false,
      },
      instructionalType: {
        type: Sequelize.ENUM,
        values: ['course', 'lesson', 'microlesson', 'diagnostic'],
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
      },
      edition: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      audience: {
        type: Sequelize.ENUM,
        values: ['employee', 'manager', 'all'],
        allowNull: false,
      },
      duration: {
        type: Sequelize.INTEGER,
      },
      countryId: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      stateId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      regionId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      frequency: {
        type: Sequelize.ENUM,
        values: ['annual', 'biennial'],
      },
      part: {
        type: Sequelize.ENUM,
        values: ['a', 'b', 'ab'],
      },
      isOffering: {
        type: Sequelize.BOOLEAN,
      },
      isClientSpecific: {
        type: Sequelize.BOOLEAN,
      },
      clientId: {
        type: Sequelize.STRING,
      },
      createdBy: {
        type: Sequelize.INTEGER,
      },
      updatedBy: {
        type: Sequelize.INTEGER,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      deletedAt: {
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex(
      'catalogItems',
      ['edition', 'audience', 'duration', 'countryId', 'stateId', 'regionId', 'frequency', 'part'],
      { name: 'catalog_items_unique_fields', unique: true },
    );
  },

  async down(queryInterface) {
    return queryInterface.dropTable('catalogItems');
  },
};
