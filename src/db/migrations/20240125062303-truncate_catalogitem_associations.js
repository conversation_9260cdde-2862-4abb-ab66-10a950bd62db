module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 0');
    await queryInterface.sequelize.query('TRUNCATE TABLE catalogItemAccounts');
    await queryInterface.sequelize.query('TRUNCATE TABLE catalogItemConcepts');
    await queryInterface.sequelize.query('TRUNCATE TABLE catalogItemPillars');
    await queryInterface.sequelize.query('TRUNCATE TABLE catalogItemIndicators');
    await queryInterface.sequelize.query('SET FOREIGN_KEY_CHECKS = 1');
  },
  async down(queryInterface, Sequelize) {
    // no going back
  },
};
