module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('catalogItemAccounts', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      accountId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
      },
      catalogItemId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    return queryInterface.addIndex(
      'catalogItemAccounts',
      ['catalogItemId', 'accountId'],
      { unique: true },
    );
  },
  down: (queryInterface) => {
    return queryInterface.dropTable('catalogItemAccounts');
  },
};
