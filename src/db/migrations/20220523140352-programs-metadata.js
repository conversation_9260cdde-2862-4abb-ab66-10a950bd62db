module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.addColumn('programs', 'catalogId', {
          type: Sequelize.DataTypes.INTEGER,
        });
      })
      .then(() => {
        return queryInterface.addColumn('programs', 'edition', {
          type: Sequelize.DataTypes.INTEGER,
        });
      })
      .then(() => {
        return queryInterface.addColumn('programs', 'version', {
          type: Sequelize.DataTypes.INTEGER,
        });
      })
      .then(() => {
        return queryInterface.addColumn('programs', 'build', {
          type: Sequelize.DataTypes.INTEGER,
        });
      })
      .then(() => {
        return queryInterface.addColumn('programs', 'isBase', {
          type: Sequelize.DataTypes.BOOLEAN,
        });
      })
      .then(() => {
        return queryInterface.addColumn('programs', 'isClone', {
          type: Sequelize.DataTypes.BOOLEAN,
        });
      })
      .then(() => {
        return queryInterface.addColumn('programs', 'isCustomized', {
          type: Sequelize.DataTypes.BOOLEAN,
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeColumn('programs', 'catalogId')
      .then(() => {
        return queryInterface.removeColumn('programs', 'edition');
      })
      .then(() => {
        return queryInterface.removeColumn('programs', 'version');
      })
      .then(() => {
        return queryInterface.removeColumn('programs', 'build');
      })
      .then(() => {
        return queryInterface.removeColumn('programs', 'isBase');
      })
      .then(() => {
        return queryInterface.removeColumn('programs', 'isClone');
      })
      .then(() => {
        return queryInterface.removeColumn('programs', 'isCustomized');
      });
  },
};
