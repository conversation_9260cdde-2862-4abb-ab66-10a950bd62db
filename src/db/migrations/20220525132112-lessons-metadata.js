module.exports = {
  up: async (queryInterface, Sequelize) => {
    return Promise.resolve()
      .then(() => {
        return queryInterface.addColumn('lessons', 'catalogId', {
          type: Sequelize.DataTypes.INTEGER,
        });
      })
      .then(() => {
        return queryInterface.addColumn('lessons', 'edition', {
          type: Sequelize.DataTypes.INTEGER,
        });
      })
      .then(() => {
        return queryInterface.addColumn('lessons', 'version', {
          type: Sequelize.DataTypes.INTEGER,
        });
      })
      .then(() => {
        return queryInterface.addColumn('lessons', 'build', {
          type: Sequelize.DataTypes.INTEGER,
        });
      })
      .then(() => {
        return queryInterface.addColumn('lessons', 'isBase', {
          type: Sequelize.DataTypes.BOOLEAN,
        });
      })
      .then(() => {
        return queryInterface.addColumn('lessons', 'isClone', {
          type: Sequelize.DataTypes.BOOLEAN,
        });
      })
      .then(() => {
        return queryInterface.addColumn('lessons', 'isCustomized', {
          type: Sequelize.DataTypes.BOOLEAN,
        });
      });
  },

  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    return queryInterface.removeColumn('lessons', 'catalogId')
      .then(() => {
        return queryInterface.removeColumn('lessons', 'edition');
      })
      .then(() => {
        return queryInterface.removeColumn('lessons', 'version');
      })
      .then(() => {
        return queryInterface.removeColumn('lessons', 'build');
      })
      .then(() => {
        return queryInterface.removeColumn('lessons', 'isBase');
      })
      .then(() => {
        return queryInterface.removeColumn('lessons', 'isClone');
      })
      .then(() => {
        return queryInterface.removeColumn('lessons', 'isCustomized');
      });
  },
};
