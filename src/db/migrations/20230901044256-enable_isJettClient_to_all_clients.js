const db = require('../../db');

const Op = db.Sequelize.Op;

module.exports = {
  up: (queryInterface, Sequelize) => {
    const migrations = [];
    migrations.push(queryInterface.changeColumn('accounts', 'isJettClient', {
      type: Sequelize.BOOLEAN,
      defaultValue: true,
    }));
    migrations.push(db.accounts.update(
      { isJettClient: true },
      {
        where: {
          isJettClient: false,
        },
      },
    ));
    return Promise.all(migrations);
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.changeColumn('accounts', 'isJettClient', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
    }); // no going back on data change
  },
};
