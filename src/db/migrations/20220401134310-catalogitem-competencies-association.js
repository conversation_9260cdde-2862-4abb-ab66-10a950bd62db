module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('catalogItemPillars', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      catalogItemId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
      },
      socialCapitalPillarId: {
        type: Sequelize.INTEGER,
        primaryKey: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
    await queryInterface.addIndex(
      'catalogItemPillars',
      ['catalogItemId', 'socialCapitalPillarId'],
      { name: 'catalog_items_pillars_fields', unique: true },
    );
  },

  async down(queryInterface) {
    return queryInterface.dropTable('catalogItemPillars');
  },
};
