module.exports = {
  up: (queryInterface, Sequelize) => {
    return queryInterface.dropTable('assessmentItemLessonCards');
  },

  down: (queryInterface, Sequelize) => {
    return queryInterface.createTable('assessmentItemLessonCards', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      assessmentItemId: {
        type: Sequelize.INTEGER,
      },
      lessonCardId: {
        type: Sequelize.INTEGER,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
    });
  },
};
