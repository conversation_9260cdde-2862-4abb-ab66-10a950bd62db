const getRoleFeaturePermissionIds = async (queryInterface, Sequelize) => {
  let roleId = null;
  let permissionId = null;
  let featureId = null;

  const roleResult = await queryInterface.sequelize.query('select id from roles where name="expert"', {
    type: Sequelize.QueryTypes.SELECT,
  });
  if (roleResult.length) {
    roleId = roleResult[0].id;
  }

  const permissionResult = await queryInterface.sequelize.query('select id from permissions where name="read"', {
    type: Sequelize.QueryTypes.SELECT,
  });
  if (permissionResult.length) {
    permissionId = permissionResult[0].id;
  }

  const featureResult = await queryInterface.sequelize.query('select id from features where name="categories"', {
    type: Sequelize.QueryTypes.SELECT,
  });
  if (featureResult.length) {
    featureId = featureResult[0].id;
  }
  return { roleId, permissionId, featureId };
};

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const { roleId, permissionId, featureId } = await getRoleFeaturePermissionIds(queryInterface, Sequelize);

    if (roleId && permissionId && featureId) {
      const roleFeaturePermission = await queryInterface.sequelize.query(
        `select id from roleFeaturePermissions where roleId=${roleId} 
        and featureId=${featureId} and permissionId=${permissionId} limit 1`,
        {
          type: Sequelize.QueryTypes.SELECT,
        },
      );
      if (!roleFeaturePermission.length) {
        await queryInterface.sequelize.query('INSERT INTO roleFeaturePermissions ' +
              `(roleId, featureId, permissionId, createdAt, updatedAt) VALUES ( ${roleId},${featureId},${permissionId},now(),now())`);
      }
    }
  },

  down: async (queryInterface, Sequelize) => {
    const { roleId, permissionId, featureId } = await getRoleFeaturePermissionIds(queryInterface, Sequelize);
    if (roleId && permissionId && featureId) {
      const roleFeaturePermission = await queryInterface.sequelize.query(
        `select id from roleFeaturePermissions where roleId=${roleId} 
    and featureId=${featureId} and permissionId=${permissionId} limit 1`,
        {
          type: Sequelize.QueryTypes.SELECT,
        },
      );

      if (roleFeaturePermission.length) {
        await queryInterface.sequelize.query(`DELETE FROM roleFeaturePermissions WHERE id=${roleFeaturePermission[0].id}`);
      }
    }
  },
};
