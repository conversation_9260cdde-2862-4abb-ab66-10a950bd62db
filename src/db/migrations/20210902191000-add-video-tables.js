module.exports = {
  up: async (queryInterface, Sequelize) => {
    let promises = [];
    promises.push(queryInterface.createTable('videos', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      csVideoId: {
        allowNull: false,
        type: Sequelize.DataTypes.INTEGER,
        unique: true,
      },
      cdnUrl: {
        allowNull: false,
        type: Sequelize.DataTypes.STRING(512),
      },
      ssFilename: {
        allowNull: false,
        type: Sequelize.DataTypes.STRING,
      },
      csLastUpdate: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
      },
    }));
    promises.push(queryInterface.createTable('subtitles', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      videoId: {
        allowNull: false,
        type: Sequelize.DataTypes.INTEGER,
      },
      csSubtitleId: {
        allowNull: false,
        type: Sequelize.DataTypes.INTEGER,
      },
      csVideoId: {
        allowNull: false,
        type: Sequelize.DataTypes.INTEGER,
      },
      languageCode: {
        allowNull: false,
        type: Sequelize.DataTypes.STRING,
      },
      languageProperName: {
        allowNull: false,
        type: Sequelize.DataTypes.STRING,
      },
      csLastUpdate: {
        allowNull: true,
        type: Sequelize.DataTypes.DATE,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
      },
    }));
    promises.push(queryInterface.createTable('captions', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.DataTypes.INTEGER,
      },
      subtitleId: {
        allowNull: false,
        type: Sequelize.DataTypes.INTEGER,
      },
      content: {
        allowNull: false,
        type: Sequelize.DataTypes.TEXT,
      },
      t_begin: {
        allowNull: false,
        type: Sequelize.DataTypes.DECIMAL(10, 3),
      },
      t_end: {
        allowNull: false,
        type: Sequelize.DataTypes.DECIMAL(10, 3),
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DataTypes.DATE,
      },
    }));
    promises.push(queryInterface.changeColumn('semaphores', 'type', {
      type: Sequelize.ENUM(
        'sweeper', 'hrsynch', 'scheduledimports', 'accountsynch', 'groupsynch',
        'campaigncompletion', 'unverifiedusers', 'expertemail', 'notificationsend',
        'texttospeech', 'machinetranslations', 'globalresults', 'videosynch',
      ),
      allowNull: false,
    }));
    // wait for  tables to be created before adding indexes and semaphore record
    await Promise.all(promises);
    promises = [];
    promises.push(queryInterface.sequelize.query(
      'INSERT INTO semaphores (type, status, createdAt, updatedAt) VALUES(?, ?, now(), now())',
      {
        type: queryInterface.sequelize.QueryTypes.INSERT,
        replacements: ['videosynch', 'unlocked'],
      },
    ));
    promises.push(queryInterface.sequelize.query(`
       ALTER TABLE captions MODIFY content TEXT CHARACTER SET utf8mb4;
    `));
    promises.push(queryInterface.addIndex('subtitles', {
      fields: ['videoId'],
    }));
    promises.push(queryInterface.addIndex('subtitles', {
      fields: ['csVideoId'],
    }));
    promises.push(queryInterface.addIndex('captions', {
      fields: ['subtitleId'],
    }));
    return Promise.all(promises);
  },
  down: async (queryInterface, Sequelize) => { // eslint-disable-line no-unused-vars
    const promises = [];
    promises.push(queryInterface.dropTable('videos'));
    promises.push(queryInterface.dropTable('subtitles'));
    promises.push(queryInterface.dropTable('captions'));
    promises.push(queryInterface.sequelize.query('DELETE FROM semaphores WHERE type = ?', {
      type: queryInterface.sequelize.QueryTypes.DELETE,
      replacements: ['videosynch'],
    }));
    // wait for semaphores delete before changing column back
    await Promise.all(promises);
    return queryInterface.changeColumn('semaphores', 'type', {
      type: Sequelize.ENUM(
        'sweeper', 'hrsynch', 'scheduledimports', 'accountsynch', 'groupsynch',
        'campaigncompletion', 'unverifiedusers', 'expertemail', 'notificationsend',
        'texttospeech', 'machinetranslations', 'globalresults',
      ),
      allowNull: false,
    });
  },
};
