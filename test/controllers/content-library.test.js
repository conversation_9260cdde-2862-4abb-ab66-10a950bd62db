/* eslint-disable max-len */
const chai = require('chai');
const server = require('../../src').getApp();
const db = require('../../src/db');
const {
  login,
  getToken,
  loadUserData,
  loadGeoLocationData,
} = require('../utils/authUtils');
const { format, addHours } = require('date-fns');
const { updateMclData } = require('../../src/services/utils/contentLibraryUtils');

const { queryInterface } = db.sequelize;
const ROOT_URL = '/content-library';
const expect = chai.expect;
const timestamp = format(new Date(), 'YYYY-MM-DD HH:mm:ss');
const pillarsData = [
  {
    id: 1,
    name: 'Competency',
    createdBy: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 2,
    name: 'Planning',
    createdBy: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];
const categoriesData = [
  {
    id: 1,
    priority: 1,
    name: 'Discrimination & Harassment',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 2,
    priority: 2,
    name: 'Ethics & Compliance',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];
const conceptData = [
  {
    id: 1,
    categoryId: 1,
    concept: 'AA (Affirmative Action)',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 2,
    categoryId: 2,
    concept: 'Price fixing',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];
const indicatorsData = [
  {
    id: 1,
    name: 'Accountability',
    createdBy: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 2,
    name: 'Allyship',
    createdBy: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];
const listingData = [
  {
    id: 1,
    rank: 1,
    type: 'course',
    lifecycle: 'published',
    title: 'Listing-1',
    countryId: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
    lastMappedAt: timestamp,
  },
  {
    id: 2,
    rank: 2,
    type: 'microlesson',
    lifecycle: 'published',
    title: 'Listing-2',
    countryId: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
    lastMappedAt: timestamp,
  },
];
const listingPillarsData = [
  {
    listingId: 1,
    pillarId: 1,
  },
  {
    listingId: 2,
    pillarId: 2,
  },
];
const catalogObj = [
  {
    id: 1,
    code: 'test',
    contentType: 'program',
    instructionalType: 'course',
    title: 'Global Anti-Bribery and Corruption-1',
    description: 'Test Description',
    regionId: 1,
    countryId: 1,
    listingId: 1,
    stateId: 1,
    edition: 4,
    duration: 90,
    audience: 'all',
    frequency: 'biennial',
    part: 'ab',
    isOffering: true,
    isClientSpecific: false,
    clientId: '',
    createdBy: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
    recommendType: 'recommend',
  },
  {
    id: 2,
    code: 'D&A',
    contentType: 'lesson',
    instructionalType: 'microlesson',
    title: 'Global Anti-Bribery and Corruption-2',
    description: 'Test Description',
    regionId: 2,
    countryId: 2,
    stateId: 2,
    listingId: 2,
    edition: 2,
    duration: 60,
    audience: 'all',
    frequency: 'biennial',
    part: 'a',
    isOffering: false,
    isClientSpecific: true,
    clientId: '',
    createdBy: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
    recommendType: 'toprecommend',
  },
];
const lessons = [
  {
    resourceId: 5,
    published: true,
    title: 'How to Create Awesome Tests',
    description: 'Describes how to test a server',
    lifecycle: 'publish',
    userId: 4,
    createdAt: timestamp,
    updatedAt: format(addHours(new Date(), 1), 'YYYY-MM-DD HH:mm:ss'),
    catalogId: 2,
  },
  {
    resourceId: 2,
    published: false,
    title: 'How to Train your Dragon',
    description: 'Pixar movie',
    lifecycle: 'publish',
    userId: 4,
    createdAt: timestamp,
    updatedAt: format(addHours(new Date(), 2), 'YYYY-MM-DD HH:mm:ss'),
    catalogId: 2,
  },
  {
    resourceId: 3,
    published: true,
    title: 'How to avoid workplace conflict',
    description: 'Description',
    lifecycle: 'publish',
    userId: 4,
    createdAt: timestamp,
    updatedAt: timestamp,
    catalogId: 2,
  },
  {
    resourceId: 1,
    published: false,
    title: 'How to Use dummy data in more detail',
    description: 'Describes how create fake data for tests again',
    lifecycle: 'publish',
    userId: 4,
    createdAt: timestamp,
    updatedAt: format(addHours(new Date(), 48), 'YYYY-MM-DD HH:mm:ss'),
    catalogId: 2,
  },
  {
    resourceId: 4,
    published: false,
    title: 'How to Use dummy data',
    description: 'Describes how create fake data for tests',
    lifecycle: 'publish',
    userId: 4,
    createdAt: timestamp,
    updatedAt: format(addHours(new Date(), 48), 'YYYY-MM-DD HH:mm:ss'),
  },
];
const resources = [
  {
    digestable: 'lessons',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: 'pending lesson edited and moved to assigned',
    helpfulCount: 1,
    isPublic: false,
    isBrowsable: true,
  },
  {
    digestable: 'lessons',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: 'How to Train your Dragon Pixar movie',
    helpfulCount: 1,
    isPublic: false,
    isBrowsable: true,
  },
  {
    digestable: 'lessons',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: 'answered question 1',
    helpfulCount: 1,
    isPublic: false,
    isBrowsable: true,
  },
  {
    digestable: 'lessons',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: 'answered question 2',
    helpfulCount: 1,
    isPublic: false,
    isBrowsable: true,
  },
  {
    digestable: 'lessons',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: 'answered question 3',
    helpfulCount: 1,
    isPublic: false,
    isBrowsable: true,
  },
  {
    digestable: 'lessons',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: 'answered private question',
    helpfulCount: 1,
    isPublic: false,
    isBrowsable: true,
  },
  {
    digestable: 'lessons',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: 'archived question',
    helpfulCount: 1,
    isPublic: false,
    isBrowsable: true,
  },
  {
    // 8
    digestable: 'programs',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: '',
    isPublic: true,
    isBrowsable: true,
  },
  {
    digestable: 'programs',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: '',
    isPublic: true,
    isBrowsable: true,
  },
  {
    digestable: 'programs',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: '',
    isPublic: true,
    isBrowsable: true,
  },
  {
    digestable: 'programs',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: '',
    isPublic: false,
    isBrowsable: true,
  },
  {
    digestable: 'programs',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: '',
    isPublic: false,
    isBrowsable: true,
  },
];
const programs = [
  {
    name: 'test program 1',
    description: 'test program 1 description',
    internalName: 'test_program_1',
    lifecycle: 'publish',
    hasCertificate: true,
    certificateText: 'certificate text',
    createdAt: timestamp,
    updatedAt: format(addHours(new Date(), 2), 'YYYY-MM-DD HH:mm:ss'),
    fileId: 1,
    minTimeInMinutes: 180,
    minCardTimeInSeconds: 3600,
    downloadInstructions: 'just push the button',
    completedMessage: 'program completed',
    resourceId: 8,
    catalogId: 1,
  },
  {
    name: 'test program 2',
    description: 'test program 2 description',
    internalName: 'test_program_2',
    lifecycle: 'publish',
    hasCertificate: true,
    certificateText: 'certificate text for program 2',
    createdAt: timestamp,
    updatedAt: format(addHours(new Date(), 1), 'YYYY-MM-DD HH:mm:ss'),
    fileId: null,
    minTimeInMinutes: 90,
    minCardTimeInSeconds: 4800,
    downloadInstructions: 'ask your manager',
    completedMessage: 'done!',
    resourceId: 9,
    catalogId: 1,
  },
  {
    name: 'test program 3',
    description: 'test program 3 description',
    internalName: 'test_program_3',
    lifecycle: 'publish',
    hasCertificate: false,
    createdAt: timestamp,
    updatedAt: timestamp,
    fileId: null,
    minTimeInMinutes: 90,
    minCardTimeInSeconds: 4800,
    resourceId: 10,
  },
  {
    name: 'test program 4',
    internalName: 'test_program_4',
    description: 'test program 4 description',
    lifecycle: 'publish',
    hasCertificate: true,
    certificateText: 'some text goes here',
    downloadInstructions: 'download instructions go here',
    completedMessage: 'program completed message here',
    createdAt: timestamp,
    updatedAt: timestamp,
    fileId: null,
    resourceId: 11,
  },
  {
    name: 'test program 5',
    description: 'test program 5 description',
    internalName: 'test_program_5',
    lifecycle: 'publish',
    hasCertificate: false,
    createdAt: timestamp,
    updatedAt: timestamp,
    fileId: null,
    resourceId: 12,
    catalogId: 1,
  },
];
const lessonPrograms = [
  {
    programId: 1,
    lessonId: 1,
    position: 3,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    programId: 1,
    lessonId: 2,
    position: 2,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    programId: 1,
    lessonId: 3,
    position: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    programId: 2,
    lessonId: 4,
    position: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    programId: 2,
    lessonId: 5,
    position: 2,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    programId: 4,
    lessonId: 1,
    position: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];
const resourceBundles = [
  {
    bundleId: 1,
    resourceId: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 2,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 3,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 4,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 5,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 6,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 7,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 8,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 9,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 10,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];

async function loadConceptData() {
  await loadUserData();
  await loadGeoLocationData();
  await queryInterface.bulkInsert('categories', categoriesData);
  await queryInterface.bulkInsert('concepts', conceptData);
  await queryInterface.bulkInsert('socialCapitalIndicators', indicatorsData);
  await queryInterface.bulkInsert('socialCapitalPillars', pillarsData);
  await queryInterface.bulkInsert('listings', listingData);
  await queryInterface.bulkInsert('listingPillars', listingPillarsData);
  await queryInterface.bulkInsert('catalogItems', catalogObj);
  await queryInterface.bulkInsert('resources', resources);
  await queryInterface.bulkInsert('lessons', lessons);
  await queryInterface.bulkInsert('programs', programs);
  await queryInterface.bulkInsert('lessonPrograms', lessonPrograms);
  await queryInterface.bulkInsert('resourceBundles', resourceBundles);
  await updateMclData();
}
const defaultParams = {
  $sort: 'alphabetical',
  $limit: 10,
  $skip: 0,
};
/* eslint prefer-arrow-callback: 0 */
/* eslint no-unused-expressions: 0 */
describe('Content Library Api', () => {
  before(async () => {
    await hootsworth.util.truncateSchema();
    await loadConceptData();
    await login('<EMAIL>');
  });
  describe('MCL listing page with newest', () => {
    it('should get a list of all content library data', async () => {
      const res = await chai
        .request(server)
        .post(`${ROOT_URL}`)
        .set('authorization', getToken())
        .send(defaultParams);
      expect(res).to.have.status(200);
      expect(res).to.be.json;
      expect(res.body).to.have.all.keys('total', 'limit', 'skip', 'data', 'isCustomTraining');
      expect(res.body.data).to.have.lengthOf(2);
      expect(res.body.data[0]).to.include({
        id: 1,
        type: 'course',
        title: 'Listing-1',
      });
    });
    it('should get a list of all content library data with pillar filters', async () => {
      const res = await chai
        .request(server)
        .post(`${ROOT_URL}`)
        .set('authorization', getToken())
        .send({ ...defaultParams, pillar: 2 });
      expect(res).to.have.status(200);
      expect(res).to.be.json;
      expect(res.body).to.have.all.keys('total', 'limit', 'skip', 'data', 'isCustomTraining');
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        type: 'microlesson',
        title: 'Listing-2',
      });
      expect(res.body.data[0].pillars[0]).to.include({
        id: 2,
        name: 'Planning',
      });
    });
    it('should get a list of all content library data with matching contentId search', async () => {
      const res = await chai
        .request(server)
        .post(`${ROOT_URL}`)
        .set('authorization', getToken())
        .send({ ...defaultParams, contentId: 4 });
      expect(res).to.have.status(200);
      expect(res).to.be.json;
      expect(res.body).to.have.all.keys('program', 'lesson');
      expect(res.body.lesson).to.include({
        id: 4,
        title: 'How to Use dummy data in more detail',
      });
    });
    it('should get a list of all content library data with matching location filter', async () => {
      const res = await chai
        .request(server)
        .post(`${ROOT_URL}`)
        .set('authorization', getToken())
        .send({ ...defaultParams, location: ['All Countries'] });
      expect(res).to.have.status(200);
      expect(res).to.be.json;
      expect(res.body.data).to.have.lengthOf(2);
      expect(res.body.data[0]).to.include({
        id: 1,
        type: 'course',
        title: 'Listing-1',
      });
    });
    it('should get a list of all content library data with matching audience filter', async () => {
      const res = await chai
        .request(server)
        .post(`${ROOT_URL}`)
        .set('authorization', getToken())
        .send({ ...defaultParams, audience: ['all'] });
      expect(res).to.have.status(200);
      expect(res).to.be.json;
      expect(res.body).to.have.all.keys('total', 'limit', 'skip', 'data', 'isCustomTraining');
    });
  });
  describe('concepts filterlist', () => {
    it('should get a list of all concepts data', async () => {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/filter/list?type=concept`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      expect(res).to.be.json;
      expect(res.body.data).to.have.lengthOf(2);
      expect(res.body.data[0]).to.include({
        id: 1,
        concept: 'AA (Affirmative Action)',
        category: 'Discrimination & Harassment',
      });
    });
    it('should get an ascending ordered list of concept data', async () => {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/filter/list?type=concept`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      expect(res.body.data).to.have.lengthOf(2);
      expect(res.body.data[0]).to.include({
        concept: 'AA (Affirmative Action)',
      });
      expect(res.body.data[1]).to.include({
        concept: 'Price fixing',
      });
    });
  });
  describe('social indicator filterlist', () => {
    it('should get a list of all social indicators data', async () => {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/filter/list?type=indicator`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      expect(res).to.be.json;
      expect(res.body.data).to.have.lengthOf(2);
      expect(res.body.data[0]).to.include({
        id: 1,
        name: 'Accountability',
      });
    });
    it('should get an ascending ordered list of social indicators data', async () => {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/filter/list?type=indicator`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      expect(res.body.data).to.have.lengthOf(2);
      expect(res.body.data[0]).to.include({
        name: 'Accountability',
      });
      expect(res.body.data[1]).to.include({
        name: 'Allyship',
      });
    });
  });
  describe('content library detail page', () => {
    it('should get a content detail based on listing', async () => {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/details/listing/1`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      expect(res).to.be.json;
      expect(res.body).to.include({
        id: 1,
        title: 'Listing-1',
        listType: 'listing',
      });
    });
    it('should get a content detail based on catalog', async () => {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/details/catalog/1`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
    });
  });
  describe('content library courselessons', () => {
    it('should get courselessons based on courseId', async () => {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/courselessons/1`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      expect(res).to.be.json;
      expect(res.body.lessons).to.have.lengthOf(3);
    });
    it('should fail to fetch courselessons for invalid courseId', async () => {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/courselessons/99`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      expect(res).to.be.json;
      expect(res.body).to.equal(null);
    });
  });

  describe('Account program and Lesson title overrides', () => {
    it('should test overrides program title', async () => {
      const overrides = {
        title: 'Override Program Title',
      };
      const res1 = await chai.request(server)
        .post(`${ROOT_URL}/1/program/manageContentConfiguration`)
        .set('authorization', getToken())
        .send(overrides);
      expect(res1).to.have.status(200);
      expect(res1).to.be.json;
      expect(res1.body).to.be.an('object');
      expect(res1.body).to.include({
        name: 'Override Program Title',
        programId: 1,
      });
    });
    it('should test overrides lesson title', async () => {
      const overrides = {
        title: 'Override Lesson Title',
      };
      const res2 = await chai.request(server)
        .post(`${ROOT_URL}/3/lesson/manageContentConfiguration`)
        .set('authorization', getToken())
        .send(overrides);
      expect(res2).to.have.status(200);
      expect(res2).to.be.json;
      expect(res2.body).to.be.an('object');
      expect(res2.body).to.include({
        title: 'Override Lesson Title',
        lessonId: 3,
      });
    });
    it('should test overrides program Configuration', async () => {
      const overrides = {
        hasLastCardMessage: true,
        completedMessage: 'Thanks You.',
        hasCertificate: true,
        certificateText: 'Certificates are an effective way to recognize accomplishments and show appreciation.',
        downloadInstructions: 'Download these Free Instructions MCQ Quiz Pdf',
        minTimeInMinutes: '5',
      };
      const res1 = await chai.request(server)
        .post(`${ROOT_URL}/1/program/manageContentConfiguration`)
        .set('authorization', getToken())
        .send(overrides);
      expect(res1).to.have.status(200);
      expect(res1).to.be.json;
      expect(res1.body).to.be.an('object');
      expect(res1.body).to.include({
        name: 'Override Program Title',
        hasLastCardMessage: true,
        hasCertificate: true,
        minTimeInMinutes: 5,
        minCardTimeInSeconds: 3600,
      });
    });
  });
});
