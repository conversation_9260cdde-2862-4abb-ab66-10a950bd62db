const chai = require('chai');
const { login, getToken, loadUserData } = require('../utils/authUtils');
const server = require('../../src').getApp();
const db = require('../../src/db');

const { queryInterface } = db.sequelize;
const ROOT_URL = '/user-lessons';
const expect = chai.expect;
const timestamp = new Date();

// Test data
const baseAssignments = [
  {
    id: 1,
    accountId: 1, // Changed to match admin account
    assignmentType: 'external',
    status: 'open',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 2,
    accountId: 1, // Changed to match admin account
    assignmentType: 'external',
    status: 'open', // Changed to open so it will be counted
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 3,
    accountId: 1, // Additional baseAssignment for lesson test
    assignmentType: 'external',
    status: 'open',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 4,
    accountId: 1, // Standalone lesson assignment for resourceId 2
    assignmentType: 'external',
    status: 'open',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 5,
    accountId: 1, // Program assignment for resourceId 2
    assignmentType: 'external',
    status: 'open',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];

const programs = [
  {
    id: 1,
    name: 'Test Program 1',
    description: 'Test program description',
    lifecycle: 'publish',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];

const lessons = [
  {
    id: 1,
    title: 'Test Lesson 1',
    description: 'Test lesson description',
    lifecycle: 'publish',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 2,
    title: 'Test Lesson 2',
    description: 'Test lesson description 2',
    lifecycle: 'publish',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];



const userLessons = [
  {
    id: 1,
    userId: 1,
    resourceId: 1,
    baseAssignmentId: 1, // First baseAssignment
    baseAssignmentParentProgramId: null, // Standalone program assignment
    type: 'program',
    status: 'open',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 2,
    userId: 2,
    resourceId: 1,
    baseAssignmentId: 2, // Second baseAssignment
    baseAssignmentParentProgramId: null, // Standalone program assignment
    type: 'program',
    status: 'inProgress',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 3,
    userId: 3,
    resourceId: 1,
    baseAssignmentId: 1, // Same as first, but completed (shouldn't be counted)
    baseAssignmentParentProgramId: null, // Standalone program assignment
    type: 'program',
    status: 'completed',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 4,
    userId: 1,
    resourceId: 1,
    baseAssignmentId: 3, // Third baseAssignment for lesson
    baseAssignmentParentProgramId: null, // Standalone lesson assignment
    type: 'lesson',
    status: 'open',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  // Additional test data for campaign assignments (should not be counted/closed)
  {
    id: 5,
    userId: 4,
    resourceId: 1,
    assignmentId: 1, // Campaign assignment (no baseAssignmentId)
    baseAssignmentId: null,
    baseAssignmentParentProgramId: null,
    type: 'program',
    status: 'open',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  // Test data for lesson assigned both as standalone and as part of program
  {
    id: 6,
    userId: 5,
    resourceId: 2, // Different lesson
    baseAssignmentId: 4, // Standalone lesson assignment
    baseAssignmentParentProgramId: null,
    type: 'lesson',
    status: 'open',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 7,
    userId: 5,
    resourceId: 2, // Same lesson, but part of program assignment
    baseAssignmentId: 5, // Program assignment
    baseAssignmentParentProgramId: 2, // Part of program with resourceId 2
    type: 'lesson',
    status: 'open',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    id: 8,
    userId: 5,
    resourceId: 2, // Program assignment parent
    baseAssignmentId: 5, // Same baseAssignment as lesson above
    baseAssignmentParentProgramId: null,
    type: 'program',
    status: 'open',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];

async function loadTestData() {
  await loadUserData();
  await queryInterface.bulkInsert('programs', programs);
  await queryInterface.bulkInsert('lessons', lessons);
  await queryInterface.bulkInsert('baseAssignments', baseAssignments);
  await queryInterface.bulkInsert('userLessons', userLessons);
}

/* eslint prefer-arrow-callback: 0 */
/* eslint no-unused-expressions: 0 */
describe('User Lessons Assignment Count API', function () {
  before(async function () {
    await hootsworth.util.truncateSchema();
    await loadTestData();
    await login('<EMAIL>');
  });

  describe('GET /user-lessons/assignment-learner-count/:contentId', function () {
    it('should return count of learners with open assignments for a program', async function () {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/assignment-learner-count/1?type=program`)
        .set('authorization', getToken());

      expect(res).to.have.status(200);
      expect(res.body).to.be.an('object');
      expect(res.body).to.have.property('count');
      expect(res.body.count).to.equal(2); // Users 1 and 2 have open/inProgress status
    });

    it('should return count of learners with open assignments for a lesson', async function () {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/assignment-learner-count/1?type=lesson`)
        .set('authorization', getToken());

      expect(res).to.have.status(200);
      expect(res.body).to.be.an('object');
      expect(res.body).to.have.property('count');
      expect(res.body.count).to.equal(1); // Only user 1 has open lesson
    });

    it('should return 400 for invalid content type', async function () {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/assignment-learner-count/1?type=invalid`)
        .set('authorization', getToken());

      expect(res).to.have.status(400);
    });

    it('should return 400 for missing type parameter', async function () {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/assignment-learner-count/1`)
        .set('authorization', getToken());

      expect(res).to.have.status(400);
    });

    it('should return 400 for invalid contentId', async function () {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/assignment-learner-count/invalid?type=program`)
        .set('authorization', getToken());

      expect(res).to.have.status(400);
    });

    it('should return 0 for non-existent content', async function () {
      const res = await chai.request(server)
        .get(`${ROOT_URL}/assignment-learner-count/999?type=program`)
        .set('authorization', getToken());

      expect(res).to.have.status(200);
      expect(res.body.count).to.equal(0);
    });

    it('should not count campaign assignments (userLessons without baseAssignmentId)', async function () {
      // User 4 has a program assignment via campaign (assignmentId but no baseAssignmentId)
      // This should not be counted in external assignment count
      const res = await chai.request(server)
        .get(`${ROOT_URL}/assignment-learner-count/1?type=program`)
        .set('authorization', getToken());

      expect(res).to.have.status(200);
      expect(res.body.count).to.equal(2); // Still only 2, campaign assignment not counted
    });

    it('should only count standalone lesson assignments (baseAssignmentParentProgramId is null)', async function () {
      // For lesson resourceId 2:
      // - User 5 has it assigned as standalone lesson (baseAssignmentId: 4, baseAssignmentParentProgramId: null)
      // - User 5 also has it as part of program assignment (baseAssignmentId: 5, baseAssignmentParentProgramId: 2)
      // Only the standalone assignment should be counted
      const res = await chai.request(server)
        .get(`${ROOT_URL}/assignment-learner-count/2?type=lesson`)
        .set('authorization', getToken());

      expect(res).to.have.status(200);
      expect(res.body.count).to.equal(1); // Only the standalone lesson assignment
    });
  });

  describe('POST /user-lessons/close-assignments/:contentId', function () {
    it('should close program assignments and all associated lesson assignments', async function () {
      const res = await chai.request(server)
        .post(`${ROOT_URL}/close-assignments/2?type=program`)
        .set('authorization', getToken());

      expect(res).to.have.status(200);
      expect(res.body).to.have.property('closedBaseAssignments');
      expect(res.body).to.have.property('closedUserLessons');
      expect(res.body.closedBaseAssignments).to.equal(1); // baseAssignment 5
      expect(res.body.closedUserLessons).to.equal(2); // Both program and lesson userLessons with baseAssignmentId 5
    });

    it('should only close standalone lesson assignments, not those that are part of program assignments', async function () {
      // Before closing, verify we have the standalone lesson assignment
      let countRes = await chai.request(server)
        .get(`${ROOT_URL}/assignment-learner-count/2?type=lesson`)
        .set('authorization', getToken());
      expect(countRes.body.count).to.equal(1);

      // Close the standalone lesson assignment
      const res = await chai.request(server)
        .post(`${ROOT_URL}/close-assignments/2?type=lesson`)
        .set('authorization', getToken());

      expect(res).to.have.status(200);
      expect(res.body.closedBaseAssignments).to.equal(1); // baseAssignment 4 (standalone)
      expect(res.body.closedUserLessons).to.equal(1); // Only the standalone lesson userLesson

      // Verify the count is now 0 for standalone lesson assignments
      countRes = await chai.request(server)
        .get(`${ROOT_URL}/assignment-learner-count/2?type=lesson`)
        .set('authorization', getToken());
      expect(countRes.body.count).to.equal(0);
    });

    it('should not close campaign assignments (userLessons without baseAssignmentId)', async function () {
      // User 4 has a campaign assignment that should not be affected
      const res = await chai.request(server)
        .post(`${ROOT_URL}/close-assignments/1?type=program`)
        .set('authorization', getToken());

      expect(res).to.have.status(200);
      // Should close the 2 external assignments but not the campaign assignment
      expect(res.body.closedBaseAssignments).to.equal(2); // baseAssignments 1 and 2
      expect(res.body.closedUserLessons).to.equal(2); // userLessons 1 and 2 (userLesson 3 is 'completed' so not closed, userLesson 5 is campaign)
    });

    it('should return 400 for invalid content type', async function () {
      const res = await chai.request(server)
        .post(`${ROOT_URL}/close-assignments/1?type=invalid`)
        .set('authorization', getToken());

      expect(res).to.have.status(400);
    });

    it('should return 400 for missing type parameter', async function () {
      const res = await chai.request(server)
        .post(`${ROOT_URL}/close-assignments/1`)
        .set('authorization', getToken());

      expect(res).to.have.status(400);
    });
  });
});
