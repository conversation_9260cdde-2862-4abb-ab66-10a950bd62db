const chai = require('chai');
const fs = require('fs');
const path = require('path');
const openpgp = require('openpgp');

const { expect } = chai;

/* <PERSON><PERSON> prefers not using arrow functions */
/* eslint prefer-arrow-callback: 0 */
/* eslint-env mocha */
describe('FileUtils Service - PGP Decryption', function () {
  let testPrivateKey;
  let testPublicKey;
  let testKeyPath;

  before(async function () {
    this.timeout(10000); // PGP key generation can be slow

    // Generate test PGP keys for testing
    const { privateKey, publicKey } = await openpgp.generateKey({
      type: 'rsa',
      rsaBits: 2048,
      userIDs: [{ name: 'Test User', email: '<EMAIL>' }],
      passphrase: 'TestPassphrase123'
    });

    testPrivateKey = privateKey;
    testPublicKey = publicKey;

    // Create a temporary key file for testing
    testKeyPath = path.join(__dirname, '../files/test-pgp-key.asc');
    fs.writeFileSync(testKeyPath, testPrivateKey);
  });

  after(function () {
    // Clean up test files
    if (fs.existsSync(testKeyPath)) {
      fs.unlinkSync(testKeyPath);
    }
  });

  describe('PGP Key Operations', function () {
    it('should successfully load and decrypt a private key', async function () {
      const privateKey = await openpgp.readPrivateKey({ armoredKey: testPrivateKey });
      const decryptedPrivateKey = await openpgp.decryptKey({
        privateKey,
        passphrase: 'TestPassphrase123'
      });

      expect(decryptedPrivateKey).to.exist;
      expect(decryptedPrivateKey.isDecrypted()).to.be.true;
    });

    it('should successfully encrypt and decrypt data', async function () {
      const testMessage = 'email,firstName,lastName\<EMAIL>,Test,User';

      const publicKeyObj = await openpgp.readKey({ armoredKey: testPublicKey });
      const message = await openpgp.createMessage({ text: testMessage });
      const encrypted = await openpgp.encrypt({
        message,
        encryptionKeys: publicKeyObj,
        format: 'binary'
      });

      const privateKey = await openpgp.readPrivateKey({ armoredKey: testPrivateKey });
      const decryptedPrivateKey = await openpgp.decryptKey({
        privateKey,
        passphrase: 'TestPassphrase123'
      });

      const encryptedMessage = await openpgp.readMessage({ binaryMessage: encrypted });
      const { data: decrypted } = await openpgp.decrypt({
        message: encryptedMessage,
        decryptionKeys: decryptedPrivateKey,
        format: 'utf8'
      });

      expect(decrypted).to.equal(testMessage);
    });

    it('should fail with incorrect passphrase', async function () {
      const privateKey = await openpgp.readPrivateKey({ armoredKey: testPrivateKey });

      try {
        await openpgp.decryptKey({
          privateKey,
          passphrase: 'WrongPassphrase'
        });
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.include('passphrase');
      }
    });

    it('should handle malformed key data', async function () {
      const malformedKey = 'This is not a valid PGP key';

      try {
        await openpgp.readPrivateKey({ armoredKey: malformedKey });
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.include('armored');
      }
    });
  });

  describe('OpenPGP v5 Compatibility', function () {
    it('should work with Node.js 20.19.4', async function () {
      expect(process.version).to.match(/^v20\./);

      const message = await openpgp.createMessage({ text: 'test' });
      expect(message).to.exist;
    });

    it('should support compatibility configuration', async function () {
      const config_openpgp = {
        allowInsecureDecryptionWithSigningKeys: true,
        ignoreUnsupportedPackets: true,
        ignoreMalformedPackets: true,
      };

      const testMessage = 'test message';
      const publicKeyObj = await openpgp.readKey({ armoredKey: testPublicKey });
      const message = await openpgp.createMessage({ text: testMessage });
      const encrypted = await openpgp.encrypt({
        message,
        encryptionKeys: publicKeyObj,
        config: config_openpgp
      });

      expect(encrypted).to.exist;
    });
  });
});
