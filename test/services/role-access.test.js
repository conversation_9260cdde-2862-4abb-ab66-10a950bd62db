const chai = require('chai');
const db = require('../../src/db');
const server = require('../../src').getApp();
const { login, getToken } = require('../utils/authUtils');
const { format, addHours } = require('date-fns');

const { queryInterface } = db.sequelize;
const expect = chai.expect;
const timestamp = format(new Date(), 'YYYY-MM-DD HH:mm:ss');

const CAMPAIGNS_URL = '/campaigns';
const PROGRAMS_URL = '/programs';
const LESSONS_URL = '/lessons';
const LESSON_CARDS_URL = '/lesson-cards';
const ANSWER_CARDS_URL = '/answer-cards';

const users = [
  {
    firstName: 'Test1',
    lastName: 'User',
    email: '<EMAIL>',
    password: '$2a$12$luO365dId/JUjLxZg5bK5.DS6FwKi1Xg1DjqeIz/HmCdOyo9Q5YyG',
    createdAt: timestamp,
    updatedAt: timestamp,
    isVerified: true,
    title: 'Guest',
  },
  {
    firstName: 'Test2',
    lastName: 'User',
    email: '<EMAIL>',
    password: '$2a$12$luO365dId/JUjLxZg5bK5.DS6FwKi1Xg1DjqeIz/HmCdOyo9Q5YyG',
    createdAt: timestamp,
    updatedAt: timestamp,
    isVerified: true,
    title: 'User',
  },
  {
    firstName: 'Test3',
    lastName: 'User',
    email: '<EMAIL>',
    password: '$2a$12$luO365dId/JUjLxZg5bK5.DS6FwKi1Xg1DjqeIz/HmCdOyo9Q5YyG',
    createdAt: timestamp,
    updatedAt: timestamp,
    isVerified: true,
    title: 'Expert',
  },
  {
    firstName: 'Test4',
    lastName: 'User',
    email: '<EMAIL>',
    password: '$2a$12$luO365dId/JUjLxZg5bK5.DS6FwKi1Xg1DjqeIz/HmCdOyo9Q5YyG',
    createdAt: timestamp,
    updatedAt: timestamp,
    isVerified: true,
    title: 'Account Admin',
  },
  {
    firstName: 'Test5',
    lastName: 'User',
    email: '<EMAIL>',
    password: '$2a$12$luO365dId/JUjLxZg5bK5.DS6FwKi1Xg1DjqeIz/HmCdOyo9Q5YyG',
    createdAt: timestamp,
    updatedAt: timestamp,
    isVerified: true,
    title: 'System Admin',
  },
  {
    firstName: 'Test6',
    lastName: 'User',
    email: '<EMAIL>',
    password: '$2a$12$luO365dId/JUjLxZg5bK5.DS6FwKi1Xg1DjqeIz/HmCdOyo9Q5YyG',
    createdAt: timestamp,
    updatedAt: timestamp,
    isVerified: true,
    title: 'Super Admin',
  },
  {
    firstName: 'Test7',
    lastName: 'User',
    email: '<EMAIL>',
    password: '$2a$12$luO365dId/JUjLxZg5bK5.DS6FwKi1Xg1DjqeIz/HmCdOyo9Q5YyG',
    createdAt: timestamp,
    updatedAt: timestamp,
    isVerified: true,
    title: 'Content Reviewer',
  },
  {
    firstName: 'Test8',
    lastName: 'User',
    email: '<EMAIL>',
    password: '$2a$12$luO365dId/JUjLxZg5bK5.DS6FwKi1Xg1DjqeIz/HmCdOyo9Q5YyG',
    createdAt: timestamp,
    updatedAt: timestamp,
    isVerified: true,
    title: 'Content Developer',
  },
];
const userRoles = [
  {
    userId: 1,
    roleId: 1, // Guest
  },
  {
    userId: 2,
    roleId: 2, // User
  },
  {
    userId: 3,
    roleId: 3, // Expert
  },
  {
    userId: 4,
    roleId: 4, // AccountAdmin
  },
  {
    userId: 5,
    roleId: 5, // SystemAdmin
  },
  {
    userId: 6,
    roleId: 6, // SuperAdmin
  },
  {
    userId: 7,
    roleId: 7, // contentReviewer
  },
  {
    userId: 8,
    roleId: 8, // contentDeveloper
  },
];
const accounts = [
  {
    name: 'account1',
    subdomain: 'emtrain',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    name: 'account2 search test',
    subdomain: 'stichfix',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    name: 'account3',
    subdomain: 'motive',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    name: 'account4',
    subdomain: 'guidespark',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    name: 'account5',
    subdomain: 'digitalthink',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];
const accountUsers = [
  {
    userId: 1,
    accountId: 1,
    roleId: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    userId: 2,
    accountId: 1,
    roleId: 2,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    userId: 3,
    accountId: 1,
    roleId: 3,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    userId: 4,
    accountId: 1,
    roleId: 4,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    userId: 5,
    accountId: 1,
    roleId: 5,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    userId: 6,
    accountId: 1,
    roleId: 6,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    userId: 7,
    accountId: 1,
    roleId: 7,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    userId: 8,
    accountId: 1,
    roleId: 8,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];

const bundles = [
  {
    name: 'Public resources',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    name: 'account1 resources',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    name: 'account2 resources',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    name: 'account3 resources',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  { // 5
    name: 'account4 resources',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    name: 'account5 resources',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];
const accountBundles = [
  {
    accountId: 1,
    bundleId: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    accountId: 1,
    bundleId: 2,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    accountId: 2,
    bundleId: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    accountId: 2,
    bundleId: 3,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    accountId: 3,
    bundleId: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    accountId: 3,
    bundleId: 4,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    accountId: 4,
    bundleId: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    accountId: 4,
    bundleId: 5,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    accountId: 5,
    bundleId: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    accountId: 5,
    bundleId: 6,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];
const resources = [
  {
    digestable: 'lessons',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: 'test lesson 1',
    isPublic: true,
  },
  {
    digestable: 'lessons',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: 'test lesson 2',
    isPublic: true,
  },
  {
    digestable: 'lessons',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: 'test lesson 3',
    isPublic: true,
  },
  {
    digestable: 'lessons',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: 'test lesson 4',
    isPublic: true,
  },
  { // 5
    digestable: 'programs',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: '',
    isPublic: true,
  },
  {
    digestable: 'programs',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: '',
    isPublic: true,
  },
  {
    digestable: 'programs',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: '',
    isPublic: true,
  },
  {
    digestable: 'programs',
    createdAt: timestamp,
    updatedAt: timestamp,
    searchText: '',
    isPublic: true,
  },
];
const lessons = [
  {
    published: false,
    title: 'draft lesson title',
    description: 'test lesson 1',
    lifecycle: 'draft',
    userId: 4,
    createdAt: timestamp,
    updatedAt: timestamp,
    resourceId: 1,
  },
  {
    published: false,
    title: 'published lesson title',
    description: 'test lesson 2',
    lifecycle: 'publish',
    userId: 4,
    createdAt: timestamp,
    updatedAt: timestamp,
    resourceId: 2,
  },
  {
    published: false,
    title: 'review lesson title',
    description: 'test lesson 3',
    lifecycle: 'review',
    userId: 4,
    createdAt: timestamp,
    updatedAt: timestamp,
    resourceId: 3,
  },
  {
    published: false,
    title: 'close lesson title',
    description: 'test lesson 4',
    lifecycle: 'close',
    userId: 4,
    createdAt: timestamp,
    updatedAt: timestamp,
    resourceId: 4,
  },
];
const campaigns = [
  {
    name: 'draft campaign',
    accountId: 1,
    lifecycle: 'draft',
    startDate: format(addHours(new Date(), 2), 'YYYY-MM-DD HH:mm:ss'),
    endDate: format(addHours(new Date(), 96), 'YYYY-MM-DD HH:mm:ss'),
    retrainingPeriod: 60,
    objective: 'just because',
    reportFrequency: 'daily',
    status: 'scheduled',
    colorTheme: '#abcd12',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    name: 'active campaign',
    accountId: 1,
    lifecycle: 'active',
    startDate: format(addHours(new Date(), 2), 'YYYY-MM-DD HH:mm:ss'),
    endDate: format(addHours(new Date(), 96), 'YYYY-MM-DD HH:mm:ss'),
    retrainingPeriod: 90,
    objective: 'gettin trained',
    reportFrequency: 'monthly',
    status: 'scheduled',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    name: 'review campaign',
    accountId: 1,
    type: 'open',
    lifecycle: 'review',
    startDate: format(addHours(new Date(), 2), 'YYYY-MM-DD HH:mm:ss'),
    duration: 10,
    retrainingPeriod: 180,
    objective: 'be a better person',
    reportFrequency: 'yearly',
    status: 'scheduled',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    name: 'close campaign',
    accountId: 1,
    lifecycle: 'close',
    startDate: format(addHours(new Date(), 2), 'YYYY-MM-DD HH:mm:ss'),
    endDate: format(addHours(new Date(), 96), 'YYYY-MM-DD HH:mm:ss'),
    retrainingPeriod: 180,
    objective: 'be a better person',
    reportFrequency: 'yearly',
    status: 'scheduled',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];
const programs = [
  {
    name: 'draft program',
    lifecycle: 'draft',
    createdAt: timestamp,
    updatedAt: format(addHours(new Date(), 2), 'YYYY-MM-DD HH:mm:ss'),
    resourceId: 5,
  },
  {
    name: 'publish program',
    lifecycle: 'publish',
    createdAt: timestamp,
    updatedAt: format(addHours(new Date(), 1), 'YYYY-MM-DD HH:mm:ss'),
    resourceId: 6,
  },
  {
    name: 'review program',
    lifecycle: 'review',
    createdAt: timestamp,
    updatedAt: timestamp,
    resourceId: 7,
  },
  {
    name: 'close program',
    lifecycle: 'close',
    createdAt: timestamp,
    updatedAt: timestamp,
    resourceId: 8,
  },
];
const lessonLessonCards = [
  {
    lessonId: 1,
    lessonCardId: 1,
    position: 1,
    isOwner: true,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    lessonId: 2,
    lessonCardId: 2,
    position: 1,
    isOwner: true,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    lessonId: 3,
    lessonCardId: 3,
    position: 1,
    isOwner: true,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    lessonId: 4,
    lessonCardId: 4,
    position: 1,
    isOwner: true,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];
const lessonCards = [
  {
    title: 'draft lesson card',
    description: 'lesson card 1 description',
    cardType: 'text',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    title: 'publish lesson card',
    description: 'lesson card 2 description',
    cardType: 'text',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    title: 'review lesson card',
    description: 'lesson card 3 boolean quiz',
    cardType: 'quizBoolean',
    list1: 'yesNo',
    list2: 'trueFalse',
    question1: 'Do you like pizza?',
    question2: 'Pizza is the best food',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    title: 'close lesson card',
    description: 'lesson card 4 description',
    cardType: 'text',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];
const answerCards = [
  {
    accountId: 1,
    userId: 4,
    lessonLessonCardId: 1,
    textAnswer: 'draft answer card',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    accountId: 1,
    userId: 4,
    lessonLessonCardId: 2,
    textAnswer: 'publish answer card',
    answer1: 2,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    accountId: 1,
    userId: 4,
    lessonLessonCardId: 3,
    textAnswer: 'review answer card',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    accountId: 1,
    userId: 4,
    lessonLessonCardId: 4,
    textAnswer: 'close answer card',
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];
const resourceBundles = [
  {
    bundleId: 1,
    resourceId: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 2,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 3,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 4,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 2,
    resourceId: 1,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 2,
    resourceId: 2,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 2,
    resourceId: 3,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 2,
    resourceId: 4,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 5,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 6,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 7,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 1,
    resourceId: 8,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 2,
    resourceId: 5,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 2,
    resourceId: 6,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 2,
    resourceId: 7,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
  {
    bundleId: 2,
    resourceId: 8,
    createdAt: timestamp,
    updatedAt: timestamp,
  },
];

async function loadData() {
  const userRolesData = userRoles.map(({ userId, roleId }) => {
    return {
      userId,
      roleId,
      createdAt: timestamp,
      updatedAt: timestamp,
    };
  });

  await queryInterface.bulkInsert('accounts', accounts);
  await queryInterface.bulkInsert('users', users);
  await queryInterface.bulkInsert('userRoles', userRolesData);
  await queryInterface.bulkInsert('accountUsers', accountUsers);
  await queryInterface.bulkInsert('resources', resources);
  await queryInterface.bulkInsert('lessons', lessons);
  await queryInterface.bulkInsert('campaigns', campaigns);
  await queryInterface.bulkInsert('programs', programs);
  await queryInterface.bulkInsert('lessonCards', lessonCards);
  await queryInterface.bulkInsert('lessonLessonCards', lessonLessonCards);
  await queryInterface.bulkInsert('answerCards', answerCards);
  await queryInterface.bulkInsert('bundles', bundles);
  await queryInterface.bulkInsert('accountBundles', accountBundles);
  await queryInterface.bulkInsert('resourceBundles', resourceBundles);
}

/* Mocha prefers not using arrow functions */
/* eslint prefer-arrow-callback: 0 */
/* eslint no-unused-expressions: 0 */

describe('Role Access Tests', function () {
  before(async function () {
    await hootsworth.util.truncateSchema();
    await loadData();
  });
  function compareItems(a, b) {
    return a.id - b.id;
  }
  describe('lessons', function () {
    it('should get a list of lessons for Guest', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSONS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' lessons
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        title: 'published lesson title',
        lifecycle: 'publish',
      });
    });
    it('should get a list of lessons for User', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSONS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' lessons
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        title: 'published lesson title',
        lifecycle: 'publish',
      });
    });
    it('should get a list of lessons for Expert', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSONS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' and 'review' lessons
      expect(res.body.data).to.have.lengthOf(2);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        title: 'published lesson title',
        lifecycle: 'publish',
      });
      expect(res.body.data[1]).to.include({
        title: 'review lesson title',
        lifecycle: 'review',
      });
    });
    it('should get a list of lessons for AccountAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSONS_URL}?$sort[id]=1`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'publish' lessons
      expect(res.body.data).to.have.lengthOf(2);
      expect(res.body.data[0]).to.include({
        title: 'published lesson title',
        lifecycle: 'publish',
      });
      expect(res.body.data[1]).to.include({
        title: 'review lesson title',
        lifecycle: 'review',
      });
    });
    it('should get a list of lessons for SystemAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSONS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' lessons
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        title: 'draft lesson title',
        lifecycle: 'draft',
      });
      expect(res.body.data[1]).to.include({
        title: 'published lesson title',
        lifecycle: 'publish',
      });
      expect(res.body.data[2]).to.include({
        title: 'review lesson title',
        lifecycle: 'review',
      });
      expect(res.body.data[3]).to.include({
        title: 'close lesson title',
        lifecycle: 'close',
      });
    });
    it('should get a list of lessons for SuperAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSONS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' lessons
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        title: 'draft lesson title',
        lifecycle: 'draft',
      });
      expect(res.body.data[1]).to.include({
        title: 'published lesson title',
        lifecycle: 'publish',
      });
      expect(res.body.data[2]).to.include({
        title: 'review lesson title',
        lifecycle: 'review',
      });
      expect(res.body.data[3]).to.include({
        title: 'close lesson title',
        lifecycle: 'close',
      });
    });
    it('should get a list of lessons for ContentReviewer', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSONS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' and 'review' lessons
      expect(res.body.data).to.have.lengthOf(2);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        title: 'published lesson title',
        lifecycle: 'publish',
      });
      expect(res.body.data[1]).to.include({
        title: 'review lesson title',
        lifecycle: 'review',
      });
    });
    it('should get a list of lessons for ContentDeveloper', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSONS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' lessons
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        title: 'draft lesson title',
        lifecycle: 'draft',
      });
      expect(res.body.data[1]).to.include({
        title: 'published lesson title',
        lifecycle: 'publish',
      });
      expect(res.body.data[2]).to.include({
        title: 'review lesson title',
        lifecycle: 'review',
      });
      expect(res.body.data[3]).to.include({
        title: 'close lesson title',
        lifecycle: 'close',
      });
    });
  });
  describe('campaigns', function () {
    it('should get a list of campaigns for Guest', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${CAMPAIGNS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'active' campaigns
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        name: 'active campaign',
        lifecycle: 'active',
      });
    });
    it('should get a list of campaigns for User', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${CAMPAIGNS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' campaigns
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        name: 'active campaign',
        lifecycle: 'active',
      });
    });
    it('should get a list of campaigns for Expert', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${CAMPAIGNS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' campaign
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        name: 'active campaign',
        lifecycle: 'active',
      });
    });
    it('should get a list of campaigns for AccountAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${CAMPAIGNS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' campaigns
      expect(res.body.data).to.have.lengthOf(4);
      expect(res.body.data[0]).to.include({
        name: 'draft campaign',
        lifecycle: 'draft',
      });
      expect(res.body.data[1]).to.include({
        name: 'active campaign',
        lifecycle: 'active',
      });
      expect(res.body.data[2]).to.include({
        name: 'review campaign',
        lifecycle: 'review',
      });
      expect(res.body.data[3]).to.include({
        name: 'close campaign',
        lifecycle: 'close',
      });
    });
    it('should get a list of campaigns for SystemAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${CAMPAIGNS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' campaigns
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        name: 'draft campaign',
        lifecycle: 'draft',
      });
      expect(res.body.data[1]).to.include({
        name: 'active campaign',
        lifecycle: 'active',
      });
      expect(res.body.data[2]).to.include({
        name: 'review campaign',
        lifecycle: 'review',
      });
      expect(res.body.data[3]).to.include({
        name: 'close campaign',
        lifecycle: 'close',
      });
    });
    it('should get a list of campaigns for SuperAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${CAMPAIGNS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' campaigns
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        name: 'draft campaign',
        lifecycle: 'draft',
      });
      expect(res.body.data[1]).to.include({
        name: 'active campaign',
        lifecycle: 'active',
      });
      expect(res.body.data[2]).to.include({
        name: 'review campaign',
        lifecycle: 'review',
      });
      expect(res.body.data[3]).to.include({
        name: 'close campaign',
        lifecycle: 'close',
      });
    });
    it('should get a list of campaigns for ContentReviewer', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${CAMPAIGNS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      expect(res.body.data).to.have.lengthOf(2);
      res.body.data.sort(compareItems);
      // only 'publish' and 'review' lessons
      expect(res.body.data[0]).to.include({
        name: 'active campaign',
        lifecycle: 'active',
      });
      expect(res.body.data[1]).to.include({
        name: 'review campaign',
        lifecycle: 'review',
      });
    });
    it('should get a list of campaigns for ContentDeveloper', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${CAMPAIGNS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'active' 'review' and 'close' campaigns
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        name: 'draft campaign',
        lifecycle: 'draft',
      });
      expect(res.body.data[1]).to.include({
        name: 'active campaign',
        lifecycle: 'active',
      });
      expect(res.body.data[2]).to.include({
        name: 'review campaign',
        lifecycle: 'review',
      });
      expect(res.body.data[3]).to.include({
        name: 'close campaign',
        lifecycle: 'close',
      });
    });
  });
  describe('programs', function () {
    it('should get a list of programs for Guest', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${PROGRAMS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' programs
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        name: 'publish program',
        lifecycle: 'publish',
      });
    });
    it('should get a list of programs for User', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${PROGRAMS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' programs
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        name: 'publish program',
        lifecycle: 'publish',
      });
    });
    it('should get a list of programs for Expert', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${PROGRAMS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' program
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        name: 'publish program',
        lifecycle: 'publish',
      });
    });
    it('should get a list of programs for AccountAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${PROGRAMS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' & 'review' programs
      expect(res.body.data).to.have.lengthOf(2);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        name: 'publish program',
        lifecycle: 'publish',
      });
      expect(res.body.data[1]).to.include({
        name: 'review program',
        lifecycle: 'review',
      });
    });
    it('should get a list of programs for SystemAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${PROGRAMS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' programs
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        name: 'draft program',
        lifecycle: 'draft',
      });
      expect(res.body.data[1]).to.include({
        name: 'publish program',
        lifecycle: 'publish',
      });
      expect(res.body.data[2]).to.include({
        name: 'review program',
        lifecycle: 'review',
      });
      expect(res.body.data[3]).to.include({
        name: 'close program',
        lifecycle: 'close',
      });
    });
    it('should get a list of programs for SuperAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${PROGRAMS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' programs
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        name: 'draft program',
        lifecycle: 'draft',
      });
      expect(res.body.data[1]).to.include({
        name: 'publish program',
        lifecycle: 'publish',
      });
      expect(res.body.data[2]).to.include({
        name: 'review program',
        lifecycle: 'review',
      });
      expect(res.body.data[3]).to.include({
        name: 'close program',
        lifecycle: 'close',
      });
    });
    it('should get a list of programs for ContentReviewer', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${PROGRAMS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      expect(res.body.data).to.have.lengthOf(2);
      res.body.data.sort(compareItems);
      // only 'publish' and 'review' lessons
      expect(res.body.data[0]).to.include({
        name: 'publish program',
        lifecycle: 'publish',
      });
      expect(res.body.data[1]).to.include({
        name: 'review program',
        lifecycle: 'review',
      });
    });
    it('should get a list of programs for ContentDeveloper', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${PROGRAMS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' programs
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        name: 'draft program',
        lifecycle: 'draft',
      });
      expect(res.body.data[1]).to.include({
        name: 'publish program',
        lifecycle: 'publish',
      });
      expect(res.body.data[2]).to.include({
        name: 'review program',
        lifecycle: 'review',
      });
      expect(res.body.data[3]).to.include({
        name: 'close program',
        lifecycle: 'close',
      });
    });
  });
  describe('lesson cards', function () {
    it('should get a list of lesson cards for Guest', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSON_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' lesson cards
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        title: 'publish lesson card',
      });
    });
    it('should get a list of lesson cards for User', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSON_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' lesson cards
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        title: 'publish lesson card',
      });
    });
    it('should get a list of lesson cards for Expert', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSON_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' program
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        title: 'publish lesson card',
      });
    });
    it('should get a list of lesson cards for AccountAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSON_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish, review' lesson cards
      expect(res.body.data).to.have.lengthOf(2);
      expect(res.body.data[0]).to.include({
        title: 'publish lesson card',
      });
    });
    it('should get a list of lesson cards for SystemAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSON_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' lesson cards
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        title: 'draft lesson card',
      });
      expect(res.body.data[1]).to.include({
        title: 'publish lesson card',
      });
      expect(res.body.data[2]).to.include({
        title: 'review lesson card',
      });
      expect(res.body.data[3]).to.include({
        title: 'close lesson card',
      });
    });
    it('should get a list of lesson cards for SuperAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSON_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' lesson cards
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        title: 'draft lesson card',
      });
      expect(res.body.data[1]).to.include({
        title: 'publish lesson card',
      });
      expect(res.body.data[2]).to.include({
        title: 'review lesson card',
      });
      expect(res.body.data[3]).to.include({
        title: 'close lesson card',
      });
    });
    it('should get a list of lesson cards for ContentReviewer', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSON_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      expect(res.body.data).to.have.lengthOf(2);
      // only 'publish' and 'review' lessons
      expect(res.body.data[0]).to.include({
        title: 'publish lesson card',
      });
      expect(res.body.data[1]).to.include({
        title: 'review lesson card',
      });
    });
    it('should get a list of lesson cards for ContentDeveloper', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${LESSON_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' lesson cards
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        title: 'draft lesson card',
      });
      expect(res.body.data[1]).to.include({
        title: 'publish lesson card',
      });
      expect(res.body.data[2]).to.include({
        title: 'review lesson card',
      });
      expect(res.body.data[3]).to.include({
        title: 'close lesson card',
      });
    });
  });
  describe('answer cards', function () {
    it('should get a list of answer cards for Guest', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${ANSWER_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' answer cards
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        textAnswer: 'publish answer card',
      });
    });
    it('should get a list of answer cards for User', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${ANSWER_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' answer cards
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        textAnswer: 'publish answer card',
      });
    });
    it('should get a list of answer cards for Expert', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${ANSWER_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // only 'publish' program
      expect(res.body.data).to.have.lengthOf(1);
      expect(res.body.data[0]).to.include({
        textAnswer: 'publish answer card',
      });
    });
    it('should get a list of answer cards for AccountAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${ANSWER_CARDS_URL}?$sort[id]=1`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // console.log(res.body.data);
      // only 'publish, review' answer cards
      expect(res.body.data).to.have.lengthOf(2);
      expect(res.body.data[0]).to.include({
        textAnswer: 'publish answer card',
      });
    });
    it('should get a list of answer cards for SystemAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${ANSWER_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' answer cards
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        textAnswer: 'draft answer card',
      });
      expect(res.body.data[1]).to.include({
        textAnswer: 'publish answer card',
      });
      expect(res.body.data[2]).to.include({
        textAnswer: 'review answer card',
      });
      expect(res.body.data[3]).to.include({
        textAnswer: 'close answer card',
      });
    });
    it('should get a list of answer cards for SuperAdmin', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${ANSWER_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' answer cards
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        textAnswer: 'draft answer card',
      });
      expect(res.body.data[1]).to.include({
        textAnswer: 'publish answer card',
      });
      expect(res.body.data[2]).to.include({
        textAnswer: 'review answer card',
      });
      expect(res.body.data[3]).to.include({
        textAnswer: 'close answer card',
      });
    });
    it('should get a list of answer cards for ContentReviewer', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${ANSWER_CARDS_URL}?$sort[id]=1`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      expect(res.body.data).to.have.lengthOf(2);
      // only 'publish' and 'review' lessons
      expect(res.body.data[0]).to.include({
        textAnswer: 'publish answer card',
      });
      expect(res.body.data[1]).to.include({
        textAnswer: 'review answer card',
      });
    });
    it('should get a list of answer cards for ContentDeveloper', async function () {
      await login('<EMAIL>');
      const res = await chai.request(server)
        .get(`${ANSWER_CARDS_URL}`)
        .set('authorization', getToken());
      expect(res).to.have.status(200);
      // 'draft' 'publish' 'review' and 'close' answer cards
      expect(res.body.data).to.have.lengthOf(4);
      res.body.data.sort(compareItems);
      expect(res.body.data[0]).to.include({
        textAnswer: 'draft answer card',
      });
      expect(res.body.data[1]).to.include({
        textAnswer: 'publish answer card',
      });
      expect(res.body.data[2]).to.include({
        textAnswer: 'review answer card',
      });
      expect(res.body.data[3]).to.include({
        textAnswer: 'close answer card',
      });
    });
  });
});
